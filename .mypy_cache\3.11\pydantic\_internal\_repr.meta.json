{"data_mtime": 1750100862, "dep_lines": [13, 10, 11, 13, 3, 5, 6, 9, 10, 1, 1, 1], "dep_prios": [10, 10, 5, 20, 5, 10, 5, 10, 20, 5, 30, 30], "dependencies": ["pydantic._internal._typing_extra", "typing_inspection.typing_objects", "typing_inspection.introspection", "pydantic._internal", "__future__", "types", "typing", "typing_extensions", "typing_inspection", "builtins", "_frozen_importlib", "abc"], "hash": "54edf4c6a9186a942a97dd04ca033a00dfe92872", "id": "pydantic._internal._repr", "ignore_all": true, "interface_hash": "4d5be8bcbb1c0eea5cc05673e2367cd838c8e09b", "mtime": 1748795478, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Python313\\Lib\\site-packages\\pydantic\\_internal\\_repr.py", "plugin_data": null, "size": 5081, "suppressed": [], "version_id": "1.15.0"}