{"data_mtime": 1750100861, "dep_lines": [4, 5, 6, 1, 2, 9, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 10, 5, 25, 5, 30, 30, 30, 30], "dependencies": ["pydantic.v1.fields", "pydantic.v1.main", "pydantic.v1.typing", "sys", "typing", "typing_extensions", "builtins", "_frozen_importlib", "_typeshed", "abc", "pydantic.v1.utils"], "hash": "a5f5b55199dd505402a18afeb3ceb79db1f107d1", "id": "pydantic.v1.annotated_types", "ignore_all": true, "interface_hash": "f2dc17f97c2ab06089b7791587f3dcc97556d9aa", "mtime": 1748795478, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Python313\\Lib\\site-packages\\pydantic\\v1\\annotated_types.py", "plugin_data": null, "size": 3157, "suppressed": [], "version_id": "1.15.0"}