{"data_mtime": 1750100861, "dep_lines": [10, 10, 3, 5, 6, 8, 1, 1, 1], "dep_prios": [10, 20, 5, 10, 5, 5, 5, 30, 30], "dependencies": ["pydantic._internal._internal_dataclass", "pydantic._internal", "__future__", "dataclasses", "typing", "pydantic_core", "builtins", "_frozen_importlib", "abc"], "hash": "4dc6bc42352275982bc1af25c5ce5b801665131b", "id": "pydantic.aliases", "ignore_all": true, "interface_hash": "c749150b0ce9c69dae32a16dd11ca453a26ad693", "mtime": 1748795478, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Python313\\Lib\\site-packages\\pydantic\\aliases.py", "plugin_data": null, "size": 4937, "suppressed": [], "version_id": "1.15.0"}