{".class": "MypyFile", "_fullname": "_decimal", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "BasicContext": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_decimal.BasicContext", "name": "BasicContext", "type": "decimal.Context"}}, "Clamped": {".class": "SymbolTableNode", "cross_ref": "decimal.Clamped", "kind": "Gdef"}, "Context": {".class": "SymbolTableNode", "cross_ref": "decimal.Context", "kind": "Gdef"}, "ConversionSyntax": {".class": "SymbolTableNode", "cross_ref": "decimal.ConversionSyntax", "kind": "Gdef"}, "Decimal": {".class": "SymbolTableNode", "cross_ref": "decimal.Decimal", "kind": "Gdef"}, "DecimalException": {".class": "SymbolTableNode", "cross_ref": "decimal.DecimalException", "kind": "Gdef"}, "DecimalTuple": {".class": "SymbolTableNode", "cross_ref": "decimal.DecimalTuple", "kind": "Gdef"}, "DefaultContext": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_decimal.DefaultContext", "name": "DefaultContext", "type": "decimal.Context"}}, "DivisionByZero": {".class": "SymbolTableNode", "cross_ref": "decimal.DivisionByZero", "kind": "Gdef"}, "DivisionImpossible": {".class": "SymbolTableNode", "cross_ref": "decimal.DivisionImpossible", "kind": "Gdef"}, "DivisionUndefined": {".class": "SymbolTableNode", "cross_ref": "decimal.DivisionUndefined", "kind": "Gdef"}, "ExtendedContext": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_decimal.ExtendedContext", "name": "ExtendedContext", "type": "decimal.Context"}}, "Final": {".class": "SymbolTableNode", "cross_ref": "typing.Final", "kind": "Gdef", "module_hidden": true, "module_public": false}, "FloatOperation": {".class": "SymbolTableNode", "cross_ref": "decimal.FloatOperation", "kind": "Gdef"}, "HAVE_CONTEXTVAR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_decimal.HAVE_CONTEXTVAR", "name": "HAVE_CONTEXTVAR", "type": "builtins.bool"}}, "HAVE_THREADS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_decimal.HAVE_THREADS", "name": "HAVE_THREADS", "type": "builtins.bool"}}, "Inexact": {".class": "SymbolTableNode", "cross_ref": "decimal.Inexact", "kind": "Gdef"}, "InvalidContext": {".class": "SymbolTableNode", "cross_ref": "decimal.InvalidContext", "kind": "Gdef"}, "InvalidOperation": {".class": "SymbolTableNode", "cross_ref": "decimal.InvalidOperation", "kind": "Gdef"}, "MAX_EMAX": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_decimal.MAX_EMAX", "name": "MAX_EMAX", "type": "builtins.int"}}, "MAX_PREC": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_decimal.MAX_PREC", "name": "MAX_PREC", "type": "builtins.int"}}, "MIN_EMIN": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_decimal.MIN_EMIN", "name": "MIN_EMIN", "type": "builtins.int"}}, "MIN_ETINY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_decimal.MIN_ETINY", "name": "MIN_ETINY", "type": "builtins.int"}}, "Overflow": {".class": "SymbolTableNode", "cross_ref": "decimal.Overflow", "kind": "Gdef"}, "ROUND_05UP": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_decimal.ROUND_05UP", "name": "ROUND_05UP", "type": "builtins.str"}}, "ROUND_CEILING": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_decimal.ROUND_CEILING", "name": "ROUND_CEILING", "type": "builtins.str"}}, "ROUND_DOWN": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_decimal.ROUND_DOWN", "name": "ROUND_DOWN", "type": "builtins.str"}}, "ROUND_FLOOR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_decimal.ROUND_FLOOR", "name": "ROUND_FLOOR", "type": "builtins.str"}}, "ROUND_HALF_DOWN": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_decimal.ROUND_HALF_DOWN", "name": "ROUND_HALF_DOWN", "type": "builtins.str"}}, "ROUND_HALF_EVEN": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_decimal.ROUND_HALF_EVEN", "name": "ROUND_HALF_EVEN", "type": "builtins.str"}}, "ROUND_HALF_UP": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_decimal.ROUND_HALF_UP", "name": "ROUND_HALF_UP", "type": "builtins.str"}}, "ROUND_UP": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_decimal.ROUND_UP", "name": "ROUND_UP", "type": "builtins.str"}}, "Rounded": {".class": "SymbolTableNode", "cross_ref": "decimal.Rounded", "kind": "Gdef"}, "Subnormal": {".class": "SymbolTableNode", "cross_ref": "decimal.Subnormal", "kind": "Gdef"}, "TypeAlias": {".class": "SymbolTableNode", "cross_ref": "typing.TypeAlias", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Underflow": {".class": "SymbolTableNode", "cross_ref": "decimal.Underflow", "kind": "Gdef"}, "_ContextManager": {".class": "SymbolTableNode", "cross_ref": "decimal._ContextManager", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_TrapType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "_decimal._TrapType", "line": 25, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "TypeType", "item": "decimal.DecimalException"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_decimal.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_decimal.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_decimal.__file__", "name": "__file__", "type": "builtins.str"}}, "__libmpdec_version__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_decimal.__libmpdec_version__", "name": "__libmpdec_version__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_decimal.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_decimal.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_decimal.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "__version__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_decimal.__version__", "name": "__version__", "type": "builtins.str"}}, "getcontext": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_decimal.getcontext", "name": "getcontext", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getcontext", "ret_type": "decimal.Context", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "localcontext": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["ctx", "prec", "rounding", "Emin", "Emax", "capitals", "clamp", "traps", "flags"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_decimal.localcontext", "name": "localcontext", "type": {".class": "CallableType", "arg_kinds": [1, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["ctx", "prec", "rounding", "Emin", "Emax", "capitals", "clamp", "traps", "flags"], "arg_types": [{".class": "UnionType", "items": ["decimal.Context", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "_decimal._TrapType"}, "builtins.bool"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "_decimal._TrapType"}, "builtins.bool"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "localcontext", "ret_type": "decimal._ContextManager", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "setcontext": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_decimal.setcontext", "name": "setcontext", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["decimal.Context"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "setcontext", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef", "module_hidden": true, "module_public": false}}, "path": "c:\\Users\\<USER>\\.vscode\\extensions\\ms-python.mypy-type-checker-2025.2.0\\bundled\\libs\\mypy\\typeshed\\stdlib\\_decimal.pyi"}