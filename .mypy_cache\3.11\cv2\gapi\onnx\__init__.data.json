{".class": "MypyFile", "_fullname": "cv2.gapi.onnx", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "PyParams": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "cv2.gapi.onnx.PyParams", "name": "PyParams", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "cv2.gapi.onnx.PyParams", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "cv2.gapi.onnx", "mro": ["cv2.gapi.onnx.PyParams", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "cv2.gapi.onnx.PyParams.__init__", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.gapi.onnx.PyParams.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cv2.gapi.onnx.PyParams"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of PyParams", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.gapi.onnx.PyParams.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cv2.gapi.onnx.PyParams"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of PyParams", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "tag", "model_path"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.gapi.onnx.PyParams.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "tag", "model_path"], "arg_types": ["cv2.gapi.onnx.PyParams", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of PyParams", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.gapi.onnx.PyParams.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "tag", "model_path"], "arg_types": ["cv2.gapi.onnx.PyParams", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of PyParams", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cv2.gapi.onnx.PyParams"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of PyParams", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "tag", "model_path"], "arg_types": ["cv2.gapi.onnx.PyParams", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of PyParams", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "cfgAddExecutionProvider": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "cv2.gapi.onnx.PyParams.cfgAddExecutionProvider", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "ep"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.gapi.onnx.PyParams.cfgAddExecutionProvider", "name": "cfgAddExecutionProvider", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "ep"], "arg_types": ["cv2.gapi.onnx.PyParams", "cv2.gapi.onnx.ep.OpenVINO"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cfgAddExecutionProvider of PyParams", "ret_type": "cv2.gapi.onnx.PyParams", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.gapi.onnx.PyParams.cfgAddExecutionProvider", "name": "cfgAddExecutionProvider", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "ep"], "arg_types": ["cv2.gapi.onnx.PyParams", "cv2.gapi.onnx.ep.OpenVINO"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cfgAddExecutionProvider of PyParams", "ret_type": "cv2.gapi.onnx.PyParams", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "ep"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.gapi.onnx.PyParams.cfgAddExecutionProvider", "name": "cfgAddExecutionProvider", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "ep"], "arg_types": ["cv2.gapi.onnx.PyParams", "cv2.gapi.onnx.ep.DirectML"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cfgAddExecutionProvider of PyParams", "ret_type": "cv2.gapi.onnx.PyParams", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.gapi.onnx.PyParams.cfgAddExecutionProvider", "name": "cfgAddExecutionProvider", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "ep"], "arg_types": ["cv2.gapi.onnx.PyParams", "cv2.gapi.onnx.ep.DirectML"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cfgAddExecutionProvider of PyParams", "ret_type": "cv2.gapi.onnx.PyParams", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "ep"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.gapi.onnx.PyParams.cfgAddExecutionProvider", "name": "cfgAddExecutionProvider", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "ep"], "arg_types": ["cv2.gapi.onnx.PyParams", "cv2.gapi.onnx.ep.CoreML"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cfgAddExecutionProvider of PyParams", "ret_type": "cv2.gapi.onnx.PyParams", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.gapi.onnx.PyParams.cfgAddExecutionProvider", "name": "cfgAddExecutionProvider", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "ep"], "arg_types": ["cv2.gapi.onnx.PyParams", "cv2.gapi.onnx.ep.CoreML"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cfgAddExecutionProvider of PyParams", "ret_type": "cv2.gapi.onnx.PyParams", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "ep"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.gapi.onnx.PyParams.cfgAddExecutionProvider", "name": "cfgAddExecutionProvider", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "ep"], "arg_types": ["cv2.gapi.onnx.PyParams", "cv2.gapi.onnx.ep.CUDA"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cfgAddExecutionProvider of PyParams", "ret_type": "cv2.gapi.onnx.PyParams", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.gapi.onnx.PyParams.cfgAddExecutionProvider", "name": "cfgAddExecutionProvider", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "ep"], "arg_types": ["cv2.gapi.onnx.PyParams", "cv2.gapi.onnx.ep.CUDA"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cfgAddExecutionProvider of PyParams", "ret_type": "cv2.gapi.onnx.PyParams", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "ep"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.gapi.onnx.PyParams.cfgAddExecutionProvider", "name": "cfgAddExecutionProvider", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "ep"], "arg_types": ["cv2.gapi.onnx.PyParams", "cv2.gapi.onnx.ep.TensorRT"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cfgAddExecutionProvider of PyParams", "ret_type": "cv2.gapi.onnx.PyParams", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.gapi.onnx.PyParams.cfgAddExecutionProvider", "name": "cfgAddExecutionProvider", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "ep"], "arg_types": ["cv2.gapi.onnx.PyParams", "cv2.gapi.onnx.ep.TensorRT"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cfgAddExecutionProvider of PyParams", "ret_type": "cv2.gapi.onnx.PyParams", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "ep"], "arg_types": ["cv2.gapi.onnx.PyParams", "cv2.gapi.onnx.ep.OpenVINO"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cfgAddExecutionProvider of PyParams", "ret_type": "cv2.gapi.onnx.PyParams", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "ep"], "arg_types": ["cv2.gapi.onnx.PyParams", "cv2.gapi.onnx.ep.DirectML"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cfgAddExecutionProvider of PyParams", "ret_type": "cv2.gapi.onnx.PyParams", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "ep"], "arg_types": ["cv2.gapi.onnx.PyParams", "cv2.gapi.onnx.ep.CoreML"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cfgAddExecutionProvider of PyParams", "ret_type": "cv2.gapi.onnx.PyParams", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "ep"], "arg_types": ["cv2.gapi.onnx.PyParams", "cv2.gapi.onnx.ep.CUDA"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cfgAddExecutionProvider of PyParams", "ret_type": "cv2.gapi.onnx.PyParams", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "ep"], "arg_types": ["cv2.gapi.onnx.PyParams", "cv2.gapi.onnx.ep.TensorRT"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cfgAddExecutionProvider of PyParams", "ret_type": "cv2.gapi.onnx.PyParams", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "cfgDisableMemPattern": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.gapi.onnx.PyParams.cfgDisableMemPattern", "name": "cfgDisableMemPattern", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cv2.gapi.onnx.PyParams"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cfgDisableMemPattern of PyParams", "ret_type": "cv2.gapi.onnx.PyParams", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "cfgMeanStd": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "layer_name", "m", "s"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.gapi.onnx.PyParams.cfgMeanStd", "name": "cfgMeanStd", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "layer_name", "m", "s"], "arg_types": ["cv2.gapi.onnx.PyParams", "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.<PERSON>.<PERSON>"}, {".class": "TypeAliasType", "args": [], "type_ref": "cv2.<PERSON>.<PERSON>"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cfgMeanStd of PyParams", "ret_type": "cv2.gapi.onnx.PyParams", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "cfgNormalize": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "layer_name", "flag"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.gapi.onnx.PyParams.cfgNormalize", "name": "cfgNormalize", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "layer_name", "flag"], "arg_types": ["cv2.gapi.onnx.PyParams", "builtins.str", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cfgNormalize of PyParams", "ret_type": "cv2.gapi.onnx.PyParams", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "cfgOptLevel": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "opt_level"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.gapi.onnx.PyParams.cfgOptLevel", "name": "cfgOptLevel", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "opt_level"], "arg_types": ["cv2.gapi.onnx.PyParams", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cfgOptLevel of PyParams", "ret_type": "cv2.gapi.onnx.PyParams", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "cfgSessionOptions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "options"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.gapi.onnx.PyParams.cfgSessionOptions", "name": "cfgSessionOptions", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "options"], "arg_types": ["cv2.gapi.onnx.PyParams", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.map_string_and_string"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cfgSessionOptions of PyParams", "ret_type": "cv2.gapi.onnx.PyParams", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cv2.gapi.onnx.PyParams.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cv2.gapi.onnx.PyParams", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TRAIT_AS_IMAGE": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.gapi.onnx.TRAIT_AS_IMAGE", "name": "TRAIT_AS_IMAGE", "type": "builtins.int"}}, "TRAIT_AS_TENSOR": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.gapi.onnx.TRAIT_AS_TENSOR", "name": "TRAIT_AS_TENSOR", "type": "builtins.int"}}, "TraitAs": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "cv2.gapi.onnx.TraitAs", "line": 16, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "builtins.int"}}, "TraitAs_IMAGE": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.gapi.onnx.TraitAs_IMAGE", "name": "TraitAs_IMAGE", "type": "builtins.int"}}, "TraitAs_TENSOR": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.gapi.onnx.TraitAs_TENSOR", "name": "TraitAs_TENSOR", "type": "builtins.int"}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "cv2.gapi.onnx.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.gapi.onnx.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.gapi.onnx.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.gapi.onnx.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.gapi.onnx.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.gapi.onnx.__package__", "name": "__package__", "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.gapi.onnx.__path__", "name": "__path__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.gapi.onnx.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_typing": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef", "module_hidden": true, "module_public": false}, "cv2": {".class": "SymbolTableNode", "cross_ref": "cv2", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ep": {".class": "SymbolTableNode", "cross_ref": "cv2.gapi.onnx.ep", "kind": "Gdef", "module_public": false}, "params": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["tag", "model_path"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.gapi.onnx.params", "name": "params", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["tag", "model_path"], "arg_types": ["builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "params", "ret_type": "cv2.gapi.onnx.PyParams", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "path": "c:\\Python313\\Lib\\site-packages\\cv2\\gapi\\onnx\\__init__.pyi"}