# -*- coding: utf-8 -*-
"""
日志工具模块
提供统一的日志配置和管理功能
"""

import sys
from pathlib import Path
from typing import Optional
from loguru import logger


def setup_logger(
    log_level: str = "INFO",
    log_file: Optional[Path] = None,
    rotation: str = "10 MB",
    retention: str = "7 days",
    debug_mode: bool = False
) -> None:
    """设置日志系统
    
    Args:
        log_level: 日志级别 (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        log_file: 日志文件路径，None表示不写入文件
        rotation: 日志轮转大小
        retention: 日志保留时间
        debug_mode: 调试模式，显示更详细的信息
    """
    
    # 移除默认处理器
    logger.remove()
    
    # 控制台日志格式
    console_format = (
        "<green>{time:YYYY-MM-DD HH:mm:ss}</green> | "
        "<level>{level: <8}</level> | "
        "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> | "
        "<level>{message}</level>"
    )
    
    # 文件日志格式
    file_format = (
        "{time:YYYY-MM-DD HH:mm:ss.SSS} | "
        "{level: <8} | "
        "{name}:{function}:{line} | "
        "{message}"
    )
    
    # 添加控制台处理器
    logger.add(
        sys.stderr,
        format=console_format,
        level=log_level,
        colorize=True,
        backtrace=debug_mode,
        diagnose=debug_mode
    )
    
    # 添加文件处理器（如果指定了日志文件）
    if log_file:
        # 确保日志目录存在
        log_file.parent.mkdir(parents=True, exist_ok=True)
        
        logger.add(
            log_file,
            format=file_format,
            level=log_level,
            rotation=rotation,
            retention=retention,
            encoding="utf-8",
            backtrace=debug_mode,
            diagnose=debug_mode
        )
    
    logger.info(f"📝 日志系统已初始化 - 级别: {log_level}")
    if log_file:
        logger.info(f"📁 日志文件: {log_file}")


def get_logger(name: str = None):
    """获取指定名称的日志器
    
    Args:
        name: 日志器名称，默认为调用模块名
        
    Returns:
        配置好的日志器实例
    """
    if name:
        return logger.bind(name=name)
    return logger


class LoggerMixin:
    """日志混入类
    
    为其他类提供日志功能
    """
    
    @property
    def logger(self):
        """获取当前类的日志器"""
        return get_logger(self.__class__.__name__)


def log_function_call(func):
    """函数调用日志装饰器
    
    记录函数的调用和返回值
    """
    def wrapper(*args, **kwargs):
        func_name = func.__name__
        logger.debug(f"🔧 调用函数: {func_name}")
        
        try:
            result = func(*args, **kwargs)
            logger.debug(f"✅ 函数完成: {func_name}")
            return result
        except Exception as e:
            logger.error(f"❌ 函数异常: {func_name} - {str(e)}")
            raise
    
    return wrapper


def log_method_call(func):
    """方法调用日志装饰器
    
    记录类方法的调用和返回值
    """
    def wrapper(self, *args, **kwargs):
        class_name = self.__class__.__name__
        method_name = func.__name__
        logger.debug(f"🔧 调用方法: {class_name}.{method_name}")
        
        try:
            result = func(self, *args, **kwargs)
            logger.debug(f"✅ 方法完成: {class_name}.{method_name}")
            return result
        except Exception as e:
            logger.error(f"❌ 方法异常: {class_name}.{method_name} - {str(e)}")
            raise
    
    return wrapper


def log_performance(func):
    """性能监控装饰器
    
    记录函数执行时间
    """
    import time
    
    def wrapper(*args, **kwargs):
        func_name = func.__name__
        start_time = time.time()
        
        try:
            result = func(*args, **kwargs)
            end_time = time.time()
            execution_time = end_time - start_time
            logger.debug(f"⏱️ {func_name} 执行时间: {execution_time:.4f}秒")
            return result
        except Exception as e:
            end_time = time.time()
            execution_time = end_time - start_time
            logger.error(f"❌ {func_name} 执行失败 (耗时: {execution_time:.4f}秒) - {str(e)}")
            raise
    
    return wrapper


# 导出常用的日志器实例
__all__ = [
    'setup_logger',
    'get_logger', 
    'LoggerMixin',
    'log_function_call',
    'log_method_call',
    'log_performance',
    'logger'
]
