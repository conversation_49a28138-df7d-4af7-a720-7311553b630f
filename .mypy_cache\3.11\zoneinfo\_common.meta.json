{"data_mtime": 1750100859, "dep_lines": [1, 2, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 5, 30, 30, 30, 30], "dependencies": ["io", "typing", "builtins", "_frozen_importlib", "_io", "abc", "types"], "hash": "fbd0fbeb99d50e41a6236dddee46f2c285f79486", "id": "zoneinfo._common", "ignore_all": true, "interface_hash": "b1c19979b2b870fe8205bc7c0a6277cb18826d79", "mtime": 1750100071, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\.vscode\\extensions\\ms-python.mypy-type-checker-2025.2.0\\bundled\\libs\\mypy\\typeshed\\stdlib\\zoneinfo\\_common.pyi", "plugin_data": null, "size": 428, "suppressed": [], "version_id": "1.15.0"}