{".class": "MypyFile", "_fullname": "numpy._expired_attrs_2_0", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "Final": {".class": "SymbolTableNode", "cross_ref": "typing.Final", "kind": "Gdef", "module_hidden": true, "module_public": false}, "TypedDict": {".class": "SymbolTableNode", "cross_ref": "typing.TypedDict", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_ExpiredAttributesType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy._expired_attrs_2_0._ExpiredAttributesType", "name": "_ExpiredAttributesType", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_final"], "fullname": "numpy._expired_attrs_2_0._ExpiredAttributesType", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "numpy._expired_attrs_2_0", "mro": ["numpy._expired_attrs_2_0._ExpiredAttributesType", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["<PERSON><PERSON><PERSON><PERSON><PERSON>", "builtins.str"], ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "builtins.str"], ["cast", "builtins.str"], ["source", "builtins.str"], ["lookfor", "builtins.str"], ["who", "builtins.str"], ["fastCopyAndTranspose", "builtins.str"], ["set_numeric_ops", "builtins.str"], ["NINF", "builtins.str"], ["PINF", "builtins.str"], ["NZERO", "builtins.str"], ["PZERO", "builtins.str"], ["add_newdoc", "builtins.str"], ["add_docstring", "builtins.str"], ["add_newdoc_ufunc", "builtins.str"], ["compat", "builtins.str"], ["safe_eval", "builtins.str"], ["float_", "builtins.str"], ["complex_", "builtins.str"], ["longfloat", "builtins.str"], ["singlecomplex", "builtins.str"], ["cfloat", "builtins.str"], ["longcomplex", "builtins.str"], ["clongfloat", "builtins.str"], ["string_", "builtins.str"], ["unicode_", "builtins.str"], ["Inf", "builtins.str"], ["Infinity", "builtins.str"], ["NaN", "builtins.str"], ["infty", "builtins.str"], ["issctype", "builtins.str"], ["maximum_sctype", "builtins.str"], ["obj2sctype", "builtins.str"], ["sctype2char", "builtins.str"], ["sctypes", "builtins.str"], ["issubsctype", "builtins.str"], ["set_string_function", "builtins.str"], ["asfarray", "builtins.str"], ["issubclass_", "builtins.str"], ["tracemalloc_domain", "builtins.str"], ["mat", "builtins.str"], ["recfromcsv", "builtins.str"], ["recfromtxt", "builtins.str"], ["deprecate", "builtins.str"], ["deprecate_with_doc", "builtins.str"], ["disp", "builtins.str"], ["find_common_type", "builtins.str"], ["round_", "builtins.str"], ["get_array_wrap", "builtins.str"], ["DataSource", "builtins.str"], ["nbytes", "builtins.str"], ["byte_bounds", "builtins.str"], ["compare_chararrays", "builtins.str"], ["format_parser", "builtins.str"], ["alltrue", "builtins.str"], ["sometrue", "builtins.str"]], "readonly_keys": [], "required_keys": ["DataSource", "Inf", "Infinity", "NINF", "NZERO", "NaN", "PINF", "PZERO", "add_docstring", "add_newdoc", "add_newdoc_ufunc", "alltrue", "asfarray", "byte_bounds", "cast", "cfloat", "clongfloat", "compare_chararrays", "compat", "complex_", "deprecate", "deprecate_with_doc", "disp", "fastCopyAndTranspose", "find_common_type", "float_", "format_parser", "get_array_wrap", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "infty", "issctype", "issubclass_", "issubsctype", "longcomplex", "longfloat", "lookfor", "mat", "maximum_sctype", "nbytes", "obj2sctype", "recfromcsv", "recfromtxt", "round_", "safe_eval", "sctype2char", "sctypes", "set_numeric_ops", "set_string_function", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "singlecomplex", "sometrue", "source", "string_", "tracemalloc_domain", "unicode_", "who"]}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy._expired_attrs_2_0.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy._expired_attrs_2_0.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__expired_attributes__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready", "has_explicit_value"], "fullname": "numpy._expired_attrs_2_0.__expired_attributes__", "name": "__expired_attributes__", "type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy._expired_attrs_2_0._ExpiredAttributesType"}}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy._expired_attrs_2_0.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy._expired_attrs_2_0.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy._expired_attrs_2_0.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy._expired_attrs_2_0.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "final": {".class": "SymbolTableNode", "cross_ref": "typing.final", "kind": "Gdef", "module_hidden": true, "module_public": false}, "type_check_only": {".class": "SymbolTableNode", "cross_ref": "typing.type_check_only", "kind": "Gdef", "module_hidden": true, "module_public": false}}, "path": "c:\\Python313\\Lib\\site-packages\\numpy\\_expired_attrs_2_0.pyi"}