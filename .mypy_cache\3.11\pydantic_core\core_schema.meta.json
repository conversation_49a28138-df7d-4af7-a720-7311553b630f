{"data_mtime": 1750100860, "dep_lines": [10, 6, 8, 9, 11, 12, 13, 14, 16, 29, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 10, 10, 5, 5, 5, 5, 5, 25, 5, 30, 30, 30, 30, 30], "dependencies": ["collections.abc", "__future__", "sys", "warnings", "datetime", "decimal", "re", "typing", "typing_extensions", "pydantic_core", "builtins", "_frozen_importlib", "_typeshed", "abc", "pydantic_core._pydantic_core", "types"], "hash": "f248b741bbc27d04ae4f112d4d977271c2f465bf", "id": "pydantic_core.core_schema", "ignore_all": true, "interface_hash": "71bbc3e7c0d8e4b1bf7f055f7460b931d6777c8d", "mtime": 1748795470, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Python313\\Lib\\site-packages\\pydantic_core\\core_schema.py", "plugin_data": null, "size": 153980, "suppressed": [], "version_id": "1.15.0"}