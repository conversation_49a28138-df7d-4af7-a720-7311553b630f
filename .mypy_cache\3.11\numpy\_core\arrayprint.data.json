{".class": "MypyFile", "_fullname": "numpy._core.arrayprint", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Final": {".class": "SymbolTableNode", "cross_ref": "typing.Final", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing.Literal", "kind": "Gdef", "module_hidden": true, "module_public": false}, "NDArray": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._array_like.NDArray", "kind": "Gdef", "module_hidden": true, "module_public": false}, "SupportsIndex": {".class": "SymbolTableNode", "cross_ref": "typing.SupportsIndex", "kind": "Gdef", "module_hidden": true, "module_public": false}, "TypeAlias": {".class": "SymbolTableNode", "cross_ref": "typing.TypeAlias", "kind": "Gdef", "module_hidden": true, "module_public": false}, "TypedDict": {".class": "SymbolTableNode", "cross_ref": "typing.TypedDict", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_CharLike_co": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._scalars._CharLike_co", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_FloatLike_co": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._scalars._FloatLike_co", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_FloatMode": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "numpy._core.arrayprint._FloatMode", "line": 27, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "fixed"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "unique"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "maxprec"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "maxprec_equal"}], "uses_pep604_syntax": false}}}, "_FormatDict": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy._core.arrayprint._FormatDict", "name": "_FormatDict", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "numpy._core.arrayprint._FormatDict", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "numpy._core.arrayprint", "mro": ["numpy._core.arrayprint._FormatDict", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["bool", {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "numpy.bool"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], ["int", {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.integer"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], ["<PERSON><PERSON><PERSON>", {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["datetime.<PERSON><PERSON><PERSON>", "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.timedelta64"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], ["datetime", {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["datetime.date", "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.datetime64"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], ["float", {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.floating"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], ["longfloat", {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.longdouble"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], ["complexfloat", {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.complexfloating"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], ["longcomplexfloat", {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.clongdouble"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], ["void", {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["numpy.void"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], ["numpystr", {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._scalars._CharLike_co"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], ["object", {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.object"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], ["all", {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.object"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], ["int_kind", {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.integer"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], ["float_kind", {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.floating"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], ["complex_kind", {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.complexfloating"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], ["str_kind", {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._scalars._CharLike_co"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]], "readonly_keys": [], "required_keys": []}}}, "_FormatOptions": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy._core.arrayprint._FormatOptions", "name": "_FormatOptions", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "numpy._core.arrayprint._FormatOptions", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "numpy._core.arrayprint", "mro": ["numpy._core.arrayprint._FormatOptions", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["precision", "builtins.int"], ["threshold", "builtins.int"], ["edgeitems", "builtins.int"], ["linewidth", "builtins.int"], ["suppress", "builtins.bool"], ["nanstr", "builtins.str"], ["infstr", "builtins.str"], ["formatter", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._core.arrayprint._FormatDict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], ["sign", {".class": "TypeAliasType", "args": [], "type_ref": "numpy._core.arrayprint._Sign"}], ["floatmode", {".class": "TypeAliasType", "args": [], "type_ref": "numpy._core.arrayprint._FloatMode"}], ["legacy", {".class": "TypeAliasType", "args": [], "type_ref": "numpy._core.arrayprint._Legacy"}]], "readonly_keys": [], "required_keys": ["edgeitems", "floatmode", "formatter", "infstr", "legacy", "linewidth", "nanstr", "precision", "sign", "suppress", "threshold"]}}}, "_GeneratorContextManager": {".class": "SymbolTableNode", "cross_ref": "contextlib._GeneratorContextManager", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_Legacy": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "numpy._core.arrayprint._Legacy", "line": 29, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "1.13"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "1.21"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "1.25"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "2.1"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}], "uses_pep604_syntax": false}}}, "_LegacyNoStyle": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "numpy._core.arrayprint._LegacyNoStyle", "line": 28, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "1.21"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "1.25"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "2.1"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}], "uses_pep604_syntax": false}}}, "_NoValueType": {".class": "SymbolTableNode", "cross_ref": "numpy._globals._NoValueType", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_ReprFunc": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "numpy._core.arrayprint._ReprFunc", "line": 32, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "numpy._typing._array_like.NDArray"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_Sign": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "numpy._core.arrayprint._Sign", "line": 30, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "-"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "+"}, {".class": "LiteralType", "fallback": "builtins.str", "value": " "}], "uses_pep604_syntax": false}}}, "_Trim": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "numpy._core.arrayprint._Trim", "line": 31, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "k"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "."}, {".class": "LiteralType", "fallback": "builtins.str", "value": "0"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "-"}], "uses_pep604_syntax": false}}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "numpy._core.arrayprint.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy._core.arrayprint.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy._core.arrayprint.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__docformat__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "final_value": "restructuredtext", "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "numpy._core.arrayprint.__docformat__", "name": "__docformat__", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "restructuredtext"}, "type_ref": "builtins.str"}}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy._core.arrayprint.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy._core.arrayprint.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy._core.arrayprint.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy._core.arrayprint.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "array2string": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "numpy._core.arrayprint.array2string", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 5], "arg_names": ["a", "max_line_width", "precision", "suppress_small", "separator", "prefix", "style", "formatter", "threshold", "edgeitems", "sign", "floatmode", "suffix", "legacy"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy._core.arrayprint.array2string", "name": "array2string", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 5], "arg_names": ["a", "max_line_width", "precision", "suppress_small", "separator", "prefix", "style", "formatter", "threshold", "edgeitems", "sign", "floatmode", "suffix", "legacy"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["typing.SupportsIndex", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.str", "builtins.str", "numpy._globals._NoValueType", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._core.arrayprint._FormatDict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._core.arrayprint._Sign"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._core.arrayprint._FloatMode"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.str", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._core.arrayprint._Legacy"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "array2string", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy._core.arrayprint.array2string", "name": "array2string", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 5], "arg_names": ["a", "max_line_width", "precision", "suppress_small", "separator", "prefix", "style", "formatter", "threshold", "edgeitems", "sign", "floatmode", "suffix", "legacy"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["typing.SupportsIndex", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.str", "builtins.str", "numpy._globals._NoValueType", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._core.arrayprint._FormatDict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._core.arrayprint._Sign"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._core.arrayprint._FloatMode"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.str", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._core.arrayprint._Legacy"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "array2string", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 3], "arg_names": ["a", "max_line_width", "precision", "suppress_small", "separator", "prefix", "style", "formatter", "threshold", "edgeitems", "sign", "floatmode", "suffix", "legacy"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy._core.arrayprint.array2string", "name": "array2string", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 3], "arg_names": ["a", "max_line_width", "precision", "suppress_small", "separator", "prefix", "style", "formatter", "threshold", "edgeitems", "sign", "floatmode", "suffix", "legacy"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["typing.SupportsIndex", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.str", "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "numpy._core.arrayprint._ReprFunc"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._core.arrayprint._FormatDict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._core.arrayprint._Sign"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._core.arrayprint._FloatMode"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.str", {".class": "LiteralType", "fallback": "builtins.str", "value": "1.13"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "array2string", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy._core.arrayprint.array2string", "name": "array2string", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 3], "arg_names": ["a", "max_line_width", "precision", "suppress_small", "separator", "prefix", "style", "formatter", "threshold", "edgeitems", "sign", "floatmode", "suffix", "legacy"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["typing.SupportsIndex", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.str", "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "numpy._core.arrayprint._ReprFunc"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._core.arrayprint._FormatDict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._core.arrayprint._Sign"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._core.arrayprint._FloatMode"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.str", {".class": "LiteralType", "fallback": "builtins.str", "value": "1.13"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "array2string", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 3, 5, 5, 5, 5, 5, 5, 3], "arg_names": ["a", "max_line_width", "precision", "suppress_small", "separator", "prefix", "style", "formatter", "threshold", "edgeitems", "sign", "floatmode", "suffix", "legacy"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy._core.arrayprint.array2string", "name": "array2string", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 3, 5, 5, 5, 5, 5, 5, 3], "arg_names": ["a", "max_line_width", "precision", "suppress_small", "separator", "prefix", "style", "formatter", "threshold", "edgeitems", "sign", "floatmode", "suffix", "legacy"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["typing.SupportsIndex", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.str", "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "numpy._core.arrayprint._ReprFunc"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._core.arrayprint._FormatDict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._core.arrayprint._Sign"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._core.arrayprint._FloatMode"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.str", {".class": "LiteralType", "fallback": "builtins.str", "value": "1.13"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "array2string", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy._core.arrayprint.array2string", "name": "array2string", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 3, 5, 5, 5, 5, 5, 5, 3], "arg_names": ["a", "max_line_width", "precision", "suppress_small", "separator", "prefix", "style", "formatter", "threshold", "edgeitems", "sign", "floatmode", "suffix", "legacy"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["typing.SupportsIndex", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.str", "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "numpy._core.arrayprint._ReprFunc"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._core.arrayprint._FormatDict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._core.arrayprint._Sign"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._core.arrayprint._FloatMode"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.str", {".class": "LiteralType", "fallback": "builtins.str", "value": "1.13"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "array2string", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 5], "arg_names": ["a", "max_line_width", "precision", "suppress_small", "separator", "prefix", "style", "formatter", "threshold", "edgeitems", "sign", "floatmode", "suffix", "legacy"], "dataclass_transform_spec": null, "deprecated": "overload def (a: numpy.ndarray[builtins.tuple[builtins.int, ...], numpy.dtype[Any]], max_line_width: Union[builtins.int, None], precision: Union[typing.SupportsIndex, None], suppress_small: Union[builtins.bool, None], separator: builtins.str, prefix: builtins.str, style: def (numpy.ndarray[builtins.tuple[builtins.int, ...], numpy.dtype[Any]]) -> builtins.str, formatter: Union[TypedDict('numpy._core.arrayprint._FormatDict', {'bool'?: def (numpy.bool[builtins.bool]) -> builtins.str, 'int'?: def (numpy.integer[Any]) -> builtins.str, 'timedelta'?: def (numpy.timedelta64[Union[datetime.timedelta, builtins.int, None]]) -> builtins.str, 'datetime'?: def (numpy.datetime64[Union[datetime.date, builtins.int, None]]) -> builtins.str, 'float'?: def (numpy.floating[Any]) -> builtins.str, 'longfloat'?: def (numpy.floating[Union[numpy._typing._nbit_base._64Bit, numpy._typing._nbit_base._96Bit, numpy._typing._nbit_base._128Bit]]) -> builtins.str, 'complexfloat'?: def (numpy.complexfloating[Any, Any]) -> builtins.str, 'longcomplexfloat'?: def (numpy.complexfloating[Union[numpy._typing._nbit_base._64Bit, numpy._typing._nbit_base._96Bit, numpy._typing._nbit_base._128Bit], Union[numpy._typing._nbit_base._64Bit, numpy._typing._nbit_base._96Bit, numpy._typing._nbit_base._128Bit]]) -> builtins.str, 'void'?: def (numpy.void) -> builtins.str, 'numpystr'?: def (Union[builtins.str, builtins.bytes]) -> builtins.str, 'object'?: def (builtins.object) -> builtins.str, 'all'?: def (builtins.object) -> builtins.str, 'int_kind'?: def (numpy.integer[Any]) -> builtins.str, 'float_kind'?: def (numpy.floating[Any]) -> builtins.str, 'complex_kind'?: def (numpy.complexfloating[Any, Any]) -> builtins.str, 'str_kind'?: def (Union[builtins.str, builtins.bytes]) -> builtins.str}), None] =, threshold: Union[builtins.int, None] =, edgeitems: Union[builtins.int, None] =, sign: Union[Union[Literal['-'], Literal['+'], Literal[' ']], None] =, floatmode: Union[Union[Literal['fixed'], Literal['unique'], Literal['maxprec'], Literal['maxprec_equal']], None] =, suffix: builtins.str =, *, legacy: Union[Union[Literal['1.21'], Literal['1.25'], Literal['2.1'], Literal[False]], None] =) -> builtins.str of function numpy._core.arrayprint.array2string is deprecated: 'style' argument is deprecated and no longer functional except in 1.13 'legacy' mode", "flags": ["is_overload", "is_decorated"], "fullname": "numpy._core.arrayprint.array2string", "name": "array2string", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 5], "arg_names": ["a", "max_line_width", "precision", "suppress_small", "separator", "prefix", "style", "formatter", "threshold", "edgeitems", "sign", "floatmode", "suffix", "legacy"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["typing.SupportsIndex", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.str", "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "numpy._core.arrayprint._ReprFunc"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._core.arrayprint._FormatDict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._core.arrayprint._Sign"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._core.arrayprint._FloatMode"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.str", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._core.arrayprint._LegacyNoStyle"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "array2string", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy._core.arrayprint.array2string", "name": "array2string", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 5], "arg_names": ["a", "max_line_width", "precision", "suppress_small", "separator", "prefix", "style", "formatter", "threshold", "edgeitems", "sign", "floatmode", "suffix", "legacy"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["typing.SupportsIndex", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.str", "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "numpy._core.arrayprint._ReprFunc"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._core.arrayprint._FormatDict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._core.arrayprint._Sign"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._core.arrayprint._FloatMode"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.str", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._core.arrayprint._LegacyNoStyle"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "array2string", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 3, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["a", "max_line_width", "precision", "suppress_small", "separator", "prefix", "style", "formatter", "threshold", "edgeitems", "sign", "floatmode", "suffix", "legacy"], "dataclass_transform_spec": null, "deprecated": "overload def (a: numpy.ndarray[builtins.tuple[builtins.int, ...], numpy.dtype[Any]], max_line_width: Union[builtins.int, None] =, precision: Union[typing.SupportsIndex, None] =, suppress_small: Union[builtins.bool, None] =, separator: builtins.str =, prefix: builtins.str =, *, style: def (numpy.ndarray[builtins.tuple[builtins.int, ...], numpy.dtype[Any]]) -> builtins.str, formatter: Union[TypedDict('numpy._core.arrayprint._FormatDict', {'bool'?: def (numpy.bool[builtins.bool]) -> builtins.str, 'int'?: def (numpy.integer[Any]) -> builtins.str, 'timedelta'?: def (numpy.timedelta64[Union[datetime.timedelta, builtins.int, None]]) -> builtins.str, 'datetime'?: def (numpy.datetime64[Union[datetime.date, builtins.int, None]]) -> builtins.str, 'float'?: def (numpy.floating[Any]) -> builtins.str, 'longfloat'?: def (numpy.floating[Union[numpy._typing._nbit_base._64Bit, numpy._typing._nbit_base._96Bit, numpy._typing._nbit_base._128Bit]]) -> builtins.str, 'complexfloat'?: def (numpy.complexfloating[Any, Any]) -> builtins.str, 'longcomplexfloat'?: def (numpy.complexfloating[Union[numpy._typing._nbit_base._64Bit, numpy._typing._nbit_base._96Bit, numpy._typing._nbit_base._128Bit], Union[numpy._typing._nbit_base._64Bit, numpy._typing._nbit_base._96Bit, numpy._typing._nbit_base._128Bit]]) -> builtins.str, 'void'?: def (numpy.void) -> builtins.str, 'numpystr'?: def (Union[builtins.str, builtins.bytes]) -> builtins.str, 'object'?: def (builtins.object) -> builtins.str, 'all'?: def (builtins.object) -> builtins.str, 'int_kind'?: def (numpy.integer[Any]) -> builtins.str, 'float_kind'?: def (numpy.floating[Any]) -> builtins.str, 'complex_kind'?: def (numpy.complexfloating[Any, Any]) -> builtins.str, 'str_kind'?: def (Union[builtins.str, builtins.bytes]) -> builtins.str}), None] =, threshold: Union[builtins.int, None] =, edgeitems: Union[builtins.int, None] =, sign: Union[Union[Literal['-'], Literal['+'], Literal[' ']], None] =, floatmode: Union[Union[Literal['fixed'], Literal['unique'], Literal['maxprec'], Literal['maxprec_equal']], None] =, suffix: builtins.str =, legacy: Union[Union[Literal['1.21'], Literal['1.25'], Literal['2.1'], Literal[False]], None] =) -> builtins.str of function numpy._core.arrayprint.array2string is deprecated: 'style' argument is deprecated and no longer functional except in 1.13 'legacy' mode", "flags": ["is_overload", "is_decorated"], "fullname": "numpy._core.arrayprint.array2string", "name": "array2string", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 3, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["a", "max_line_width", "precision", "suppress_small", "separator", "prefix", "style", "formatter", "threshold", "edgeitems", "sign", "floatmode", "suffix", "legacy"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["typing.SupportsIndex", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.str", "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "numpy._core.arrayprint._ReprFunc"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._core.arrayprint._FormatDict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._core.arrayprint._Sign"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._core.arrayprint._FloatMode"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.str", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._core.arrayprint._LegacyNoStyle"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "array2string", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy._core.arrayprint.array2string", "name": "array2string", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 3, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["a", "max_line_width", "precision", "suppress_small", "separator", "prefix", "style", "formatter", "threshold", "edgeitems", "sign", "floatmode", "suffix", "legacy"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["typing.SupportsIndex", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.str", "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "numpy._core.arrayprint._ReprFunc"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._core.arrayprint._FormatDict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._core.arrayprint._Sign"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._core.arrayprint._FloatMode"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.str", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._core.arrayprint._LegacyNoStyle"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "array2string", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 5], "arg_names": ["a", "max_line_width", "precision", "suppress_small", "separator", "prefix", "style", "formatter", "threshold", "edgeitems", "sign", "floatmode", "suffix", "legacy"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["typing.SupportsIndex", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.str", "builtins.str", "numpy._globals._NoValueType", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._core.arrayprint._FormatDict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._core.arrayprint._Sign"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._core.arrayprint._FloatMode"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.str", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._core.arrayprint._Legacy"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "array2string", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 3], "arg_names": ["a", "max_line_width", "precision", "suppress_small", "separator", "prefix", "style", "formatter", "threshold", "edgeitems", "sign", "floatmode", "suffix", "legacy"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["typing.SupportsIndex", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.str", "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "numpy._core.arrayprint._ReprFunc"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._core.arrayprint._FormatDict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._core.arrayprint._Sign"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._core.arrayprint._FloatMode"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.str", {".class": "LiteralType", "fallback": "builtins.str", "value": "1.13"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "array2string", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 3, 5, 5, 5, 5, 5, 5, 3], "arg_names": ["a", "max_line_width", "precision", "suppress_small", "separator", "prefix", "style", "formatter", "threshold", "edgeitems", "sign", "floatmode", "suffix", "legacy"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["typing.SupportsIndex", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.str", "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "numpy._core.arrayprint._ReprFunc"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._core.arrayprint._FormatDict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._core.arrayprint._Sign"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._core.arrayprint._FloatMode"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.str", {".class": "LiteralType", "fallback": "builtins.str", "value": "1.13"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "array2string", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 5], "arg_names": ["a", "max_line_width", "precision", "suppress_small", "separator", "prefix", "style", "formatter", "threshold", "edgeitems", "sign", "floatmode", "suffix", "legacy"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["typing.SupportsIndex", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.str", "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "numpy._core.arrayprint._ReprFunc"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._core.arrayprint._FormatDict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._core.arrayprint._Sign"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._core.arrayprint._FloatMode"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.str", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._core.arrayprint._LegacyNoStyle"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "array2string", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 3, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["a", "max_line_width", "precision", "suppress_small", "separator", "prefix", "style", "formatter", "threshold", "edgeitems", "sign", "floatmode", "suffix", "legacy"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["typing.SupportsIndex", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.str", "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "numpy._core.arrayprint._ReprFunc"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._core.arrayprint._FormatDict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._core.arrayprint._Sign"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._core.arrayprint._FloatMode"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.str", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._core.arrayprint._LegacyNoStyle"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "array2string", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "array_repr": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["arr", "max_line_width", "precision", "suppress_small"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy._core.arrayprint.array_repr", "name": "array_repr", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["arr", "max_line_width", "precision", "suppress_small"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.int"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, "typing.SupportsIndex"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.bool"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "array_repr", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "array_str": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["a", "max_line_width", "precision", "suppress_small"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy._core.arrayprint.array_str", "name": "array_str", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["a", "max_line_width", "precision", "suppress_small"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.int"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, "typing.SupportsIndex"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.bool"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "array_str", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "deprecated": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.deprecated", "kind": "Gdef", "module_hidden": true, "module_public": false}, "format_float_positional": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["x", "precision", "unique", "fractional", "trim", "sign", "pad_left", "pad_right", "min_digits"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy._core.arrayprint.format_float_positional", "name": "format_float_positional", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["x", "precision", "unique", "fractional", "trim", "sign", "pad_left", "pad_right", "min_digits"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._scalars._FloatLike_co"}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.int"], "uses_pep604_syntax": true}, "builtins.bool", "builtins.bool", {".class": "TypeAliasType", "args": [], "type_ref": "numpy._core.arrayprint._Trim"}, "builtins.bool", {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.int"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.int"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.int"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "format_float_positional", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "format_float_scientific": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["x", "precision", "unique", "trim", "sign", "pad_left", "exp_digits", "min_digits"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy._core.arrayprint.format_float_scientific", "name": "format_float_scientific", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["x", "precision", "unique", "trim", "sign", "pad_left", "exp_digits", "min_digits"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._scalars._FloatLike_co"}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.int"], "uses_pep604_syntax": true}, "builtins.bool", {".class": "TypeAliasType", "args": [], "type_ref": "numpy._core.arrayprint._Trim"}, "builtins.bool", {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.int"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.int"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.int"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "format_float_scientific", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_printoptions": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy._core.arrayprint.get_printoptions", "name": "get_printoptions", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_printoptions", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy._core.arrayprint._FormatOptions"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "np": {".class": "SymbolTableNode", "cross_ref": "numpy", "kind": "Gdef", "module_hidden": true, "module_public": false}, "overload": {".class": "SymbolTableNode", "cross_ref": "typing.overload", "kind": "Gdef", "module_hidden": true, "module_public": false}, "printoptions": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 5, 5], "arg_names": ["precision", "threshold", "edgeitems", "linewidth", "suppress", "nanstr", "infstr", "formatter", "sign", "floatmode", "legacy", "override_repr"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy._core.arrayprint.printoptions", "name": "printoptions", "type": {".class": "CallableType", "arg_kinds": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 5, 5], "arg_names": ["precision", "threshold", "edgeitems", "linewidth", "suppress", "nanstr", "infstr", "formatter", "sign", "floatmode", "legacy", "override_repr"], "arg_types": [{".class": "UnionType", "items": [{".class": "NoneType"}, "typing.SupportsIndex"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.int"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.int"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.int"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.bool"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.str"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.str"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._core.arrayprint._FormatDict"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._core.arrayprint._Sign"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._core.arrayprint._FloatMode"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._core.arrayprint._Legacy"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._core.arrayprint._ReprFunc"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "printoptions", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._core.arrayprint._FormatOptions"}, {".class": "NoneType"}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "contextlib._GeneratorContextManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_printoptions": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 5, 5], "arg_names": ["precision", "threshold", "edgeitems", "linewidth", "suppress", "nanstr", "infstr", "formatter", "sign", "floatmode", "legacy", "override_repr"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy._core.arrayprint.set_printoptions", "name": "set_printoptions", "type": {".class": "CallableType", "arg_kinds": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 5, 5], "arg_names": ["precision", "threshold", "edgeitems", "linewidth", "suppress", "nanstr", "infstr", "formatter", "sign", "floatmode", "legacy", "override_repr"], "arg_types": [{".class": "UnionType", "items": [{".class": "NoneType"}, "typing.SupportsIndex"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.int"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.int"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.int"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.bool"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.str"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.str"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._core.arrayprint._FormatDict"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._core.arrayprint._Sign"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._core.arrayprint._FloatMode"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._core.arrayprint._Legacy"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._core.arrayprint._ReprFunc"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_printoptions", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "type_check_only": {".class": "SymbolTableNode", "cross_ref": "typing.type_check_only", "kind": "Gdef", "module_hidden": true, "module_public": false}}, "path": "c:\\Python313\\Lib\\site-packages\\numpy\\_core\\arrayprint.pyi"}