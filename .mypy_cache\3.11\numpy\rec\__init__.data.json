{".class": "MypyFile", "_fullname": "numpy.rec", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "numpy.rec.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.rec.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.rec.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.rec.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.rec.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.rec.__package__", "name": "__package__", "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.rec.__path__", "name": "__path__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.rec.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "array": {".class": "SymbolTableNode", "cross_ref": "numpy._core.records.array", "kind": "Gdef"}, "find_duplicate": {".class": "SymbolTableNode", "cross_ref": "numpy._core.records.find_duplicate", "kind": "Gdef"}, "format_parser": {".class": "SymbolTableNode", "cross_ref": "numpy._core.records.format_parser", "kind": "Gdef"}, "fromarrays": {".class": "SymbolTableNode", "cross_ref": "numpy._core.records.fromarrays", "kind": "Gdef"}, "fromfile": {".class": "SymbolTableNode", "cross_ref": "numpy._core.records.fromfile", "kind": "Gdef"}, "fromrecords": {".class": "SymbolTableNode", "cross_ref": "numpy._core.records.fromrecords", "kind": "Gdef"}, "fromstring": {".class": "SymbolTableNode", "cross_ref": "numpy._core.records.fromstring", "kind": "Gdef"}, "recarray": {".class": "SymbolTableNode", "cross_ref": "numpy._core.records.recarray", "kind": "Gdef"}, "record": {".class": "SymbolTableNode", "cross_ref": "numpy._core.records.record", "kind": "Gdef"}}, "path": "c:\\Python313\\Lib\\site-packages\\numpy\\rec\\__init__.pyi"}