{"data_mtime": 1750100849, "dep_lines": [26, 30, 17, 19, 20, 21, 22, 23, 24, 26, 1, 1, 1, 1], "dep_prios": [10, 25, 5, 10, 10, 10, 10, 10, 10, 20, 5, 30, 30, 30], "dependencies": ["PIL.Image", "PIL.ImageWin", "__future__", "io", "os", "shutil", "subprocess", "sys", "tempfile", "PIL", "builtins", "_frozen_importlib", "abc", "typing"], "hash": "cec0d1017b9c5aa9515ebda3bcfb30a5c290693d", "id": "PIL.ImageGrab", "ignore_all": true, "interface_hash": "5fba9daf28bce261ce38184b86ccff11ae6931bc", "mtime": 1745696559, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Python313\\Lib\\site-packages\\PIL\\ImageGrab.py", "plugin_data": null, "size": 6594, "suppressed": [], "version_id": "1.15.0"}