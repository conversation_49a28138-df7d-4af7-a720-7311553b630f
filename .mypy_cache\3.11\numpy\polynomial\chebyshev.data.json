{".class": "MypyFile", "_fullname": "numpy.polynomial.cheb<PERSON><PERSON>v", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "ABCPolyBase": {".class": "SymbolTableNode", "cross_ref": "numpy.polynomial._polybase.ABCPolyBase", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Chebyshev": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "T"}], "extra_attrs": null, "type_ref": "numpy.polynomial._polybase.ABCPolyBase"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy.polynomial.chebyshev.Chebyshev", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "numpy.polynomial.chebyshev.Chebyshev", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "numpy.polynomial.cheb<PERSON><PERSON>v", "mro": ["numpy.polynomial.chebyshev.Chebyshev", "numpy.polynomial._polybase.ABCPolyBase", "builtins.object"], "names": {".class": "SymbolTable", "interpolate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": ["is_class"], "fullname": "numpy.polynomial.chebyshev.Chebyshev.interpolate", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1], "arg_names": [null, "func", "deg", "domain", "args"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_overload", "is_decorated"], "fullname": "numpy.polynomial.chebyshev.Chebyshev.interpolate", "name": "interpolate", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1], "arg_names": [null, "func", "deg", "domain", "args"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial.chebyshev._Self", "id": -1, "name": "_Self", "namespace": "numpy.polynomial.chebyshev.Chebyshev.interpolate#0", "upper_bound": "builtins.object", "values": [], "variance": 0}}, {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeAliasType", "args": ["numpy.float64"], "type_ref": "numpy._typing._array_like.NDArray"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._CoefSeries"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._scalars._IntLike_co"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co"}], "uses_pep604_syntax": true}, {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "interpolate of Chebyshev", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial.chebyshev._Self", "id": -1, "name": "_Self", "namespace": "numpy.polynomial.chebyshev.Chebyshev.interpolate#0", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial.chebyshev._Self", "id": -1, "name": "_Self", "namespace": "numpy.polynomial.chebyshev.Chebyshev.interpolate#0", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_classmethod", "is_ready", "is_inferred"], "fullname": "numpy.polynomial.chebyshev.Chebyshev.interpolate", "name": "interpolate", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1], "arg_names": [null, "func", "deg", "domain", "args"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial.chebyshev._Self", "id": -1, "name": "_Self", "namespace": "numpy.polynomial.chebyshev.Chebyshev.interpolate#0", "upper_bound": "builtins.object", "values": [], "variance": 0}}, {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeAliasType", "args": ["numpy.float64"], "type_ref": "numpy._typing._array_like.NDArray"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._CoefSeries"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._scalars._IntLike_co"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co"}], "uses_pep604_syntax": true}, {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "interpolate of Chebyshev", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial.chebyshev._Self", "id": -1, "name": "_Self", "namespace": "numpy.polynomial.chebyshev.Chebyshev.interpolate#0", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial.chebyshev._Self", "id": -1, "name": "_Self", "namespace": "numpy.polynomial.chebyshev.Chebyshev.interpolate#0", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 3], "arg_names": [null, "func", "deg", "domain", "args"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_overload", "is_decorated"], "fullname": "numpy.polynomial.chebyshev.Chebyshev.interpolate", "name": "interpolate", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 3], "arg_names": [null, "func", "deg", "domain", "args"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial.chebyshev._Self", "id": -1, "name": "_Self", "namespace": "numpy.polynomial.chebyshev.Chebyshev.interpolate#1", "upper_bound": "builtins.object", "values": [], "variance": 0}}, {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": [null, null, null], "arg_types": [{".class": "TypeAliasType", "args": ["numpy.float64"], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": true, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._CoefSeries"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._scalars._IntLike_co"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co"}], "uses_pep604_syntax": true}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "interpolate of Chebyshev", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial.chebyshev._Self", "id": -1, "name": "_Self", "namespace": "numpy.polynomial.chebyshev.Chebyshev.interpolate#1", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial.chebyshev._Self", "id": -1, "name": "_Self", "namespace": "numpy.polynomial.chebyshev.Chebyshev.interpolate#1", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_classmethod", "is_ready", "is_inferred"], "fullname": "numpy.polynomial.chebyshev.Chebyshev.interpolate", "name": "interpolate", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 3], "arg_names": [null, "func", "deg", "domain", "args"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial.chebyshev._Self", "id": -1, "name": "_Self", "namespace": "numpy.polynomial.chebyshev.Chebyshev.interpolate#1", "upper_bound": "builtins.object", "values": [], "variance": 0}}, {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": [null, null, null], "arg_types": [{".class": "TypeAliasType", "args": ["numpy.float64"], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": true, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._CoefSeries"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._scalars._IntLike_co"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co"}], "uses_pep604_syntax": true}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "interpolate of Chebyshev", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial.chebyshev._Self", "id": -1, "name": "_Self", "namespace": "numpy.polynomial.chebyshev.Chebyshev.interpolate#1", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial.chebyshev._Self", "id": -1, "name": "_Self", "namespace": "numpy.polynomial.chebyshev.Chebyshev.interpolate#1", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": [null, null, null, null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_overload", "is_decorated"], "fullname": "numpy.polynomial.chebyshev.Chebyshev.interpolate", "name": "interpolate", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": [null, null, null, null, null], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial.chebyshev._Self", "id": -1, "name": "_Self", "namespace": "numpy.polynomial.chebyshev.Chebyshev.interpolate", "upper_bound": "builtins.object", "values": [], "variance": 0}}, {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": [null, null, null], "arg_types": [{".class": "TypeAliasType", "args": ["numpy.float64"], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": true, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._CoefSeries"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._scalars._IntLike_co"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co"}], "uses_pep604_syntax": true}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "interpolate of Chebyshev", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial.chebyshev._Self", "id": -1, "name": "_Self", "namespace": "numpy.polynomial.chebyshev.Chebyshev.interpolate", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial.chebyshev._Self", "id": -1, "name": "_Self", "namespace": "numpy.polynomial.chebyshev.Chebyshev.interpolate", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_classmethod", "is_ready", "is_inferred"], "fullname": "numpy.polynomial.chebyshev.Chebyshev.interpolate", "name": "interpolate", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": [null, null, null, null, null], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial.chebyshev._Self", "id": -1, "name": "_Self", "namespace": "numpy.polynomial.chebyshev.Chebyshev.interpolate", "upper_bound": "builtins.object", "values": [], "variance": 0}}, {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": [null, null, null], "arg_types": [{".class": "TypeAliasType", "args": ["numpy.float64"], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": true, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._CoefSeries"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._scalars._IntLike_co"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co"}], "uses_pep604_syntax": true}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "interpolate of Chebyshev", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial.chebyshev._Self", "id": -1, "name": "_Self", "namespace": "numpy.polynomial.chebyshev.Chebyshev.interpolate", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial.chebyshev._Self", "id": -1, "name": "_Self", "namespace": "numpy.polynomial.chebyshev.Chebyshev.interpolate", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1], "arg_names": [null, "func", "deg", "domain", "args"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial.chebyshev._Self", "id": -1, "name": "_Self", "namespace": "numpy.polynomial.chebyshev.Chebyshev.interpolate#0", "upper_bound": "builtins.object", "values": [], "variance": 0}}, {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeAliasType", "args": ["numpy.float64"], "type_ref": "numpy._typing._array_like.NDArray"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._CoefSeries"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._scalars._IntLike_co"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co"}], "uses_pep604_syntax": true}, {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "interpolate of Chebyshev", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial.chebyshev._Self", "id": -1, "name": "_Self", "namespace": "numpy.polynomial.chebyshev.Chebyshev.interpolate#0", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial.chebyshev._Self", "id": -1, "name": "_Self", "namespace": "numpy.polynomial.chebyshev.Chebyshev.interpolate#0", "upper_bound": "builtins.object", "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 3], "arg_names": [null, "func", "deg", "domain", "args"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial.chebyshev._Self", "id": -1, "name": "_Self", "namespace": "numpy.polynomial.chebyshev.Chebyshev.interpolate#1", "upper_bound": "builtins.object", "values": [], "variance": 0}}, {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": [null, null, null], "arg_types": [{".class": "TypeAliasType", "args": ["numpy.float64"], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": true, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._CoefSeries"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._scalars._IntLike_co"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co"}], "uses_pep604_syntax": true}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "interpolate of Chebyshev", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial.chebyshev._Self", "id": -1, "name": "_Self", "namespace": "numpy.polynomial.chebyshev.Chebyshev.interpolate#1", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial.chebyshev._Self", "id": -1, "name": "_Self", "namespace": "numpy.polynomial.chebyshev.Chebyshev.interpolate#1", "upper_bound": "builtins.object", "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": [null, null, null, null, null], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial.chebyshev._Self", "id": -1, "name": "_Self", "namespace": "numpy.polynomial.chebyshev.Chebyshev.interpolate", "upper_bound": "builtins.object", "values": [], "variance": 0}}, {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": [null, null, null], "arg_types": [{".class": "TypeAliasType", "args": ["numpy.float64"], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": true, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._CoefSeries"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._scalars._IntLike_co"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co"}], "uses_pep604_syntax": true}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "interpolate of Chebyshev", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial.chebyshev._Self", "id": -1, "name": "_Self", "namespace": "numpy.polynomial.chebyshev.Chebyshev.interpolate", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial.chebyshev._Self", "id": -1, "name": "_Self", "namespace": "numpy.polynomial.chebyshev.Chebyshev.interpolate", "upper_bound": "builtins.object", "values": [], "variance": 0}]}]}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial.chebyshev.Chebyshev.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "numpy.polynomial.chebyshev.Chebyshev", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Concatenate": {".class": "SymbolTableNode", "cross_ref": "typing.Concatenate", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Final": {".class": "SymbolTableNode", "cross_ref": "typing.Final", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef", "module_hidden": true, "module_public": false}, "L": {".class": "SymbolTableNode", "cross_ref": "typing.Literal", "kind": "Gdef", "module_hidden": true, "module_public": false}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_Array1": {".class": "SymbolTableNode", "cross_ref": "numpy.polynomial._polytypes._Array1", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_Array2": {".class": "SymbolTableNode", "cross_ref": "numpy.polynomial._polytypes._Array2", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_CoefSeries": {".class": "SymbolTableNode", "cross_ref": "numpy.polynomial._polytypes._CoefSeries", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_FuncBinOp": {".class": "SymbolTableNode", "cross_ref": "numpy.polynomial._polytypes._FuncBinOp", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_FuncCompanion": {".class": "SymbolTableNode", "cross_ref": "numpy.polynomial._polytypes._FuncCompanion", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_FuncDer": {".class": "SymbolTableNode", "cross_ref": "numpy.polynomial._polytypes._FuncDer", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_FuncFit": {".class": "SymbolTableNode", "cross_ref": "numpy.polynomial._polytypes._FuncFit", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_FuncFromRoots": {".class": "SymbolTableNode", "cross_ref": "numpy.polynomial._polytypes._FuncFromRoots", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_FuncGauss": {".class": "SymbolTableNode", "cross_ref": "numpy.polynomial._polytypes._FuncGauss", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_FuncInteg": {".class": "SymbolTableNode", "cross_ref": "numpy.polynomial._polytypes._FuncInteg", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_FuncLine": {".class": "SymbolTableNode", "cross_ref": "numpy.polynomial._polytypes._FuncLine", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_FuncPoly2Ortho": {".class": "SymbolTableNode", "cross_ref": "numpy.polynomial._polytypes._FuncPoly2Ortho", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_FuncPow": {".class": "SymbolTableNode", "cross_ref": "numpy.polynomial._polytypes._FuncPow", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_FuncPts": {".class": "SymbolTableNode", "cross_ref": "numpy.polynomial._polytypes._FuncPts", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_FuncRoots": {".class": "SymbolTableNode", "cross_ref": "numpy.polynomial._polytypes._FuncRoots", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_FuncUnOp": {".class": "SymbolTableNode", "cross_ref": "numpy.polynomial._polytypes._FuncUnOp", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_FuncVal": {".class": "SymbolTableNode", "cross_ref": "numpy.polynomial._polytypes._FuncVal", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_FuncVal2D": {".class": "SymbolTableNode", "cross_ref": "numpy.polynomial._polytypes._FuncVal2D", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_FuncVal3D": {".class": "SymbolTableNode", "cross_ref": "numpy.polynomial._polytypes._FuncVal3D", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_FuncValFromRoots": {".class": "SymbolTableNode", "cross_ref": "numpy.polynomial._polytypes._FuncValFromRoots", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_FuncVander": {".class": "SymbolTableNode", "cross_ref": "numpy.polynomial._polytypes._FuncVander", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_FuncVander2D": {".class": "SymbolTableNode", "cross_ref": "numpy.polynomial._polytypes._FuncVander2D", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_FuncVander3D": {".class": "SymbolTableNode", "cross_ref": "numpy.polynomial._polytypes._FuncVander3D", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_FuncWeight": {".class": "SymbolTableNode", "cross_ref": "numpy.polynomial._polytypes._FuncWeight", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_IntLike_co": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._scalars._IntLike_co", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_RT": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial.chebyshev._RT", "name": "_RT", "upper_bound": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": ["builtins.int", "builtins.float", "builtins.complex"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.number"}, {".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "numpy.bool"}, "numpy.object_"], "uses_pep604_syntax": true}, "values": [], "variance": 0}}, "_SCT": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial.chebyshev._SCT", "name": "_SCT", "upper_bound": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": ["builtins.int", "builtins.float", "builtins.complex"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.number"}, "numpy.object_"], "uses_pep604_syntax": true}, "values": [], "variance": 0}}, "_Self": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial.chebyshev._Self", "name": "_Self", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "_Series": {".class": "SymbolTableNode", "cross_ref": "numpy.polynomial._polytypes._Series", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_SeriesLikeCoef_co": {".class": "SymbolTableNode", "cross_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co", "kind": "Gdef", "module_hidden": true, "module_public": false}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "numpy.polynomial.chebyshev.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.chebyshev.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.chebyshev.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.chebyshev.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.chebyshev.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.chebyshev.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.chebyshev.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_cseries_to_zseries": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["c"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.polynomial.chebyshev._cseries_to_zseries", "name": "_cseries_to_zseries", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["c"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial.chebyshev._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.polynomial.chebyshev._cseries_to_zseries", "upper_bound": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": ["builtins.int", "builtins.float", "builtins.complex"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.number"}, "numpy.object_"], "uses_pep604_syntax": true}, "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_cseries_to_zseries", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial.chebyshev._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.polynomial.chebyshev._cseries_to_zseries", "upper_bound": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": ["builtins.int", "builtins.float", "builtins.complex"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.number"}, "numpy.object_"], "uses_pep604_syntax": true}, "values": [], "variance": 0}], "type_ref": "numpy.polynomial._polytypes._Series"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial.chebyshev._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.polynomial.chebyshev._cseries_to_zseries", "upper_bound": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": ["builtins.int", "builtins.float", "builtins.complex"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.number"}, "numpy.object_"], "uses_pep604_syntax": true}, "values": [], "variance": 0}]}}}, "_zseries_der": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["zs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.polynomial.chebyshev._zseries_der", "name": "_zseries_der", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["zs"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial.chebyshev._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.polynomial.chebyshev._zseries_der", "upper_bound": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": ["builtins.int", "builtins.float", "builtins.complex"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.number"}, "numpy.object_"], "uses_pep604_syntax": true}, "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_zseries_der", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial.chebyshev._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.polynomial.chebyshev._zseries_der", "upper_bound": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": ["builtins.int", "builtins.float", "builtins.complex"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.number"}, "numpy.object_"], "uses_pep604_syntax": true}, "values": [], "variance": 0}], "type_ref": "numpy.polynomial._polytypes._Series"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial.chebyshev._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.polynomial.chebyshev._zseries_der", "upper_bound": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": ["builtins.int", "builtins.float", "builtins.complex"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.number"}, "numpy.object_"], "uses_pep604_syntax": true}, "values": [], "variance": 0}]}}}, "_zseries_div": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["z1", "z2"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.polynomial.chebyshev._zseries_div", "name": "_zseries_div", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["z1", "z2"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial.chebyshev._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.polynomial.chebyshev._zseries_div", "upper_bound": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": ["builtins.int", "builtins.float", "builtins.complex"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.number"}, "numpy.object_"], "uses_pep604_syntax": true}, "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial.chebyshev._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.polynomial.chebyshev._zseries_div", "upper_bound": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": ["builtins.int", "builtins.float", "builtins.complex"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.number"}, "numpy.object_"], "uses_pep604_syntax": true}, "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_zseries_div", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial.chebyshev._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.polynomial.chebyshev._zseries_div", "upper_bound": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": ["builtins.int", "builtins.float", "builtins.complex"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.number"}, "numpy.object_"], "uses_pep604_syntax": true}, "values": [], "variance": 0}], "type_ref": "numpy.polynomial._polytypes._Series"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial.chebyshev._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.polynomial.chebyshev._zseries_div", "upper_bound": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": ["builtins.int", "builtins.float", "builtins.complex"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.number"}, "numpy.object_"], "uses_pep604_syntax": true}, "values": [], "variance": 0}]}}}, "_zseries_int": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["zs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.polynomial.chebyshev._zseries_int", "name": "_zseries_int", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["zs"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial.chebyshev._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.polynomial.chebyshev._zseries_int", "upper_bound": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": ["builtins.int", "builtins.float", "builtins.complex"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.number"}, "numpy.object_"], "uses_pep604_syntax": true}, "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_zseries_int", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial.chebyshev._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.polynomial.chebyshev._zseries_int", "upper_bound": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": ["builtins.int", "builtins.float", "builtins.complex"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.number"}, "numpy.object_"], "uses_pep604_syntax": true}, "values": [], "variance": 0}], "type_ref": "numpy.polynomial._polytypes._Series"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial.chebyshev._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.polynomial.chebyshev._zseries_int", "upper_bound": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": ["builtins.int", "builtins.float", "builtins.complex"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.number"}, "numpy.object_"], "uses_pep604_syntax": true}, "values": [], "variance": 0}]}}}, "_zseries_mul": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["z1", "z2"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.polynomial.chebyshev._zseries_mul", "name": "_zseries_mul", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["z1", "z2"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial.chebyshev._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.polynomial.chebyshev._zseries_mul", "upper_bound": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": ["builtins.int", "builtins.float", "builtins.complex"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.number"}, "numpy.object_"], "uses_pep604_syntax": true}, "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial.chebyshev._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.polynomial.chebyshev._zseries_mul", "upper_bound": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": ["builtins.int", "builtins.float", "builtins.complex"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.number"}, "numpy.object_"], "uses_pep604_syntax": true}, "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_zseries_mul", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial.chebyshev._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.polynomial.chebyshev._zseries_mul", "upper_bound": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": ["builtins.int", "builtins.float", "builtins.complex"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.number"}, "numpy.object_"], "uses_pep604_syntax": true}, "values": [], "variance": 0}], "type_ref": "numpy.polynomial._polytypes._Series"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial.chebyshev._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.polynomial.chebyshev._zseries_mul", "upper_bound": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": ["builtins.int", "builtins.float", "builtins.complex"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.number"}, "numpy.object_"], "uses_pep604_syntax": true}, "values": [], "variance": 0}]}}}, "_zseries_to_cseries": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["zs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.polynomial.chebyshev._zseries_to_cseries", "name": "_zseries_to_cseries", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["zs"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial.chebyshev._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.polynomial.chebyshev._zseries_to_cseries", "upper_bound": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": ["builtins.int", "builtins.float", "builtins.complex"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.number"}, "numpy.object_"], "uses_pep604_syntax": true}, "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_zseries_to_cseries", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial.chebyshev._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.polynomial.chebyshev._zseries_to_cseries", "upper_bound": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": ["builtins.int", "builtins.float", "builtins.complex"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.number"}, "numpy.object_"], "uses_pep604_syntax": true}, "values": [], "variance": 0}], "type_ref": "numpy.polynomial._polytypes._Series"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial.chebyshev._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.polynomial.chebyshev._zseries_to_cseries", "upper_bound": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": ["builtins.int", "builtins.float", "builtins.complex"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.number"}, "numpy.object_"], "uses_pep604_syntax": true}, "values": [], "variance": 0}]}}}, "cheb2poly": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.chebyshev.cheb2poly", "name": "cheb2poly", "type": {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "cheb2poly"}], "extra_attrs": null, "type_ref": "numpy.polynomial._polytypes._FuncUnOp"}}}, "chebadd": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.chebyshev.chebadd", "name": "cheb<PERSON><PERSON>", "type": {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "cheb<PERSON><PERSON>"}], "extra_attrs": null, "type_ref": "numpy.polynomial._polytypes._FuncBinOp"}}}, "chebcompanion": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.chebyshev.chebcompanion", "name": "chebcompanion", "type": {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "chebcompanion"}], "extra_attrs": null, "type_ref": "numpy.polynomial._polytypes._FuncCompanion"}}}, "chebder": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.chebyshev.chebder", "name": "chebder", "type": {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "chebder"}], "extra_attrs": null, "type_ref": "numpy.polynomial._polytypes._FuncDer"}}}, "chebdiv": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.chebyshev.chebdiv", "name": "chebdiv", "type": {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "chebdiv"}], "extra_attrs": null, "type_ref": "numpy.polynomial._polytypes._FuncBinOp"}}}, "chebdomain": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "numpy.polynomial.chebyshev.chebdomain", "name": "cheb<PERSON>ain", "type": {".class": "TypeAliasType", "args": ["numpy.float64"], "type_ref": "numpy.polynomial._polytypes._Array2"}}}, "chebfit": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.chebyshev.chebfit", "name": "chebfit", "type": {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "chebfit"}], "extra_attrs": null, "type_ref": "numpy.polynomial._polytypes._FuncFit"}}}, "chebfromroots": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.chebyshev.chebfromroots", "name": "chebfromroots", "type": {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "chebfromroots"}], "extra_attrs": null, "type_ref": "numpy.polynomial._polytypes._FuncFromRoots"}}}, "chebgauss": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.chebyshev.chebgauss", "name": "cheb<PERSON>ss", "type": {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "cheb<PERSON>ss"}], "extra_attrs": null, "type_ref": "numpy.polynomial._polytypes._FuncGauss"}}}, "chebgrid2d": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.chebyshev.chebgrid2d", "name": "chebgrid2d", "type": {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "chebgrid2d"}], "extra_attrs": null, "type_ref": "numpy.polynomial._polytypes._FuncVal2D"}}}, "chebgrid3d": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.chebyshev.chebgrid3d", "name": "chebgrid3d", "type": {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "chebgrid3d"}], "extra_attrs": null, "type_ref": "numpy.polynomial._polytypes._FuncVal3D"}}}, "chebint": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.chebyshev.chebint", "name": "chebint", "type": {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "chebint"}], "extra_attrs": null, "type_ref": "numpy.polynomial._polytypes._FuncInteg"}}}, "chebinterpolate": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "numpy.polynomial.chebyshev.chebinterpolate", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["func", "deg", "args"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.polynomial.chebyshev.chebinterpolate", "name": "chebinterpolate", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["func", "deg", "args"], "arg_types": ["numpy.ufunc", {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._scalars._IntLike_co"}, {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "chebinterpolate", "ret_type": {".class": "TypeAliasType", "args": [{".class": "UnionType", "items": ["numpy.float64", "numpy.complex128", "numpy.object_"], "uses_pep604_syntax": true}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.polynomial.chebyshev.chebinterpolate", "name": "chebinterpolate", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["func", "deg", "args"], "arg_types": ["numpy.ufunc", {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._scalars._IntLike_co"}, {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "chebinterpolate", "ret_type": {".class": "TypeAliasType", "args": [{".class": "UnionType", "items": ["numpy.float64", "numpy.complex128", "numpy.object_"], "uses_pep604_syntax": true}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["func", "deg", "args"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.polynomial.chebyshev.chebinterpolate", "name": "chebinterpolate", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["func", "deg", "args"], "arg_types": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeAliasType", "args": ["numpy.float64"], "type_ref": "numpy._typing._array_like.NDArray"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial.chebyshev._RT", "id": -1, "name": "_RT", "namespace": "numpy.polynomial.chebyshev.chebinterpolate#1", "upper_bound": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": ["builtins.int", "builtins.float", "builtins.complex"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.number"}, {".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "numpy.bool"}, "numpy.object_"], "uses_pep604_syntax": true}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._scalars._IntLike_co"}, {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "chebinterpolate", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial.chebyshev._RT", "id": -1, "name": "_RT", "namespace": "numpy.polynomial.chebyshev.chebinterpolate#1", "upper_bound": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": ["builtins.int", "builtins.float", "builtins.complex"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.number"}, {".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "numpy.bool"}, "numpy.object_"], "uses_pep604_syntax": true}, "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial.chebyshev._RT", "id": -1, "name": "_RT", "namespace": "numpy.polynomial.chebyshev.chebinterpolate#1", "upper_bound": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": ["builtins.int", "builtins.float", "builtins.complex"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.number"}, {".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "numpy.bool"}, "numpy.object_"], "uses_pep604_syntax": true}, "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.polynomial.chebyshev.chebinterpolate", "name": "chebinterpolate", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["func", "deg", "args"], "arg_types": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeAliasType", "args": ["numpy.float64"], "type_ref": "numpy._typing._array_like.NDArray"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial.chebyshev._RT", "id": -1, "name": "_RT", "namespace": "numpy.polynomial.chebyshev.chebinterpolate#1", "upper_bound": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": ["builtins.int", "builtins.float", "builtins.complex"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.number"}, {".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "numpy.bool"}, "numpy.object_"], "uses_pep604_syntax": true}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._scalars._IntLike_co"}, {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "chebinterpolate", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial.chebyshev._RT", "id": -1, "name": "_RT", "namespace": "numpy.polynomial.chebyshev.chebinterpolate#1", "upper_bound": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": ["builtins.int", "builtins.float", "builtins.complex"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.number"}, {".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "numpy.bool"}, "numpy.object_"], "uses_pep604_syntax": true}, "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial.chebyshev._RT", "id": -1, "name": "_RT", "namespace": "numpy.polynomial.chebyshev.chebinterpolate#1", "upper_bound": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": ["builtins.int", "builtins.float", "builtins.complex"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.number"}, {".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "numpy.bool"}, "numpy.object_"], "uses_pep604_syntax": true}, "values": [], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["func", "deg", "args"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.polynomial.chebyshev.chebinterpolate", "name": "chebinterpolate", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["func", "deg", "args"], "arg_types": [{".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": [null, null, null], "arg_types": [{".class": "TypeAliasType", "args": ["numpy.float64"], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": true, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial.chebyshev._RT", "id": -1, "name": "_RT", "namespace": "numpy.polynomial.chebyshev.chebinterpolate", "upper_bound": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": ["builtins.int", "builtins.float", "builtins.complex"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.number"}, {".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "numpy.bool"}, "numpy.object_"], "uses_pep604_syntax": true}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._scalars._IntLike_co"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "chebinterpolate", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial.chebyshev._RT", "id": -1, "name": "_RT", "namespace": "numpy.polynomial.chebyshev.chebinterpolate", "upper_bound": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": ["builtins.int", "builtins.float", "builtins.complex"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.number"}, {".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "numpy.bool"}, "numpy.object_"], "uses_pep604_syntax": true}, "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial.chebyshev._RT", "id": -1, "name": "_RT", "namespace": "numpy.polynomial.chebyshev.chebinterpolate", "upper_bound": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": ["builtins.int", "builtins.float", "builtins.complex"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.number"}, {".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "numpy.bool"}, "numpy.object_"], "uses_pep604_syntax": true}, "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.polynomial.chebyshev.chebinterpolate", "name": "chebinterpolate", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["func", "deg", "args"], "arg_types": [{".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": [null, null, null], "arg_types": [{".class": "TypeAliasType", "args": ["numpy.float64"], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": true, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial.chebyshev._RT", "id": -1, "name": "_RT", "namespace": "numpy.polynomial.chebyshev.chebinterpolate", "upper_bound": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": ["builtins.int", "builtins.float", "builtins.complex"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.number"}, {".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "numpy.bool"}, "numpy.object_"], "uses_pep604_syntax": true}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._scalars._IntLike_co"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "chebinterpolate", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial.chebyshev._RT", "id": -1, "name": "_RT", "namespace": "numpy.polynomial.chebyshev.chebinterpolate", "upper_bound": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": ["builtins.int", "builtins.float", "builtins.complex"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.number"}, {".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "numpy.bool"}, "numpy.object_"], "uses_pep604_syntax": true}, "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial.chebyshev._RT", "id": -1, "name": "_RT", "namespace": "numpy.polynomial.chebyshev.chebinterpolate", "upper_bound": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": ["builtins.int", "builtins.float", "builtins.complex"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.number"}, {".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "numpy.bool"}, "numpy.object_"], "uses_pep604_syntax": true}, "values": [], "variance": 0}]}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["func", "deg", "args"], "arg_types": ["numpy.ufunc", {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._scalars._IntLike_co"}, {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "chebinterpolate", "ret_type": {".class": "TypeAliasType", "args": [{".class": "UnionType", "items": ["numpy.float64", "numpy.complex128", "numpy.object_"], "uses_pep604_syntax": true}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["func", "deg", "args"], "arg_types": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeAliasType", "args": ["numpy.float64"], "type_ref": "numpy._typing._array_like.NDArray"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial.chebyshev._RT", "id": -1, "name": "_RT", "namespace": "numpy.polynomial.chebyshev.chebinterpolate#1", "upper_bound": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": ["builtins.int", "builtins.float", "builtins.complex"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.number"}, {".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "numpy.bool"}, "numpy.object_"], "uses_pep604_syntax": true}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._scalars._IntLike_co"}, {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "chebinterpolate", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial.chebyshev._RT", "id": -1, "name": "_RT", "namespace": "numpy.polynomial.chebyshev.chebinterpolate#1", "upper_bound": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": ["builtins.int", "builtins.float", "builtins.complex"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.number"}, {".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "numpy.bool"}, "numpy.object_"], "uses_pep604_syntax": true}, "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial.chebyshev._RT", "id": -1, "name": "_RT", "namespace": "numpy.polynomial.chebyshev.chebinterpolate#1", "upper_bound": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": ["builtins.int", "builtins.float", "builtins.complex"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.number"}, {".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "numpy.bool"}, "numpy.object_"], "uses_pep604_syntax": true}, "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["func", "deg", "args"], "arg_types": [{".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": [null, null, null], "arg_types": [{".class": "TypeAliasType", "args": ["numpy.float64"], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": true, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial.chebyshev._RT", "id": -1, "name": "_RT", "namespace": "numpy.polynomial.chebyshev.chebinterpolate", "upper_bound": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": ["builtins.int", "builtins.float", "builtins.complex"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.number"}, {".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "numpy.bool"}, "numpy.object_"], "uses_pep604_syntax": true}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._scalars._IntLike_co"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "chebinterpolate", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial.chebyshev._RT", "id": -1, "name": "_RT", "namespace": "numpy.polynomial.chebyshev.chebinterpolate", "upper_bound": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": ["builtins.int", "builtins.float", "builtins.complex"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.number"}, {".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "numpy.bool"}, "numpy.object_"], "uses_pep604_syntax": true}, "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial.chebyshev._RT", "id": -1, "name": "_RT", "namespace": "numpy.polynomial.chebyshev.chebinterpolate", "upper_bound": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": ["builtins.int", "builtins.float", "builtins.complex"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.number"}, {".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "numpy.bool"}, "numpy.object_"], "uses_pep604_syntax": true}, "values": [], "variance": 0}]}]}}}, "chebline": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.chebyshev.chebline", "name": "chebline", "type": {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "chebline"}], "extra_attrs": null, "type_ref": "numpy.polynomial._polytypes._FuncLine"}}}, "chebmul": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.chebyshev.chebmul", "name": "cheb<PERSON>l", "type": {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "cheb<PERSON>l"}], "extra_attrs": null, "type_ref": "numpy.polynomial._polytypes._FuncBinOp"}}}, "chebmulx": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.chebyshev.chebmulx", "name": "chebmulx", "type": {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "chebmulx"}], "extra_attrs": null, "type_ref": "numpy.polynomial._polytypes._FuncUnOp"}}}, "chebone": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "numpy.polynomial.chebyshev.chebone", "name": "chebone", "type": {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.int_"}], "type_ref": "numpy.polynomial._polytypes._Array1"}}}, "chebpow": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.chebyshev.chebpow", "name": "cheb<PERSON>w", "type": {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "cheb<PERSON>w"}], "extra_attrs": null, "type_ref": "numpy.polynomial._polytypes._FuncPow"}}}, "chebpts1": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.chebyshev.chebpts1", "name": "chebpts1", "type": {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "chebpts1"}], "extra_attrs": null, "type_ref": "numpy.polynomial._polytypes._FuncPts"}}}, "chebpts2": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.chebyshev.chebpts2", "name": "chebpts2", "type": {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "chebpts2"}], "extra_attrs": null, "type_ref": "numpy.polynomial._polytypes._FuncPts"}}}, "chebroots": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.chebyshev.chebroots", "name": "chebroots", "type": {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "chebroots"}], "extra_attrs": null, "type_ref": "numpy.polynomial._polytypes._FuncRoots"}}}, "chebsub": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.chebyshev.chebsub", "name": "cheb<PERSON>b", "type": {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "cheb<PERSON>b"}], "extra_attrs": null, "type_ref": "numpy.polynomial._polytypes._FuncBinOp"}}}, "chebtrim": {".class": "SymbolTableNode", "cross_ref": "numpy.polynomial.polyutils.trimcoef", "kind": "Gdef"}, "chebval": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.chebyshev.chebval", "name": "cheb<PERSON>", "type": {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "cheb<PERSON>"}], "extra_attrs": null, "type_ref": "numpy.polynomial._polytypes._FuncVal"}}}, "chebval2d": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.chebyshev.chebval2d", "name": "chebval2d", "type": {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "chebval2d"}], "extra_attrs": null, "type_ref": "numpy.polynomial._polytypes._FuncVal2D"}}}, "chebval3d": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.chebyshev.chebval3d", "name": "chebval3d", "type": {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "chebval3d"}], "extra_attrs": null, "type_ref": "numpy.polynomial._polytypes._FuncVal3D"}}}, "chebvalfromroots": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.chebyshev.chebvalfromroots", "name": "chebvalfromroots", "type": {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "chebvalfromroots"}], "extra_attrs": null, "type_ref": "numpy.polynomial._polytypes._FuncValFromRoots"}}}, "chebvander": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.chebyshev.chebvander", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "<PERSON><PERSON><PERSON><PERSON>"}], "extra_attrs": null, "type_ref": "numpy.polynomial._polytypes._FuncVander"}}}, "chebvander2d": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.chebyshev.chebvander2d", "name": "chebvander2d", "type": {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "chebvander2d"}], "extra_attrs": null, "type_ref": "numpy.polynomial._polytypes._FuncVander2D"}}}, "chebvander3d": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.chebyshev.chebvander3d", "name": "chebvander3d", "type": {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "chebvander3d"}], "extra_attrs": null, "type_ref": "numpy.polynomial._polytypes._FuncVander3D"}}}, "chebweight": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.chebyshev.chebweight", "name": "chebweight", "type": {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "chebweight"}], "extra_attrs": null, "type_ref": "numpy.polynomial._polytypes._FuncWeight"}}}, "chebx": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "numpy.polynomial.chebyshev.chebx", "name": "chebx", "type": {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.int_"}], "type_ref": "numpy.polynomial._polytypes._Array2"}}}, "chebzero": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "numpy.polynomial.chebyshev.chebzero", "name": "chebzero", "type": {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.int_"}], "type_ref": "numpy.polynomial._polytypes._Array1"}}}, "np": {".class": "SymbolTableNode", "cross_ref": "numpy", "kind": "Gdef", "module_hidden": true, "module_public": false}, "npt": {".class": "SymbolTableNode", "cross_ref": "numpy.typing", "kind": "Gdef", "module_hidden": true, "module_public": false}, "overload": {".class": "SymbolTableNode", "cross_ref": "typing.overload", "kind": "Gdef", "module_hidden": true, "module_public": false}, "poly2cheb": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.chebyshev.poly2cheb", "name": "poly2cheb", "type": {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "poly2cheb"}], "extra_attrs": null, "type_ref": "numpy.polynomial._polytypes._FuncPoly2Ortho"}}}}, "path": "c:\\Python313\\Lib\\site-packages\\numpy\\polynomial\\chebyshev.pyi"}