{"data_mtime": 1750100833, "dep_lines": [16, 18, 19, 20, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 5, 5, 5, 30, 30, 30, 30, 30], "dependencies": ["__future__", "re", "io", "typing", "builtins", "_frozen_importlib", "_typeshed", "abc", "enum", "typing_extensions"], "hash": "08d5d192e2c3de44b17d7ddb5e95630cf0b62f7a", "id": "PIL.GimpPaletteFile", "ignore_all": true, "interface_hash": "43a73912b14c62e38fc8ff0f157552111664dd77", "mtime": 1745696559, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Python313\\Lib\\site-packages\\PIL\\GimpPaletteFile.py", "plugin_data": null, "size": 1887, "suppressed": [], "version_id": "1.15.0"}