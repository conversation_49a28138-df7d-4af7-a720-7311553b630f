{"data_mtime": 1750100862, "dep_lines": [9, 3, 8, 12, 13, 14, 1, 4, 6, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 25, 25, 25, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["pydantic.plugin._schema_validator", "collections.abc", "pydantic.errors", "pydantic.dataclasses", "pydantic.main", "pydantic.type_adapter", "__future__", "typing", "pydantic_core", "builtins", "_frozen_importlib", "_typeshed", "abc", "pydantic._internal._dataclasses", "pydantic._internal._model_construction", "pydantic.plugin", "pydantic_core._pydantic_core", "pydantic_core.core_schema"], "hash": "e68ec0a0e8bb9feae8494f568f60ebffbe93004a", "id": "pydantic._internal._mock_val_ser", "ignore_all": true, "interface_hash": "d95f5eed29372a17913d977e0bc6fb31496ec9f4", "mtime": 1748795478, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Python313\\Lib\\site-packages\\pydantic\\_internal\\_mock_val_ser.py", "plugin_data": null, "size": 8885, "suppressed": [], "version_id": "1.15.0"}