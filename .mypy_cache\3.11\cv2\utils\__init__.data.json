{".class": "MypyFile", "_fullname": "cv2.utils", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "ClassWithKeywordProperties": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "cv2.utils.ClassWithKeywordProperties", "name": "ClassWithKeywordProperties", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "cv2.utils.ClassWithKeywordProperties", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "cv2.utils", "mro": ["cv2.utils.ClassWithKeywordProperties", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "lambda_arg", "except_arg"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.utils.ClassWithKeywordProperties.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "lambda_arg", "except_arg"], "arg_types": ["cv2.utils.ClassWithKeywordProperties", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ClassWithKeywordProperties", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "except_": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "cv2.utils.ClassWithKeywordProperties.except_", "name": "except_", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cv2.utils.ClassWithKeywordProperties"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "except_ of ClassWithKeywordProperties", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "cv2.utils.ClassWithKeywordProperties.except_", "name": "except_", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cv2.utils.ClassWithKeywordProperties"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "except_ of ClassWithKeywordProperties", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "lambda_": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "cv2.utils.ClassWithKeywordProperties.lambda_", "name": "lambda_", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cv2.utils.ClassWithKeywordProperties.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cv2.utils.ClassWithKeywordProperties", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "cv2.utils.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.utils.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.utils.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.utils.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.utils.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.utils.__package__", "name": "__package__", "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.utils.__path__", "name": "__path__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.utils.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_typing": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef", "module_hidden": true, "module_public": false}, "copyMatAndDumpNamedArguments": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "cv2.utils.copyMatAndDumpNamedArguments", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["src", "dst", "lambda_", "sigma"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.utils.copyMatAndDumpNamedArguments", "name": "copyMatAndDumpNamedArguments", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["src", "dst", "lambda_", "sigma"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "copyMatAndDumpNamedArguments", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.utils.copyMatAndDumpNamedArguments", "name": "copyMatAndDumpNamedArguments", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["src", "dst", "lambda_", "sigma"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "copyMatAndDumpNamedArguments", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["src", "dst", "lambda_", "sigma"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.utils.copyMatAndDumpNamedArguments", "name": "copyMatAndDumpNamedArguments", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["src", "dst", "lambda_", "sigma"], "arg_types": ["cv2.UMat", {".class": "UnionType", "items": ["cv2.UMat", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "copyMatAndDumpNamedArguments", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "cv2.UMat"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.utils.copyMatAndDumpNamedArguments", "name": "copyMatAndDumpNamedArguments", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["src", "dst", "lambda_", "sigma"], "arg_types": ["cv2.UMat", {".class": "UnionType", "items": ["cv2.UMat", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "copyMatAndDumpNamedArguments", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "cv2.UMat"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["src", "dst", "lambda_", "sigma"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "copyMatAndDumpNamedArguments", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["src", "dst", "lambda_", "sigma"], "arg_types": ["cv2.UMat", {".class": "UnionType", "items": ["cv2.UMat", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "copyMatAndDumpNamedArguments", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "cv2.UMat"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "cv2": {".class": "SymbolTableNode", "cross_ref": "cv2", "kind": "Gdef", "module_hidden": true, "module_public": false}, "dumpBool": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["argument"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.utils.dumpBool", "name": "dumpBool", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["argument"], "arg_types": ["builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "dumpBool", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "dumpCString": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["argument"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.utils.dumpCString", "name": "dumpCString", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["argument"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "dumpCString", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "dumpDouble": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["argument"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.utils.dumpDouble", "name": "dumpDouble", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["argument"], "arg_types": ["builtins.float"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "dumpDouble", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "dumpFloat": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["argument"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.utils.dumpFloat", "name": "dumpFloat", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["argument"], "arg_types": ["builtins.float"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "dumpFloat", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "dumpInputArray": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "cv2.utils.dumpInputArray", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["argument"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.utils.dumpInputArray", "name": "dumpInputArray", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["argument"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "dumpInputArray", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.utils.dumpInputArray", "name": "dumpInputArray", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["argument"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "dumpInputArray", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["argument"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.utils.dumpInputArray", "name": "dumpInputArray", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["argument"], "arg_types": ["cv2.UMat"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "dumpInputArray", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.utils.dumpInputArray", "name": "dumpInputArray", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["argument"], "arg_types": ["cv2.UMat"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "dumpInputArray", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": ["argument"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "dumpInputArray", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0], "arg_names": ["argument"], "arg_types": ["cv2.UMat"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "dumpInputArray", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "dumpInputArrayOfArrays": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "cv2.utils.dumpInputArrayOfArrays", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["argument"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.utils.dumpInputArrayOfArrays", "name": "dumpInputArrayOfArrays", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["argument"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "dumpInputArrayOfArrays", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.utils.dumpInputArrayOfArrays", "name": "dumpInputArrayOfArrays", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["argument"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "dumpInputArrayOfArrays", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["argument"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.utils.dumpInputArrayOfArrays", "name": "dumpInputArrayOfArrays", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["argument"], "arg_types": [{".class": "Instance", "args": ["cv2.UMat"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "dumpInputArrayOfArrays", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.utils.dumpInputArrayOfArrays", "name": "dumpInputArrayOfArrays", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["argument"], "arg_types": [{".class": "Instance", "args": ["cv2.UMat"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "dumpInputArrayOfArrays", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": ["argument"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "dumpInputArrayOfArrays", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0], "arg_names": ["argument"], "arg_types": [{".class": "Instance", "args": ["cv2.UMat"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "dumpInputArrayOfArrays", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "dumpInputOutputArray": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "cv2.utils.dumpInputOutputArray", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["argument"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.utils.dumpInputOutputArray", "name": "dumpInputOutputArray", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["argument"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "dumpInputOutputArray", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.utils.dumpInputOutputArray", "name": "dumpInputOutputArray", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["argument"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "dumpInputOutputArray", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["argument"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.utils.dumpInputOutputArray", "name": "dumpInputOutputArray", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["argument"], "arg_types": ["cv2.UMat"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "dumpInputOutputArray", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "cv2.UMat"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.utils.dumpInputOutputArray", "name": "dumpInputOutputArray", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["argument"], "arg_types": ["cv2.UMat"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "dumpInputOutputArray", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "cv2.UMat"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": ["argument"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "dumpInputOutputArray", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0], "arg_names": ["argument"], "arg_types": ["cv2.UMat"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "dumpInputOutputArray", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "cv2.UMat"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "dumpInputOutputArrayOfArrays": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "cv2.utils.dumpInputOutputArrayOfArrays", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["argument"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.utils.dumpInputOutputArrayOfArrays", "name": "dumpInputOutputArrayOfArrays", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["argument"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "dumpInputOutputArrayOfArrays", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.utils.dumpInputOutputArrayOfArrays", "name": "dumpInputOutputArrayOfArrays", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["argument"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "dumpInputOutputArrayOfArrays", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["argument"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.utils.dumpInputOutputArrayOfArrays", "name": "dumpInputOutputArrayOfArrays", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["argument"], "arg_types": [{".class": "Instance", "args": ["cv2.UMat"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "dumpInputOutputArrayOfArrays", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "Instance", "args": ["cv2.UMat"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.utils.dumpInputOutputArrayOfArrays", "name": "dumpInputOutputArrayOfArrays", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["argument"], "arg_types": [{".class": "Instance", "args": ["cv2.UMat"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "dumpInputOutputArrayOfArrays", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "Instance", "args": ["cv2.UMat"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": ["argument"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "dumpInputOutputArrayOfArrays", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0], "arg_names": ["argument"], "arg_types": [{".class": "Instance", "args": ["cv2.UMat"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "dumpInputOutputArrayOfArrays", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "Instance", "args": ["cv2.UMat"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "dumpInt": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["argument"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.utils.dumpInt", "name": "dumpInt", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["argument"], "arg_types": ["builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "dumpInt", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "dumpInt64": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["argument"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.utils.dumpInt64", "name": "dumpInt64", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["argument"], "arg_types": ["builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "dumpInt64", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "dumpRange": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["argument"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.utils.dumpRange", "name": "dumpRange", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["argument"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.Range"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "dumpRange", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "dumpRect": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["argument"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.utils.dumpRect", "name": "dumpRect", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["argument"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.Rect"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "dumpRect", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "dumpRotatedRect": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["argument"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.utils.dumpRotatedRect", "name": "dumpRotatedRect", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["argument"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.RotatedRect"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "dumpRotatedRect", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "dumpSizeT": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["argument"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.utils.dumpSizeT", "name": "dumpSizeT", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["argument"], "arg_types": ["builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "dumpSizeT", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "dumpString": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["argument"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.utils.dumpString", "name": "dumpString", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["argument"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "dumpString", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "dumpTermCriteria": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["argument"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.utils.dumpTermCriteria", "name": "dumpTermCriteria", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["argument"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.TermCriteria"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "dumpTermCriteria", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "dumpVec2i": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1], "arg_names": ["value"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.utils.dumpVec2i", "name": "dumpVec2i", "type": {".class": "CallableType", "arg_kinds": [1], "arg_names": ["value"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.Vec2i"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "dumpVec2i", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "dumpVectorOfDouble": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["vec"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.utils.dumpVectorOfDouble", "name": "dumpVectorOfDouble", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["vec"], "arg_types": [{".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "dumpVectorOfDouble", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "dumpVectorOfInt": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["vec"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.utils.dumpVectorOfInt", "name": "dumpVectorOfInt", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["vec"], "arg_types": [{".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "dumpVectorOfInt", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "dumpVectorOfRect": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["vec"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.utils.dumpVectorOfRect", "name": "dumpVectorOfRect", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["vec"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.Rect"}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "dumpVectorOfRect", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "fs": {".class": "SymbolTableNode", "cross_ref": "cv2.utils.fs", "kind": "Gdef", "module_public": false}, "generateVectorOfInt": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["len"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.utils.generateVectorOfInt", "name": "generateVectorOfInt", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["len"], "arg_types": ["builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "generateVectorOfInt", "ret_type": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "typing.Sequence"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "generateVectorOfMat": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["len", "rows", "cols", "dtype", "vec"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.utils.generateVectorOfMat", "name": "generateVectorOfMat", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["len", "rows", "cols", "dtype", "vec"], "arg_types": ["builtins.int", "builtins.int", "builtins.int", "builtins.int", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "generateVectorOfMat", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "generateVectorOfRect": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["len"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.utils.generateVectorOfRect", "name": "generateVectorOfRect", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["len"], "arg_types": ["builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "generateVectorOfRect", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.Rect"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "nested": {".class": "SymbolTableNode", "cross_ref": "cv2.utils.nested", "kind": "Gdef", "module_public": false}, "testAsyncArray": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "cv2.utils.testAsyncArray", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["argument"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.utils.testAsyncArray", "name": "testAsyncArray", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["argument"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "testAsyncArray", "ret_type": "cv2.As<PERSON><PERSON><PERSON><PERSON>", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.utils.testAsyncArray", "name": "testAsyncArray", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["argument"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "testAsyncArray", "ret_type": "cv2.As<PERSON><PERSON><PERSON><PERSON>", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["argument"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.utils.testAsyncArray", "name": "testAsyncArray", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["argument"], "arg_types": ["cv2.UMat"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "testAsyncArray", "ret_type": "cv2.As<PERSON><PERSON><PERSON><PERSON>", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.utils.testAsyncArray", "name": "testAsyncArray", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["argument"], "arg_types": ["cv2.UMat"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "testAsyncArray", "ret_type": "cv2.As<PERSON><PERSON><PERSON><PERSON>", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": ["argument"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "testAsyncArray", "ret_type": "cv2.As<PERSON><PERSON><PERSON><PERSON>", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0], "arg_names": ["argument"], "arg_types": ["cv2.UMat"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "testAsyncArray", "ret_type": "cv2.As<PERSON><PERSON><PERSON><PERSON>", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "testAsyncException": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.utils.testAsyncException", "name": "testAsyncException", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "testAsyncException", "ret_type": "cv2.As<PERSON><PERSON><PERSON><PERSON>", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "testOverloadResolution": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "cv2.utils.testOverloadResolution", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["value", "point"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.utils.testOverloadResolution", "name": "testOverloadResolution", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["value", "point"], "arg_types": ["builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.Point"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "testOverloadResolution", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.utils.testOverloadResolution", "name": "testOverloadResolution", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["value", "point"], "arg_types": ["builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.Point"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "testOverloadResolution", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["rect"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.utils.testOverloadResolution", "name": "testOverloadResolution", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["rect"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.Rect"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "testOverloadResolution", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.utils.testOverloadResolution", "name": "testOverloadResolution", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["rect"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.Rect"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "testOverloadResolution", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["value", "point"], "arg_types": ["builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.Point"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "testOverloadResolution", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0], "arg_names": ["rect"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.Rect"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "testOverloadResolution", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "testOverwriteNativeMethod": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["argument"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.utils.testOverwriteNativeMethod", "name": "testOverwriteNativeMethod", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["argument"], "arg_types": ["builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "testOverwriteNativeMethod", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "testRaiseGeneralException": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.utils.testRaiseGeneralException", "name": "testRaiseGeneralException", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "testRaiseGeneralException", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "testReservedKeywordConversion": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["positional_argument", "lambda_", "from_"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.utils.testReservedKeywordConversion", "name": "testReservedKeywordConversion", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["positional_argument", "lambda_", "from_"], "arg_types": ["builtins.int", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "testReservedKeywordConversion", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "testRotatedRect": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["x", "y", "w", "h", "angle"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.utils.testRotatedRect", "name": "testRotatedRect", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["x", "y", "w", "h", "angle"], "arg_types": ["builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "testRotatedRect", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.RotatedRect"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "testRotatedRectVector": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["x", "y", "w", "h", "angle"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.utils.testRotatedRectVector", "name": "testRotatedRectVector", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["x", "y", "w", "h", "angle"], "arg_types": ["builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "testRotatedRectVector", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.RotatedRect"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "path": "c:\\Python313\\Lib\\site-packages\\cv2\\utils\\__init__.pyi"}