{"data_mtime": 1750100846, "dep_lines": [17, 137, 165, 256, 261, 267, 314, 318, 322, 333, 344, 349, 390, 421, 428, 441, 445, 449, 464, 506, 512, 529, 546, 559, 572, 590, 596, 614, 628, 634, 15, 16, 19, 180, 182, 227, 227, 227, 227, 227, 227, 227, 227, 227, 227, 227, 227, 227, 227, 227, 227, 247, 247, 247, 439, 504, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 203, 224, 225, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 5, 10, 5, 5, 10, 10, 10, 10, 10, 10, 5, 5, 5, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["numpy._core._internal", "numpy._typing._callable", "numpy._typing._extended_precision", "numpy._core.records", "numpy._core.function_base", "numpy._core.fromnumeric", "numpy._core._asarray", "numpy._core._type_aliases", "numpy._core._ufunc_config", "numpy._core.arrayprint", "numpy._core.einsumfunc", "numpy._core.multiarray", "numpy._core.numeric", "numpy._core.numerictypes", "numpy._core.shape_base", "numpy.lib.scimath", "numpy.lib._arraypad_impl", "numpy.lib._arraysetops_impl", "numpy.lib._function_base_impl", "numpy.lib._histograms_impl", "numpy.lib._index_tricks_impl", "numpy.lib._nanfunctions_impl", "numpy.lib._npyio_impl", "numpy.lib._polynomial_impl", "numpy.lib._shape_base_impl", "numpy.lib._stride_tricks_impl", "numpy.lib._twodim_base_impl", "numpy.lib._type_check_impl", "numpy.lib._ufunclike_impl", "numpy.lib._utils_impl", "numpy.__config__", "numpy._pytesttester", "numpy._typing", "numpy._array_api_info", "collections.abc", "numpy.char", "numpy.core", "numpy.ctypeslib", "numpy.dtypes", "numpy.exceptions", "numpy.f2py", "numpy.fft", "numpy.lib", "numpy.linalg", "numpy.ma", "numpy.polynomial", "numpy.random", "numpy.rec", "numpy.strings", "numpy.testing", "numpy.typing", "numpy.matlib", "numpy.matrixlib", "numpy.version", "numpy._expired_attrs_2_0", "numpy._globals", "builtins", "sys", "mmap", "ctypes", "array", "datetime", "abc", "types", "decimal", "fractions", "uuid", "typing", "_typeshed", "typing_extensions", "_ctypes", "_frozen_importlib", "enum", "numbers", "numpy._core", "numpy._typing._array_like", "numpy._typing._dtype_like", "numpy._typing._nbit_base", "numpy._typing._nested_sequence", "numpy._typing._ufunc", "os"], "hash": "962d563ae1437cee38ec8cedb1fca9fa7e1e4c0b", "id": "numpy", "ignore_all": true, "interface_hash": "c413abd13aac78a6822054ddb3abf16e3ecc5925", "mtime": 1748795461, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Python313\\Lib\\site-packages\\numpy\\__init__.pyi", "plugin_data": null, "size": 217089, "suppressed": [], "version_id": "1.15.0"}