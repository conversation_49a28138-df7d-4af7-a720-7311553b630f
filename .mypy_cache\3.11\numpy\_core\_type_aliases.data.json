{".class": "MypyFile", "_fullname": "numpy._core._type_aliases", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Collection": {".class": "SymbolTableNode", "cross_ref": "typing.Collection", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Final": {".class": "SymbolTableNode", "cross_ref": "typing.Final", "kind": "Gdef", "module_hidden": true, "module_public": false}, "L": {".class": "SymbolTableNode", "cross_ref": "typing.Literal", "kind": "Gdef", "module_hidden": true, "module_public": false}, "TypeAlias": {".class": "SymbolTableNode", "cross_ref": "typing.TypeAlias", "kind": "Gdef", "module_hidden": true, "module_public": false}, "TypedDict": {".class": "SymbolTableNode", "cross_ref": "typing.TypedDict", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_AbstractTypeName": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "numpy._core._type_aliases._AbstractTypeName", "line": 48, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "generic"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "flexible"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "character"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "number"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "integer"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "inexact"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "unsignedinteger"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "<PERSON><PERSON><PERSON><PERSON>"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "floating"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "complexfloating"}], "uses_pep604_syntax": false}}}, "_AliasesType": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy._core._type_aliases._AliasesType", "name": "_AliasesType", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "numpy._core._type_aliases._AliasesType", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "numpy._core._type_aliases", "mro": ["numpy._core._type_aliases._AliasesType", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["double", {".class": "LiteralType", "fallback": "builtins.str", "value": "float64"}], ["cdouble", {".class": "LiteralType", "fallback": "builtins.str", "value": "complex128"}], ["single", {".class": "LiteralType", "fallback": "builtins.str", "value": "float32"}], ["csingle", {".class": "LiteralType", "fallback": "builtins.str", "value": "complex64"}], ["half", {".class": "LiteralType", "fallback": "builtins.str", "value": "float16"}], ["bool_", {".class": "LiteralType", "fallback": "builtins.str", "value": "bool"}], ["int_", {".class": "LiteralType", "fallback": "builtins.str", "value": "intp"}], ["uint", {".class": "LiteralType", "fallback": "builtins.str", "value": "intp"}]], "readonly_keys": [], "required_keys": ["bool_", "cdouble", "csingle", "double", "half", "int_", "single", "uint"]}}}, "_CNamesDict": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy._core._type_aliases._CNamesDict", "name": "_CNamesDict", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "numpy._core._type_aliases._CNamesDict", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "numpy._core._type_aliases", "mro": ["numpy._core._type_aliases._CNamesDict", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["BOOL", {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "numpy.bool"}], "extra_attrs": null, "type_ref": "numpy.dtype"}], ["HALF", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.half"}], "extra_attrs": null, "type_ref": "numpy.dtype"}], ["FLOAT", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.single"}], "extra_attrs": null, "type_ref": "numpy.dtype"}], ["DOUBLE", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.double"}], "extra_attrs": null, "type_ref": "numpy.dtype"}], ["LONGDOUBLE", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.longdouble"}], "extra_attrs": null, "type_ref": "numpy.dtype"}], ["CFLOAT", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.c<PERSON>le"}], "extra_attrs": null, "type_ref": "numpy.dtype"}], ["CDOUBLE", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.cdouble"}], "extra_attrs": null, "type_ref": "numpy.dtype"}], ["CLONGDOUBLE", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.clongdouble"}], "extra_attrs": null, "type_ref": "numpy.dtype"}], ["STRING", {".class": "Instance", "args": ["numpy.bytes_"], "extra_attrs": null, "type_ref": "numpy.dtype"}], ["UNICODE", {".class": "Instance", "args": ["numpy.str_"], "extra_attrs": null, "type_ref": "numpy.dtype"}], ["VOID", {".class": "Instance", "args": ["numpy.void"], "extra_attrs": null, "type_ref": "numpy.dtype"}], ["OBJECT", {".class": "Instance", "args": ["numpy.object_"], "extra_attrs": null, "type_ref": "numpy.dtype"}], ["DATETIME", {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["datetime.date", "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.datetime64"}], "extra_attrs": null, "type_ref": "numpy.dtype"}], ["TIMEDELTA", {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["datetime.<PERSON><PERSON><PERSON>", "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.timedelta64"}], "extra_attrs": null, "type_ref": "numpy.dtype"}], ["BYTE", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.byte"}], "extra_attrs": null, "type_ref": "numpy.dtype"}], ["UBYTE", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.ubyte"}], "extra_attrs": null, "type_ref": "numpy.dtype"}], ["SHORT", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.short"}], "extra_attrs": null, "type_ref": "numpy.dtype"}], ["USHORT", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.ushort"}], "extra_attrs": null, "type_ref": "numpy.dtype"}], ["INT", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intc"}], "extra_attrs": null, "type_ref": "numpy.dtype"}], ["UINT", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.uintc"}], "extra_attrs": null, "type_ref": "numpy.dtype"}], ["LONG", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.long"}], "extra_attrs": null, "type_ref": "numpy.dtype"}], ["ULONG", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.ulong"}], "extra_attrs": null, "type_ref": "numpy.dtype"}], ["LONGLONG", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.longlong"}], "extra_attrs": null, "type_ref": "numpy.dtype"}], ["ULONGLONG", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.ul<PERSON>"}], "extra_attrs": null, "type_ref": "numpy.dtype"}]], "readonly_keys": [], "required_keys": ["BOOL", "BYTE", "CDOUBLE", "CFLOAT", "CLONGDOUBLE", "DATETIME", "DOUBLE", "FLOAT", "HALF", "INT", "LONG", "LONGDOUBLE", "LONGLONG", "OBJECT", "SHORT", "STRING", "TIMEDELTA", "UBYTE", "UINT", "ULONG", "ULONGLONG", "UNICODE", "USHORT", "VOID"]}}}, "_ExtraAliasesType": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy._core._type_aliases._ExtraAliasesType", "name": "_ExtraAliasesType", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "numpy._core._type_aliases._ExtraAliasesType", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "numpy._core._type_aliases", "mro": ["numpy._core._type_aliases._ExtraAliasesType", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["float", {".class": "LiteralType", "fallback": "builtins.str", "value": "float64"}], ["complex", {".class": "LiteralType", "fallback": "builtins.str", "value": "complex128"}], ["object", {".class": "LiteralType", "fallback": "builtins.str", "value": "object_"}], ["bytes", {".class": "LiteralType", "fallback": "builtins.str", "value": "bytes_"}], ["a", {".class": "LiteralType", "fallback": "builtins.str", "value": "bytes_"}], ["int", {".class": "LiteralType", "fallback": "builtins.str", "value": "int_"}], ["str", {".class": "LiteralType", "fallback": "builtins.str", "value": "str_"}], ["unicode", {".class": "LiteralType", "fallback": "builtins.str", "value": "str_"}]], "readonly_keys": [], "required_keys": ["a", "bytes", "complex", "float", "int", "object", "str", "unicode"]}}}, "_SCTypes": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy._core._type_aliases._SCTypes", "name": "_SCTypes", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "numpy._core._type_aliases._SCTypes", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "numpy._core._type_aliases", "mro": ["numpy._core._type_aliases._SCTypes", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["int", {".class": "Instance", "args": [{".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.<PERSON><PERSON><PERSON>r"}}], "extra_attrs": null, "type_ref": "typing.Collection"}], ["uint", {".class": "Instance", "args": [{".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.unsignedinteger"}}], "extra_attrs": null, "type_ref": "typing.Collection"}], ["float", {".class": "Instance", "args": [{".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.floating"}}], "extra_attrs": null, "type_ref": "typing.Collection"}], ["complex", {".class": "Instance", "args": [{".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.complexfloating"}}], "extra_attrs": null, "type_ref": "typing.Collection"}], ["others", {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.bytes", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.flexible"}}, {".class": "TypeType", "item": {".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "numpy.bool"}}, {".class": "TypeType", "item": "numpy.object_"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "typing.Collection"}]], "readonly_keys": [], "required_keys": ["complex", "float", "int", "others", "uint"]}}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "numpy._core._type_aliases.__all__", "name": "__all__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy._core._type_aliases.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy._core._type_aliases.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy._core._type_aliases.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy._core._type_aliases.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy._core._type_aliases.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy._core._type_aliases.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_abstract_type_names": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "numpy._core._type_aliases._abstract_type_names", "name": "_abstract_type_names", "type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._core._type_aliases._AbstractTypeName"}], "extra_attrs": null, "type_ref": "builtins.set"}}}, "_aliases": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "numpy._core._type_aliases._aliases", "name": "_aliases", "type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy._core._type_aliases._AliasesType"}}}, "_extra_aliases": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "numpy._core._type_aliases._extra_aliases", "name": "_extra_aliases", "type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy._core._type_aliases._ExtraAliasesType"}}}, "allTypes": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "numpy._core._type_aliases.allTypes", "name": "allTypes", "type": {".class": "Instance", "args": ["builtins.str", {".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "c_names_dict": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "numpy._core._type_aliases.c_names_dict", "name": "c_names_dict", "type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy._core._type_aliases._CNamesDict"}}}, "np": {".class": "SymbolTableNode", "cross_ref": "numpy", "kind": "Gdef", "module_hidden": true, "module_public": false}, "sctypeDict": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "numpy._core._type_aliases.sctypeDict", "name": "sctypeDict", "type": {".class": "Instance", "args": ["builtins.str", {".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "sctypes": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "numpy._core._type_aliases.sctypes", "name": "sctypes", "type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy._core._type_aliases._SCTypes"}}}, "type_check_only": {".class": "SymbolTableNode", "cross_ref": "typing.type_check_only", "kind": "Gdef", "module_hidden": true, "module_public": false}}, "path": "c:\\Python313\\Lib\\site-packages\\numpy\\_core\\_type_aliases.pyi"}