{"data_mtime": 1750100846, "dep_lines": [24, 25, 26, 4, 1, 3, 5, 7, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 10, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["numpy._typing._nbit_base", "numpy._typing._nested_sequence", "numpy._typing._shape", "collections.abc", "__future__", "sys", "typing", "numpy", "builtins", "_frozen_importlib", "_typeshed", "abc", "array", "datetime", "mmap", "numpy._typing._dtype_like", "numpy.dtypes", "types"], "hash": "51d2ed13a2ad324bb29f4800536a3bd0cf18851a", "id": "numpy._typing._array_like", "ignore_all": true, "interface_hash": "b9f3b578f58b53da5abbc06cf256a7ea0c00fe99", "mtime": 1748795461, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Python313\\Lib\\site-packages\\numpy\\_typing\\_array_like.py", "plugin_data": null, "size": 5757, "suppressed": [], "version_id": "1.15.0"}