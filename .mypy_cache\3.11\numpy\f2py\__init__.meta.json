{"data_mtime": 1750100833, "dep_lines": [3, 1, 2, 4, 1, 1, 1, 1], "dep_prios": [5, 5, 10, 5, 5, 30, 30, 30], "dependencies": ["collections.abc", "_typeshed", "subprocess", "typing", "builtins", "_frozen_importlib", "abc", "os"], "hash": "3e6d6878c6338d661150dc195d1868b516074e88", "id": "numpy.f2py", "ignore_all": true, "interface_hash": "e93ab39241481cf57f4c44ecfb74a58ee58a02b6", "mtime": 1748795461, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Python313\\Lib\\site-packages\\numpy\\f2py\\__init__.pyi", "plugin_data": null, "size": 1103, "suppressed": [], "version_id": "1.15.0"}