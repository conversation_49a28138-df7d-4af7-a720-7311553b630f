{".class": "MypyFile", "_fullname": "tarfile", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "AREGTYPE": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tarfile.AREGTYPE", "name": "AREGTYPE", "type": "builtins.bytes"}}, "AbsoluteLinkError": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["tarfile.FilterError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "tarfile.AbsoluteLinkError", "name": "AbsoluteLinkError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "tarfile.AbsoluteLinkError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "tarfile", "mro": ["tarfile.AbsoluteLinkError", "tarfile.FilterError", "tarfile.TarError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "tarinfo"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tarfile.AbsoluteLinkError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "tarinfo"], "arg_types": ["tarfile.AbsoluteLinkError", "tarfile.TarInfo"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of AbsoluteLinkError", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tarfile.AbsoluteLinkError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tarfile.AbsoluteLinkError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AbsolutePathError": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["tarfile.FilterError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "tarfile.AbsolutePathError", "name": "AbsolutePathError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "tarfile.AbsolutePathError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "tarfile", "mro": ["tarfile.AbsolutePathError", "tarfile.FilterError", "tarfile.TarError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "tarinfo"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tarfile.AbsolutePathError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "tarinfo"], "arg_types": ["tarfile.AbsolutePathError", "tarfile.TarInfo"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of AbsolutePathError", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tarfile.AbsolutePathError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tarfile.AbsolutePathError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "BLKTYPE": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tarfile.BLKTYPE", "name": "BLKTYPE", "type": "builtins.bytes"}}, "BLOCKSIZE": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tarfile.BLOCKSIZE", "name": "BLOCKSIZE", "type": "builtins.int"}}, "CHRTYPE": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tarfile.CHRTYPE", "name": "CHRTYPE", "type": "builtins.bytes"}}, "CONTTYPE": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tarfile.CONTTYPE", "name": "CONTTYPE", "type": "builtins.bytes"}}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ClassVar": {".class": "SymbolTableNode", "cross_ref": "typing.ClassVar", "kind": "Gdef", "module_hidden": true, "module_public": false}, "CompressionError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["tarfile.TarError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "tarfile.CompressionError", "name": "CompressionError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "tarfile.CompressionError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "tarfile", "mro": ["tarfile.CompressionError", "tarfile.TarError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tarfile.CompressionError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tarfile.CompressionError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DEFAULT_FORMAT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tarfile.DEFAULT_FORMAT", "name": "DEFAULT_FORMAT", "type": "builtins.int"}}, "DIRTYPE": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tarfile.DIRTYPE", "name": "DIRTYPE", "type": "builtins.bytes"}}, "ENCODING": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tarfile.ENCODING", "name": "ENCODING", "type": "builtins.str"}}, "ExFileObject": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["_io.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "tarfile.ExFileObject", "name": "ExFileObject", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "tarfile.ExFileObject", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "tarfile", "mro": ["tarfile.ExFileObject", "_io.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "io.BufferedIOBase", "_io._BufferedIOBase", "io.IOBase", "_io._IOBase", "typing.BinaryIO", "typing.IO", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "tarfile", "tarinfo"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tarfile.ExFileObject.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "tarfile", "tarinfo"], "arg_types": ["tarfile.ExFileObject", "tarfile.TarFile", "tarfile.TarInfo"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ExFileObject", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tarfile.ExFileObject.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tarfile.ExFileObject", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ExtractError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["tarfile.TarError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "tarfile.ExtractError", "name": "ExtractError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "tarfile.ExtractError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "tarfile", "mro": ["tarfile.ExtractError", "tarfile.TarError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tarfile.ExtractError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tarfile.ExtractError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FIFOTYPE": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tarfile.FIFOTYPE", "name": "FIFOTYPE", "type": "builtins.bytes"}}, "FilterError": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["tarfile.TarError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "tarfile.FilterError", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "tarfile.FilterError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "tarfile", "mro": ["tarfile.FilterError", "tarfile.TarError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "tarinfo": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "tarfile.FilterError.tarinfo", "name": "tarinfo", "type": "tarfile.TarInfo"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tarfile.FilterError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tarfile.FilterError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "GNUTYPE_LONGLINK": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tarfile.GNUTYPE_LONGLINK", "name": "GNUTYPE_LONGLINK", "type": "builtins.bytes"}}, "GNUTYPE_LONGNAME": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tarfile.GNUTYPE_LONGNAME", "name": "GNUTYPE_LONGNAME", "type": "builtins.bytes"}}, "GNUTYPE_SPARSE": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tarfile.GNUTYPE_SPARSE", "name": "GNUTYPE_SPARSE", "type": "builtins.bytes"}}, "GNU_FORMAT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tarfile.GNU_FORMAT", "name": "GNU_FORMAT", "type": "builtins.int"}}, "GNU_MAGIC": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tarfile.GNU_MAGIC", "name": "GNU_MAGIC", "type": "builtins.bytes"}}, "GNU_TYPES": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tarfile.GNU_TYPES", "name": "GNU_TYPES", "type": {".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "HeaderError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["tarfile.TarError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "tarfile.HeaderError", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "tarfile.HeaderError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "tarfile", "mro": ["tarfile.HeaderError", "tarfile.TarError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tarfile.HeaderError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tarfile.HeaderError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "IO": {".class": "SymbolTableNode", "cross_ref": "typing.IO", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Iterator": {".class": "SymbolTableNode", "cross_ref": "typing.Iterator", "kind": "Gdef", "module_hidden": true, "module_public": false}, "LENGTH_LINK": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tarfile.LENGTH_LINK", "name": "LENGTH_LINK", "type": "builtins.int"}}, "LENGTH_NAME": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tarfile.LENGTH_NAME", "name": "LENGTH_NAME", "type": "builtins.int"}}, "LENGTH_PREFIX": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tarfile.LENGTH_PREFIX", "name": "LENGTH_PREFIX", "type": "builtins.int"}}, "LNKTYPE": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tarfile.LNKTYPE", "name": "LNKTYPE", "type": "builtins.bytes"}}, "LinkOutsideDestinationError": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["tarfile.FilterError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "tarfile.LinkOutsideDestinationError", "name": "LinkOutsideDestinationError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "tarfile.LinkOutsideDestinationError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "tarfile", "mro": ["tarfile.LinkOutsideDestinationError", "tarfile.FilterError", "tarfile.TarError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "tarinfo", "path"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tarfile.LinkOutsideDestinationError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "tarinfo", "path"], "arg_types": ["tarfile.LinkOutsideDestinationError", "tarfile.TarInfo", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of LinkOutsideDestinationError", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tarfile.LinkOutsideDestinationError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tarfile.LinkOutsideDestinationError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing.Literal", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Mapping": {".class": "SymbolTableNode", "cross_ref": "typing.Mapping", "kind": "Gdef", "module_hidden": true, "module_public": false}, "NUL": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tarfile.NUL", "name": "NUL", "type": "builtins.bytes"}}, "OutsideDestinationError": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["tarfile.FilterError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "tarfile.OutsideDestinationError", "name": "OutsideDestinationError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "tarfile.OutsideDestinationError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "tarfile", "mro": ["tarfile.OutsideDestinationError", "tarfile.FilterError", "tarfile.TarError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "tarinfo", "path"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tarfile.OutsideDestinationError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "tarinfo", "path"], "arg_types": ["tarfile.OutsideDestinationError", "tarfile.TarInfo", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of OutsideDestinationError", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tarfile.OutsideDestinationError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tarfile.OutsideDestinationError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "PAX_FIELDS": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tarfile.PAX_FIELDS", "name": "PAX_FIELDS", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "PAX_FORMAT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tarfile.PAX_FORMAT", "name": "PAX_FORMAT", "type": "builtins.int"}}, "PAX_NAME_FIELDS": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tarfile.PAX_NAME_FIELDS", "name": "PAX_NAME_FIELDS", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}}}, "PAX_NUMBER_FIELDS": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tarfile.PAX_NUMBER_FIELDS", "name": "PAX_NUMBER_FIELDS", "type": {".class": "Instance", "args": ["builtins.str", "builtins.type"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "POSIX_MAGIC": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tarfile.POSIX_MAGIC", "name": "POSIX_MAGIC", "type": "builtins.bytes"}}, "Protocol": {".class": "SymbolTableNode", "cross_ref": "typing.Protocol", "kind": "Gdef", "module_hidden": true, "module_public": false}, "RECORDSIZE": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tarfile.RECORDSIZE", "name": "RECORDSIZE", "type": "builtins.int"}}, "REGTYPE": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tarfile.REGTYPE", "name": "REGTYPE", "type": "builtins.bytes"}}, "REGULAR_TYPES": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tarfile.REGULAR_TYPES", "name": "REGULAR_TYPES", "type": {".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "ReadError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["tarfile.TarError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "tarfile.ReadError", "name": "ReadError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "tarfile.ReadError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "tarfile", "mro": ["tarfile.ReadError", "tarfile.TarError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tarfile.ReadError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tarfile.ReadError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ReadableBuffer": {".class": "SymbolTableNode", "cross_ref": "_typeshed.ReadableBuffer", "kind": "Gdef", "module_hidden": true, "module_public": false}, "SOLARIS_XHDTYPE": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tarfile.SOLARIS_XHDTYPE", "name": "SOLARIS_XHDTYPE", "type": "builtins.bytes"}}, "SUPPORTED_TYPES": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tarfile.SUPPORTED_TYPES", "name": "SUPPORTED_TYPES", "type": {".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "SYMTYPE": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tarfile.SYMTYPE", "name": "SYMTYPE", "type": "builtins.bytes"}}, "Self": {".class": "SymbolTableNode", "cross_ref": "typing.Self", "kind": "Gdef", "module_hidden": true, "module_public": false}, "SpecialFileError": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["tarfile.FilterError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "tarfile.SpecialFileError", "name": "SpecialFileError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "tarfile.SpecialFileError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "tarfile", "mro": ["tarfile.SpecialFileError", "tarfile.FilterError", "tarfile.TarError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "tarinfo"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tarfile.SpecialFileError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "tarinfo"], "arg_types": ["tarfile.SpecialFileError", "tarfile.TarInfo"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of SpecialFileError", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tarfile.SpecialFileError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tarfile.SpecialFileError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "StrOrBytesPath": {".class": "SymbolTableNode", "cross_ref": "_typeshed.StrOrBytesPath", "kind": "Gdef", "module_hidden": true, "module_public": false}, "StrPath": {".class": "SymbolTableNode", "cross_ref": "_typeshed.StrPath", "kind": "Gdef", "module_hidden": true, "module_public": false}, "StreamError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["tarfile.TarError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "tarfile.StreamError", "name": "StreamError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "tarfile.StreamError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "tarfile", "mro": ["tarfile.StreamError", "tarfile.TarError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tarfile.StreamError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tarfile.StreamError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SupportsRead": {".class": "SymbolTableNode", "cross_ref": "_typeshed.SupportsRead", "kind": "Gdef", "module_hidden": true, "module_public": false}, "TarError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.Exception"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "tarfile.TarError", "name": "TarError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "tarfile.TarError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "tarfile", "mro": ["tarfile.TarError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tarfile.TarError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tarfile.TarError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TarFile": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "tarfile.TarFile", "name": "TarFile", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "tarfile.TarFile", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "tarfile", "mro": ["tarfile.TarFile", "builtins.object"], "names": {".class": "SymbolTable", "OPEN_METH": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready"], "fullname": "tarfile.TarFile.OPEN_METH", "name": "OPEN_METH", "type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "typing.Mapping"}}}, "__enter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tarfile.TarFile.__enter__", "name": "__enter__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tarfile.TarFile.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tarfile.TarFile", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__enter__ of TarFile", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tarfile.TarFile.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tarfile.TarFile", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tarfile.TarFile.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tarfile.TarFile", "values": [], "variance": 0}]}}}, "__exit__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": [null, null, null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tarfile.TarFile.__exit__", "name": "__exit__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": [null, null, null, null], "arg_types": ["tarfile.TarFile", {".class": "UnionType", "items": [{".class": "TypeType", "item": "builtins.BaseException"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.BaseException", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["types.TracebackType", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__exit__ of TarFile", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "name", "mode", "<PERSON><PERSON><PERSON>", "format", "tarinfo", "dereference", "ignore_zeros", "encoding", "errors", "pax_headers", "debug", "errorlevel", "copybufsize"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tarfile.TarFile.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "name", "mode", "<PERSON><PERSON><PERSON>", "format", "tarinfo", "dereference", "ignore_zeros", "encoding", "errors", "pax_headers", "debug", "errorlevel", "copybufsize"], "arg_types": ["tarfile.TarFile", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.StrOrBytesPath"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "r"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "a"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "w"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "x"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["tarfile._<PERSON><PERSON>j", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeType", "item": "tarfile.TarInfo"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TarFile", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__iter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tarfile.TarFile.__iter__", "name": "__iter__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["tarfile.TarFile"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__iter__ of TarFile", "ret_type": {".class": "Instance", "args": ["tarfile.TarInfo"], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_extract_member": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "tarinfo", "targetpath", "set_attrs", "numeric_owner"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tarfile.TarFile._extract_member", "name": "_extract_member", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "tarinfo", "targetpath", "set_attrs", "numeric_owner"], "arg_types": ["tarfile.TarFile", "tarfile.TarInfo", "builtins.str", "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_extract_member of TarFile", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "add": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 5], "arg_names": ["self", "name", "arcname", "recursive", "filter"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tarfile.TarFile.add", "name": "add", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 5], "arg_names": ["self", "name", "arcname", "recursive", "filter"], "arg_types": ["tarfile.TarFile", {".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.StrPath"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.StrPath"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["tarfile.TarInfo"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "UnionType", "items": ["tarfile.TarInfo", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add of TarFile", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "addfile": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "tarinfo", "<PERSON><PERSON><PERSON>"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tarfile.TarFile.addfile", "name": "addfile", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "tarinfo", "<PERSON><PERSON><PERSON>"], "arg_types": ["tarfile.TarFile", "tarfile.TarInfo", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "_typeshed.SupportsRead"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "addfile of TarFile", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "bz2open": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": ["is_class"], "fullname": "tarfile.TarFile.bz2open", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["cls", "name", "mode", "<PERSON><PERSON><PERSON>", "compresslevel", "format", "tarinfo", "dereference", "ignore_zeros", "encoding", "pax_headers", "debug", "errorlevel"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_overload", "is_decorated"], "fullname": "tarfile.TarFile.bz2open", "name": "bz2open", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["cls", "name", "mode", "<PERSON><PERSON><PERSON>", "compresslevel", "format", "tarinfo", "dereference", "ignore_zeros", "encoding", "pax_headers", "debug", "errorlevel"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tarfile.TarFile.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tarfile.TarFile", "values": [], "variance": 0}}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.StrOrBytesPath"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "w"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "x"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["tarfile._Bz2WritableFileobj", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeType", "item": "tarfile.TarInfo"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "bz2open of TarFile", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tarfile.TarFile.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tarfile.TarFile", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tarfile.TarFile.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tarfile.TarFile", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_classmethod", "is_ready", "is_inferred"], "fullname": "tarfile.TarFile.bz2open", "name": "bz2open", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["cls", "name", "mode", "<PERSON><PERSON><PERSON>", "compresslevel", "format", "tarinfo", "dereference", "ignore_zeros", "encoding", "pax_headers", "debug", "errorlevel"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tarfile.TarFile.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tarfile.TarFile", "values": [], "variance": 0}}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.StrOrBytesPath"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "w"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "x"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["tarfile._Bz2WritableFileobj", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeType", "item": "tarfile.TarInfo"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "bz2open of TarFile", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tarfile.TarFile.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tarfile.TarFile", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tarfile.TarFile.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tarfile.TarFile", "values": [], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["cls", "name", "mode", "<PERSON><PERSON><PERSON>", "compresslevel", "format", "tarinfo", "dereference", "ignore_zeros", "encoding", "pax_headers", "debug", "errorlevel"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_overload", "is_decorated"], "fullname": "tarfile.TarFile.bz2open", "name": "bz2open", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["cls", "name", "mode", "<PERSON><PERSON><PERSON>", "compresslevel", "format", "tarinfo", "dereference", "ignore_zeros", "encoding", "pax_headers", "debug", "errorlevel"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tarfile.TarFile.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tarfile.TarFile", "values": [], "variance": 0}}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.StrOrBytesPath"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "LiteralType", "fallback": "builtins.str", "value": "r"}, {".class": "UnionType", "items": ["tarfile._Bz2ReadableFileobj", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeType", "item": "tarfile.TarInfo"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "bz2open of TarFile", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tarfile.TarFile.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tarfile.TarFile", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tarfile.TarFile.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tarfile.TarFile", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_classmethod", "is_ready", "is_inferred"], "fullname": "tarfile.TarFile.bz2open", "name": "bz2open", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["cls", "name", "mode", "<PERSON><PERSON><PERSON>", "compresslevel", "format", "tarinfo", "dereference", "ignore_zeros", "encoding", "pax_headers", "debug", "errorlevel"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tarfile.TarFile.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tarfile.TarFile", "values": [], "variance": 0}}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.StrOrBytesPath"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "LiteralType", "fallback": "builtins.str", "value": "r"}, {".class": "UnionType", "items": ["tarfile._Bz2ReadableFileobj", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeType", "item": "tarfile.TarInfo"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "bz2open of TarFile", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tarfile.TarFile.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tarfile.TarFile", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tarfile.TarFile.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tarfile.TarFile", "values": [], "variance": 0}]}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["cls", "name", "mode", "<PERSON><PERSON><PERSON>", "compresslevel", "format", "tarinfo", "dereference", "ignore_zeros", "encoding", "pax_headers", "debug", "errorlevel"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tarfile.TarFile.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tarfile.TarFile", "values": [], "variance": 0}}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.StrOrBytesPath"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "w"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "x"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["tarfile._Bz2WritableFileobj", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeType", "item": "tarfile.TarInfo"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "bz2open of TarFile", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tarfile.TarFile.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tarfile.TarFile", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tarfile.TarFile.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tarfile.TarFile", "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["cls", "name", "mode", "<PERSON><PERSON><PERSON>", "compresslevel", "format", "tarinfo", "dereference", "ignore_zeros", "encoding", "pax_headers", "debug", "errorlevel"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tarfile.TarFile.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tarfile.TarFile", "values": [], "variance": 0}}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.StrOrBytesPath"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "LiteralType", "fallback": "builtins.str", "value": "r"}, {".class": "UnionType", "items": ["tarfile._Bz2ReadableFileobj", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeType", "item": "tarfile.TarInfo"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "bz2open of TarFile", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tarfile.TarFile.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tarfile.TarFile", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tarfile.TarFile.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tarfile.TarFile", "values": [], "variance": 0}]}]}}}, "chmod": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "tarinfo", "targetpath"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tarfile.TarFile.chmod", "name": "chmod", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "tarinfo", "targetpath"], "arg_types": ["tarfile.TarFile", "tarfile.TarInfo", {".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.StrOrBytesPath"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "chmod of TarFile", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "chown": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "tarinfo", "targetpath", "numeric_owner"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tarfile.TarFile.chown", "name": "chown", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "tarinfo", "targetpath", "numeric_owner"], "arg_types": ["tarfile.TarFile", "tarfile.TarInfo", {".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.StrOrBytesPath"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "chown of TarFile", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "close": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tarfile.TarFile.close", "name": "close", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["tarfile.TarFile"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "close of TarFile", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "debug": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "tarfile.TarFile.debug", "name": "debug", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "dereference": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "tarfile.TarFile.dereference", "name": "dereference", "type": {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "encoding": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "tarfile.TarFile.encoding", "name": "encoding", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "errorlevel": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "tarfile.TarFile.errorlevel", "name": "errorlevel", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "errors": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "tarfile.TarFile.errors", "name": "errors", "type": "builtins.str"}}, "extract": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 5, 5], "arg_names": ["self", "member", "path", "set_attrs", "numeric_owner", "filter"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tarfile.TarFile.extract", "name": "extract", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 5, 5], "arg_names": ["self", "member", "path", "set_attrs", "numeric_owner", "filter"], "arg_types": ["tarfile.TarFile", {".class": "UnionType", "items": ["builtins.str", "tarfile.TarInfo"], "uses_pep604_syntax": true}, {".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.StrOrBytesPath"}, "builtins.bool", "builtins.bool", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "tarfile._TarfileFilter"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "extract of TarFile", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "extractall": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 5, 5], "arg_names": ["self", "path", "members", "numeric_owner", "filter"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tarfile.TarFile.extractall", "name": "extractall", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 5, 5], "arg_names": ["self", "path", "members", "numeric_owner", "filter"], "arg_types": ["tarfile.TarFile", {".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.StrOrBytesPath"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["tarfile.TarInfo"], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "tarfile._TarfileFilter"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "extractall of TarFile", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "extractfile": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "member"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tarfile.TarFile.extractfile", "name": "extractfile", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "member"], "arg_types": ["tarfile.TarFile", {".class": "UnionType", "items": ["builtins.str", "tarfile.TarInfo"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "extractfile of TarFile", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "typing.IO"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "extraction_filter": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "tarfile.TarFile.extraction_filter", "name": "extraction_filter", "type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "tarfile._FilterFunction"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "fileobj": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "tarfile.TarFile.fileobj", "name": "<PERSON><PERSON><PERSON>", "type": {".class": "UnionType", "items": ["tarfile._<PERSON><PERSON>j", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "fileobject": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "tarfile.TarFile.fileobject", "name": "fileobject", "type": {".class": "TypeType", "item": "tarfile.ExFileObject"}}}, "format": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "tarfile.TarFile.format", "name": "format", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "getmember": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tarfile.TarFile.getmember", "name": "getmember", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "name"], "arg_types": ["tarfile.TarFile", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getmember of TarFile", "ret_type": "tarfile.TarInfo", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "getmembers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tarfile.TarFile.getmembers", "name": "getmembers", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["tarfile.TarFile"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getmembers of TarFile", "ret_type": {".class": "Instance", "args": ["tarfile.TarInfo"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "getnames": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tarfile.TarFile.getnames", "name": "getnames", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["tarfile.TarFile"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getnames of TarFile", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "gettarinfo": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "name", "arcname", "<PERSON><PERSON><PERSON>"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tarfile.TarFile.gettarinfo", "name": "gettarinfo", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "name", "arcname", "<PERSON><PERSON><PERSON>"], "arg_types": ["tarfile.TarFile", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.StrOrBytesPath"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "typing.IO"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "gettarinfo of TarFile", "ret_type": "tarfile.TarInfo", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "gzopen": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": ["is_class"], "fullname": "tarfile.TarFile.gzopen", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["cls", "name", "mode", "<PERSON><PERSON><PERSON>", "compresslevel", "format", "tarinfo", "dereference", "ignore_zeros", "encoding", "pax_headers", "debug", "errorlevel"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_overload", "is_decorated"], "fullname": "tarfile.TarFile.gzopen", "name": "gzopen", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["cls", "name", "mode", "<PERSON><PERSON><PERSON>", "compresslevel", "format", "tarinfo", "dereference", "ignore_zeros", "encoding", "pax_headers", "debug", "errorlevel"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tarfile.TarFile.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tarfile.TarFile", "values": [], "variance": 0}}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.StrOrBytesPath"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "LiteralType", "fallback": "builtins.str", "value": "r"}, {".class": "UnionType", "items": ["gzip._ReadableFileobj", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeType", "item": "tarfile.TarInfo"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "gzopen of TarFile", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tarfile.TarFile.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tarfile.TarFile", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tarfile.TarFile.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tarfile.TarFile", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_classmethod", "is_ready", "is_inferred"], "fullname": "tarfile.TarFile.gzopen", "name": "gzopen", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["cls", "name", "mode", "<PERSON><PERSON><PERSON>", "compresslevel", "format", "tarinfo", "dereference", "ignore_zeros", "encoding", "pax_headers", "debug", "errorlevel"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tarfile.TarFile.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tarfile.TarFile", "values": [], "variance": 0}}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.StrOrBytesPath"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "LiteralType", "fallback": "builtins.str", "value": "r"}, {".class": "UnionType", "items": ["gzip._ReadableFileobj", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeType", "item": "tarfile.TarInfo"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "gzopen of TarFile", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tarfile.TarFile.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tarfile.TarFile", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tarfile.TarFile.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tarfile.TarFile", "values": [], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["cls", "name", "mode", "<PERSON><PERSON><PERSON>", "compresslevel", "format", "tarinfo", "dereference", "ignore_zeros", "encoding", "pax_headers", "debug", "errorlevel"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_overload", "is_decorated"], "fullname": "tarfile.TarFile.gzopen", "name": "gzopen", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["cls", "name", "mode", "<PERSON><PERSON><PERSON>", "compresslevel", "format", "tarinfo", "dereference", "ignore_zeros", "encoding", "pax_headers", "debug", "errorlevel"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tarfile.TarFile.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tarfile.TarFile", "values": [], "variance": 0}}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.StrOrBytesPath"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "w"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "x"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["gzip._WritableFileobj", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeType", "item": "tarfile.TarInfo"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "gzopen of TarFile", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tarfile.TarFile.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tarfile.TarFile", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tarfile.TarFile.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tarfile.TarFile", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_classmethod", "is_ready", "is_inferred"], "fullname": "tarfile.TarFile.gzopen", "name": "gzopen", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["cls", "name", "mode", "<PERSON><PERSON><PERSON>", "compresslevel", "format", "tarinfo", "dereference", "ignore_zeros", "encoding", "pax_headers", "debug", "errorlevel"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tarfile.TarFile.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tarfile.TarFile", "values": [], "variance": 0}}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.StrOrBytesPath"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "w"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "x"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["gzip._WritableFileobj", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeType", "item": "tarfile.TarInfo"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "gzopen of TarFile", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tarfile.TarFile.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tarfile.TarFile", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tarfile.TarFile.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tarfile.TarFile", "values": [], "variance": 0}]}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["cls", "name", "mode", "<PERSON><PERSON><PERSON>", "compresslevel", "format", "tarinfo", "dereference", "ignore_zeros", "encoding", "pax_headers", "debug", "errorlevel"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tarfile.TarFile.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tarfile.TarFile", "values": [], "variance": 0}}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.StrOrBytesPath"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "LiteralType", "fallback": "builtins.str", "value": "r"}, {".class": "UnionType", "items": ["gzip._ReadableFileobj", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeType", "item": "tarfile.TarInfo"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "gzopen of TarFile", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tarfile.TarFile.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tarfile.TarFile", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tarfile.TarFile.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tarfile.TarFile", "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["cls", "name", "mode", "<PERSON><PERSON><PERSON>", "compresslevel", "format", "tarinfo", "dereference", "ignore_zeros", "encoding", "pax_headers", "debug", "errorlevel"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tarfile.TarFile.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tarfile.TarFile", "values": [], "variance": 0}}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.StrOrBytesPath"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "w"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "x"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["gzip._WritableFileobj", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeType", "item": "tarfile.TarInfo"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "gzopen of TarFile", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tarfile.TarFile.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tarfile.TarFile", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tarfile.TarFile.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tarfile.TarFile", "values": [], "variance": 0}]}]}}}, "ignore_zeros": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "tarfile.TarFile.ignore_zeros", "name": "ignore_zeros", "type": {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "list": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 5], "arg_names": ["self", "verbose", "members"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tarfile.TarFile.list", "name": "list", "type": {".class": "CallableType", "arg_kinds": [0, 1, 5], "arg_names": ["self", "verbose", "members"], "arg_types": ["tarfile.TarFile", "builtins.bool", {".class": "UnionType", "items": [{".class": "Instance", "args": ["tarfile.TarInfo"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "list of TarFile", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "makedev": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "tarinfo", "targetpath"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tarfile.TarFile.makedev", "name": "makedev", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "tarinfo", "targetpath"], "arg_types": ["tarfile.TarFile", "tarfile.TarInfo", {".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.StrOrBytesPath"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "makedev of TarFile", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "makedir": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "tarinfo", "targetpath"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tarfile.TarFile.makedir", "name": "makedir", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "tarinfo", "targetpath"], "arg_types": ["tarfile.TarFile", "tarfile.TarInfo", {".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.StrOrBytesPath"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "makedir of TarFile", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "makefifo": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "tarinfo", "targetpath"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tarfile.TarFile.makefifo", "name": "makefifo", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "tarinfo", "targetpath"], "arg_types": ["tarfile.TarFile", "tarfile.TarInfo", {".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.StrOrBytesPath"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "make<PERSON>fo of TarFile", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "makefile": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "tarinfo", "targetpath"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tarfile.TarFile.makefile", "name": "makefile", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "tarinfo", "targetpath"], "arg_types": ["tarfile.TarFile", "tarfile.TarInfo", {".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.StrOrBytesPath"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "makefile of TarFile", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "makelink": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "tarinfo", "targetpath"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tarfile.TarFile.makelink", "name": "makelink", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "tarinfo", "targetpath"], "arg_types": ["tarfile.TarFile", "tarfile.TarInfo", {".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.StrOrBytesPath"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "makelink of TarFile", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "makeunknown": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "tarinfo", "targetpath"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tarfile.TarFile.makeunknown", "name": "makeunknown", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "tarinfo", "targetpath"], "arg_types": ["tarfile.TarFile", "tarfile.TarInfo", {".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.StrOrBytesPath"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "makeunknown of TarFile", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "mode": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "tarfile.TarFile.mode", "name": "mode", "type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "r"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "a"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "w"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "x"}], "uses_pep604_syntax": false}}}, "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "tarfile.TarFile.name", "name": "name", "type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.StrOrBytesPath"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "next": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tarfile.TarFile.next", "name": "next", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["tarfile.TarFile"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "next of TarFile", "ret_type": {".class": "UnionType", "items": ["tarfile.TarInfo", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "offset": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "tarfile.TarFile.offset", "name": "offset", "type": "builtins.int"}}, "open": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["cls", "name", "mode", "<PERSON><PERSON><PERSON>", "bufsize", "format", "tarinfo", "dereference", "ignore_zeros", "encoding", "errors", "pax_headers", "debug", "errorlevel"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "tarfile.TarFile.open", "name": "open", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["cls", "name", "mode", "<PERSON><PERSON><PERSON>", "bufsize", "format", "tarinfo", "dereference", "ignore_zeros", "encoding", "errors", "pax_headers", "debug", "errorlevel"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tarfile.TarFile.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tarfile.TarFile", "values": [], "variance": 0}}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.StrOrBytesPath"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "typing.IO"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeType", "item": "tarfile.TarInfo"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "open of TarFile", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tarfile.TarFile.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tarfile.TarFile", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tarfile.TarFile.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tarfile.TarFile", "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "tarfile.TarFile.open", "name": "open", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["cls", "name", "mode", "<PERSON><PERSON><PERSON>", "bufsize", "format", "tarinfo", "dereference", "ignore_zeros", "encoding", "errors", "pax_headers", "debug", "errorlevel"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tarfile.TarFile.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tarfile.TarFile", "values": [], "variance": 0}}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.StrOrBytesPath"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "typing.IO"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeType", "item": "tarfile.TarInfo"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "open of TarFile", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tarfile.TarFile.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tarfile.TarFile", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tarfile.TarFile.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tarfile.TarFile", "values": [], "variance": 0}]}}}}, "pax_headers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "tarfile.TarFile.pax_headers", "name": "pax_headers", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "tarinfo": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "tarfile.TarFile.tarinfo", "name": "tarinfo", "type": {".class": "TypeType", "item": "tarfile.TarInfo"}}}, "taropen": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["cls", "name", "mode", "<PERSON><PERSON><PERSON>", "compresslevel", "format", "tarinfo", "dereference", "ignore_zeros", "encoding", "pax_headers", "debug", "errorlevel"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "tarfile.TarFile.taropen", "name": "ta<PERSON>en", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["cls", "name", "mode", "<PERSON><PERSON><PERSON>", "compresslevel", "format", "tarinfo", "dereference", "ignore_zeros", "encoding", "pax_headers", "debug", "errorlevel"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tarfile.TarFile.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tarfile.TarFile", "values": [], "variance": 0}}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.StrOrBytesPath"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "r"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "a"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "w"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "x"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["tarfile._<PERSON><PERSON>j", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeType", "item": "tarfile.TarInfo"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "taropen of TarFile", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tarfile.TarFile.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tarfile.TarFile", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tarfile.TarFile.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tarfile.TarFile", "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "tarfile.TarFile.taropen", "name": "ta<PERSON>en", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["cls", "name", "mode", "<PERSON><PERSON><PERSON>", "compresslevel", "format", "tarinfo", "dereference", "ignore_zeros", "encoding", "pax_headers", "debug", "errorlevel"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tarfile.TarFile.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tarfile.TarFile", "values": [], "variance": 0}}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.StrOrBytesPath"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "r"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "a"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "w"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "x"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["tarfile._<PERSON><PERSON>j", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeType", "item": "tarfile.TarInfo"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "taropen of TarFile", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tarfile.TarFile.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tarfile.TarFile", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tarfile.TarFile.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tarfile.TarFile", "values": [], "variance": 0}]}}}}, "utime": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "tarinfo", "targetpath"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tarfile.TarFile.utime", "name": "utime", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "tarinfo", "targetpath"], "arg_types": ["tarfile.TarFile", "tarfile.TarInfo", {".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.StrOrBytesPath"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "utime of TarFile", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "xzopen": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["cls", "name", "mode", "<PERSON><PERSON><PERSON>", "preset", "format", "tarinfo", "dereference", "ignore_zeros", "encoding", "pax_headers", "debug", "errorlevel"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "tarfile.TarFile.xzopen", "name": "xzopen", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["cls", "name", "mode", "<PERSON><PERSON><PERSON>", "preset", "format", "tarinfo", "dereference", "ignore_zeros", "encoding", "pax_headers", "debug", "errorlevel"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tarfile.TarFile.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tarfile.TarFile", "values": [], "variance": 0}}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.StrOrBytesPath"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "r"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "w"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "x"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "typing.IO"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeType", "item": "tarfile.TarInfo"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "xzopen of TarFile", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tarfile.TarFile.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tarfile.TarFile", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tarfile.TarFile.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tarfile.TarFile", "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "tarfile.TarFile.xzopen", "name": "xzopen", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["cls", "name", "mode", "<PERSON><PERSON><PERSON>", "preset", "format", "tarinfo", "dereference", "ignore_zeros", "encoding", "pax_headers", "debug", "errorlevel"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tarfile.TarFile.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tarfile.TarFile", "values": [], "variance": 0}}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.StrOrBytesPath"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "r"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "w"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "x"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "typing.IO"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeType", "item": "tarfile.TarInfo"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "xzopen of TarFile", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tarfile.TarFile.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tarfile.TarFile", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tarfile.TarFile.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tarfile.TarFile", "values": [], "variance": 0}]}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tarfile.TarFile.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tarfile.TarFile", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TarInfo": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "tarfile.TarInfo", "name": "TarInfo", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "tarfile.TarInfo", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "tarfile", "mro": ["tarfile.TarInfo", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tarfile.TarInfo.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "name"], "arg_types": ["tarfile.TarInfo", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TarInfo", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "chksum": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "tarfile.TarInfo.chksum", "name": "chksum", "type": "builtins.int"}}, "create_gnu_header": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "info", "encoding", "errors"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tarfile.TarInfo.create_gnu_header", "name": "create_gnu_header", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "info", "encoding", "errors"], "arg_types": ["tarfile.TarInfo", {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", "builtins.int", "builtins.bytes", {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "typing.Mapping"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "typing.Mapping"}, "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_gnu_header of TarInfo", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_pax_global_header": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "pax_headers"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "tarfile.TarInfo.create_pax_global_header", "name": "create_pax_global_header", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "pax_headers"], "arg_types": [{".class": "TypeType", "item": "tarfile.TarInfo"}, {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "typing.Mapping"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_pax_global_header of TarInfo", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "tarfile.TarInfo.create_pax_global_header", "name": "create_pax_global_header", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "pax_headers"], "arg_types": [{".class": "TypeType", "item": "tarfile.TarInfo"}, {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "typing.Mapping"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_pax_global_header of TarInfo", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "create_pax_header": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "info", "encoding"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tarfile.TarInfo.create_pax_header", "name": "create_pax_header", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "info", "encoding"], "arg_types": ["tarfile.TarInfo", {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", "builtins.int", "builtins.bytes", {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "typing.Mapping"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "typing.Mapping"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_pax_header of TarInfo", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_ustar_header": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "info", "encoding", "errors"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tarfile.TarInfo.create_ustar_header", "name": "create_ustar_header", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "info", "encoding", "errors"], "arg_types": ["tarfile.TarInfo", {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", "builtins.int", "builtins.bytes", {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "typing.Mapping"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "typing.Mapping"}, "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_ustar_header of TarInfo", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "devmajor": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "tarfile.TarInfo.devmajor", "name": "dev<PERSON><PERSON><PERSON>", "type": "builtins.int"}}, "devminor": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "tarfile.TarInfo.devminor", "name": "dev<PERSON><PERSON>", "type": "builtins.int"}}, "frombuf": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["cls", "buf", "encoding", "errors"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "tarfile.TarInfo.frombuf", "name": "frombuf", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["cls", "buf", "encoding", "errors"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tarfile.TarInfo.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tarfile.TarInfo", "values": [], "variance": 0}}, {".class": "UnionType", "items": ["builtins.bytes", "builtins.bytearray"], "uses_pep604_syntax": true}, "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "frombuf of TarInfo", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tarfile.TarInfo.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tarfile.TarInfo", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tarfile.TarInfo.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tarfile.TarInfo", "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "tarfile.TarInfo.frombuf", "name": "frombuf", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["cls", "buf", "encoding", "errors"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tarfile.TarInfo.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tarfile.TarInfo", "values": [], "variance": 0}}, {".class": "UnionType", "items": ["builtins.bytes", "builtins.bytearray"], "uses_pep604_syntax": true}, "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "frombuf of TarInfo", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tarfile.TarInfo.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tarfile.TarInfo", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tarfile.TarInfo.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tarfile.TarInfo", "values": [], "variance": 0}]}}}}, "fromtarfile": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "tarfile"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "tarfile.TarInfo.fromtarfile", "name": "fromtarfile", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "tarfile"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tarfile.TarInfo.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tarfile.TarInfo", "values": [], "variance": 0}}, "tarfile.TarFile"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "fromtarfile of TarInfo", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tarfile.TarInfo.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tarfile.TarInfo", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tarfile.TarInfo.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tarfile.TarInfo", "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "tarfile.TarInfo.fromtarfile", "name": "fromtarfile", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "tarfile"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tarfile.TarInfo.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tarfile.TarInfo", "values": [], "variance": 0}}, "tarfile.TarFile"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "fromtarfile of TarInfo", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tarfile.TarInfo.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tarfile.TarInfo", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tarfile.TarInfo.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tarfile.TarInfo", "values": [], "variance": 0}]}}}}, "get_info": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tarfile.TarInfo.get_info", "name": "get_info", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["tarfile.TarInfo"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_info of TarInfo", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", "builtins.int", "builtins.bytes", {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "typing.Mapping"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "typing.Mapping"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "gid": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "tarfile.TarInfo.gid", "name": "gid", "type": "builtins.int"}}, "gname": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "tarfile.TarInfo.gname", "name": "gname", "type": "builtins.str"}}, "isblk": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tarfile.TarInfo.isblk", "name": "isblk", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["tarfile.TarInfo"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "isblk of TarInfo", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "ischr": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tarfile.TarInfo.ischr", "name": "ischr", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["tarfile.TarInfo"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ischr of TarInfo", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "isdev": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tarfile.TarInfo.isdev", "name": "isdev", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["tarfile.TarInfo"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "isdev of TarInfo", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "isdir": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tarfile.TarInfo.isdir", "name": "isdir", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["tarfile.TarInfo"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "isdir of TarInfo", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "isfifo": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tarfile.TarInfo.isfifo", "name": "isfifo", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["tarfile.TarInfo"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "isfifo of TarInfo", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "isfile": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tarfile.TarInfo.isfile", "name": "isfile", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["tarfile.TarInfo"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "isfile of TarInfo", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "islnk": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tarfile.TarInfo.islnk", "name": "islnk", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["tarfile.TarInfo"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "islnk of TarInfo", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "isreg": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tarfile.TarInfo.isreg", "name": "isreg", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["tarfile.TarInfo"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "isreg of TarInfo", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "issparse": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tarfile.TarInfo.issparse", "name": "is<PERSON><PERSON><PERSON>", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["tarfile.TarInfo"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "issparse of TarInfo", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "issym": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tarfile.TarInfo.issym", "name": "issym", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["tarfile.TarInfo"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "issym of TarInfo", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "linkname": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "tarfile.TarInfo.linkname", "name": "linkname", "type": "builtins.str"}}, "linkpath": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": ["is_property"], "fullname": "tarfile.TarInfo.linkpath", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_overload", "is_decorated"], "fullname": "tarfile.TarInfo.linkpath", "name": "linkpath", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["tarfile.TarInfo"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "linkpath of TarInfo", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_settable_property", "is_ready", "is_inferred"], "fullname": "tarfile.TarInfo.linkpath", "name": "linkpath", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["tarfile.TarInfo"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "linkpath of TarInfo", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "linkname"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "tarfile.TarInfo.linkpath", "name": "linkpath", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "linkname"], "arg_types": ["tarfile.TarInfo", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "linkpath of TarInfo", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_inferred"], "fullname": "", "name": "linkpath", "type": null}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["tarfile.TarInfo"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "linkpath of TarInfo", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "mode": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "tarfile.TarInfo.mode", "name": "mode", "type": "builtins.int"}}, "mtime": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "tarfile.TarInfo.mtime", "name": "mtime", "type": {".class": "UnionType", "items": ["builtins.int", "builtins.float"], "uses_pep604_syntax": true}}}, "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "tarfile.TarInfo.name", "name": "name", "type": "builtins.str"}}, "offset": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "tarfile.TarInfo.offset", "name": "offset", "type": "builtins.int"}}, "offset_data": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "tarfile.TarInfo.offset_data", "name": "offset_data", "type": "builtins.int"}}, "path": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "tarfile.TarInfo.path", "name": "path", "type": "builtins.str"}}, "pax_headers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "tarfile.TarInfo.pax_headers", "name": "pax_headers", "type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "typing.Mapping"}}}, "replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "name", "mtime", "mode", "linkname", "uid", "gid", "uname", "gname", "deep"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tarfile.TarInfo.replace", "name": "replace", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "name", "mtime", "mode", "linkname", "uid", "gid", "uname", "gname", "deep"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tarfile.TarInfo.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tarfile.TarInfo", "values": [], "variance": 0}, "builtins.str", "builtins.float", "builtins.int", "builtins.str", "builtins.int", "builtins.int", "builtins.str", "builtins.str", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "replace of TarInfo", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tarfile.TarInfo.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tarfile.TarInfo", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tarfile.TarInfo.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tarfile.TarInfo", "values": [], "variance": 0}]}}}, "size": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "tarfile.TarInfo.size", "name": "size", "type": "builtins.int"}}, "sparse": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "tarfile.TarInfo.sparse", "name": "sparse", "type": {".class": "UnionType", "items": ["builtins.bytes", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "tarfile": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "tarfile.TarInfo.tarfile", "name": "tarfile", "type": {".class": "UnionType", "items": ["tarfile.TarFile", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "tobuf": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "format", "encoding", "errors"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tarfile.TarInfo.tobuf", "name": "tobuf", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "format", "encoding", "errors"], "arg_types": ["tarfile.TarInfo", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "tobuf of TarInfo", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "tarfile.TarInfo.type", "name": "type", "type": "builtins.bytes"}}, "uid": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "tarfile.TarInfo.uid", "name": "uid", "type": "builtins.int"}}, "uname": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "tarfile.TarInfo.uname", "name": "uname", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tarfile.TarInfo.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tarfile.TarInfo", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TracebackType": {".class": "SymbolTableNode", "cross_ref": "types.TracebackType", "kind": "Gdef", "module_hidden": true, "module_public": false}, "TypeAlias": {".class": "SymbolTableNode", "cross_ref": "typing.TypeAlias", "kind": "Gdef", "module_hidden": true, "module_public": false}, "USTAR_FORMAT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tarfile.USTAR_FORMAT", "name": "USTAR_FORMAT", "type": "builtins.int"}}, "WriteableBuffer": {".class": "SymbolTableNode", "cross_ref": "_typeshed.WriteableBuffer", "kind": "Gdef", "module_hidden": true, "module_public": false}, "XGLTYPE": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tarfile.XGLTYPE", "name": "XGLTYPE", "type": "builtins.bytes"}}, "XHDTYPE": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tarfile.XHDTYPE", "name": "XHDTYPE", "type": "builtins.bytes"}}, "_Bz2ReadableFileobj": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["bz2._ReadableFileobj"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "tarfile._Bz2ReadableFileobj", "name": "_Bz2ReadableFileobj", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "tarfile._Bz2ReadableFileobj", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "tarfile", "mro": ["tarfile._Bz2ReadableFileobj", "bz2._ReadableFileobj", "_compression._Reader", "builtins.object"], "names": {".class": "SymbolTable", "close": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tarfile._Bz2ReadableFileobj.close", "name": "close", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["tarfile._Bz2ReadableFileobj"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "close of _Bz2ReadableFileobj", "ret_type": "builtins.object", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tarfile._Bz2ReadableFileobj.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tarfile._Bz2ReadableFileobj", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_Bz2WritableFileobj": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["bz2._WritableFileobj"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "tarfile._Bz2WritableFileobj", "name": "_Bz2WritableFileobj", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "tarfile._Bz2WritableFileobj", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "tarfile", "mro": ["tarfile._Bz2WritableFileobj", "bz2._WritableFileobj", "builtins.object"], "names": {".class": "SymbolTable", "close": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tarfile._Bz2WritableFileobj.close", "name": "close", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["tarfile._Bz2WritableFileobj"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "close of _Bz2WritableFileobj", "ret_type": "builtins.object", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tarfile._Bz2WritableFileobj.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tarfile._Bz2WritableFileobj", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_Fileobj": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "tarfile._<PERSON><PERSON>j", "name": "_<PERSON><PERSON>j", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_protocol"], "fullname": "tarfile._<PERSON><PERSON>j", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "tarfile", "mro": ["tarfile._<PERSON><PERSON>j", "builtins.object"], "names": {".class": "SymbolTable", "close": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tarfile._Fileobj.close", "name": "close", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["tarfile._<PERSON><PERSON>j"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "close of _<PERSON><PERSON>j", "ret_type": "builtins.object", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "read": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tarfile._Fileobj.read", "name": "read", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["tarfile._<PERSON><PERSON>j", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "read of _Fileobj", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "seek": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tarfile._Fileobj.seek", "name": "seek", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["tarfile._<PERSON><PERSON>j", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "seek of _<PERSON><PERSON><PERSON>", "ret_type": "builtins.object", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "tell": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tarfile._Fileobj.tell", "name": "tell", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["tarfile._<PERSON><PERSON>j"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "tell of _<PERSON><PERSON><PERSON>", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "write": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tarfile._Fileobj.write", "name": "write", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["tarfile._<PERSON><PERSON>j", "builtins.bytes"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "write of _Fileobj", "ret_type": "builtins.object", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tarfile._Fileobj.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tarfile._<PERSON><PERSON>j", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_FilterFunction": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "tarfile._FilterFunction", "line": 42, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["tarfile.TarInfo", "builtins.str"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "UnionType", "items": ["tarfile.TarInfo", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_GzipReadableFileobj": {".class": "SymbolTableNode", "cross_ref": "gzip._ReadableFileobj", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_GzipWritableFileobj": {".class": "SymbolTableNode", "cross_ref": "gzip._WritableFileobj", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_TarfileFilter": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "tarfile._TarfileFilter", "line": 43, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "fully_trusted"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "tar"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "data"}, {".class": "TypeAliasType", "args": [], "type_ref": "tarfile._FilterFunction"}], "uses_pep604_syntax": true}}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "tarfile.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tarfile.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tarfile.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tarfile.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tarfile.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tarfile.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tarfile.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_list": {".class": "SymbolTableNode", "cross_ref": "builtins.list", "kind": "Gdef", "module_hidden": true, "module_public": false}, "bz2": {".class": "SymbolTableNode", "cross_ref": "bz2", "kind": "Gdef", "module_hidden": true, "module_public": false}, "data_filter": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["member", "dest_path"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tarfile.data_filter", "name": "data_filter", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["member", "dest_path"], "arg_types": ["tarfile.TarInfo", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "data_filter", "ret_type": "tarfile.TarInfo", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "fully_trusted_filter": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["member", "dest_path"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tarfile.fully_trusted_filter", "name": "fully_trusted_filter", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["member", "dest_path"], "arg_types": ["tarfile.TarInfo", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "fully_trusted_filter", "ret_type": "tarfile.TarInfo", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "io": {".class": "SymbolTableNode", "cross_ref": "io", "kind": "Gdef", "module_hidden": true, "module_public": false}, "is_tarfile": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tarfile.is_tarfile", "name": "is_tarfile", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["name"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.StrOrBytesPath"}, {".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "typing.IO"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_tarfile", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "open": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "tarfile.open", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1, 1, 1, 1, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["name", "mode", "<PERSON><PERSON><PERSON>", "bufsize", "format", "tarinfo", "dereference", "ignore_zeros", "encoding", "errors", "pax_headers", "debug", "errorlevel"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "tarfile.open", "name": "open", "type": {".class": "CallableType", "arg_kinds": [1, 1, 1, 1, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["name", "mode", "<PERSON><PERSON><PERSON>", "bufsize", "format", "tarinfo", "dereference", "ignore_zeros", "encoding", "errors", "pax_headers", "debug", "errorlevel"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.StrOrBytesPath"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "r"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "r:*"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "r:"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "r:gz"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "r:bz2"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "r:xz"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "typing.IO"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeType", "item": "tarfile.TarInfo"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "open", "ret_type": "tarfile.TarFile", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "tarfile.open", "name": "open", "type": {".class": "CallableType", "arg_kinds": [1, 1, 1, 1, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["name", "mode", "<PERSON><PERSON><PERSON>", "bufsize", "format", "tarinfo", "dereference", "ignore_zeros", "encoding", "errors", "pax_headers", "debug", "errorlevel"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.StrOrBytesPath"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "r"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "r:*"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "r:"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "r:gz"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "r:bz2"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "r:xz"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "typing.IO"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeType", "item": "tarfile.TarInfo"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "open", "ret_type": "tarfile.TarFile", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["name", "mode", "<PERSON><PERSON><PERSON>", "bufsize", "format", "tarinfo", "dereference", "ignore_zeros", "encoding", "errors", "pax_headers", "debug", "errorlevel"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "tarfile.open", "name": "open", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["name", "mode", "<PERSON><PERSON><PERSON>", "bufsize", "format", "tarinfo", "dereference", "ignore_zeros", "encoding", "errors", "pax_headers", "debug", "errorlevel"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.StrOrBytesPath"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "x"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "x:"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "a"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "a:"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "w"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "w:"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "w:tar"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["tarfile._<PERSON><PERSON>j", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeType", "item": "tarfile.TarInfo"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "open", "ret_type": "tarfile.TarFile", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "tarfile.open", "name": "open", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["name", "mode", "<PERSON><PERSON><PERSON>", "bufsize", "format", "tarinfo", "dereference", "ignore_zeros", "encoding", "errors", "pax_headers", "debug", "errorlevel"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.StrOrBytesPath"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "x"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "x:"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "a"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "a:"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "w"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "w:"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "w:tar"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["tarfile._<PERSON><PERSON>j", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeType", "item": "tarfile.TarInfo"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "open", "ret_type": "tarfile.TarFile", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1, 3, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["name", "mode", "<PERSON><PERSON><PERSON>", "bufsize", "format", "tarinfo", "dereference", "ignore_zeros", "encoding", "errors", "pax_headers", "debug", "errorlevel"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "tarfile.open", "name": "open", "type": {".class": "CallableType", "arg_kinds": [1, 3, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["name", "mode", "<PERSON><PERSON><PERSON>", "bufsize", "format", "tarinfo", "dereference", "ignore_zeros", "encoding", "errors", "pax_headers", "debug", "errorlevel"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.StrOrBytesPath"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "x"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "x:"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "a"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "a:"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "w"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "w:"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "w:tar"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["tarfile._<PERSON><PERSON>j", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeType", "item": "tarfile.TarInfo"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "open", "ret_type": "tarfile.TarFile", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "tarfile.open", "name": "open", "type": {".class": "CallableType", "arg_kinds": [1, 3, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["name", "mode", "<PERSON><PERSON><PERSON>", "bufsize", "format", "tarinfo", "dereference", "ignore_zeros", "encoding", "errors", "pax_headers", "debug", "errorlevel"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.StrOrBytesPath"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "x"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "x:"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "a"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "a:"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "w"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "w:"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "w:tar"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["tarfile._<PERSON><PERSON>j", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeType", "item": "tarfile.TarInfo"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "open", "ret_type": "tarfile.TarFile", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["name", "mode", "<PERSON><PERSON><PERSON>", "bufsize", "format", "tarinfo", "dereference", "ignore_zeros", "encoding", "errors", "pax_headers", "debug", "errorlevel", "compresslevel"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "tarfile.open", "name": "open", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["name", "mode", "<PERSON><PERSON><PERSON>", "bufsize", "format", "tarinfo", "dereference", "ignore_zeros", "encoding", "errors", "pax_headers", "debug", "errorlevel", "compresslevel"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.StrOrBytesPath"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "x:gz"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "x:bz2"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "w:gz"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "w:bz2"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["tarfile._<PERSON><PERSON>j", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeType", "item": "tarfile.TarInfo"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "open", "ret_type": "tarfile.TarFile", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "tarfile.open", "name": "open", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["name", "mode", "<PERSON><PERSON><PERSON>", "bufsize", "format", "tarinfo", "dereference", "ignore_zeros", "encoding", "errors", "pax_headers", "debug", "errorlevel", "compresslevel"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.StrOrBytesPath"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "x:gz"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "x:bz2"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "w:gz"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "w:bz2"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["tarfile._<PERSON><PERSON>j", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeType", "item": "tarfile.TarInfo"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "open", "ret_type": "tarfile.TarFile", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1, 3, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["name", "mode", "<PERSON><PERSON><PERSON>", "bufsize", "format", "tarinfo", "dereference", "ignore_zeros", "encoding", "errors", "pax_headers", "debug", "errorlevel", "compresslevel"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "tarfile.open", "name": "open", "type": {".class": "CallableType", "arg_kinds": [1, 3, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["name", "mode", "<PERSON><PERSON><PERSON>", "bufsize", "format", "tarinfo", "dereference", "ignore_zeros", "encoding", "errors", "pax_headers", "debug", "errorlevel", "compresslevel"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.StrOrBytesPath"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "x:gz"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "x:bz2"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "w:gz"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "w:bz2"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["tarfile._<PERSON><PERSON>j", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeType", "item": "tarfile.TarInfo"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "open", "ret_type": "tarfile.TarFile", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "tarfile.open", "name": "open", "type": {".class": "CallableType", "arg_kinds": [1, 3, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["name", "mode", "<PERSON><PERSON><PERSON>", "bufsize", "format", "tarinfo", "dereference", "ignore_zeros", "encoding", "errors", "pax_headers", "debug", "errorlevel", "compresslevel"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.StrOrBytesPath"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "x:gz"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "x:bz2"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "w:gz"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "w:bz2"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["tarfile._<PERSON><PERSON>j", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeType", "item": "tarfile.TarInfo"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "open", "ret_type": "tarfile.TarFile", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["name", "mode", "<PERSON><PERSON><PERSON>", "bufsize", "format", "tarinfo", "dereference", "ignore_zeros", "encoding", "errors", "pax_headers", "debug", "errorlevel", "preset"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "tarfile.open", "name": "open", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["name", "mode", "<PERSON><PERSON><PERSON>", "bufsize", "format", "tarinfo", "dereference", "ignore_zeros", "encoding", "errors", "pax_headers", "debug", "errorlevel", "preset"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.StrOrBytesPath"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "x:xz"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "w:xz"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["tarfile._<PERSON><PERSON>j", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeType", "item": "tarfile.TarInfo"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.int", "value": 0}, {".class": "LiteralType", "fallback": "builtins.int", "value": 1}, {".class": "LiteralType", "fallback": "builtins.int", "value": 2}, {".class": "LiteralType", "fallback": "builtins.int", "value": 3}, {".class": "LiteralType", "fallback": "builtins.int", "value": 4}, {".class": "LiteralType", "fallback": "builtins.int", "value": 5}, {".class": "LiteralType", "fallback": "builtins.int", "value": 6}, {".class": "LiteralType", "fallback": "builtins.int", "value": 7}, {".class": "LiteralType", "fallback": "builtins.int", "value": 8}, {".class": "LiteralType", "fallback": "builtins.int", "value": 9}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "open", "ret_type": "tarfile.TarFile", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "tarfile.open", "name": "open", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["name", "mode", "<PERSON><PERSON><PERSON>", "bufsize", "format", "tarinfo", "dereference", "ignore_zeros", "encoding", "errors", "pax_headers", "debug", "errorlevel", "preset"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.StrOrBytesPath"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "x:xz"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "w:xz"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["tarfile._<PERSON><PERSON>j", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeType", "item": "tarfile.TarInfo"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.int", "value": 0}, {".class": "LiteralType", "fallback": "builtins.int", "value": 1}, {".class": "LiteralType", "fallback": "builtins.int", "value": 2}, {".class": "LiteralType", "fallback": "builtins.int", "value": 3}, {".class": "LiteralType", "fallback": "builtins.int", "value": 4}, {".class": "LiteralType", "fallback": "builtins.int", "value": 5}, {".class": "LiteralType", "fallback": "builtins.int", "value": 6}, {".class": "LiteralType", "fallback": "builtins.int", "value": 7}, {".class": "LiteralType", "fallback": "builtins.int", "value": 8}, {".class": "LiteralType", "fallback": "builtins.int", "value": 9}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "open", "ret_type": "tarfile.TarFile", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1, 3, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["name", "mode", "<PERSON><PERSON><PERSON>", "bufsize", "format", "tarinfo", "dereference", "ignore_zeros", "encoding", "errors", "pax_headers", "debug", "errorlevel", "preset"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "tarfile.open", "name": "open", "type": {".class": "CallableType", "arg_kinds": [1, 3, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["name", "mode", "<PERSON><PERSON><PERSON>", "bufsize", "format", "tarinfo", "dereference", "ignore_zeros", "encoding", "errors", "pax_headers", "debug", "errorlevel", "preset"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.StrOrBytesPath"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "x:xz"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "w:xz"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["tarfile._<PERSON><PERSON>j", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeType", "item": "tarfile.TarInfo"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.int", "value": 0}, {".class": "LiteralType", "fallback": "builtins.int", "value": 1}, {".class": "LiteralType", "fallback": "builtins.int", "value": 2}, {".class": "LiteralType", "fallback": "builtins.int", "value": 3}, {".class": "LiteralType", "fallback": "builtins.int", "value": 4}, {".class": "LiteralType", "fallback": "builtins.int", "value": 5}, {".class": "LiteralType", "fallback": "builtins.int", "value": 6}, {".class": "LiteralType", "fallback": "builtins.int", "value": 7}, {".class": "LiteralType", "fallback": "builtins.int", "value": 8}, {".class": "LiteralType", "fallback": "builtins.int", "value": 9}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "open", "ret_type": "tarfile.TarFile", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "tarfile.open", "name": "open", "type": {".class": "CallableType", "arg_kinds": [1, 3, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["name", "mode", "<PERSON><PERSON><PERSON>", "bufsize", "format", "tarinfo", "dereference", "ignore_zeros", "encoding", "errors", "pax_headers", "debug", "errorlevel", "preset"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.StrOrBytesPath"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "x:xz"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "w:xz"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["tarfile._<PERSON><PERSON>j", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeType", "item": "tarfile.TarInfo"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.int", "value": 0}, {".class": "LiteralType", "fallback": "builtins.int", "value": 1}, {".class": "LiteralType", "fallback": "builtins.int", "value": 2}, {".class": "LiteralType", "fallback": "builtins.int", "value": 3}, {".class": "LiteralType", "fallback": "builtins.int", "value": 4}, {".class": "LiteralType", "fallback": "builtins.int", "value": 5}, {".class": "LiteralType", "fallback": "builtins.int", "value": 6}, {".class": "LiteralType", "fallback": "builtins.int", "value": 7}, {".class": "LiteralType", "fallback": "builtins.int", "value": 8}, {".class": "LiteralType", "fallback": "builtins.int", "value": 9}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "open", "ret_type": "tarfile.TarFile", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1, 3, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["name", "mode", "<PERSON><PERSON><PERSON>", "bufsize", "format", "tarinfo", "dereference", "ignore_zeros", "encoding", "errors", "pax_headers", "debug", "errorlevel", "preset"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "tarfile.open", "name": "open", "type": {".class": "CallableType", "arg_kinds": [1, 3, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["name", "mode", "<PERSON><PERSON><PERSON>", "bufsize", "format", "tarinfo", "dereference", "ignore_zeros", "encoding", "errors", "pax_headers", "debug", "errorlevel", "preset"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.StrOrBytesPath"}, "typing_extensions.Buffer", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "r|*"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "r|"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "r|gz"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "r|bz2"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "r|xz"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "typing.IO"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeType", "item": "tarfile.TarInfo"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "open", "ret_type": "tarfile.TarFile", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "tarfile.open", "name": "open", "type": {".class": "CallableType", "arg_kinds": [1, 3, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["name", "mode", "<PERSON><PERSON><PERSON>", "bufsize", "format", "tarinfo", "dereference", "ignore_zeros", "encoding", "errors", "pax_headers", "debug", "errorlevel", "preset"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.StrOrBytesPath"}, "typing_extensions.Buffer", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "r|*"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "r|"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "r|gz"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "r|bz2"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "r|xz"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "typing.IO"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeType", "item": "tarfile.TarInfo"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "open", "ret_type": "tarfile.TarFile", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1, 3, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["name", "mode", "<PERSON><PERSON><PERSON>", "bufsize", "format", "tarinfo", "dereference", "ignore_zeros", "encoding", "errors", "pax_headers", "debug", "errorlevel", "preset"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "tarfile.open", "name": "open", "type": {".class": "CallableType", "arg_kinds": [1, 3, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["name", "mode", "<PERSON><PERSON><PERSON>", "bufsize", "format", "tarinfo", "dereference", "ignore_zeros", "encoding", "errors", "pax_headers", "debug", "errorlevel", "preset"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.StrOrBytesPath"}, "typing_extensions.Buffer", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "w|"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "w|gz"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "w|bz2"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "w|xz"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "typing.IO"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeType", "item": "tarfile.TarInfo"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "open", "ret_type": "tarfile.TarFile", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "tarfile.open", "name": "open", "type": {".class": "CallableType", "arg_kinds": [1, 3, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["name", "mode", "<PERSON><PERSON><PERSON>", "bufsize", "format", "tarinfo", "dereference", "ignore_zeros", "encoding", "errors", "pax_headers", "debug", "errorlevel", "preset"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.StrOrBytesPath"}, "typing_extensions.Buffer", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "w|"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "w|gz"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "w|bz2"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "w|xz"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "typing.IO"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeType", "item": "tarfile.TarInfo"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "open", "ret_type": "tarfile.TarFile", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [1, 1, 1, 1, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["name", "mode", "<PERSON><PERSON><PERSON>", "bufsize", "format", "tarinfo", "dereference", "ignore_zeros", "encoding", "errors", "pax_headers", "debug", "errorlevel"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.StrOrBytesPath"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "r"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "r:*"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "r:"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "r:gz"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "r:bz2"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "r:xz"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "typing.IO"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeType", "item": "tarfile.TarInfo"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "open", "ret_type": "tarfile.TarFile", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["name", "mode", "<PERSON><PERSON><PERSON>", "bufsize", "format", "tarinfo", "dereference", "ignore_zeros", "encoding", "errors", "pax_headers", "debug", "errorlevel"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.StrOrBytesPath"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "x"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "x:"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "a"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "a:"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "w"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "w:"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "w:tar"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["tarfile._<PERSON><PERSON>j", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeType", "item": "tarfile.TarInfo"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "open", "ret_type": "tarfile.TarFile", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [1, 3, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["name", "mode", "<PERSON><PERSON><PERSON>", "bufsize", "format", "tarinfo", "dereference", "ignore_zeros", "encoding", "errors", "pax_headers", "debug", "errorlevel"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.StrOrBytesPath"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "x"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "x:"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "a"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "a:"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "w"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "w:"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "w:tar"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["tarfile._<PERSON><PERSON>j", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeType", "item": "tarfile.TarInfo"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "open", "ret_type": "tarfile.TarFile", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["name", "mode", "<PERSON><PERSON><PERSON>", "bufsize", "format", "tarinfo", "dereference", "ignore_zeros", "encoding", "errors", "pax_headers", "debug", "errorlevel", "compresslevel"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.StrOrBytesPath"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "x:gz"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "x:bz2"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "w:gz"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "w:bz2"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["tarfile._<PERSON><PERSON>j", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeType", "item": "tarfile.TarInfo"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "open", "ret_type": "tarfile.TarFile", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [1, 3, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["name", "mode", "<PERSON><PERSON><PERSON>", "bufsize", "format", "tarinfo", "dereference", "ignore_zeros", "encoding", "errors", "pax_headers", "debug", "errorlevel", "compresslevel"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.StrOrBytesPath"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "x:gz"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "x:bz2"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "w:gz"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "w:bz2"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["tarfile._<PERSON><PERSON>j", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeType", "item": "tarfile.TarInfo"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "open", "ret_type": "tarfile.TarFile", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["name", "mode", "<PERSON><PERSON><PERSON>", "bufsize", "format", "tarinfo", "dereference", "ignore_zeros", "encoding", "errors", "pax_headers", "debug", "errorlevel", "preset"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.StrOrBytesPath"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "x:xz"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "w:xz"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["tarfile._<PERSON><PERSON>j", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeType", "item": "tarfile.TarInfo"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.int", "value": 0}, {".class": "LiteralType", "fallback": "builtins.int", "value": 1}, {".class": "LiteralType", "fallback": "builtins.int", "value": 2}, {".class": "LiteralType", "fallback": "builtins.int", "value": 3}, {".class": "LiteralType", "fallback": "builtins.int", "value": 4}, {".class": "LiteralType", "fallback": "builtins.int", "value": 5}, {".class": "LiteralType", "fallback": "builtins.int", "value": 6}, {".class": "LiteralType", "fallback": "builtins.int", "value": 7}, {".class": "LiteralType", "fallback": "builtins.int", "value": 8}, {".class": "LiteralType", "fallback": "builtins.int", "value": 9}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "open", "ret_type": "tarfile.TarFile", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [1, 3, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["name", "mode", "<PERSON><PERSON><PERSON>", "bufsize", "format", "tarinfo", "dereference", "ignore_zeros", "encoding", "errors", "pax_headers", "debug", "errorlevel", "preset"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.StrOrBytesPath"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "x:xz"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "w:xz"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["tarfile._<PERSON><PERSON>j", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeType", "item": "tarfile.TarInfo"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.int", "value": 0}, {".class": "LiteralType", "fallback": "builtins.int", "value": 1}, {".class": "LiteralType", "fallback": "builtins.int", "value": 2}, {".class": "LiteralType", "fallback": "builtins.int", "value": 3}, {".class": "LiteralType", "fallback": "builtins.int", "value": 4}, {".class": "LiteralType", "fallback": "builtins.int", "value": 5}, {".class": "LiteralType", "fallback": "builtins.int", "value": 6}, {".class": "LiteralType", "fallback": "builtins.int", "value": 7}, {".class": "LiteralType", "fallback": "builtins.int", "value": 8}, {".class": "LiteralType", "fallback": "builtins.int", "value": 9}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "open", "ret_type": "tarfile.TarFile", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [1, 3, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["name", "mode", "<PERSON><PERSON><PERSON>", "bufsize", "format", "tarinfo", "dereference", "ignore_zeros", "encoding", "errors", "pax_headers", "debug", "errorlevel", "preset"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.StrOrBytesPath"}, "typing_extensions.Buffer", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "r|*"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "r|"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "r|gz"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "r|bz2"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "r|xz"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "typing.IO"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeType", "item": "tarfile.TarInfo"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "open", "ret_type": "tarfile.TarFile", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [1, 3, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["name", "mode", "<PERSON><PERSON><PERSON>", "bufsize", "format", "tarinfo", "dereference", "ignore_zeros", "encoding", "errors", "pax_headers", "debug", "errorlevel", "preset"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.StrOrBytesPath"}, "typing_extensions.Buffer", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "w|"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "w|gz"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "w|bz2"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "w|xz"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "typing.IO"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeType", "item": "tarfile.TarInfo"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "open", "ret_type": "tarfile.TarFile", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "overload": {".class": "SymbolTableNode", "cross_ref": "typing.overload", "kind": "Gdef", "module_hidden": true, "module_public": false}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef", "module_hidden": true, "module_public": false}, "tar_filter": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["member", "dest_path"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tarfile.tar_filter", "name": "tar_filter", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["member", "dest_path"], "arg_types": ["tarfile.TarInfo", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "tar_filter", "ret_type": "tarfile.TarInfo", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "path": "c:\\Users\\<USER>\\.vscode\\extensions\\ms-python.mypy-type-checker-2025.2.0\\bundled\\libs\\mypy\\typeshed\\stdlib\\tarfile.pyi"}