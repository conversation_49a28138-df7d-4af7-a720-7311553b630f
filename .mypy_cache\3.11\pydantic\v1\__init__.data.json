{".class": "MypyFile", "_fullname": "pydantic.v1", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AmqpDsn": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.networks.AmqpDsn", "kind": "Gdef"}, "AnyHttpUrl": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.networks.AnyHttpUrl", "kind": "Gdef"}, "AnyStrMaxLengthError": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.errors.AnyStrMaxLengthError", "kind": "Gdef", "module_public": false}, "AnyStrMinLengthError": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.errors.AnyStrMinLengthError", "kind": "Gdef", "module_public": false}, "AnyUrl": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.networks.AnyUrl", "kind": "Gdef"}, "ArbitraryTypeError": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.errors.ArbitraryTypeError", "kind": "Gdef", "module_public": false}, "BaseConfig": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.config.BaseConfig", "kind": "Gdef"}, "BaseModel": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.main.BaseModel", "kind": "Gdef"}, "BaseSettings": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.env_settings.BaseSettings", "kind": "Gdef"}, "BoolError": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.errors.BoolError", "kind": "Gdef", "module_public": false}, "ByteSize": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.types.ByteSize", "kind": "Gdef"}, "BytesError": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.errors.BytesError", "kind": "Gdef", "module_public": false}, "CallableError": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.errors.CallableError", "kind": "Gdef", "module_public": false}, "ClassError": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.errors.ClassError", "kind": "Gdef", "module_public": false}, "CockroachDsn": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.networks.CockroachDsn", "kind": "Gdef"}, "ColorError": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.errors.ColorError", "kind": "Gdef", "module_public": false}, "ConfigDict": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.config.ConfigDict", "kind": "Gdef"}, "ConfigError": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.errors.ConfigError", "kind": "Gdef", "module_public": false}, "ConstrainedBytes": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.types.ConstrainedBytes", "kind": "Gdef"}, "ConstrainedDate": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.types.ConstrainedDate", "kind": "Gdef"}, "ConstrainedDecimal": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.types.ConstrainedDecimal", "kind": "Gdef"}, "ConstrainedFloat": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.types.ConstrainedFloat", "kind": "Gdef"}, "ConstrainedFrozenSet": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.types.ConstrainedFrozenSet", "kind": "Gdef"}, "ConstrainedInt": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.types.ConstrainedInt", "kind": "Gdef"}, "ConstrainedList": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.types.ConstrainedList", "kind": "Gdef"}, "ConstrainedSet": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.types.ConstrainedSet", "kind": "Gdef"}, "ConstrainedStr": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.types.ConstrainedStr", "kind": "Gdef"}, "DataclassTypeError": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.errors.DataclassTypeError", "kind": "Gdef", "module_public": false}, "DateError": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.errors.DateError", "kind": "Gdef", "module_public": false}, "DateNotInTheFutureError": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.errors.DateNotInTheFutureError", "kind": "Gdef", "module_public": false}, "DateNotInThePastError": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.errors.DateNotInThePastError", "kind": "Gdef", "module_public": false}, "DateTimeError": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.errors.DateTimeError", "kind": "Gdef", "module_public": false}, "DecimalError": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.errors.DecimalError", "kind": "Gdef", "module_public": false}, "DecimalIsNotFiniteError": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.errors.DecimalIsNotFiniteError", "kind": "Gdef", "module_public": false}, "DecimalMaxDigitsError": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.errors.DecimalMaxDigitsError", "kind": "Gdef", "module_public": false}, "DecimalMaxPlacesError": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.errors.DecimalMaxPlacesError", "kind": "Gdef", "module_public": false}, "DecimalWholeDigitsError": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.errors.DecimalWholeDigitsError", "kind": "Gdef", "module_public": false}, "DictError": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.errors.DictError", "kind": "Gdef", "module_public": false}, "DirectoryPath": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.types.DirectoryPath", "kind": "Gdef"}, "DurationError": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.errors.DurationError", "kind": "Gdef", "module_public": false}, "EmailError": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.errors.EmailError", "kind": "Gdef", "module_public": false}, "EmailStr": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.networks.EmailStr", "kind": "Gdef"}, "EnumError": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.errors.EnumError", "kind": "Gdef", "module_public": false}, "EnumMemberError": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.errors.EnumMemberError", "kind": "Gdef", "module_public": false}, "Extra": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.config.Extra", "kind": "Gdef"}, "ExtraError": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.errors.ExtraError", "kind": "Gdef", "module_public": false}, "Field": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.fields.Field", "kind": "Gdef"}, "FilePath": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.types.FilePath", "kind": "Gdef"}, "FileUrl": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.networks.FileUrl", "kind": "Gdef"}, "FiniteFloat": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.types.FiniteFloat", "kind": "Gdef"}, "FloatError": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.errors.FloatError", "kind": "Gdef", "module_public": false}, "FrozenSetError": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.errors.FrozenSetError", "kind": "Gdef", "module_public": false}, "FrozenSetMaxLengthError": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.errors.FrozenSetMaxLengthError", "kind": "Gdef", "module_public": false}, "FrozenSetMinLengthError": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.errors.FrozenSetMinLengthError", "kind": "Gdef", "module_public": false}, "FutureDate": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.types.FutureDate", "kind": "Gdef"}, "HashableError": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.errors.HashableError", "kind": "Gdef", "module_public": false}, "HttpUrl": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.networks.HttpUrl", "kind": "Gdef"}, "IPv4AddressError": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.errors.IPv4AddressError", "kind": "Gdef", "module_public": false}, "IPv4InterfaceError": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.errors.IPv4InterfaceError", "kind": "Gdef", "module_public": false}, "IPv4NetworkError": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.errors.IPv4NetworkError", "kind": "Gdef", "module_public": false}, "IPv6AddressError": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.errors.IPv6AddressError", "kind": "Gdef", "module_public": false}, "IPv6InterfaceError": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.errors.IPv6InterfaceError", "kind": "Gdef", "module_public": false}, "IPv6NetworkError": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.errors.IPv6NetworkError", "kind": "Gdef", "module_public": false}, "IPvAnyAddress": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.networks.IPvAnyAddress", "kind": "Gdef"}, "IPvAnyAddressError": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.errors.IPvAnyAddressError", "kind": "Gdef", "module_public": false}, "IPvAnyInterface": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.networks.IPvAnyInterface", "kind": "Gdef"}, "IPvAnyInterfaceError": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.errors.IPvAnyInterfaceError", "kind": "Gdef", "module_public": false}, "IPvAnyNetwork": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.networks.IPvAnyNetwork", "kind": "Gdef"}, "IPvAnyNetworkError": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.errors.IPvAnyNetworkError", "kind": "Gdef", "module_public": false}, "IntEnumError": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.errors.IntEnumError", "kind": "Gdef", "module_public": false}, "IntegerError": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.errors.IntegerError", "kind": "Gdef", "module_public": false}, "InvalidByteSize": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.errors.InvalidByteSize", "kind": "Gdef", "module_public": false}, "InvalidByteSizeUnit": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.errors.InvalidByteSizeUnit", "kind": "Gdef", "module_public": false}, "InvalidDiscriminator": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.errors.InvalidDiscriminator", "kind": "Gdef", "module_public": false}, "InvalidLengthForBrand": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.errors.InvalidLengthForBrand", "kind": "Gdef", "module_public": false}, "Json": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.types.Json", "kind": "Gdef"}, "JsonError": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.errors.JsonError", "kind": "Gdef", "module_public": false}, "JsonTypeError": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.errors.JsonTypeError", "kind": "Gdef", "module_public": false}, "JsonWrapper": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.types.JsonWrapper", "kind": "Gdef"}, "KafkaDsn": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.networks.KafkaDsn", "kind": "Gdef"}, "ListError": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.errors.ListError", "kind": "Gdef", "module_public": false}, "ListMaxLengthError": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.errors.ListMaxLengthError", "kind": "Gdef", "module_public": false}, "ListMinLengthError": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.errors.ListMinLengthError", "kind": "Gdef", "module_public": false}, "ListUniqueItemsError": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.errors.ListUniqueItemsError", "kind": "Gdef", "module_public": false}, "LuhnValidationError": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.errors.LuhnValidationError", "kind": "Gdef", "module_public": false}, "MissingDiscriminator": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.errors.MissingDiscriminator", "kind": "Gdef", "module_public": false}, "MissingError": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.errors.MissingError", "kind": "Gdef", "module_public": false}, "MongoDsn": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.networks.MongoDsn", "kind": "Gdef"}, "NameEmail": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.networks.NameEmail", "kind": "Gdef"}, "NegativeFloat": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.types.NegativeFloat", "kind": "Gdef"}, "NegativeInt": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.types.NegativeInt", "kind": "Gdef"}, "NonNegativeFloat": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.types.NonNegativeFloat", "kind": "Gdef"}, "NonNegativeInt": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.types.NonNegativeInt", "kind": "Gdef"}, "NonPositiveFloat": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.types.NonPositiveFloat", "kind": "Gdef"}, "NonPositiveInt": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.types.NonPositiveInt", "kind": "Gdef"}, "NoneBytes": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.types.NoneBytes", "kind": "Gdef"}, "NoneIsAllowedError": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.errors.NoneIsAllowedError", "kind": "Gdef", "module_public": false}, "NoneIsNotAllowedError": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.errors.NoneIsNotAllowedError", "kind": "Gdef", "module_public": false}, "NoneStr": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.types.NoneStr", "kind": "Gdef"}, "NoneStrBytes": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.types.NoneStrBytes", "kind": "Gdef"}, "NotDigitError": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.errors.NotDigitError", "kind": "Gdef", "module_public": false}, "NotNoneError": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.errors.NotNoneError", "kind": "Gdef", "module_public": false}, "NumberNotGeError": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.errors.NumberNotGeError", "kind": "Gdef", "module_public": false}, "NumberNotGtError": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.errors.NumberNotGtError", "kind": "Gdef", "module_public": false}, "NumberNotLeError": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.errors.NumberNotLeError", "kind": "Gdef", "module_public": false}, "NumberNotLtError": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.errors.NumberNotLtError", "kind": "Gdef", "module_public": false}, "NumberNotMultipleError": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.errors.NumberNotMultipleError", "kind": "Gdef", "module_public": false}, "PastDate": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.types.PastDate", "kind": "Gdef"}, "PathError": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.errors.PathError", "kind": "Gdef", "module_public": false}, "PathNotADirectoryError": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.errors.PathNotADirectoryError", "kind": "Gdef", "module_public": false}, "PathNotAFileError": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.errors.PathNotAFileError", "kind": "Gdef", "module_public": false}, "PathNotExistsError": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.errors.PathNotExistsError", "kind": "Gdef", "module_public": false}, "PatternError": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.errors.PatternError", "kind": "Gdef", "module_public": false}, "PaymentCardNumber": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.types.PaymentCardNumber", "kind": "Gdef"}, "PositiveFloat": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.types.PositiveFloat", "kind": "Gdef"}, "PositiveInt": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.types.PositiveInt", "kind": "Gdef"}, "PostgresDsn": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.networks.PostgresDsn", "kind": "Gdef"}, "PrivateAttr": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.fields.PrivateAttr", "kind": "Gdef"}, "Protocol": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.parse.Protocol", "kind": "Gdef"}, "PyObject": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.types.PyObject", "kind": "Gdef"}, "PyObjectError": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.errors.PyObjectError", "kind": "Gdef", "module_public": false}, "PydanticTypeError": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.errors.PydanticTypeError", "kind": "Gdef", "module_public": false}, "PydanticValueError": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.errors.PydanticValueError", "kind": "Gdef", "module_public": false}, "RedisDsn": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.networks.RedisDsn", "kind": "Gdef"}, "Required": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.fields.Required", "kind": "Gdef"}, "SecretBytes": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.types.SecretBytes", "kind": "Gdef"}, "SecretField": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.types.SecretField", "kind": "Gdef"}, "SecretStr": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.types.SecretStr", "kind": "Gdef"}, "SequenceError": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.errors.SequenceError", "kind": "Gdef", "module_public": false}, "SetError": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.errors.SetError", "kind": "Gdef", "module_public": false}, "SetMaxLengthError": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.errors.SetMaxLengthError", "kind": "Gdef", "module_public": false}, "SetMinLengthError": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.errors.SetMinLengthError", "kind": "Gdef", "module_public": false}, "StrBytes": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.types.StrBytes", "kind": "Gdef"}, "StrError": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.errors.StrError", "kind": "Gdef", "module_public": false}, "StrRegexError": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.errors.StrRegexError", "kind": "Gdef", "module_public": false}, "StrictBool": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.types.StrictBool", "kind": "Gdef"}, "StrictBoolError": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.errors.StrictBoolError", "kind": "Gdef", "module_public": false}, "StrictBytes": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.types.StrictBytes", "kind": "Gdef"}, "StrictFloat": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.types.StrictFloat", "kind": "Gdef"}, "StrictInt": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.types.StrictInt", "kind": "Gdef"}, "StrictStr": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.types.StrictStr", "kind": "Gdef"}, "SubclassError": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.errors.SubclassError", "kind": "Gdef", "module_public": false}, "TimeError": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.errors.TimeError", "kind": "Gdef", "module_public": false}, "TupleError": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.errors.<PERSON>ple<PERSON>rror", "kind": "Gdef", "module_public": false}, "TupleLengthError": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.errors.TupleLengthError", "kind": "Gdef", "module_public": false}, "UUID1": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.types.UUID1", "kind": "Gdef"}, "UUID3": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.types.UUID3", "kind": "Gdef"}, "UUID4": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.types.UUID4", "kind": "Gdef"}, "UUID5": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.types.UUID5", "kind": "Gdef"}, "UUIDError": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.errors.UUIDError", "kind": "Gdef", "module_public": false}, "UUIDVersionError": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.errors.UUIDVersionError", "kind": "Gdef", "module_public": false}, "UrlError": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.errors.UrlError", "kind": "Gdef", "module_public": false}, "UrlExtraError": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.errors.UrlExtraError", "kind": "Gdef", "module_public": false}, "UrlHostError": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.errors.UrlHostError", "kind": "Gdef", "module_public": false}, "UrlHostTldError": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.errors.UrlHostTldError", "kind": "Gdef", "module_public": false}, "UrlPortError": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.errors.UrlPortError", "kind": "Gdef", "module_public": false}, "UrlSchemeError": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.errors.UrlSchemeError", "kind": "Gdef", "module_public": false}, "UrlSchemePermittedError": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.errors.UrlSchemePermittedError", "kind": "Gdef", "module_public": false}, "UrlUserInfoError": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.errors.UrlUserInfoError", "kind": "Gdef", "module_public": false}, "VERSION": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.version.VERSION", "kind": "Gdef"}, "ValidationError": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.error_wrappers.ValidationError", "kind": "Gdef"}, "WrongConstantError": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.errors.WrongConstantError", "kind": "Gdef", "module_public": false}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pydantic.v1.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic.v1.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic.v1.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic.v1.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic.v1.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic.v1.__package__", "name": "__package__", "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic.v1.__path__", "name": "__path__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic.v1.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "__version__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pydantic.v1.__version__", "name": "__version__", "type": "builtins.str"}}, "compiled": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.version.compiled", "kind": "Gdef"}, "conbytes": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.types.conbytes", "kind": "Gdef"}, "condate": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.types.condate", "kind": "Gdef"}, "condecimal": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.types.condecimal", "kind": "Gdef"}, "confloat": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.types.confloat", "kind": "Gdef"}, "confrozenset": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.types.confrozenset", "kind": "Gdef"}, "conint": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.types.conint", "kind": "Gdef"}, "conlist": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.types.conlist", "kind": "Gdef"}, "conset": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.types.conset", "kind": "Gdef"}, "constr": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.types.constr", "kind": "Gdef"}, "create_model": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.main.create_model", "kind": "Gdef"}, "create_model_from_namedtuple": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.annotated_types.create_model_from_namedtuple", "kind": "Gdef"}, "create_model_from_typeddict": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.annotated_types.create_model_from_typeddict", "kind": "Gdef"}, "dataclasses": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.dataclasses", "kind": "Gdef"}, "parse_file_as": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.tools.parse_file_as", "kind": "Gdef"}, "parse_obj_as": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.tools.parse_obj_as", "kind": "Gdef"}, "parse_raw_as": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.tools.parse_raw_as", "kind": "Gdef"}, "root_validator": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.class_validators.root_validator", "kind": "Gdef"}, "schema_json_of": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.tools.schema_json_of", "kind": "Gdef"}, "schema_of": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.tools.schema_of", "kind": "Gdef"}, "stricturl": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.networks.stricturl", "kind": "Gdef"}, "validate_arguments": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.decorator.validate_arguments", "kind": "Gdef"}, "validate_email": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.networks.validate_email", "kind": "Gdef"}, "validate_model": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.main.validate_model", "kind": "Gdef"}, "validator": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.class_validators.validator", "kind": "Gdef"}}, "path": "c:\\Python313\\Lib\\site-packages\\pydantic\\v1\\__init__.py"}