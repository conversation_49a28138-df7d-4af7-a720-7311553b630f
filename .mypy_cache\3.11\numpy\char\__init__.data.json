{".class": "MypyFile", "_fullname": "numpy.char", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "numpy.char.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.char.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.char.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.char.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.char.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.char.__package__", "name": "__package__", "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.char.__path__", "name": "__path__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.char.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "add": {".class": "SymbolTableNode", "cross_ref": "numpy._core.defchararray.add", "kind": "Gdef"}, "array": {".class": "SymbolTableNode", "cross_ref": "numpy._core.defchararray.array", "kind": "Gdef"}, "asarray": {".class": "SymbolTableNode", "cross_ref": "numpy._core.defchararray.asarray", "kind": "Gdef"}, "capitalize": {".class": "SymbolTableNode", "cross_ref": "numpy._core.defchararray.capitalize", "kind": "Gdef"}, "center": {".class": "SymbolTableNode", "cross_ref": "numpy._core.defchararray.center", "kind": "Gdef"}, "chararray": {".class": "SymbolTableNode", "cross_ref": "numpy._core.defchararray.chararray", "kind": "Gdef"}, "compare_chararrays": {".class": "SymbolTableNode", "cross_ref": "numpy._core.multiarray.compare_chararrays", "kind": "Gdef"}, "count": {".class": "SymbolTableNode", "cross_ref": "numpy._core.defchararray.count", "kind": "Gdef"}, "decode": {".class": "SymbolTableNode", "cross_ref": "numpy._core.defchararray.decode", "kind": "Gdef"}, "encode": {".class": "SymbolTableNode", "cross_ref": "numpy._core.defchararray.encode", "kind": "Gdef"}, "endswith": {".class": "SymbolTableNode", "cross_ref": "numpy._core.defchararray.endswith", "kind": "Gdef"}, "equal": {".class": "SymbolTableNode", "cross_ref": "numpy._core.defchararray.equal", "kind": "Gdef"}, "expandtabs": {".class": "SymbolTableNode", "cross_ref": "numpy._core.defchararray.expandtabs", "kind": "Gdef"}, "find": {".class": "SymbolTableNode", "cross_ref": "numpy._core.defchararray.find", "kind": "Gdef"}, "greater": {".class": "SymbolTableNode", "cross_ref": "numpy._core.defchararray.greater", "kind": "Gdef"}, "greater_equal": {".class": "SymbolTableNode", "cross_ref": "numpy._core.defchararray.greater_equal", "kind": "Gdef"}, "index": {".class": "SymbolTableNode", "cross_ref": "numpy._core.defchararray.index", "kind": "Gdef"}, "isalnum": {".class": "SymbolTableNode", "cross_ref": "numpy._core.defchararray.isalnum", "kind": "Gdef"}, "isalpha": {".class": "SymbolTableNode", "cross_ref": "numpy._core.defchararray.isalpha", "kind": "Gdef"}, "isdecimal": {".class": "SymbolTableNode", "cross_ref": "numpy._core.defchararray.isdecimal", "kind": "Gdef"}, "isdigit": {".class": "SymbolTableNode", "cross_ref": "numpy._core.defchararray.isdigit", "kind": "Gdef"}, "islower": {".class": "SymbolTableNode", "cross_ref": "numpy._core.defchararray.islower", "kind": "Gdef"}, "isnumeric": {".class": "SymbolTableNode", "cross_ref": "numpy._core.defchararray.isnumeric", "kind": "Gdef"}, "isspace": {".class": "SymbolTableNode", "cross_ref": "numpy._core.defchararray.isspace", "kind": "Gdef"}, "istitle": {".class": "SymbolTableNode", "cross_ref": "numpy._core.defchararray.istitle", "kind": "Gdef"}, "isupper": {".class": "SymbolTableNode", "cross_ref": "numpy._core.defchararray.isupper", "kind": "Gdef"}, "join": {".class": "SymbolTableNode", "cross_ref": "numpy._core.defchararray.join", "kind": "Gdef"}, "less": {".class": "SymbolTableNode", "cross_ref": "numpy._core.defchararray.less", "kind": "Gdef"}, "less_equal": {".class": "SymbolTableNode", "cross_ref": "numpy._core.defchararray.less_equal", "kind": "Gdef"}, "ljust": {".class": "SymbolTableNode", "cross_ref": "numpy._core.defchararray.ljust", "kind": "Gdef"}, "lower": {".class": "SymbolTableNode", "cross_ref": "numpy._core.defchararray.lower", "kind": "Gdef"}, "lstrip": {".class": "SymbolTableNode", "cross_ref": "numpy._core.defchararray.lstrip", "kind": "Gdef"}, "mod": {".class": "SymbolTableNode", "cross_ref": "numpy._core.defchararray.mod", "kind": "Gdef"}, "multiply": {".class": "SymbolTableNode", "cross_ref": "numpy._core.defchararray.multiply", "kind": "Gdef"}, "not_equal": {".class": "SymbolTableNode", "cross_ref": "numpy._core.defchararray.not_equal", "kind": "Gdef"}, "partition": {".class": "SymbolTableNode", "cross_ref": "numpy._core.defchararray.partition", "kind": "Gdef"}, "replace": {".class": "SymbolTableNode", "cross_ref": "numpy._core.defchararray.replace", "kind": "Gdef"}, "rfind": {".class": "SymbolTableNode", "cross_ref": "numpy._core.defchararray.rfind", "kind": "Gdef"}, "rindex": {".class": "SymbolTableNode", "cross_ref": "numpy._core.defchararray.rindex", "kind": "Gdef"}, "rjust": {".class": "SymbolTableNode", "cross_ref": "numpy._core.defchararray.rjust", "kind": "Gdef"}, "rpartition": {".class": "SymbolTableNode", "cross_ref": "numpy._core.defchararray.rpartition", "kind": "Gdef"}, "rsplit": {".class": "SymbolTableNode", "cross_ref": "numpy._core.defchararray.rsplit", "kind": "Gdef"}, "rstrip": {".class": "SymbolTableNode", "cross_ref": "numpy._core.defchararray.rstrip", "kind": "Gdef"}, "split": {".class": "SymbolTableNode", "cross_ref": "numpy._core.defchararray.split", "kind": "Gdef"}, "splitlines": {".class": "SymbolTableNode", "cross_ref": "numpy._core.defchararray.splitlines", "kind": "Gdef"}, "startswith": {".class": "SymbolTableNode", "cross_ref": "numpy._core.defchararray.startswith", "kind": "Gdef"}, "str_len": {".class": "SymbolTableNode", "cross_ref": "numpy._core.defchararray.str_len", "kind": "Gdef"}, "strip": {".class": "SymbolTableNode", "cross_ref": "numpy._core.defchararray.strip", "kind": "Gdef"}, "swapcase": {".class": "SymbolTableNode", "cross_ref": "numpy._core.defchararray.swapcase", "kind": "Gdef"}, "title": {".class": "SymbolTableNode", "cross_ref": "numpy._core.defchararray.title", "kind": "Gdef"}, "translate": {".class": "SymbolTableNode", "cross_ref": "numpy._core.defchararray.translate", "kind": "Gdef"}, "upper": {".class": "SymbolTableNode", "cross_ref": "numpy._core.defchararray.upper", "kind": "Gdef"}, "zfill": {".class": "SymbolTableNode", "cross_ref": "numpy._core.defchararray.zfill", "kind": "Gdef"}}, "path": "c:\\Python313\\Lib\\site-packages\\numpy\\char\\__init__.pyi"}