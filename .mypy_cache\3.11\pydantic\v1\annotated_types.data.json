{".class": "MypyFile", "_fullname": "pydantic.v1.annotated_types", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "BaseModel": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.main.BaseModel", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "FrozenSet": {".class": "SymbolTableNode", "cross_ref": "typing.FrozenSet", "kind": "Gdef"}, "NamedTuple": {".class": "SymbolTableNode", "cross_ref": "typing.NamedTuple", "kind": "Gdef"}, "Required": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.fields.Required", "kind": "Gdef"}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "Type": {".class": "SymbolTableNode", "cross_ref": "typing.Type", "kind": "Gdef"}, "TypedDict": {".class": "SymbolTableNode", "cross_ref": "typing.TypedDict", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic.v1.annotated_types.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic.v1.annotated_types.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic.v1.annotated_types.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic.v1.annotated_types.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic.v1.annotated_types.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic.v1.annotated_types.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "create_model": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.main.create_model", "kind": "Gdef"}, "create_model_from_namedtuple": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["namedtuple_cls", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.v1.annotated_types.create_model_from_namedtuple", "name": "create_model_from_namedtuple", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["namedtuple_cls", "kwargs"], "arg_types": [{".class": "TypeType", "item": "typing.NamedTuple"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_model_from_namedtuple", "ret_type": {".class": "TypeType", "item": "pydantic.v1.main.BaseModel"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_model_from_typeddict": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["typeddict_cls", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.v1.annotated_types.create_model_from_typeddict", "name": "create_model_from_typeddict", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["typeddict_cls", "kwargs"], "arg_types": [{".class": "TypeType", "item": {".class": "UnboundType", "args": [], "expr": "TypedDict", "expr_fallback": "builtins.str", "name": "TypedDict"}}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_model_from_typeddict", "ret_type": {".class": "TypeType", "item": "pydantic.v1.main.BaseModel"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_legacy_typeddict": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["_"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.v1.annotated_types.is_legacy_typeddict", "name": "is_legacy_typeddict", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["_"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_legacy_typeddict", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_typeddict": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.typing.is_typeddict", "kind": "Gdef"}, "is_typeddict_special": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.typing.is_typeddict_special", "kind": "Gdef"}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef"}}, "path": "c:\\Python313\\Lib\\site-packages\\pydantic\\v1\\annotated_types.py"}