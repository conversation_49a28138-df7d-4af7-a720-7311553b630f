{"data_mtime": 1750100860, "dep_lines": [7, 9, 11, 12, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 30, 30, 30, 30], "dependencies": ["__future__", "typing", "pydantic_core", "typing_extensions", "builtins", "_frozen_importlib", "abc", "pydantic_core._pydantic_core", "pydantic_core.core_schema"], "hash": "74b20e5e48edeaf59260adaeaa3ff691e29cc791", "id": "pydantic.plugin", "ignore_all": true, "interface_hash": "acb52b8ab36f1b8b65c73f6d3df44b8446bdbbd4", "mtime": 1748795478, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Python313\\Lib\\site-packages\\pydantic\\plugin\\__init__.py", "plugin_data": null, "size": 6965, "suppressed": [], "version_id": "1.15.0"}