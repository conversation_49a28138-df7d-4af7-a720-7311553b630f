# 斗地主AI助手 - 核心依赖包
# 安装命令: pip install -r requirements.txt

# 核心框架
flet>=0.21.0
numpy>=1.24.0
opencv-python>=4.8.0
Pillow>=10.0.0

# OCR引擎 (可选，需要编译工具)
# paddleocr>=2.7.0
# easyocr>=1.7.0

# 机器学习 (可选，用于高级功能)
# torch>=2.0.0
# torchvision>=0.15.0
# scikit-learn>=1.3.0

# 系统交互
psutil>=5.9.0
pywin32>=306; sys_platform == "win32"

# 日志和配置
loguru>=0.7.0
pyyaml>=6.0

# 网络和API
requests>=2.31.0

# 数据验证
pydantic>=2.0.0

# 测试框架 (开发用)
pytest>=7.4.0

# 开发工具 (可选)
# black>=23.0.0
# flake8>=6.0.0

# 安装说明：
# 1. 创建虚拟环境: python -m venv venv
# 2. 激活虚拟环境: venv\Scripts\activate
# 3. 安装依赖: pip install -r requirements.txt

# 国内镜像源（如果下载慢）：
# pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/