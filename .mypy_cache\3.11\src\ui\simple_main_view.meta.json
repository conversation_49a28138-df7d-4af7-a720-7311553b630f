{"data_mtime": 1750101535, "dep_lines": [10, 9, 1, 1, 1, 1, 8], "dep_prios": [5, 5, 5, 30, 30, 30, 10], "dependencies": ["src.utils.logger", "typing", "builtins", "_frozen_importlib", "abc", "src.utils"], "hash": "57c6a26cdf78f3fafcde819bc495879f67aa7ff2", "id": "src.ui.simple_main_view", "ignore_all": true, "interface_hash": "37c191c275f6966f17fb475161185768571c6fb3", "mtime": 1750101196, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "E:\\db\\0617\\src\\ui\\simple_main_view.py", "plugin_data": null, "size": 10293, "suppressed": ["flet"], "version_id": "1.15.0"}