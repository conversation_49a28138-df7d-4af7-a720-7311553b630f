#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单主视图界面
临时的简化版本，用于修复编码问题期间的基本显示
"""

import flet as ft
from typing import Optional, Callable
from ..utils.logger import LoggerMixin


class SimpleMainView(ft.Column, LoggerMixin):
    """简单主视图界面"""
    
    def __init__(
        self,
        page: ft.Page,
        config_manager=None,
        window_service=None,
        strategy_service=None,
        on_settings_click: Optional[Callable] = None,
        on_window_select_click: Optional[Callable] = None
    ):
        super().__init__()
        self.page = page
        self.config = config_manager
        self.window_service = window_service
        self.strategy_service = strategy_service
        
        # 回调函数
        self.on_settings_click = on_settings_click
        self.on_window_select_click = on_window_select_click
        
        # 状态
        self.is_monitoring = False
        self.selected_window = None
        
        # 构建界面
        self.controls = [self.build()]
        
        self.logger.info("简单主视图初始化完成")
    
    def build(self) -> ft.Control:
        """构建界面"""
        return ft.Container(
            content=ft.Column([
                # 头部
                self._build_header(),
                ft.Divider(height=2),
                
                # 主要内容区域
                ft.Container(
                    content=ft.Column([
                        # 欢迎信息
                        ft.Container(
                            content=ft.Column([
                                ft.Icon(ft.Icons.CASINO, size=64, color=ft.Colors.BLUE),
                                ft.Text(
                                    "斗地主AI助手",
                                    size=32,
                                    weight=ft.FontWeight.BOLD,
                                    color=ft.Colors.BLUE
                                ),
                                ft.Text(
                                    "智能斗地主辅助工具",
                                    size=16,
                                    color=ft.Colors.GREY_600
                                ),
                            ], horizontal_alignment=ft.CrossAxisAlignment.CENTER),
                            alignment=ft.alignment.center,
                            padding=40
                        ),
                        
                        # 功能状态
                        self._build_status_panel(),
                        
                        # 控制按钮
                        self._build_control_buttons(),
                        
                    ], horizontal_alignment=ft.CrossAxisAlignment.CENTER),
                    expand=True
                ),
                
                # 底部状态栏
                self._build_footer(),
                
            ]),
            padding=20,
            expand=True
        )
    
    def _build_header(self) -> ft.Control:
        """构建头部"""
        return ft.Row([
            ft.Text(
                "斗地主AI助手",
                size=20,
                weight=ft.FontWeight.BOLD,
                color=ft.Colors.BLUE
            ),
            ft.Container(expand=True),
            ft.Container(
                content=ft.Row([
                    ft.Icon(ft.Icons.CIRCLE, color=ft.Colors.GREEN, size=12),
                    ft.Text("运行中", size=12, color=ft.Colors.GREEN)
                ]),
                bgcolor=ft.Colors.GREEN_50,
                padding=ft.padding.symmetric(horizontal=8, vertical=4),
                border_radius=12
            ),
            ft.IconButton(
                icon=ft.Icons.SETTINGS,
                tooltip="设置",
                on_click=lambda _: self._show_message("设置功能开发中...")
            )
        ])
    
    def _build_status_panel(self) -> ft.Control:
        """构建状态面板"""
        return ft.Container(
            content=ft.Column([
                ft.Text("系统状态", size=18, weight=ft.FontWeight.BOLD),
                ft.Divider(height=1),
                
                # 服务状态
                ft.Row([
                    ft.Icon(ft.Icons.WINDOW, color=ft.Colors.GREEN),
                    ft.Text("窗口服务", size=14),
                    ft.Container(expand=True),
                    ft.Text("正常", color=ft.Colors.GREEN, weight=ft.FontWeight.BOLD)
                ]),
                
                ft.Row([
                    ft.Icon(ft.Icons.PSYCHOLOGY, color=ft.Colors.GREEN),
                    ft.Text("策略服务", size=14),
                    ft.Container(expand=True),
                    ft.Text("正常", color=ft.Colors.GREEN, weight=ft.FontWeight.BOLD)
                ]),
                
                ft.Row([
                    ft.Icon(ft.Icons.VISIBILITY, color=ft.Colors.ORANGE),
                    ft.Text("视觉服务", size=14),
                    ft.Container(expand=True),
                    ft.Text("维护中", color=ft.Colors.ORANGE, weight=ft.FontWeight.BOLD)
                ]),
                
                ft.Row([
                    ft.Icon(ft.Icons.TEXT_FIELDS, color=ft.Colors.ORANGE),
                    ft.Text("OCR服务", size=14),
                    ft.Container(expand=True),
                    ft.Text("维护中", color=ft.Colors.ORANGE, weight=ft.FontWeight.BOLD)
                ]),
                
                ft.Divider(height=1),
                
                # 目标窗口状态
                ft.Row([
                    ft.Icon(ft.Icons.DESKTOP_WINDOWS, color=ft.Colors.BLUE),
                    ft.Text("目标窗口", size=14),
                    ft.Container(expand=True),
                    ft.Text("未选择", color=ft.Colors.GREY_600, weight=ft.FontWeight.BOLD)
                ]),
                
            ]),
            bgcolor=ft.Colors.BLUE_50,
            padding=20,
            border_radius=10,
            width=400
        )
    
    def _build_control_buttons(self) -> ft.Control:
        """构建控制按钮"""
        return ft.Container(
            content=ft.Column([
                ft.Row([
                    ft.ElevatedButton(
                        text="选择游戏窗口",
                        icon=ft.Icons.WINDOW,
                        on_click=self._select_window,
                        width=200,
                        height=50
                    ),
                    ft.ElevatedButton(
                        text="开始监控",
                        icon=ft.Icons.PLAY_ARROW,
                        on_click=self._toggle_monitoring,
                        bgcolor=ft.Colors.GREEN,
                        color=ft.Colors.WHITE,
                        width=200,
                        height=50
                    ),
                ], alignment=ft.MainAxisAlignment.CENTER),
                
                ft.Row([
                    ft.ElevatedButton(
                        text="手动分析",
                        icon=ft.Icons.ANALYTICS,
                        on_click=self._manual_analysis,
                        width=200,
                        height=50
                    ),
                    ft.ElevatedButton(
                        text="查看日志",
                        icon=ft.Icons.LIST_ALT,
                        on_click=self._show_logs,
                        width=200,
                        height=50
                    ),
                ], alignment=ft.MainAxisAlignment.CENTER),
                
            ]),
            padding=20
        )
    
    def _build_footer(self) -> ft.Control:
        """构建底部状态栏"""
        return ft.Container(
            content=ft.Row([
                ft.Text("版本: 1.0.0", size=12, color=ft.Colors.GREY_600),
                ft.Container(expand=True),
                ft.Text("就绪", size=12, color=ft.Colors.GREY_600),
            ]),
            bgcolor=ft.Colors.GREY_100,
            padding=10,
            border_radius=5
        )
    
    def _select_window(self, e):
        """选择窗口"""
        try:
            if self.window_service:
                windows = self.window_service.get_all_windows()
                self._show_message(f"发现 {len(windows)} 个窗口")
            else:
                self._show_message("窗口服务不可用")
        except Exception as ex:
            self._show_message(f"获取窗口列表失败: {ex}")
    
    def _toggle_monitoring(self, e):
        """切换监控状态"""
        if not self.is_monitoring:
            self.is_monitoring = True
            e.control.text = "停止监控"
            e.control.icon = ft.Icons.STOP
            e.control.bgcolor = ft.Colors.RED
            self._show_message("开始监控游戏...")
        else:
            self.is_monitoring = False
            e.control.text = "开始监控"
            e.control.icon = ft.Icons.PLAY_ARROW
            e.control.bgcolor = ft.Colors.GREEN
            self._show_message("停止监控")
        
        self.page.update()
    
    def _manual_analysis(self, e):
        """手动分析"""
        self._show_message("手动分析功能开发中...")
    
    def _show_logs(self, e):
        """显示日志"""
        self._show_message("日志查看功能开发中...")
    
    def _show_message(self, message: str):
        """显示消息"""
        if self.page:
            snack_bar = ft.SnackBar(
                content=ft.Text(message),
                duration=3000
            )
            self.page.snack_bar = snack_bar
            snack_bar.open = True
            self.page.update()
    
    def set_target_window(self, window_info):
        """设置目标窗口"""
        self.selected_window = window_info
        self.logger.info(f"设置目标窗口: {window_info}")
    
    def start_monitoring(self):
        """开始监控"""
        self.is_monitoring = True
        self.logger.info("开始监控")
    
    def stop_monitoring(self):
        """停止监控"""
        self.is_monitoring = False
        self.logger.info("停止监控")
