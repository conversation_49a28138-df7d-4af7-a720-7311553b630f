# -*- coding: utf-8 -*-
"""
策略分析服务

负责斗地主游戏策略分析、出牌建议、概率计算等功能
集成多种AI算法：规则引擎、深度学习、蒙特卡洛树搜索等
"""

import random
import math
from typing import List, Dict, Optional, Tuple, Set, Any
from dataclasses import dataclass, field
from enum import Enum
from collections import Counter, defaultdict
import numpy as np
from copy import deepcopy

from ..utils.logger import LoggerMixin

# AI相关导入
try:
    import torch
    import torch.nn as nn
    import torch.nn.functional as F
    TORCH_AVAILABLE = True
except ImportError:
    TORCH_AVAILABLE = False
    print("Warning: PyTorch not available. Deep learning features will be disabled.")


class CardType(Enum):
    """牌型枚举"""
    SINGLE = "single"  # 单牌
    PAIR = "pair"  # 对子
    TRIPLE = "triple"  # 三张
    TRIPLE_WITH_SINGLE = "triple_with_single"  # 三带一
    TRIPLE_WITH_PAIR = "triple_with_pair"  # 三带二
    STRAIGHT = "straight"  # 顺子
    PAIR_STRAIGHT = "pair_straight"  # 连对
    TRIPLE_STRAIGHT = "triple_straight"  # 飞机
    BOMB = "bomb"  # 炸弹
    ROCKET = "rocket"  # 火箭
    INVALID = "invalid"  # 无效牌型


class PlayerPosition(Enum):
    """玩家位置"""
    LANDLORD = "landlord"  # 地主
    FARMER1 = "farmer1"  # 农民1
    FARMER2 = "farmer2"  # 农民2


@dataclass
class Card:
    """扑克牌数据类"""
    suit: str  # 花色: 'spades', 'hearts', 'diamonds', 'clubs', 'joker'
    rank: str  # 点数: '3'-'K', 'A', '2', 'small_joker', 'big_joker'
    value: int  # 数值: 3-17 (3-K=3-13, A=14, 2=15, 小王=16, 大王=17)
    
    def __str__(self) -> str:
        if self.suit == 'joker':
            return self.rank
        return f"{self.rank}{self.suit[0].upper()}"
    
    def __eq__(self, other) -> bool:
        return isinstance(other, Card) and self.value == other.value
    
    def __lt__(self, other) -> bool:
        return isinstance(other, Card) and self.value < other.value
    
    def __hash__(self) -> int:
        return hash((self.suit, self.rank, self.value))


@dataclass
class CardCombination:
    """牌型组合"""
    cards: List[Card]
    card_type: CardType
    main_value: int  # 主要牌值（如三带一中的三张牌值）
    length: int = 0  # 连牌长度
    power: int = 0  # 牌力大小
    
    def __post_init__(self):
        if self.power == 0:
            self.power = self._calculate_power()
    
    def _calculate_power(self) -> int:
        """计算牌型威力"""
        type_power = {
            CardType.SINGLE: 1,
            CardType.PAIR: 2,
            CardType.TRIPLE: 3,
            CardType.TRIPLE_WITH_SINGLE: 4,
            CardType.TRIPLE_WITH_PAIR: 5,
            CardType.STRAIGHT: 6,
            CardType.PAIR_STRAIGHT: 7,
            CardType.TRIPLE_STRAIGHT: 8,
            CardType.BOMB: 100,
            CardType.ROCKET: 200
        }
        base_power = type_power.get(self.card_type, 0)
        return base_power * 100 + self.main_value
    
    def can_beat(self, other: 'CardCombination') -> bool:
        """判断是否能压过另一个牌型"""
        if not other:
            return True
        
        # 火箭最大
        if self.card_type == CardType.ROCKET:
            return True
        if other.card_type == CardType.ROCKET:
            return False
        
        # 炸弹压非炸弹
        if self.card_type == CardType.BOMB and other.card_type != CardType.BOMB:
            return True
        if other.card_type == CardType.BOMB and self.card_type != CardType.BOMB:
            return False
        
        # 同类型比较
        if self.card_type == other.card_type:
            if self.card_type in [CardType.STRAIGHT, CardType.PAIR_STRAIGHT, CardType.TRIPLE_STRAIGHT]:
                return (self.length == other.length and 
                       self.main_value > other.main_value)
            return self.main_value > other.main_value
        
        return False
    
    def __str__(self) -> str:
        cards_str = ' '.join(str(card) for card in sorted(self.cards))
        return f"{self.card_type.value}: {cards_str}"


@dataclass
class GameState:
    """游戏状态"""
    my_cards: List[Card]
    played_cards: List[Card]  # 已出的牌
    last_combination: Optional[CardCombination]  # 上一手牌
    my_position: PlayerPosition
    scores: Dict[PlayerPosition, int] = field(default_factory=dict)
    remaining_cards: Dict[PlayerPosition, int] = field(default_factory=dict)
    
    def __post_init__(self):
        if not self.scores:
            self.scores = {pos: 0 for pos in PlayerPosition}
        if not self.remaining_cards:
            self.remaining_cards = {pos: 17 for pos in PlayerPosition}


@dataclass
class StrategyResult:
    """策略分析结果"""
    recommended_combination: Optional[CardCombination]
    confidence: float  # 置信度 0-1
    reasoning: str  # 推理过程
    alternative_combinations: List[CardCombination] = field(default_factory=list)
    win_probability: float = 0.0  # 胜率预测
    risk_level: str = "medium"  # 风险等级: low, medium, high


class CardAnalyzer:
    """牌型分析器"""
    
    @staticmethod
    def create_card(suit: str, rank: str) -> Card:
        """创建扑克牌"""
        value_map = {
            '3': 3, '4': 4, '5': 5, '6': 6, '7': 7, '8': 8, '9': 9, '10': 10,
            'J': 11, 'Q': 12, 'K': 13, 'A': 14, '2': 15,
            'small_joker': 16, 'big_joker': 17
        }
        return Card(suit, rank, value_map[rank])
    
    @staticmethod
    def analyze_combination(cards: List[Card]) -> CardCombination:
        """分析牌型"""
        if not cards:
            return CardCombination([], CardType.INVALID, 0)
        
        cards = sorted(cards, key=lambda x: x.value)
        card_count = Counter(card.value for card in cards)
        
        # 火箭（双王）
        if len(cards) == 2 and 16 in card_count and 17 in card_count:
            return CardCombination(cards, CardType.ROCKET, 17)
        
        # 炸弹
        if len(cards) == 4 and len(card_count) == 1:
            return CardCombination(cards, CardType.BOMB, cards[0].value)
        
        # 单牌
        if len(cards) == 1:
            return CardCombination(cards, CardType.SINGLE, cards[0].value)
        
        # 对子
        if len(cards) == 2 and len(card_count) == 1:
            return CardCombination(cards, CardType.PAIR, cards[0].value)
        
        # 三张
        if len(cards) == 3 and len(card_count) == 1:
            return CardCombination(cards, CardType.TRIPLE, cards[0].value)
        
        # 三带一
        if len(cards) == 4 and 3 in card_count.values() and 1 in card_count.values():
            main_value = [v for v, c in card_count.items() if c == 3][0]
            return CardCombination(cards, CardType.TRIPLE_WITH_SINGLE, main_value)
        
        # 三带二
        if len(cards) == 5 and 3 in card_count.values() and 2 in card_count.values():
            main_value = [v for v, c in card_count.items() if c == 3][0]
            return CardCombination(cards, CardType.TRIPLE_WITH_PAIR, main_value)
        
        # 顺子（至少5张连续单牌）
        if (len(cards) >= 5 and len(card_count) == len(cards) and 
            CardAnalyzer._is_consecutive(list(card_count.keys()))):
            return CardCombination(cards, CardType.STRAIGHT, min(card_count.keys()), len(cards))
        
        # 连对（至少3对连续对子）
        if (len(cards) >= 6 and len(cards) % 2 == 0 and 
            all(c == 2 for c in card_count.values()) and
            CardAnalyzer._is_consecutive(list(card_count.keys()))):
            return CardCombination(cards, CardType.PAIR_STRAIGHT, min(card_count.keys()), len(cards) // 2)
        
        # 飞机（至少2个连续三张）
        if (len(cards) >= 6 and 
            all(c >= 3 for c in card_count.values()) and
            CardAnalyzer._is_consecutive([v for v, c in card_count.items() if c >= 3])):
            triple_values = [v for v, c in card_count.items() if c >= 3]
            return CardCombination(cards, CardType.TRIPLE_STRAIGHT, min(triple_values), len(triple_values))
        
        return CardCombination(cards, CardType.INVALID, 0)
    
    @staticmethod
    def _is_consecutive(values: List[int]) -> bool:
        """检查数值是否连续"""
        if not values:
            return False
        values = sorted(values)
        # 2和王不能参与顺子
        if any(v >= 15 for v in values):
            return False
        return all(values[i] + 1 == values[i + 1] for i in range(len(values) - 1))
    
    @staticmethod
    def find_all_combinations(cards: List[Card]) -> List[CardCombination]:
        """找出所有可能的牌型组合"""
        combinations = []
        
        # 单牌
        for card in cards:
            combinations.append(CardAnalyzer.analyze_combination([card]))
        
        # 对子
        card_count = Counter(card.value for card in cards)
        for value, count in card_count.items():
            if count >= 2:
                pair_cards = [card for card in cards if card.value == value][:2]
                combinations.append(CardAnalyzer.analyze_combination(pair_cards))
        
        # 三张
        for value, count in card_count.items():
            if count >= 3:
                triple_cards = [card for card in cards if card.value == value][:3]
                combinations.append(CardAnalyzer.analyze_combination(triple_cards))
        
        # 炸弹
        for value, count in card_count.items():
            if count == 4:
                bomb_cards = [card for card in cards if card.value == value]
                combinations.append(CardAnalyzer.analyze_combination(bomb_cards))
        
        # 火箭
        if 16 in card_count and 17 in card_count:
            joker_cards = [card for card in cards if card.value in [16, 17]]
            combinations.append(CardAnalyzer.analyze_combination(joker_cards))
        
        # TODO: 添加更复杂的组合（顺子、连对、飞机等）
        
        return [combo for combo in combinations if combo.card_type != CardType.INVALID]


class RuleEngine:
    """规则引擎"""
    
    def __init__(self):
        self.logger = LoggerMixin().logger
    
    def evaluate_hand_strength(self, cards: List[Card]) -> float:
        """评估手牌强度"""
        if not cards:
            return 0.0
        
        score = 0.0
        card_count = Counter(card.value for card in cards)
        
        # 大牌加分
        for card in cards:
            if card.value >= 15:  # 2和王
                score += (card.value - 14) * 10
            elif card.value >= 12:  # J, Q, K, A
                score += (card.value - 11) * 5
        
        # 对子、三张、炸弹加分
        for value, count in card_count.items():
            if count == 2:
                score += 5
            elif count == 3:
                score += 15
            elif count == 4:
                score += 50
        
        # 连牌潜力加分
        consecutive_count = self._count_consecutive_potential(list(card_count.keys()))
        score += consecutive_count * 3
        
        return min(score / len(cards), 100.0)  # 归一化到0-100
    
    def _count_consecutive_potential(self, values: List[int]) -> int:
        """计算连牌潜力"""
        values = sorted([v for v in values if v < 15])  # 排除2和王
        if len(values) < 3:
            return 0
        
        max_consecutive = 0
        current_consecutive = 1
        
        for i in range(1, len(values)):
            if values[i] == values[i-1] + 1:
                current_consecutive += 1
            else:
                max_consecutive = max(max_consecutive, current_consecutive)
                current_consecutive = 1
        
        return max(max_consecutive, current_consecutive)
    
    def should_play_aggressive(self, game_state: GameState) -> bool:
        """判断是否应该激进出牌"""
        my_cards_count = len(game_state.my_cards)
        
        # 手牌少时激进
        if my_cards_count <= 5:
            return True
        
        # 地主且农民手牌少时激进
        if game_state.my_position == PlayerPosition.LANDLORD:
            farmer_min_cards = min(
                game_state.remaining_cards.get(PlayerPosition.FARMER1, 17),
                game_state.remaining_cards.get(PlayerPosition.FARMER2, 17)
            )
            if farmer_min_cards <= 3:
                return True
        
        # 农民且地主手牌少时保守
        if game_state.my_position != PlayerPosition.LANDLORD:
            landlord_cards = game_state.remaining_cards.get(PlayerPosition.LANDLORD, 17)
            if landlord_cards <= 3:
                return False
        
        return False
    
    def calculate_win_probability(self, game_state: GameState) -> float:
        """计算胜率"""
        my_cards = len(game_state.my_cards)
        total_remaining = sum(game_state.remaining_cards.values())
        
        if total_remaining == 0:
            return 1.0 if my_cards == 0 else 0.0
        
        # 基础胜率：基于手牌数量
        base_prob = 1.0 - (my_cards / total_remaining)
        
        # 手牌质量调整
        hand_strength = self.evaluate_hand_strength(game_state.my_cards)
        quality_bonus = (hand_strength - 50) / 100  # -0.5 到 0.5
        
        # 位置调整
        position_bonus = 0.0
        if game_state.my_position == PlayerPosition.LANDLORD:
            position_bonus = 0.1  # 地主略有优势
        
        final_prob = base_prob + quality_bonus + position_bonus
        return max(0.0, min(1.0, final_prob))


class MonteCarloSimulator:
    """蒙特卡洛模拟器"""
    
    def __init__(self, num_simulations: int = 1000):
        self.num_simulations = num_simulations
        self.logger = LoggerMixin().logger
    
    def simulate_game_outcome(self, game_state: GameState, 
                            combination: CardCombination) -> float:
        """模拟游戏结果"""
        win_count = 0
        
        for _ in range(self.num_simulations):
            if self._simulate_single_game(game_state, combination):
                win_count += 1
        
        return win_count / self.num_simulations
    
    def _simulate_single_game(self, game_state: GameState, 
                            combination: CardCombination) -> bool:
        """模拟单局游戏"""
        # 简化的模拟逻辑
        # 实际实现需要更复杂的游戏逻辑
        
        remaining_cards = len(game_state.my_cards) - len(combination.cards)
        
        # 如果出完牌就赢了
        if remaining_cards == 0:
            return True
        
        # 基于剩余牌数和牌型强度的简单概率
        win_prob = 0.5
        
        if combination.card_type in [CardType.BOMB, CardType.ROCKET]:
            win_prob += 0.2
        
        if remaining_cards <= 3:
            win_prob += 0.3
        elif remaining_cards <= 6:
            win_prob += 0.1
        
        return random.random() < win_prob


class StrategyService(LoggerMixin):
    """策略分析服务"""
    
    def __init__(self):
        super().__init__()
        self.rule_engine = RuleEngine()
        self.monte_carlo = MonteCarloSimulator()
        self.card_analyzer = CardAnalyzer()
        
        # 深度学习模型（如果可用）
        self.dl_model = None
        if TORCH_AVAILABLE:
            self._init_deep_learning_model()
        
        self.logger.info("策略分析服务初始化完成")
    
    def _init_deep_learning_model(self):
        """初始化深度学习模型"""
        try:
            # 这里应该加载预训练的模型
            # 目前使用简单的占位符
            self.dl_model = None  # SimpleStrategyNet()
            self.logger.info("深度学习模型初始化完成")
        except Exception as e:
            self.logger.warning(f"深度学习模型初始化失败: {e}")
    
    def analyze_strategy(self, game_state: GameState) -> StrategyResult:
        """分析最佳策略"""
        try:
            # 找出所有可能的出牌组合
            all_combinations = self.card_analyzer.find_all_combinations(game_state.my_cards)
            
            # 过滤出能够出的牌
            valid_combinations = self._filter_valid_combinations(
                all_combinations, game_state.last_combination
            )
            
            if not valid_combinations:
                return StrategyResult(
                    recommended_combination=None,
                    confidence=1.0,
                    reasoning="没有可出的牌，只能过牌",
                    win_probability=self.rule_engine.calculate_win_probability(game_state),
                    risk_level="low"
                )
            
            # 评估每个组合
            scored_combinations = []
            for combo in valid_combinations:
                score = self._evaluate_combination(combo, game_state)
                scored_combinations.append((combo, score))
            
            # 排序并选择最佳组合
            scored_combinations.sort(key=lambda x: x[1], reverse=True)
            best_combo, best_score = scored_combinations[0]
            
            # 计算置信度
            confidence = self._calculate_confidence(scored_combinations)
            
            # 生成推理过程
            reasoning = self._generate_reasoning(best_combo, game_state)
            
            # 计算胜率
            win_prob = self.monte_carlo.simulate_game_outcome(game_state, best_combo)
            
            # 评估风险等级
            risk_level = self._assess_risk_level(best_combo, game_state)
            
            return StrategyResult(
                recommended_combination=best_combo,
                confidence=confidence,
                reasoning=reasoning,
                alternative_combinations=[combo for combo, _ in scored_combinations[1:4]],
                win_probability=win_prob,
                risk_level=risk_level
            )
            
        except Exception as e:
            self.logger.error(f"策略分析失败: {e}")
            return StrategyResult(
                recommended_combination=None,
                confidence=0.0,
                reasoning=f"分析失败: {e}",
                win_probability=0.0,
                risk_level="high"
            )
    
    def _filter_valid_combinations(self, combinations: List[CardCombination], 
                                 last_combination: Optional[CardCombination]) -> List[CardCombination]:
        """过滤出有效的出牌组合"""
        if not last_combination:
            return combinations
        
        return [combo for combo in combinations if combo.can_beat(last_combination)]
    
    def _evaluate_combination(self, combination: CardCombination, 
                            game_state: GameState) -> float:
        """评估牌型组合的得分"""
        score = 0.0
        
        # 基础牌力得分
        score += combination.power * 0.3
        
        # 手牌减少得分
        cards_reduction = len(combination.cards)
        remaining_cards = len(game_state.my_cards) - cards_reduction
        if remaining_cards == 0:
            score += 1000  # 能够出完牌的巨大奖励
        else:
            score += cards_reduction * 10
        
        # 策略调整
        if self.rule_engine.should_play_aggressive(game_state):
            # 激进策略：优先大牌
            if combination.card_type in [CardType.BOMB, CardType.ROCKET]:
                score += 200
            elif combination.main_value >= 14:  # A或以上
                score += 50
        else:
            # 保守策略：优先小牌
            if combination.main_value <= 10:
                score += 30
        
        # 手牌平衡性
        remaining_hand = [card for card in game_state.my_cards 
                         if card not in combination.cards]
        balance_score = self._evaluate_hand_balance(remaining_hand)
        score += balance_score * 0.2
        
        return score
    
    def _evaluate_hand_balance(self, cards: List[Card]) -> float:
        """评估手牌平衡性"""
        if not cards:
            return 100.0
        
        card_count = Counter(card.value for card in cards)
        
        # 偏好有组合潜力的牌
        balance_score = 0.0
        
        for value, count in card_count.items():
            if count >= 2:
                balance_score += count * 10
        
        # 连牌潜力
        consecutive_potential = self.rule_engine._count_consecutive_potential(
            list(card_count.keys())
        )
        balance_score += consecutive_potential * 5
        
        return balance_score
    
    def _calculate_confidence(self, scored_combinations: List[Tuple[CardCombination, float]]) -> float:
        """计算决策置信度"""
        if len(scored_combinations) < 2:
            return 1.0
        
        best_score = scored_combinations[0][1]
        second_score = scored_combinations[1][1]
        
        if second_score == 0:
            return 1.0
        
        score_diff = abs(best_score - second_score)
        confidence = min(score_diff / best_score, 1.0)
        
        return max(0.5, confidence)  # 最低50%置信度
    
    def _generate_reasoning(self, combination: CardCombination, 
                          game_state: GameState) -> str:
        """生成推理过程"""
        reasoning_parts = []
        
        # 牌型描述
        reasoning_parts.append(f"推荐出牌: {combination}")
        
        # 策略说明
        if self.rule_engine.should_play_aggressive(game_state):
            reasoning_parts.append("当前采用激进策略")
        else:
            reasoning_parts.append("当前采用保守策略")
        
        # 特殊情况说明
        remaining = len(game_state.my_cards) - len(combination.cards)
        if remaining == 0:
            reasoning_parts.append("此牌可以直接获胜")
        elif remaining <= 3:
            reasoning_parts.append("出牌后手牌较少，胜利在望")
        
        if combination.card_type in [CardType.BOMB, CardType.ROCKET]:
            reasoning_parts.append("使用炸弹/火箭确保控制权")
        
        return "; ".join(reasoning_parts)
    
    def _assess_risk_level(self, combination: CardCombination, 
                         game_state: GameState) -> str:
        """评估风险等级"""
        # 基于牌型和游戏状态评估风险
        if combination.card_type in [CardType.BOMB, CardType.ROCKET]:
            return "high"  # 炸弹风险高
        
        remaining = len(game_state.my_cards) - len(combination.cards)
        if remaining <= 2:
            return "low"  # 快要赢了，风险低
        elif remaining <= 5:
            return "medium"
        else:
            return "low"  # 手牌多，风险低
    
    def get_card_play_suggestion(self, my_cards: List[str], 
                               last_played: Optional[List[str]] = None,
                               position: str = "farmer1") -> Dict[str, Any]:
        """获取出牌建议（外部接口）"""
        try:
            # 转换输入格式
            card_objects = self._parse_cards(my_cards)
            last_combination = None
            if last_played:
                last_card_objects = self._parse_cards(last_played)
                last_combination = self.card_analyzer.analyze_combination(last_card_objects)
            
            # 创建游戏状态
            game_state = GameState(
                my_cards=card_objects,
                played_cards=[],
                last_combination=last_combination,
                my_position=PlayerPosition(position)
            )
            
            # 分析策略
            result = self.analyze_strategy(game_state)
            
            # 转换输出格式
            return {
                "success": True,
                "recommended_cards": (
                    [str(card) for card in result.recommended_combination.cards]
                    if result.recommended_combination else None
                ),
                "card_type": (
                    result.recommended_combination.card_type.value
                    if result.recommended_combination else None
                ),
                "confidence": result.confidence,
                "reasoning": result.reasoning,
                "win_probability": result.win_probability,
                "risk_level": result.risk_level,
                "alternatives": [
                    {
                        "cards": [str(card) for card in combo.cards],
                        "type": combo.card_type.value
                    }
                    for combo in result.alternative_combinations
                ]
            }
            
        except Exception as e:
            self.logger.error(f"获取出牌建议失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "recommended_cards": None
            }
    
    def _parse_cards(self, card_strings: List[str]) -> List[Card]:
        """解析牌面字符串"""
        cards = []
        for card_str in card_strings:
            try:
                if card_str.lower() in ['small_joker', 'big_joker']:
                    cards.append(self.card_analyzer.create_card('joker', card_str.lower()))
                else:
                    # 解析普通牌 如 "3H", "KS", "AS"
                    if len(card_str) >= 2:
                        rank = card_str[:-1]
                        suit_char = card_str[-1].lower()
                        suit_map = {'h': 'hearts', 's': 'spades', 'd': 'diamonds', 'c': 'clubs'}
                        suit = suit_map.get(suit_char, 'spades')
                        cards.append(self.card_analyzer.create_card(suit, rank))
            except Exception as e:
                self.logger.warning(f"解析牌面失败: {card_str}, {e}")
        
        return cards
    
    def cleanup(self):
        """清理资源"""
        self.logger.info("策略分析服务已清理")
