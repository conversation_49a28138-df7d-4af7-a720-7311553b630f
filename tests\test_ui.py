# -*- coding: utf-8 -*-
"""
UI组件测试用例

测试用户界面组件的功能。
"""

import unittest
from unittest.mock import Mock, patch, MagicMock

import flet as ft

# 导入要测试的UI组件
from src.ui.components.card_display import CardDisplay, HandDisplay
from src.ui.components.game_info import GameInfoPanel, PlayerInfoCard
from src.ui.components.suggestion_panel import SuggestionPanel, AnalysisCard
from src.ui.components.status_bar import StatusBar, ProgressIndicator, StatusLevel
from src.ui.components.controls import (
    StyledButton, ButtonStyle, LabeledControl, SearchBox,
    ToggleGroup, InfoCard, Separator
)
from src.ui.components.dialogs import MessageDialog, InputDialog, DialogType

# 导入数据模型
from src.models.game_state import Card, CardSuit, CardRank, Player, GameState
from src.services.strategy_service import PlaySuggestion, GameAnalysis


class TestCardDisplay(unittest.TestCase):
    """卡牌显示组件测试"""
    
    def setUp(self):
        """测试前准备"""
        self.test_card = Card(suit=CardSuit.SPADES, rank=CardRank.ACE)
    
    def test_card_display_init(self):
        """测试卡牌显示初始化"""
        card_display = CardDisplay(
            card=self.test_card,
            size="medium",
            selectable=True
        )
        
        self.assertEqual(card_display.card, self.test_card)
        self.assertEqual(card_display.size, "medium")
        self.assertTrue(card_display.selectable)
        self.assertFalse(card_display.selected)
    
    def test_card_display_selection(self):
        """测试卡牌选择"""
        card_display = CardDisplay(card=self.test_card, selectable=True)
        
        # 测试选择
        card_display.set_selected(True)
        self.assertTrue(card_display.selected)
        
        # 测试取消选择
        card_display.set_selected(False)
        self.assertFalse(card_display.selected)
    
    def test_hand_display_init(self):
        """测试手牌显示初始化"""
        cards = [
            Card(suit=CardSuit.SPADES, rank=CardRank.ACE),
            Card(suit=CardSuit.HEARTS, rank=CardRank.KING),
            Card(suit=CardSuit.DIAMONDS, rank=CardRank.QUEEN)
        ]
        
        hand_display = HandDisplay(
            cards=cards,
            title="我的手牌",
            selectable=True
        )
        
        self.assertEqual(len(hand_display.cards), 3)
        self.assertEqual(hand_display.title, "我的手牌")
        self.assertTrue(hand_display.selectable)
    
    def test_hand_display_selection(self):
        """测试手牌选择"""
        cards = [
            Card(suit=CardSuit.SPADES, rank=CardRank.ACE),
            Card(suit=CardSuit.HEARTS, rank=CardRank.KING)
        ]
        
        hand_display = HandDisplay(cards=cards, selectable=True)
        
        # 测试选择卡牌
        hand_display.select_card(0)
        selected = hand_display.get_selected_cards()
        self.assertEqual(len(selected), 1)
        self.assertEqual(selected[0], cards[0])
        
        # 测试清除选择
        hand_display.clear_selection()
        selected = hand_display.get_selected_cards()
        self.assertEqual(len(selected), 0)


class TestGameInfo(unittest.TestCase):
    """游戏信息组件测试"""
    
    def setUp(self):
        """测试前准备"""
        self.test_player = Player(
            id="player1",
            name="测试玩家",
            position=0,
            role="地主",
            card_count=17,
            score=1000
        )
        
        self.test_game_state = GameState(
            players=[self.test_player],
            current_player=0,
            phase="playing",
            round_count=1,
            duration=300
        )
    
    def test_game_info_panel_init(self):
        """测试游戏信息面板初始化"""
        panel = GameInfoPanel(
            game_state=self.test_game_state,
            show_details=True
        )
        
        self.assertEqual(panel.game_state, self.test_game_state)
        self.assertTrue(panel.show_details)
    
    def test_player_info_card_init(self):
        """测试玩家信息卡片初始化"""
        card = PlayerInfoCard(
            player=self.test_player,
            is_current=True,
            compact=False
        )
        
        self.assertEqual(card.player, self.test_player)
        self.assertTrue(card.is_current)
        self.assertFalse(card.compact)
    
    def test_game_info_update(self):
        """测试游戏信息更新"""
        panel = GameInfoPanel(game_state=self.test_game_state)
        
        # 创建新的游戏状态
        new_game_state = GameState(
            players=[self.test_player],
            current_player=0,
            phase="finished",
            round_count=2,
            duration=600
        )
        
        # 更新游戏状态
        panel.update_game_state(new_game_state)
        self.assertEqual(panel.game_state.phase, "finished")
        self.assertEqual(panel.game_state.round_count, 2)


class TestSuggestionPanel(unittest.TestCase):
    """建议面板组件测试"""
    
    def setUp(self):
        """测试前准备"""
        self.test_suggestion = PlaySuggestion(
            suggested_cards=[
                Card(suit=CardSuit.SPADES, rank=CardRank.ACE)
            ],
            confidence=0.85,
            reasoning="这是最优出牌",
            expected_value=0.7,
            risk_level=0.3
        )
        
        self.test_analysis = GameAnalysis(
            win_probability=0.65,
            position_strength=0.8,
            threat_level=0.4,
            key_insights=["对手手牌较少", "应该保守出牌"],
            recommended_strategy="保守策略"
        )
    
    def test_suggestion_panel_init(self):
        """测试建议面板初始化"""
        panel = SuggestionPanel(
            suggestion=self.test_suggestion,
            on_refresh=lambda: None,
            on_adopt=lambda: None
        )
        
        self.assertEqual(panel.suggestion, self.test_suggestion)
        self.assertIsNotNone(panel.on_refresh_callback)
        self.assertIsNotNone(panel.on_adopt_callback)
    
    def test_analysis_card_init(self):
        """测试分析卡片初始化"""
        card = AnalysisCard(
            analysis=self.test_analysis,
            compact=False
        )
        
        self.assertEqual(card.analysis, self.test_analysis)
        self.assertFalse(card.compact)
    
    def test_suggestion_update(self):
        """测试建议更新"""
        panel = SuggestionPanel(suggestion=self.test_suggestion)
        
        # 创建新建议
        new_suggestion = PlaySuggestion(
            suggested_cards=[
                Card(suit=CardSuit.HEARTS, rank=CardRank.KING)
            ],
            confidence=0.9,
            reasoning="更好的出牌选择",
            expected_value=0.8,
            risk_level=0.2
        )
        
        # 更新建议
        panel.update_suggestion(new_suggestion)
        self.assertEqual(panel.suggestion.confidence, 0.9)
        self.assertEqual(panel.suggestion.reasoning, "更好的出牌选择")


class TestStatusBar(unittest.TestCase):
    """状态栏组件测试"""
    
    def test_status_bar_init(self):
        """测试状态栏初始化"""
        status_bar = StatusBar(
            initial_message="就绪",
            show_timestamp=True,
            show_fps=True,
            show_memory=True
        )
        
        self.assertEqual(status_bar.current_message, "就绪")
        self.assertEqual(status_bar.current_level, StatusLevel.INFO)
        self.assertTrue(status_bar.show_timestamp)
        self.assertTrue(status_bar.show_fps)
        self.assertTrue(status_bar.show_memory)
    
    def test_status_update(self):
        """测试状态更新"""
        status_bar = StatusBar()
        
        # 测试不同级别的状态更新
        status_bar.update_status("处理中", StatusLevel.INFO)
        self.assertEqual(status_bar.current_message, "处理中")
        self.assertEqual(status_bar.current_level, StatusLevel.INFO)
        
        status_bar.update_status("成功完成", StatusLevel.SUCCESS)
        self.assertEqual(status_bar.current_message, "成功完成")
        self.assertEqual(status_bar.current_level, StatusLevel.SUCCESS)
        
        status_bar.update_status("发生错误", StatusLevel.ERROR)
        self.assertEqual(status_bar.current_message, "发生错误")
        self.assertEqual(status_bar.current_level, StatusLevel.ERROR)
    
    def test_fps_update(self):
        """测试FPS更新"""
        status_bar = StatusBar(show_fps=True)
        
        status_bar.update_fps(30)
        self.assertEqual(status_bar.fps_counter, 30)
        
        status_bar.update_fps(15)
        self.assertEqual(status_bar.fps_counter, 15)
    
    def test_memory_update(self):
        """测试内存更新"""
        status_bar = StatusBar(show_memory=True)
        
        status_bar.update_memory(50.5)
        self.assertEqual(status_bar.memory_usage, 50.5)
        
        status_bar.update_memory(150.0)
        self.assertEqual(status_bar.memory_usage, 150.0)


class TestProgressIndicator(unittest.TestCase):
    """进度指示器测试"""
    
    def test_progress_indicator_init(self):
        """测试进度指示器初始化"""
        indicator = ProgressIndicator(
            title="处理中",
            show_percentage=True,
            show_eta=True,
            indeterminate=False
        )
        
        self.assertEqual(indicator.title, "处理中")
        self.assertTrue(indicator.show_percentage)
        self.assertTrue(indicator.show_eta)
        self.assertFalse(indicator.indeterminate)
        self.assertEqual(indicator.current_progress, 0.0)
    
    def test_progress_update(self):
        """测试进度更新"""
        indicator = ProgressIndicator()
        
        # 测试进度更新
        indicator.update_progress(0.5, "处理步骤1")
        self.assertEqual(indicator.current_progress, 0.5)
        
        indicator.update_progress(1.0, "完成")
        self.assertEqual(indicator.current_progress, 1.0)
    
    def test_step_update(self):
        """测试步骤更新"""
        indicator = ProgressIndicator()
        
        indicator.update_step(5, 10, "步骤5")
        self.assertEqual(indicator.current_step, 5)
        self.assertEqual(indicator.total_steps, 10)
        self.assertEqual(indicator.current_progress, 0.5)
    
    def test_indeterminate_mode(self):
        """测试不确定进度模式"""
        indicator = ProgressIndicator()
        
        indicator.set_indeterminate(True)
        self.assertTrue(indicator.indeterminate)
        
        indicator.set_indeterminate(False)
        self.assertFalse(indicator.indeterminate)
    
    def test_reset(self):
        """测试重置"""
        indicator = ProgressIndicator()
        
        # 设置一些进度
        indicator.update_progress(0.7)
        indicator.update_step(7, 10)
        
        # 重置
        indicator.reset()
        self.assertEqual(indicator.current_progress, 0.0)
        self.assertEqual(indicator.current_step, 0)
    
    def test_complete(self):
        """测试完成"""
        indicator = ProgressIndicator()
        
        indicator.complete("处理完成")
        self.assertEqual(indicator.current_progress, 1.0)


class TestControls(unittest.TestCase):
    """控件组件测试"""
    
    def test_styled_button_init(self):
        """测试样式化按钮初始化"""
        button = StyledButton(
            text="测试按钮",
            style=ButtonStyle.PRIMARY,
            icon=ft.Icons.PLAY_ARROW
        )
        
        self.assertEqual(button.text, "测试按钮")
        self.assertEqual(button.button_style, ButtonStyle.PRIMARY)
        self.assertEqual(button.icon, ft.Icons.PLAY_ARROW)
    
    def test_labeled_control_init(self):
        """测试带标签控件初始化"""
        text_field = ft.TextField(value="测试值")
        labeled = LabeledControl(
            label="测试标签",
            control=text_field,
            required=True,
            help_text="这是帮助文本"
        )
        
        self.assertEqual(labeled.label, "测试标签")
        self.assertEqual(labeled.control, text_field)
        self.assertTrue(labeled.required)
        self.assertEqual(labeled.help_text, "这是帮助文本")
    
    def test_labeled_control_error(self):
        """测试带标签控件错误处理"""
        text_field = ft.TextField()
        labeled = LabeledControl(label="测试", control=text_field)
        
        # 设置错误
        labeled.set_error("输入错误")
        self.assertEqual(labeled.error_text, "输入错误")
        
        # 清除错误
        labeled.clear_error()
        self.assertIsNone(labeled.error_text)
    
    def test_search_box_init(self):
        """测试搜索框初始化"""
        search_box = SearchBox(
            placeholder="搜索游戏...",
            width=300
        )
        
        self.assertEqual(search_box.placeholder, "搜索游戏...")
        self.assertEqual(search_box.width, 300)
    
    def test_search_box_value(self):
        """测试搜索框值操作"""
        search_box = SearchBox()
        
        # 设置值
        search_box.set_value("测试搜索")
        self.assertEqual(search_box.get_value(), "测试搜索")
        
        # 清除值
        search_box.set_value("")
        self.assertEqual(search_box.get_value(), "")
    
    def test_toggle_group_init(self):
        """测试切换组初始化"""
        options = [
            {"value": "option1", "text": "选项1"},
            {"value": "option2", "text": "选项2"},
            {"value": "option3", "text": "选项3"}
        ]
        
        toggle_group = ToggleGroup(
            options=options,
            selected_value="option1",
            multiple=False
        )
        
        self.assertEqual(len(toggle_group.options), 3)
        self.assertEqual(toggle_group.selected_value, "option1")
        self.assertFalse(toggle_group.multiple)
    
    def test_toggle_group_selection(self):
        """测试切换组选择"""
        options = [
            {"value": "option1", "text": "选项1"},
            {"value": "option2", "text": "选项2"}
        ]
        
        # 单选模式
        toggle_group = ToggleGroup(options=options, multiple=False)
        toggle_group.set_selected("option2")
        self.assertEqual(toggle_group.get_selected(), "option2")
        
        # 多选模式
        toggle_group_multi = ToggleGroup(options=options, multiple=True)
        toggle_group_multi.set_selected(["option1", "option2"])
        selected = toggle_group_multi.get_selected()
        self.assertIn("option1", selected)
        self.assertIn("option2", selected)
    
    def test_info_card_init(self):
        """测试信息卡片初始化"""
        card = InfoCard(
            title="测试卡片",
            content="这是卡片内容",
            icon=ft.Icons.INFO,
            collapsible=True
        )
        
        self.assertEqual(card.title, "测试卡片")
        self.assertEqual(card.content, "这是卡片内容")
        self.assertEqual(card.icon, ft.Icons.INFO)
        self.assertTrue(card.collapsible)
    
    def test_info_card_collapse(self):
        """测试信息卡片折叠"""
        card = InfoCard(
            title="测试",
            content="内容",
            collapsible=True,
            initially_collapsed=False
        )
        
        # 测试折叠
        card.collapse()
        self.assertTrue(card.collapsed)
        
        # 测试展开
        card.expand()
        self.assertFalse(card.collapsed)
    
    def test_separator_init(self):
        """测试分隔符初始化"""
        # 无文本分隔符
        separator1 = Separator()
        self.assertIsNone(separator1.text)
        
        # 带文本分隔符
        separator2 = Separator(text="分隔线")
        self.assertEqual(separator2.text, "分隔线")


class TestDialogs(unittest.TestCase):
    """对话框组件测试"""
    
    def test_message_dialog_init(self):
        """测试消息对话框初始化"""
        dialog = MessageDialog(
            title="测试标题",
            message="测试消息",
            dialog_type=DialogType.INFO,
            show_cancel=True
        )
        
        self.assertEqual(dialog.dialog_type, DialogType.INFO)
        self.assertIsNotNone(dialog.title)
        self.assertIsNotNone(dialog.content)
        self.assertIsNotNone(dialog.actions)
    
    def test_input_dialog_init(self):
        """测试输入对话框初始化"""
        dialog = InputDialog(
            title="输入测试",
            prompt="请输入内容",
            initial_value="默认值",
            placeholder="提示文本"
        )
        
        self.assertIsNotNone(dialog.input_field)
        self.assertEqual(dialog.input_field.value, "默认值")
        self.assertEqual(dialog.input_field.hint_text, "提示文本")
    
    def test_input_dialog_validation(self):
        """测试输入对话框验证"""
        def validate_not_empty(value: str) -> str:
            return "不能为空" if not value.strip() else None
        
        dialog = InputDialog(
            title="验证测试",
            prompt="输入内容",
            validate=validate_not_empty
        )
        
        self.assertIsNotNone(dialog.validate_callback)


if __name__ == '__main__':
    # 运行测试
    unittest.main(verbosity=2)