{"data_mtime": 1750100849, "dep_lines": [21, 24, 24, 24, 24, 28, 18, 20, 22, 24, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 10, 10, 10, 25, 5, 10, 5, 20, 5, 30, 30, 30, 30, 30], "dependencies": ["collections.abc", "PIL.GimpGradientFile", "PIL.GimpPaletteFile", "PIL.ImageColor", "PIL.PaletteFile", "PIL.Image", "__future__", "array", "typing", "PIL", "builtins", "_frozen_importlib", "_typeshed", "abc", "types", "typing_extensions"], "hash": "13330c2440f6f2abece90de16c7044f267bee2d4", "id": "PIL.ImagePalette", "ignore_all": true, "interface_hash": "129bb9ba2d4845a1decbbf09e6c3bae3ee9fbeee", "mtime": 1745696559, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Python313\\Lib\\site-packages\\PIL\\ImagePalette.py", "plugin_data": null, "size": 9295, "suppressed": [], "version_id": "1.15.0"}