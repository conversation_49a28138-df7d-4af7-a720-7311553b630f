{".class": "MypyFile", "_fullname": "cv2.detail", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "ARG_KIND_GARRAY": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.detail.ARG_KIND_GARRAY", "name": "ARG_KIND_GARRAY", "type": "builtins.int"}}, "ARG_KIND_GFRAME": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.detail.ARG_KIND_GFRAME", "name": "ARG_KIND_GFRAME", "type": "builtins.int"}}, "ARG_KIND_GMAT": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.detail.ARG_KIND_GMAT", "name": "ARG_KIND_GMAT", "type": "builtins.int"}}, "ARG_KIND_GMATP": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.detail.ARG_KIND_GMATP", "name": "ARG_KIND_GMATP", "type": "builtins.int"}}, "ARG_KIND_GOBJREF": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.detail.ARG_KIND_GOBJREF", "name": "ARG_KIND_GOBJREF", "type": "builtins.int"}}, "ARG_KIND_GOPAQUE": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.detail.ARG_KIND_GOPAQUE", "name": "ARG_KIND_GOPAQUE", "type": "builtins.int"}}, "ARG_KIND_GSCALAR": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.detail.ARG_KIND_GSCALAR", "name": "ARG_KIND_GSCALAR", "type": "builtins.int"}}, "ARG_KIND_OPAQUE": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.detail.ARG_KIND_OPAQUE", "name": "ARG_KIND_OPAQUE", "type": "builtins.int"}}, "ARG_KIND_OPAQUE_VAL": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.detail.ARG_KIND_OPAQUE_VAL", "name": "ARG_KIND_OPAQUE_VAL", "type": "builtins.int"}}, "AffineBasedEstimator": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["cv2.detail.Estimator"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "cv2.detail.AffineBasedEstimator", "name": "AffineBasedEstimator", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "cv2.detail.AffineBasedEstimator", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "cv2.detail", "mro": ["cv2.detail.AffineBasedEstimator", "cv2.detail.Estimator", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.detail.AffineBasedEstimator.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cv2.detail.AffineBasedEstimator"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of AffineBasedEstimator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cv2.detail.AffineBasedEstimator.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cv2.detail.AffineBasedEstimator", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AffineBestOf2NearestMatcher": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["cv2.detail.BestOf2NearestMatcher"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "cv2.detail.AffineBestOf2NearestMatcher", "name": "AffineBestOf2NearestMatcher", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "cv2.detail.AffineBestOf2NearestMatcher", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "cv2.detail", "mro": ["cv2.detail.AffineBestOf2NearestMatcher", "cv2.detail.BestOf2NearestMatcher", "cv2.detail.FeaturesMatcher", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["self", "full_affine", "try_use_gpu", "match_conf", "num_matches_thresh1"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.detail.AffineBestOf2NearestMatcher.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["self", "full_affine", "try_use_gpu", "match_conf", "num_matches_thresh1"], "arg_types": ["cv2.detail.AffineBestOf2NearestMatcher", "builtins.bool", "builtins.bool", "builtins.float", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of AffineBestOf2NearestMatcher", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cv2.detail.AffineBestOf2NearestMatcher.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cv2.detail.AffineBestOf2NearestMatcher", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ArgKind": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "cv2.detail.ArgKind", "line": 83, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "builtins.int"}}, "ArgKind_GARRAY": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.detail.ArgKind_GARRAY", "name": "ArgKind_GARRAY", "type": "builtins.int"}}, "ArgKind_GFRAME": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.detail.ArgKind_GFRAME", "name": "ArgKind_GFRAME", "type": "builtins.int"}}, "ArgKind_GMAT": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.detail.ArgKind_GMAT", "name": "ArgKind_GMAT", "type": "builtins.int"}}, "ArgKind_GMATP": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.detail.ArgKind_GMATP", "name": "ArgKind_GMATP", "type": "builtins.int"}}, "ArgKind_GOBJREF": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.detail.ArgKind_GOBJREF", "name": "ArgKind_GOBJREF", "type": "builtins.int"}}, "ArgKind_GOPAQUE": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.detail.ArgKind_GOPAQUE", "name": "ArgKind_GOPAQUE", "type": "builtins.int"}}, "ArgKind_GSCALAR": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.detail.ArgKind_GSCALAR", "name": "ArgKind_GSCALAR", "type": "builtins.int"}}, "ArgKind_OPAQUE": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.detail.ArgKind_OPAQUE", "name": "ArgKind_OPAQUE", "type": "builtins.int"}}, "ArgKind_OPAQUE_VAL": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.detail.ArgKind_OPAQUE_VAL", "name": "ArgKind_OPAQUE_VAL", "type": "builtins.int"}}, "BLENDER_FEATHER": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.detail.BLENDER_FEATHER", "name": "BLENDER_FEATHER", "type": "builtins.int"}}, "BLENDER_MULTI_BAND": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.detail.BLENDER_MULTI_BAND", "name": "BLENDER_MULTI_BAND", "type": "builtins.int"}}, "BLENDER_NO": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.detail.BLENDER_NO", "name": "BLENDER_NO", "type": "builtins.int"}}, "BestOf2NearestMatcher": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["cv2.detail.FeaturesMatcher"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "cv2.detail.BestOf2NearestMatcher", "name": "BestOf2NearestMatcher", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "cv2.detail.BestOf2NearestMatcher", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "cv2.detail", "mro": ["cv2.detail.BestOf2NearestMatcher", "cv2.detail.FeaturesMatcher", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1], "arg_names": ["self", "try_use_gpu", "match_conf", "num_matches_thresh1", "num_matches_thresh2", "matches_confindece_thresh"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.detail.BestOf2NearestMatcher.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1], "arg_names": ["self", "try_use_gpu", "match_conf", "num_matches_thresh1", "num_matches_thresh2", "matches_confindece_thresh"], "arg_types": ["cv2.detail.BestOf2NearestMatcher", "builtins.bool", "builtins.float", "builtins.int", "builtins.int", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of BestOf2NearestMatcher", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "collectGarbage": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.detail.BestOf2NearestMatcher.collectGarbage", "name": "collectGarbage", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cv2.detail.BestOf2NearestMatcher"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "collectGarbage of BestOf2NearestMatcher", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1], "arg_names": ["cls", "try_use_gpu", "match_conf", "num_matches_thresh1", "num_matches_thresh2", "matches_confindece_thresh"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "cv2.detail.BestOf2NearestMatcher.create", "name": "create", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1], "arg_names": ["cls", "try_use_gpu", "match_conf", "num_matches_thresh1", "num_matches_thresh2", "matches_confindece_thresh"], "arg_types": [{".class": "TypeType", "item": "cv2.detail.BestOf2NearestMatcher"}, "builtins.bool", "builtins.float", "builtins.int", "builtins.int", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create of BestOf2NearestMatcher", "ret_type": "cv2.detail.BestOf2NearestMatcher", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "cv2.detail.BestOf2NearestMatcher.create", "name": "create", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1], "arg_names": ["cls", "try_use_gpu", "match_conf", "num_matches_thresh1", "num_matches_thresh2", "matches_confindece_thresh"], "arg_types": [{".class": "TypeType", "item": "cv2.detail.BestOf2NearestMatcher"}, "builtins.bool", "builtins.float", "builtins.int", "builtins.int", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create of BestOf2NearestMatcher", "ret_type": "cv2.detail.BestOf2NearestMatcher", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cv2.detail.BestOf2NearestMatcher.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cv2.detail.BestOf2NearestMatcher", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "BestOf2NearestRangeMatcher": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["cv2.detail.BestOf2NearestMatcher"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "cv2.detail.BestOf2NearestRangeMatcher", "name": "BestOf2NearestRangeMatcher", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "cv2.detail.BestOf2NearestRangeMatcher", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "cv2.detail", "mro": ["cv2.detail.BestOf2NearestRangeMatcher", "cv2.detail.BestOf2NearestMatcher", "cv2.detail.FeaturesMatcher", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1], "arg_names": ["self", "range_width", "try_use_gpu", "match_conf", "num_matches_thresh1", "num_matches_thresh2"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.detail.BestOf2NearestRangeMatcher.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1], "arg_names": ["self", "range_width", "try_use_gpu", "match_conf", "num_matches_thresh1", "num_matches_thresh2"], "arg_types": ["cv2.detail.BestOf2NearestRangeMatcher", "builtins.int", "builtins.bool", "builtins.float", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of BestOf2NearestRangeMatcher", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cv2.detail.BestOf2NearestRangeMatcher.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cv2.detail.BestOf2NearestRangeMatcher", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Blender": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "cv2.detail.Blender", "name": "<PERSON><PERSON>der", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "cv2.detail.Blender", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "cv2.detail", "mro": ["cv2.detail.Blender", "builtins.object"], "names": {".class": "SymbolTable", "blend": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "cv2.detail.Blender.blend", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "dst", "dst_mask"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.detail.Blender.blend", "name": "blend", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "dst", "dst_mask"], "arg_types": ["cv2.detail.Blender", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "blend of Blender", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.detail.Blender.blend", "name": "blend", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "dst", "dst_mask"], "arg_types": ["cv2.detail.Blender", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "blend of Blender", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "dst", "dst_mask"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.detail.Blender.blend", "name": "blend", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "dst", "dst_mask"], "arg_types": ["cv2.detail.Blender", "cv2.UMat", "cv2.UMat"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "blend of Blender", "ret_type": {".class": "TupleType", "implicit": false, "items": ["cv2.UMat", "cv2.UMat"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.detail.Blender.blend", "name": "blend", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "dst", "dst_mask"], "arg_types": ["cv2.detail.Blender", "cv2.UMat", "cv2.UMat"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "blend of Blender", "ret_type": {".class": "TupleType", "implicit": false, "items": ["cv2.UMat", "cv2.UMat"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "dst", "dst_mask"], "arg_types": ["cv2.detail.Blender", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "blend of Blender", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "dst", "dst_mask"], "arg_types": ["cv2.detail.Blender", "cv2.UMat", "cv2.UMat"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "blend of Blender", "ret_type": {".class": "TupleType", "implicit": false, "items": ["cv2.UMat", "cv2.UMat"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "createDefault": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["cls", "type", "try_gpu"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "cv2.detail.Blender.createDefault", "name": "createDefault", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["cls", "type", "try_gpu"], "arg_types": [{".class": "TypeType", "item": "cv2.detail.Blender"}, "builtins.int", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "createDefault of Blender", "ret_type": "cv2.detail.Blender", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "cv2.detail.Blender.createDefault", "name": "createDefault", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["cls", "type", "try_gpu"], "arg_types": [{".class": "TypeType", "item": "cv2.detail.Blender"}, "builtins.int", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "createDefault of Blender", "ret_type": "cv2.detail.Blender", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "feed": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "cv2.detail.Blender.feed", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "img", "mask", "tl"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.detail.Blender.feed", "name": "feed", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "img", "mask", "tl"], "arg_types": ["cv2.detail.Blender", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.Point"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "feed of Blender", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.detail.Blender.feed", "name": "feed", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "img", "mask", "tl"], "arg_types": ["cv2.detail.Blender", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.Point"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "feed of Blender", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "img", "mask", "tl"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.detail.Blender.feed", "name": "feed", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "img", "mask", "tl"], "arg_types": ["cv2.detail.Blender", "cv2.UMat", "cv2.UMat", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.Point"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "feed of Blender", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.detail.Blender.feed", "name": "feed", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "img", "mask", "tl"], "arg_types": ["cv2.detail.Blender", "cv2.UMat", "cv2.UMat", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.Point"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "feed of Blender", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "img", "mask", "tl"], "arg_types": ["cv2.detail.Blender", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.Point"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "feed of Blender", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "img", "mask", "tl"], "arg_types": ["cv2.detail.Blender", "cv2.UMat", "cv2.UMat", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.Point"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "feed of Blender", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "prepare": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "cv2.detail.Blender.prepare", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "corners", "sizes"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.detail.Blender.prepare", "name": "prepare", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "corners", "sizes"], "arg_types": ["cv2.detail.Blender", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.Point"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.<PERSON>.Size"}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "prepare of Blender", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.detail.Blender.prepare", "name": "prepare", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "corners", "sizes"], "arg_types": ["cv2.detail.Blender", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.Point"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.<PERSON>.Size"}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "prepare of Blender", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "dst_roi"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.detail.Blender.prepare", "name": "prepare", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "dst_roi"], "arg_types": ["cv2.detail.Blender", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.Rect"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "prepare of Blender", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.detail.Blender.prepare", "name": "prepare", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "dst_roi"], "arg_types": ["cv2.detail.Blender", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.Rect"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "prepare of Blender", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "corners", "sizes"], "arg_types": ["cv2.detail.Blender", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.Point"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.<PERSON>.Size"}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "prepare of Blender", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "dst_roi"], "arg_types": ["cv2.detail.Blender", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.Rect"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "prepare of Blender", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cv2.detail.Blender.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cv2.detail.Blender", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Blender_FEATHER": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.detail.Blender_FEATHER", "name": "Blender_FEATHER", "type": "builtins.int"}}, "Blender_MULTI_BAND": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.detail.Blender_MULTI_BAND", "name": "Blender_MULTI_BAND", "type": "builtins.int"}}, "Blender_NO": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.detail.Blender_NO", "name": "Blender_NO", "type": "builtins.int"}}, "BlocksChannelsCompensator": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["cv2.detail.BlocksCompensator"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "cv2.detail.BlocksChannelsCompensator", "name": "BlocksChannelsCompensator", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "cv2.detail.BlocksChannelsCompensator", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "cv2.detail", "mro": ["cv2.detail.BlocksChannelsCompensator", "cv2.detail.BlocksCompensator", "cv2.detail.ExposureCompensator", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "bl_width", "bl_height", "nr_feeds"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.detail.BlocksChannelsCompensator.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "bl_width", "bl_height", "nr_feeds"], "arg_types": ["cv2.detail.BlocksChannelsCompensator", "builtins.int", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of BlocksChannelsCompensator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cv2.detail.BlocksChannelsCompensator.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cv2.detail.BlocksChannelsCompensator", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "BlocksCompensator": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["cv2.detail.ExposureCompensator"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "cv2.detail.BlocksCompensator", "name": "BlocksCompensator", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "cv2.detail.BlocksCompensator", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "cv2.detail", "mro": ["cv2.detail.BlocksCompensator", "cv2.detail.ExposureCompensator", "builtins.object"], "names": {".class": "SymbolTable", "apply": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "cv2.detail.BlocksCompensator.apply", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "index", "corner", "image", "mask"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.detail.BlocksCompensator.apply", "name": "apply", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "index", "corner", "image", "mask"], "arg_types": ["cv2.detail.BlocksCompensator", "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.Point"}, {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "apply of BlocksCompensator", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.detail.BlocksCompensator.apply", "name": "apply", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "index", "corner", "image", "mask"], "arg_types": ["cv2.detail.BlocksCompensator", "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.Point"}, {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "apply of BlocksCompensator", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "index", "corner", "image", "mask"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.detail.BlocksCompensator.apply", "name": "apply", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "index", "corner", "image", "mask"], "arg_types": ["cv2.detail.BlocksCompensator", "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.Point"}, "cv2.UMat", "cv2.UMat"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "apply of BlocksCompensator", "ret_type": "cv2.UMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.detail.BlocksCompensator.apply", "name": "apply", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "index", "corner", "image", "mask"], "arg_types": ["cv2.detail.BlocksCompensator", "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.Point"}, "cv2.UMat", "cv2.UMat"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "apply of BlocksCompensator", "ret_type": "cv2.UMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "index", "corner", "image", "mask"], "arg_types": ["cv2.detail.BlocksCompensator", "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.Point"}, {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "apply of BlocksCompensator", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "index", "corner", "image", "mask"], "arg_types": ["cv2.detail.BlocksCompensator", "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.Point"}, "cv2.UMat", "cv2.UMat"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "apply of BlocksCompensator", "ret_type": "cv2.UMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "getBlockSize": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.detail.BlocksCompensator.getBlockSize", "name": "getBlockSize", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cv2.detail.BlocksCompensator"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getBlockSize of BlocksCompensator", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "cv2.<PERSON>.Size"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "getMatGains": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "umv"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.detail.BlocksCompensator.getMatGains", "name": "getMatGains", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "umv"], "arg_types": ["cv2.detail.BlocksCompensator", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getMatGains of BlocksCompensator", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "getNrFeeds": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.detail.BlocksCompensator.getNrFeeds", "name": "getNrFeeds", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cv2.detail.BlocksCompensator"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getNrFeeds of BlocksCompensator", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "getNrGainsFilteringIterations": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.detail.BlocksCompensator.getNrGainsFilteringIterations", "name": "getNrGainsFilteringIterations", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cv2.detail.BlocksCompensator"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getNrGainsFilteringIterations of BlocksCompensator", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "getSimilarityThreshold": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.detail.BlocksCompensator.getSimilarityThreshold", "name": "getSimilarityThreshold", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cv2.detail.BlocksCompensator"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getSimilarityThreshold of BlocksCompensator", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "setBlockSize": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "cv2.detail.BlocksCompensator.setBlockSize", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "width", "height"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.detail.BlocksCompensator.setBlockSize", "name": "setBlockSize", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "width", "height"], "arg_types": ["cv2.detail.BlocksCompensator", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "setBlockSize of BlocksCompensator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.detail.BlocksCompensator.setBlockSize", "name": "setBlockSize", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "width", "height"], "arg_types": ["cv2.detail.BlocksCompensator", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "setBlockSize of BlocksCompensator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "size"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.detail.BlocksCompensator.setBlockSize", "name": "setBlockSize", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "size"], "arg_types": ["cv2.detail.BlocksCompensator", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.<PERSON>.Size"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "setBlockSize of BlocksCompensator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.detail.BlocksCompensator.setBlockSize", "name": "setBlockSize", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "size"], "arg_types": ["cv2.detail.BlocksCompensator", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.<PERSON>.Size"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "setBlockSize of BlocksCompensator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "width", "height"], "arg_types": ["cv2.detail.BlocksCompensator", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "setBlockSize of BlocksCompensator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "size"], "arg_types": ["cv2.detail.BlocksCompensator", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.<PERSON>.Size"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "setBlockSize of BlocksCompensator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "setMatGains": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "umv"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.detail.BlocksCompensator.setMatGains", "name": "setMatGains", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "umv"], "arg_types": ["cv2.detail.BlocksCompensator", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "setMatGains of BlocksCompensator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "setNrFeeds": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "nr_feeds"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.detail.BlocksCompensator.setNrFeeds", "name": "setNrFeeds", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "nr_feeds"], "arg_types": ["cv2.detail.BlocksCompensator", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "setNrFeeds of BlocksCompensator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "setNrGainsFilteringIterations": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "nr_iterations"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.detail.BlocksCompensator.setNrGainsFilteringIterations", "name": "setNrGainsFilteringIterations", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "nr_iterations"], "arg_types": ["cv2.detail.BlocksCompensator", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "setNrGainsFilteringIterations of BlocksCompensator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "setSimilarityThreshold": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "similarity_threshold"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.detail.BlocksCompensator.setSimilarityThreshold", "name": "setSimilarityThreshold", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "similarity_threshold"], "arg_types": ["cv2.detail.BlocksCompensator", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "setSimilarityThreshold of BlocksCompensator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cv2.detail.BlocksCompensator.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cv2.detail.BlocksCompensator", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "BlocksGainCompensator": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["cv2.detail.BlocksCompensator"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "cv2.detail.BlocksGainCompensator", "name": "BlocksGainCompensator", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "cv2.detail.BlocksGainCompensator", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "cv2.detail", "mro": ["cv2.detail.BlocksGainCompensator", "cv2.detail.BlocksCompensator", "cv2.detail.ExposureCompensator", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "cv2.detail.BlocksGainCompensator.__init__", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "bl_width", "bl_height"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.detail.BlocksGainCompensator.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "bl_width", "bl_height"], "arg_types": ["cv2.detail.BlocksGainCompensator", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of BlocksGainCompensator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.detail.BlocksGainCompensator.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "bl_width", "bl_height"], "arg_types": ["cv2.detail.BlocksGainCompensator", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of BlocksGainCompensator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "bl_width", "bl_height", "nr_feeds"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.detail.BlocksGainCompensator.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "bl_width", "bl_height", "nr_feeds"], "arg_types": ["cv2.detail.BlocksGainCompensator", "builtins.int", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of BlocksGainCompensator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.detail.BlocksGainCompensator.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "bl_width", "bl_height", "nr_feeds"], "arg_types": ["cv2.detail.BlocksGainCompensator", "builtins.int", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of BlocksGainCompensator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "bl_width", "bl_height"], "arg_types": ["cv2.detail.BlocksGainCompensator", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of BlocksGainCompensator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "bl_width", "bl_height", "nr_feeds"], "arg_types": ["cv2.detail.BlocksGainCompensator", "builtins.int", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of BlocksGainCompensator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "apply": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "cv2.detail.BlocksGainCompensator.apply", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "index", "corner", "image", "mask"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.detail.BlocksGainCompensator.apply", "name": "apply", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "index", "corner", "image", "mask"], "arg_types": ["cv2.detail.BlocksGainCompensator", "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.Point"}, {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "apply of BlocksGainCompensator", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.detail.BlocksGainCompensator.apply", "name": "apply", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "index", "corner", "image", "mask"], "arg_types": ["cv2.detail.BlocksGainCompensator", "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.Point"}, {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "apply of BlocksGainCompensator", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "index", "corner", "image", "mask"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.detail.BlocksGainCompensator.apply", "name": "apply", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "index", "corner", "image", "mask"], "arg_types": ["cv2.detail.BlocksGainCompensator", "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.Point"}, "cv2.UMat", "cv2.UMat"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "apply of BlocksGainCompensator", "ret_type": "cv2.UMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.detail.BlocksGainCompensator.apply", "name": "apply", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "index", "corner", "image", "mask"], "arg_types": ["cv2.detail.BlocksGainCompensator", "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.Point"}, "cv2.UMat", "cv2.UMat"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "apply of BlocksGainCompensator", "ret_type": "cv2.UMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "index", "corner", "image", "mask"], "arg_types": ["cv2.detail.BlocksGainCompensator", "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.Point"}, {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "apply of BlocksGainCompensator", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "index", "corner", "image", "mask"], "arg_types": ["cv2.detail.BlocksGainCompensator", "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.Point"}, "cv2.UMat", "cv2.UMat"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "apply of BlocksGainCompensator", "ret_type": "cv2.UMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "getMatGains": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "umv"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.detail.BlocksGainCompensator.getMatGains", "name": "getMatGains", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "umv"], "arg_types": ["cv2.detail.BlocksGainCompensator", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getMatGains of BlocksGainCompensator", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "setMatGains": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "umv"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.detail.BlocksGainCompensator.setMatGains", "name": "setMatGains", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "umv"], "arg_types": ["cv2.detail.BlocksGainCompensator", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "setMatGains of BlocksGainCompensator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cv2.detail.BlocksGainCompensator.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cv2.detail.BlocksGainCompensator", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "BundleAdjusterAffine": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["cv2.detail.BundleAdjusterBase"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "cv2.detail.BundleAdjusterAffine", "name": "BundleAdjusterAffine", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "cv2.detail.BundleAdjusterAffine", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "cv2.detail", "mro": ["cv2.detail.BundleAdjusterAffine", "cv2.detail.BundleAdjusterBase", "cv2.detail.Estimator", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.detail.BundleAdjusterAffine.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cv2.detail.BundleAdjusterAffine"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of BundleAdjusterAffine", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cv2.detail.BundleAdjusterAffine.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cv2.detail.BundleAdjusterAffine", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "BundleAdjusterAffinePartial": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["cv2.detail.BundleAdjusterBase"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "cv2.detail.BundleAdjusterAffinePartial", "name": "BundleAdjusterAffinePartial", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "cv2.detail.BundleAdjusterAffinePartial", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "cv2.detail", "mro": ["cv2.detail.BundleAdjusterAffinePartial", "cv2.detail.BundleAdjusterBase", "cv2.detail.Estimator", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.detail.BundleAdjusterAffinePartial.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cv2.detail.BundleAdjusterAffinePartial"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of BundleAdjusterAffinePartial", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cv2.detail.BundleAdjusterAffinePartial.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cv2.detail.BundleAdjusterAffinePartial", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "BundleAdjusterBase": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["cv2.detail.Estimator"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "cv2.detail.BundleAdjusterBase", "name": "BundleAdjusterBase", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "cv2.detail.BundleAdjusterBase", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "cv2.detail", "mro": ["cv2.detail.BundleAdjusterBase", "cv2.detail.Estimator", "builtins.object"], "names": {".class": "SymbolTable", "confThresh": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.detail.BundleAdjusterBase.confThresh", "name": "conf<PERSON><PERSON><PERSON>", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cv2.detail.BundleAdjusterBase"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "confT<PERSON><PERSON> of BundleAdjusterBase", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "refinementMask": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.detail.BundleAdjusterBase.refinementMask", "name": "refinementMask", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cv2.detail.BundleAdjusterBase"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "refinementMask of BundleAdjusterBase", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "setConfThresh": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "conf_thresh"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.detail.BundleAdjusterBase.setConfThresh", "name": "set<PERSON>on<PERSON><PERSON><PERSON><PERSON>", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "conf_thresh"], "arg_types": ["cv2.detail.BundleAdjusterBase", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "setConfT<PERSON>esh of BundleAdjusterBase", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "setRefinementMask": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "mask"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.detail.BundleAdjusterBase.setRefinementMask", "name": "setRefinementMask", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "mask"], "arg_types": ["cv2.detail.BundleAdjusterBase", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "setRefinementMask of BundleAdjusterBase", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "setTermCriteria": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "term_criteria"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.detail.BundleAdjusterBase.setTermCriteria", "name": "setTermCriteria", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "term_criteria"], "arg_types": ["cv2.detail.BundleAdjusterBase", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.TermCriteria"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "setTermCriteria of BundleAdjusterBase", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "termCriteria": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.detail.BundleAdjusterBase.termCriteria", "name": "termCriteria", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cv2.detail.BundleAdjusterBase"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "termCriteria of BundleAdjusterBase", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.TermCriteria"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cv2.detail.BundleAdjusterBase.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cv2.detail.BundleAdjusterBase", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "BundleAdjusterRay": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["cv2.detail.BundleAdjusterBase"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "cv2.detail.BundleAdjusterRay", "name": "BundleAdjusterRay", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "cv2.detail.BundleAdjusterRay", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "cv2.detail", "mro": ["cv2.detail.BundleAdjusterRay", "cv2.detail.BundleAdjusterBase", "cv2.detail.Estimator", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.detail.BundleAdjusterRay.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cv2.detail.BundleAdjusterRay"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of BundleAdjusterRay", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cv2.detail.BundleAdjusterRay.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cv2.detail.BundleAdjusterRay", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "BundleAdjusterReproj": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["cv2.detail.BundleAdjusterBase"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "cv2.detail.BundleAdjusterReproj", "name": "BundleAdjusterReproj", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "cv2.detail.BundleAdjusterReproj", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "cv2.detail", "mro": ["cv2.detail.BundleAdjusterReproj", "cv2.detail.BundleAdjusterBase", "cv2.detail.Estimator", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.detail.BundleAdjusterReproj.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cv2.detail.BundleAdjusterReproj"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of BundleAdjusterReproj", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cv2.detail.BundleAdjusterReproj.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cv2.detail.BundleAdjusterReproj", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CameraParams": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "cv2.detail.CameraParams", "name": "CameraParams", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "cv2.detail.CameraParams", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "cv2.detail", "mro": ["cv2.detail.CameraParams", "builtins.object"], "names": {".class": "SymbolTable", "K": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.detail.CameraParams.K", "name": "K", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cv2.detail.CameraParams"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "K of CameraParams", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "R": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "cv2.detail.CameraParams.R", "name": "R", "type": {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}}}, "aspect": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "cv2.detail.CameraParams.aspect", "name": "aspect", "type": "builtins.float"}}, "focal": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "cv2.detail.CameraParams.focal", "name": "focal", "type": "builtins.float"}}, "ppx": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "cv2.detail.CameraParams.ppx", "name": "ppx", "type": "builtins.float"}}, "ppy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "cv2.detail.CameraParams.ppy", "name": "ppy", "type": "builtins.float"}}, "t": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "cv2.detail.CameraParams.t", "name": "t", "type": {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cv2.detail.CameraParams.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cv2.detail.CameraParams", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ChannelsCompensator": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["cv2.detail.ExposureCompensator"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "cv2.detail.ChannelsCompensator", "name": "ChannelsCompensator", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "cv2.detail.ChannelsCompensator", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "cv2.detail", "mro": ["cv2.detail.ChannelsCompensator", "cv2.detail.ExposureCompensator", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "nr_feeds"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.detail.ChannelsCompensator.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "nr_feeds"], "arg_types": ["cv2.detail.ChannelsCompensator", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ChannelsCompensator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "apply": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "cv2.detail.ChannelsCompensator.apply", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "index", "corner", "image", "mask"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.detail.ChannelsCompensator.apply", "name": "apply", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "index", "corner", "image", "mask"], "arg_types": ["cv2.detail.ChannelsCompensator", "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.Point"}, {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "apply of ChannelsCompensator", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.detail.ChannelsCompensator.apply", "name": "apply", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "index", "corner", "image", "mask"], "arg_types": ["cv2.detail.ChannelsCompensator", "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.Point"}, {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "apply of ChannelsCompensator", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "index", "corner", "image", "mask"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.detail.ChannelsCompensator.apply", "name": "apply", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "index", "corner", "image", "mask"], "arg_types": ["cv2.detail.ChannelsCompensator", "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.Point"}, "cv2.UMat", "cv2.UMat"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "apply of ChannelsCompensator", "ret_type": "cv2.UMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.detail.ChannelsCompensator.apply", "name": "apply", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "index", "corner", "image", "mask"], "arg_types": ["cv2.detail.ChannelsCompensator", "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.Point"}, "cv2.UMat", "cv2.UMat"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "apply of ChannelsCompensator", "ret_type": "cv2.UMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "index", "corner", "image", "mask"], "arg_types": ["cv2.detail.ChannelsCompensator", "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.Point"}, {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "apply of ChannelsCompensator", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "index", "corner", "image", "mask"], "arg_types": ["cv2.detail.ChannelsCompensator", "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.Point"}, "cv2.UMat", "cv2.UMat"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "apply of ChannelsCompensator", "ret_type": "cv2.UMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "getMatGains": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "umv"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.detail.ChannelsCompensator.getMatGains", "name": "getMatGains", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "umv"], "arg_types": ["cv2.detail.ChannelsCompensator", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getMatGains of ChannelsCompensator", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "getNrFeeds": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.detail.ChannelsCompensator.getNrFeeds", "name": "getNrFeeds", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cv2.detail.ChannelsCompensator"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getNrFeeds of ChannelsCompensator", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "getSimilarityThreshold": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.detail.ChannelsCompensator.getSimilarityThreshold", "name": "getSimilarityThreshold", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cv2.detail.ChannelsCompensator"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getSimilarityThreshold of ChannelsCompensator", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "setMatGains": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "umv"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.detail.ChannelsCompensator.setMatGains", "name": "setMatGains", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "umv"], "arg_types": ["cv2.detail.ChannelsCompensator", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "setMatGains of ChannelsCompensator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "setNrFeeds": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "nr_feeds"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.detail.ChannelsCompensator.setNrFeeds", "name": "setNrFeeds", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "nr_feeds"], "arg_types": ["cv2.detail.ChannelsCompensator", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "setNrFeeds of ChannelsCompensator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "setSimilarityThreshold": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "similarity_threshold"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.detail.ChannelsCompensator.setSimilarityThreshold", "name": "setSimilarityThreshold", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "similarity_threshold"], "arg_types": ["cv2.detail.ChannelsCompensator", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "setSimilarityThreshold of ChannelsCompensator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cv2.detail.ChannelsCompensator.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cv2.detail.ChannelsCompensator", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DP_SEAM_FINDER_COLOR": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.detail.DP_SEAM_FINDER_COLOR", "name": "DP_SEAM_FINDER_COLOR", "type": "builtins.int"}}, "DP_SEAM_FINDER_COLOR_GRAD": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.detail.DP_SEAM_FINDER_COLOR_GRAD", "name": "DP_SEAM_FINDER_COLOR_GRAD", "type": "builtins.int"}}, "DpSeamFinder": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["cv2.detail.SeamFinder"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "cv2.detail.DpSeamFinder", "name": "DpSeamFinder", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "cv2.detail.DpSeamFinder", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "cv2.detail", "mro": ["cv2.detail.DpSeamFinder", "cv2.detail.SeamFinder", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "costFunc"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.detail.DpSeamFinder.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "costFunc"], "arg_types": ["cv2.detail.DpSeamFinder", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of DpSeamFinder", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "setCostFunction": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "val"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.detail.DpSeamFinder.setCostFunction", "name": "setCostFunction", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "val"], "arg_types": ["cv2.detail.DpSeamFinder", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "setCostFunction of DpSeamFinder", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cv2.detail.DpSeamFinder.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cv2.detail.DpSeamFinder", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DpSeamFinder_COLOR": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.detail.DpSeamFinder_COLOR", "name": "DpSeamFinder_COLOR", "type": "builtins.int"}}, "DpSeamFinder_COLOR_GRAD": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.detail.DpSeamFinder_COLOR_GRAD", "name": "DpSeamFinder_COLOR_GRAD", "type": "builtins.int"}}, "DpSeamFinder_CostFunction": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "cv2.detail.DpSeamFinder_CostFunction", "line": 116, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "builtins.int"}}, "EXPOSURE_COMPENSATOR_CHANNELS": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.detail.EXPOSURE_COMPENSATOR_CHANNELS", "name": "EXPOSURE_COMPENSATOR_CHANNELS", "type": "builtins.int"}}, "EXPOSURE_COMPENSATOR_CHANNELS_BLOCKS": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.detail.EXPOSURE_COMPENSATOR_CHANNELS_BLOCKS", "name": "EXPOSURE_COMPENSATOR_CHANNELS_BLOCKS", "type": "builtins.int"}}, "EXPOSURE_COMPENSATOR_GAIN": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.detail.EXPOSURE_COMPENSATOR_GAIN", "name": "EXPOSURE_COMPENSATOR_GAIN", "type": "builtins.int"}}, "EXPOSURE_COMPENSATOR_GAIN_BLOCKS": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.detail.EXPOSURE_COMPENSATOR_GAIN_BLOCKS", "name": "EXPOSURE_COMPENSATOR_GAIN_BLOCKS", "type": "builtins.int"}}, "EXPOSURE_COMPENSATOR_NO": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.detail.EXPOSURE_COMPENSATOR_NO", "name": "EXPOSURE_COMPENSATOR_NO", "type": "builtins.int"}}, "Estimator": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "cv2.detail.Estimator", "name": "Estimator", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "cv2.detail.Estimator", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "cv2.detail", "mro": ["cv2.detail.Estimator", "builtins.object"], "names": {".class": "SymbolTable", "apply": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "features", "pairwise_matches", "cameras"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.detail.Estimator.apply", "name": "apply", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "features", "pairwise_matches", "cameras"], "arg_types": ["cv2.detail.Estimator", {".class": "Instance", "args": ["cv2.detail.ImageFeatures"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "Instance", "args": ["cv2.detail.MatchesInfo"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "Instance", "args": ["cv2.detail.CameraParams"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "apply of Estimator", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.bool", {".class": "Instance", "args": ["cv2.detail.CameraParams"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cv2.detail.Estimator.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cv2.detail.Estimator", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ExposureCompensator": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "cv2.detail.ExposureCompensator", "name": "ExposureCompensator", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "cv2.detail.ExposureCompensator", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "cv2.detail", "mro": ["cv2.detail.ExposureCompensator", "builtins.object"], "names": {".class": "SymbolTable", "apply": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "cv2.detail.ExposureCompensator.apply", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "index", "corner", "image", "mask"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.detail.ExposureCompensator.apply", "name": "apply", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "index", "corner", "image", "mask"], "arg_types": ["cv2.detail.ExposureCompensator", "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.Point"}, {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "apply of ExposureCompensator", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.detail.ExposureCompensator.apply", "name": "apply", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "index", "corner", "image", "mask"], "arg_types": ["cv2.detail.ExposureCompensator", "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.Point"}, {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "apply of ExposureCompensator", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "index", "corner", "image", "mask"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.detail.ExposureCompensator.apply", "name": "apply", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "index", "corner", "image", "mask"], "arg_types": ["cv2.detail.ExposureCompensator", "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.Point"}, "cv2.UMat", "cv2.UMat"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "apply of ExposureCompensator", "ret_type": "cv2.UMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.detail.ExposureCompensator.apply", "name": "apply", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "index", "corner", "image", "mask"], "arg_types": ["cv2.detail.ExposureCompensator", "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.Point"}, "cv2.UMat", "cv2.UMat"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "apply of ExposureCompensator", "ret_type": "cv2.UMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "index", "corner", "image", "mask"], "arg_types": ["cv2.detail.ExposureCompensator", "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.Point"}, {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "apply of ExposureCompensator", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "index", "corner", "image", "mask"], "arg_types": ["cv2.detail.ExposureCompensator", "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.Point"}, "cv2.UMat", "cv2.UMat"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "apply of ExposureCompensator", "ret_type": "cv2.UMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "createDefault": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "type"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "cv2.detail.ExposureCompensator.createDefault", "name": "createDefault", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "type"], "arg_types": [{".class": "TypeType", "item": "cv2.detail.ExposureCompensator"}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "createDefault of ExposureCompensator", "ret_type": "cv2.detail.ExposureCompensator", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "cv2.detail.ExposureCompensator.createDefault", "name": "createDefault", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "type"], "arg_types": [{".class": "TypeType", "item": "cv2.detail.ExposureCompensator"}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "createDefault of ExposureCompensator", "ret_type": "cv2.detail.ExposureCompensator", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "feed": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "corners", "images", "masks"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.detail.ExposureCompensator.feed", "name": "feed", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "corners", "images", "masks"], "arg_types": ["cv2.detail.ExposureCompensator", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.Point"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "Instance", "args": ["cv2.UMat"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "Instance", "args": ["cv2.UMat"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "feed of ExposureCompensator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "getMatGains": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "arg1"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.detail.ExposureCompensator.getMatGains", "name": "getMatGains", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "arg1"], "arg_types": ["cv2.detail.ExposureCompensator", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getMatGains of ExposureCompensator", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "getUpdateGain": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.detail.ExposureCompensator.getUpdateGain", "name": "getUpdateGain", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cv2.detail.ExposureCompensator"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getUpdateGain of ExposureCompensator", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "setMatGains": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "arg1"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.detail.ExposureCompensator.setMatGains", "name": "setMatGains", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "arg1"], "arg_types": ["cv2.detail.ExposureCompensator", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "setMatGains of ExposureCompensator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "setUpdateGain": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "b"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.detail.ExposureCompensator.setUpdateGain", "name": "setUpdateGain", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "b"], "arg_types": ["cv2.detail.ExposureCompensator", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "setUpdateGain of ExposureCompensator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cv2.detail.ExposureCompensator.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cv2.detail.ExposureCompensator", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ExposureCompensator_CHANNELS": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.detail.ExposureCompensator_CHANNELS", "name": "ExposureCompensator_CHANNELS", "type": "builtins.int"}}, "ExposureCompensator_CHANNELS_BLOCKS": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.detail.ExposureCompensator_CHANNELS_BLOCKS", "name": "ExposureCompensator_CHANNELS_BLOCKS", "type": "builtins.int"}}, "ExposureCompensator_GAIN": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.detail.ExposureCompensator_GAIN", "name": "ExposureCompensator_GAIN", "type": "builtins.int"}}, "ExposureCompensator_GAIN_BLOCKS": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.detail.ExposureCompensator_GAIN_BLOCKS", "name": "ExposureCompensator_GAIN_BLOCKS", "type": "builtins.int"}}, "ExposureCompensator_NO": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.detail.ExposureCompensator_NO", "name": "ExposureCompensator_NO", "type": "builtins.int"}}, "FeatherBlender": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["cv2.detail.Blender"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "cv2.detail.FeatherBlender", "name": "FeatherBlender", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "cv2.detail.FeatherBlender", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "cv2.detail", "mro": ["cv2.detail.FeatherBlender", "cv2.detail.Blender", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "sharpness"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.detail.FeatherBlender.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "sharpness"], "arg_types": ["cv2.detail.FeatherBlender", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of FeatherBlender", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "blend": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "cv2.detail.FeatherBlender.blend", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "dst", "dst_mask"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.detail.FeatherBlender.blend", "name": "blend", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "dst", "dst_mask"], "arg_types": ["cv2.detail.FeatherBlender", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "blend of FeatherBlender", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.detail.FeatherBlender.blend", "name": "blend", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "dst", "dst_mask"], "arg_types": ["cv2.detail.FeatherBlender", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "blend of FeatherBlender", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "dst", "dst_mask"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.detail.FeatherBlender.blend", "name": "blend", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "dst", "dst_mask"], "arg_types": ["cv2.detail.FeatherBlender", "cv2.UMat", "cv2.UMat"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "blend of FeatherBlender", "ret_type": {".class": "TupleType", "implicit": false, "items": ["cv2.UMat", "cv2.UMat"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.detail.FeatherBlender.blend", "name": "blend", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "dst", "dst_mask"], "arg_types": ["cv2.detail.FeatherBlender", "cv2.UMat", "cv2.UMat"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "blend of FeatherBlender", "ret_type": {".class": "TupleType", "implicit": false, "items": ["cv2.UMat", "cv2.UMat"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "dst", "dst_mask"], "arg_types": ["cv2.detail.FeatherBlender", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "blend of FeatherBlender", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "dst", "dst_mask"], "arg_types": ["cv2.detail.FeatherBlender", "cv2.UMat", "cv2.UMat"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "blend of FeatherBlender", "ret_type": {".class": "TupleType", "implicit": false, "items": ["cv2.UMat", "cv2.UMat"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "createWeightMaps": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "masks", "corners", "weight_maps"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.detail.FeatherBlender.createWeightMaps", "name": "createWeightMaps", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "masks", "corners", "weight_maps"], "arg_types": ["cv2.detail.FeatherBlender", {".class": "Instance", "args": ["cv2.UMat"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.Point"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "Instance", "args": ["cv2.UMat"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "createWeightMaps of FeatherBlender", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.Rect"}, {".class": "Instance", "args": ["cv2.UMat"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "feed": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "cv2.detail.FeatherBlender.feed", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "img", "mask", "tl"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.detail.FeatherBlender.feed", "name": "feed", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "img", "mask", "tl"], "arg_types": ["cv2.detail.FeatherBlender", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.Point"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "feed of FeatherBlender", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.detail.FeatherBlender.feed", "name": "feed", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "img", "mask", "tl"], "arg_types": ["cv2.detail.FeatherBlender", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.Point"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "feed of FeatherBlender", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "img", "mask", "tl"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.detail.FeatherBlender.feed", "name": "feed", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "img", "mask", "tl"], "arg_types": ["cv2.detail.FeatherBlender", "cv2.UMat", "cv2.UMat", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.Point"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "feed of FeatherBlender", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.detail.FeatherBlender.feed", "name": "feed", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "img", "mask", "tl"], "arg_types": ["cv2.detail.FeatherBlender", "cv2.UMat", "cv2.UMat", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.Point"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "feed of FeatherBlender", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "img", "mask", "tl"], "arg_types": ["cv2.detail.FeatherBlender", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.Point"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "feed of FeatherBlender", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "img", "mask", "tl"], "arg_types": ["cv2.detail.FeatherBlender", "cv2.UMat", "cv2.UMat", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.Point"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "feed of FeatherBlender", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "prepare": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "dst_roi"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.detail.FeatherBlender.prepare", "name": "prepare", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "dst_roi"], "arg_types": ["cv2.detail.FeatherBlender", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.Rect"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "prepare of FeatherBlender", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "setSharpness": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "val"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.detail.FeatherBlender.setSharpness", "name": "setSharpness", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "val"], "arg_types": ["cv2.detail.FeatherBlender", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "setSharpness of FeatherBlender", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "sharpness": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.detail.FeatherBlender.sharpness", "name": "sharpness", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cv2.detail.FeatherBlender"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "sharpness of FeatherBlender", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cv2.detail.FeatherBlender.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cv2.detail.FeatherBlender", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FeaturesMatcher": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "cv2.detail.FeaturesMatcher", "name": "FeaturesMatcher", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "cv2.detail.FeaturesMatcher", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "cv2.detail", "mro": ["cv2.detail.FeaturesMatcher", "builtins.object"], "names": {".class": "SymbolTable", "apply": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "features1", "features2"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.detail.FeaturesMatcher.apply", "name": "apply", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "features1", "features2"], "arg_types": ["cv2.detail.FeaturesMatcher", "cv2.detail.ImageFeatures", "cv2.detail.ImageFeatures"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "apply of FeaturesMatcher", "ret_type": "cv2.detail.MatchesInfo", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "apply2": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "features", "mask"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.detail.FeaturesMatcher.apply2", "name": "apply2", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "features", "mask"], "arg_types": ["cv2.detail.FeaturesMatcher", {".class": "Instance", "args": ["cv2.detail.ImageFeatures"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "UnionType", "items": ["cv2.UMat", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "apply2 of FeaturesMatcher", "ret_type": {".class": "Instance", "args": ["cv2.detail.MatchesInfo"], "extra_attrs": null, "type_ref": "typing.Sequence"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "collectGarbage": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.detail.FeaturesMatcher.collectGarbage", "name": "collectGarbage", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cv2.detail.FeaturesMatcher"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "collectGarbage of FeaturesMatcher", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "isThreadSafe": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.detail.FeaturesMatcher.isThreadSafe", "name": "isThreadSafe", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cv2.detail.FeaturesMatcher"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "isThreadSafe of FeaturesMatcher", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cv2.detail.FeaturesMatcher.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cv2.detail.FeaturesMatcher", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "GRAPH_CUT_SEAM_FINDER_BASE_COST_COLOR": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.detail.GRAPH_CUT_SEAM_FINDER_BASE_COST_COLOR", "name": "GRAPH_CUT_SEAM_FINDER_BASE_COST_COLOR", "type": "builtins.int"}}, "GRAPH_CUT_SEAM_FINDER_BASE_COST_COLOR_GRAD": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.detail.GRAPH_CUT_SEAM_FINDER_BASE_COST_COLOR_GRAD", "name": "GRAPH_CUT_SEAM_FINDER_BASE_COST_COLOR_GRAD", "type": "builtins.int"}}, "GainCompensator": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["cv2.detail.ExposureCompensator"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "cv2.detail.GainCompensator", "name": "GainCompensator", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "cv2.detail.GainCompensator", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "cv2.detail", "mro": ["cv2.detail.GainCompensator", "cv2.detail.ExposureCompensator", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "cv2.detail.GainCompensator.__init__", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.detail.GainCompensator.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cv2.detail.GainCompensator"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of GainCompensator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.detail.GainCompensator.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cv2.detail.GainCompensator"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of GainCompensator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "nr_feeds"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.detail.GainCompensator.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "nr_feeds"], "arg_types": ["cv2.detail.GainCompensator", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of GainCompensator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.detail.GainCompensator.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "nr_feeds"], "arg_types": ["cv2.detail.GainCompensator", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of GainCompensator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cv2.detail.GainCompensator"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of GainCompensator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "nr_feeds"], "arg_types": ["cv2.detail.GainCompensator", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of GainCompensator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "apply": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "cv2.detail.GainCompensator.apply", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "index", "corner", "image", "mask"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.detail.GainCompensator.apply", "name": "apply", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "index", "corner", "image", "mask"], "arg_types": ["cv2.detail.GainCompensator", "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.Point"}, {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "apply of GainCompensator", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.detail.GainCompensator.apply", "name": "apply", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "index", "corner", "image", "mask"], "arg_types": ["cv2.detail.GainCompensator", "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.Point"}, {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "apply of GainCompensator", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "index", "corner", "image", "mask"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.detail.GainCompensator.apply", "name": "apply", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "index", "corner", "image", "mask"], "arg_types": ["cv2.detail.GainCompensator", "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.Point"}, "cv2.UMat", "cv2.UMat"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "apply of GainCompensator", "ret_type": "cv2.UMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.detail.GainCompensator.apply", "name": "apply", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "index", "corner", "image", "mask"], "arg_types": ["cv2.detail.GainCompensator", "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.Point"}, "cv2.UMat", "cv2.UMat"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "apply of GainCompensator", "ret_type": "cv2.UMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "index", "corner", "image", "mask"], "arg_types": ["cv2.detail.GainCompensator", "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.Point"}, {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "apply of GainCompensator", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "index", "corner", "image", "mask"], "arg_types": ["cv2.detail.GainCompensator", "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.Point"}, "cv2.UMat", "cv2.UMat"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "apply of GainCompensator", "ret_type": "cv2.UMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "getMatGains": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "umv"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.detail.GainCompensator.getMatGains", "name": "getMatGains", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "umv"], "arg_types": ["cv2.detail.GainCompensator", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getMatGains of GainCompensator", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "getNrFeeds": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.detail.GainCompensator.getNrFeeds", "name": "getNrFeeds", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cv2.detail.GainCompensator"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getNrFeeds of GainCompensator", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "getSimilarityThreshold": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.detail.GainCompensator.getSimilarityThreshold", "name": "getSimilarityThreshold", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cv2.detail.GainCompensator"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getSimila<PERSON><PERSON><PERSON><PERSON><PERSON> of GainCompensator", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "setMatGains": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "umv"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.detail.GainCompensator.setMatGains", "name": "setMatGains", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "umv"], "arg_types": ["cv2.detail.GainCompensator", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "setMatGains of GainCompensator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "setNrFeeds": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "nr_feeds"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.detail.GainCompensator.setNrFeeds", "name": "setNrFeeds", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "nr_feeds"], "arg_types": ["cv2.detail.GainCompensator", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "setNrFeeds of GainCompensator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "setSimilarityThreshold": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "similarity_threshold"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.detail.GainCompensator.setSimilarityThreshold", "name": "setSimilarityThreshold", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "similarity_threshold"], "arg_types": ["cv2.detail.GainCompensator", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "setS<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> of GainCompensator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cv2.detail.GainCompensator.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cv2.detail.GainCompensator", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "GraphCutSeamFinder": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "cv2.detail.GraphCutSeamFinder", "name": "GraphCutSeamFinder", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "cv2.detail.GraphCutSeamFinder", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "cv2.detail", "mro": ["cv2.detail.GraphCutSeamFinder", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "cost_type", "terminal_cost", "bad_region_penalty"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.detail.GraphCutSeamFinder.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "cost_type", "terminal_cost", "bad_region_penalty"], "arg_types": ["cv2.detail.GraphCutSeamFinder", "builtins.str", "builtins.float", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of GraphCutSeamFinder", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "find": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "src", "corners", "masks"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.detail.GraphCutSeamFinder.find", "name": "find", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "src", "corners", "masks"], "arg_types": ["cv2.detail.GraphCutSeamFinder", {".class": "Instance", "args": ["cv2.UMat"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.Point"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "Instance", "args": ["cv2.UMat"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "find of GraphCutSeamFinder", "ret_type": {".class": "Instance", "args": ["cv2.UMat"], "extra_attrs": null, "type_ref": "typing.Sequence"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cv2.detail.GraphCutSeamFinder.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cv2.detail.GraphCutSeamFinder", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "GraphCutSeamFinderBase_COST_COLOR": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.detail.GraphCutSeamFinderBase_COST_COLOR", "name": "GraphCutSeamFinderBase_COST_COLOR", "type": "builtins.int"}}, "GraphCutSeamFinderBase_COST_COLOR_GRAD": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.detail.GraphCutSeamFinderBase_COST_COLOR_GRAD", "name": "GraphCutSeamFinderBase_COST_COLOR_GRAD", "type": "builtins.int"}}, "GraphCutSeamFinderBase_CostType": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "cv2.detail.GraphCutSeamFinderBase_CostType", "line": 128, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "builtins.int"}}, "HomographyBasedEstimator": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["cv2.detail.Estimator"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "cv2.detail.HomographyBasedEstimator", "name": "HomographyBasedEstimator", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "cv2.detail.HomographyBasedEstimator", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "cv2.detail", "mro": ["cv2.detail.HomographyBasedEstimator", "cv2.detail.Estimator", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "is_focals_estimated"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.detail.HomographyBasedEstimator.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "is_focals_estimated"], "arg_types": ["cv2.detail.HomographyBasedEstimator", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of HomographyBasedEstimator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cv2.detail.HomographyBasedEstimator.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cv2.detail.HomographyBasedEstimator", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ImageFeatures": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "cv2.detail.ImageFeatures", "name": "ImageFeatures", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "cv2.detail.ImageFeatures", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "cv2.detail", "mro": ["cv2.detail.ImageFeatures", "builtins.object"], "names": {".class": "SymbolTable", "descriptors": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "cv2.detail.ImageFeatures.descriptors", "name": "descriptors", "type": "cv2.UMat"}}, "getKeypoints": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.detail.ImageFeatures.getKeypoints", "name": "getKeypoints", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cv2.detail.ImageFeatures"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getKeypoints of ImageFeatures", "ret_type": {".class": "Instance", "args": ["cv2.KeyPoint"], "extra_attrs": null, "type_ref": "typing.Sequence"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "img_idx": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "cv2.detail.ImageFeatures.img_idx", "name": "img_idx", "type": "builtins.int"}}, "img_size": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "cv2.detail.ImageFeatures.img_size", "name": "img_size", "type": {".class": "TypeAliasType", "args": [], "type_ref": "cv2.<PERSON>.Size"}}}, "keypoints": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "cv2.detail.ImageFeatures.keypoints", "name": "keypoints", "type": {".class": "Instance", "args": ["cv2.KeyPoint"], "extra_attrs": null, "type_ref": "typing.Sequence"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cv2.detail.ImageFeatures.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cv2.detail.ImageFeatures", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "MatchesInfo": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "cv2.detail.MatchesInfo", "name": "MatchesInfo", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "cv2.detail.MatchesInfo", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "cv2.detail", "mro": ["cv2.detail.MatchesInfo", "builtins.object"], "names": {".class": "SymbolTable", "H": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "cv2.detail.MatchesInfo.H", "name": "H", "type": {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}}}, "confidence": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "cv2.detail.MatchesInfo.confidence", "name": "confidence", "type": "builtins.float"}}, "dst_img_idx": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "cv2.detail.MatchesInfo.dst_img_idx", "name": "dst_img_idx", "type": "builtins.int"}}, "getInliers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.detail.MatchesInfo.getInliers", "name": "getInliers", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cv2.detail.MatchesInfo"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getInliers of MatchesInfo", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint8"}], "extra_attrs": null, "type_ref": "numpy.dtype"}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "getMatches": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.detail.MatchesInfo.getMatches", "name": "getMatches", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cv2.detail.MatchesInfo"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getMatches of MatchesInfo", "ret_type": {".class": "Instance", "args": ["cv2.DMatch"], "extra_attrs": null, "type_ref": "typing.Sequence"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "inliers_mask": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "cv2.detail.MatchesInfo.inliers_mask", "name": "inliers_mask", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint8"}], "extra_attrs": null, "type_ref": "numpy.dtype"}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}}}, "matches": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "cv2.detail.MatchesInfo.matches", "name": "matches", "type": {".class": "Instance", "args": ["cv2.DMatch"], "extra_attrs": null, "type_ref": "typing.Sequence"}}}, "num_inliers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "cv2.detail.MatchesInfo.num_inliers", "name": "num_inliers", "type": "builtins.int"}}, "src_img_idx": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "cv2.detail.MatchesInfo.src_img_idx", "name": "src_img_idx", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cv2.detail.MatchesInfo.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cv2.detail.MatchesInfo", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "MultiBandBlender": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["cv2.detail.Blender"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "cv2.detail.MultiBandBlender", "name": "MultiBandBlender", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "cv2.detail.MultiBandBlender", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "cv2.detail", "mro": ["cv2.detail.MultiBandBlender", "cv2.detail.Blender", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "try_gpu", "num_bands", "weight_type"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.detail.MultiBandBlender.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "try_gpu", "num_bands", "weight_type"], "arg_types": ["cv2.detail.MultiBandBlender", "builtins.int", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of MultiBandBlender", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "blend": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "cv2.detail.MultiBandBlender.blend", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "dst", "dst_mask"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.detail.MultiBandBlender.blend", "name": "blend", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "dst", "dst_mask"], "arg_types": ["cv2.detail.MultiBandBlender", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "blend of MultiBandBlender", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.detail.MultiBandBlender.blend", "name": "blend", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "dst", "dst_mask"], "arg_types": ["cv2.detail.MultiBandBlender", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "blend of MultiBandBlender", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "dst", "dst_mask"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.detail.MultiBandBlender.blend", "name": "blend", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "dst", "dst_mask"], "arg_types": ["cv2.detail.MultiBandBlender", "cv2.UMat", "cv2.UMat"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "blend of MultiBandBlender", "ret_type": {".class": "TupleType", "implicit": false, "items": ["cv2.UMat", "cv2.UMat"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.detail.MultiBandBlender.blend", "name": "blend", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "dst", "dst_mask"], "arg_types": ["cv2.detail.MultiBandBlender", "cv2.UMat", "cv2.UMat"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "blend of MultiBandBlender", "ret_type": {".class": "TupleType", "implicit": false, "items": ["cv2.UMat", "cv2.UMat"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "dst", "dst_mask"], "arg_types": ["cv2.detail.MultiBandBlender", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "blend of MultiBandBlender", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "dst", "dst_mask"], "arg_types": ["cv2.detail.MultiBandBlender", "cv2.UMat", "cv2.UMat"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "blend of MultiBandBlender", "ret_type": {".class": "TupleType", "implicit": false, "items": ["cv2.UMat", "cv2.UMat"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "feed": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "cv2.detail.MultiBandBlender.feed", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "img", "mask", "tl"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.detail.MultiBandBlender.feed", "name": "feed", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "img", "mask", "tl"], "arg_types": ["cv2.detail.MultiBandBlender", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.Point"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "feed of MultiBandBlender", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.detail.MultiBandBlender.feed", "name": "feed", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "img", "mask", "tl"], "arg_types": ["cv2.detail.MultiBandBlender", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.Point"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "feed of MultiBandBlender", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "img", "mask", "tl"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.detail.MultiBandBlender.feed", "name": "feed", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "img", "mask", "tl"], "arg_types": ["cv2.detail.MultiBandBlender", "cv2.UMat", "cv2.UMat", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.Point"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "feed of MultiBandBlender", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.detail.MultiBandBlender.feed", "name": "feed", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "img", "mask", "tl"], "arg_types": ["cv2.detail.MultiBandBlender", "cv2.UMat", "cv2.UMat", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.Point"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "feed of MultiBandBlender", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "img", "mask", "tl"], "arg_types": ["cv2.detail.MultiBandBlender", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.Point"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "feed of MultiBandBlender", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "img", "mask", "tl"], "arg_types": ["cv2.detail.MultiBandBlender", "cv2.UMat", "cv2.UMat", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.Point"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "feed of MultiBandBlender", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "numBands": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.detail.MultiBandBlender.numBands", "name": "numBands", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cv2.detail.MultiBandBlender"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "numBands of MultiBandBlender", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "prepare": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "dst_roi"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.detail.MultiBandBlender.prepare", "name": "prepare", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "dst_roi"], "arg_types": ["cv2.detail.MultiBandBlender", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.Rect"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "prepare of MultiBandBlender", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "setNumBands": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "val"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.detail.MultiBandBlender.setNumBands", "name": "setNumBands", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "val"], "arg_types": ["cv2.detail.MultiBandBlender", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "setNumBands of MultiBandBlender", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cv2.detail.MultiBandBlender.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cv2.detail.MultiBandBlender", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "NoBundleAdjuster": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["cv2.detail.BundleAdjusterBase"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "cv2.detail.NoBundleAdjuster", "name": "NoBundleAdjuster", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "cv2.detail.NoBundleAdjuster", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "cv2.detail", "mro": ["cv2.detail.NoBundleAdjuster", "cv2.detail.BundleAdjusterBase", "cv2.detail.Estimator", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.detail.NoBundleAdjuster.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cv2.detail.NoBundleAdjuster"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of NoBundleAdjuster", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cv2.detail.NoBundleAdjuster.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cv2.detail.NoBundleAdjuster", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "NoExposureCompensator": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["cv2.detail.ExposureCompensator"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "cv2.detail.NoExposureCompensator", "name": "NoExposureCompensator", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "cv2.detail.NoExposureCompensator", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "cv2.detail", "mro": ["cv2.detail.NoExposureCompensator", "cv2.detail.ExposureCompensator", "builtins.object"], "names": {".class": "SymbolTable", "apply": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "cv2.detail.NoExposureCompensator.apply", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "arg1", "arg2", "arg3", "arg4"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.detail.NoExposureCompensator.apply", "name": "apply", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "arg1", "arg2", "arg3", "arg4"], "arg_types": ["cv2.detail.NoExposureCompensator", "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.Point"}, {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "apply of NoExposureCompensator", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.detail.NoExposureCompensator.apply", "name": "apply", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "arg1", "arg2", "arg3", "arg4"], "arg_types": ["cv2.detail.NoExposureCompensator", "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.Point"}, {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "apply of NoExposureCompensator", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "arg1", "arg2", "arg3", "arg4"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.detail.NoExposureCompensator.apply", "name": "apply", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "arg1", "arg2", "arg3", "arg4"], "arg_types": ["cv2.detail.NoExposureCompensator", "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.Point"}, "cv2.UMat", "cv2.UMat"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "apply of NoExposureCompensator", "ret_type": "cv2.UMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.detail.NoExposureCompensator.apply", "name": "apply", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "arg1", "arg2", "arg3", "arg4"], "arg_types": ["cv2.detail.NoExposureCompensator", "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.Point"}, "cv2.UMat", "cv2.UMat"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "apply of NoExposureCompensator", "ret_type": "cv2.UMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "arg1", "arg2", "arg3", "arg4"], "arg_types": ["cv2.detail.NoExposureCompensator", "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.Point"}, {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "apply of NoExposureCompensator", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "arg1", "arg2", "arg3", "arg4"], "arg_types": ["cv2.detail.NoExposureCompensator", "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.Point"}, "cv2.UMat", "cv2.UMat"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "apply of NoExposureCompensator", "ret_type": "cv2.UMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "getMatGains": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "umv"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.detail.NoExposureCompensator.getMatGains", "name": "getMatGains", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "umv"], "arg_types": ["cv2.detail.NoExposureCompensator", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getMatGains of NoExposureCompensator", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "setMatGains": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "umv"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.detail.NoExposureCompensator.setMatGains", "name": "setMatGains", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "umv"], "arg_types": ["cv2.detail.NoExposureCompensator", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "setMatGains of NoExposureCompensator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cv2.detail.NoExposureCompensator.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cv2.detail.NoExposureCompensator", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "NoSeamFinder": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["cv2.detail.SeamFinder"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "cv2.detail.NoSeamFinder", "name": "NoSeamFinder", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "cv2.detail.NoSeamFinder", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "cv2.detail", "mro": ["cv2.detail.NoSeamFinder", "cv2.detail.SeamFinder", "builtins.object"], "names": {".class": "SymbolTable", "find": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "arg1", "arg2", "arg3"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.detail.NoSeamFinder.find", "name": "find", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "arg1", "arg2", "arg3"], "arg_types": ["cv2.detail.NoSeamFinder", {".class": "Instance", "args": ["cv2.UMat"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.Point"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "Instance", "args": ["cv2.UMat"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "find of NoSeamFinder", "ret_type": {".class": "Instance", "args": ["cv2.UMat"], "extra_attrs": null, "type_ref": "typing.Sequence"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cv2.detail.NoSeamFinder.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cv2.detail.NoSeamFinder", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "OPAQUE_KIND_CV_BOOL": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.detail.OPAQUE_KIND_CV_BOOL", "name": "OPAQUE_KIND_CV_BOOL", "type": "builtins.int"}}, "OPAQUE_KIND_CV_DOUBLE": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.detail.OPAQUE_KIND_CV_DOUBLE", "name": "OPAQUE_KIND_CV_DOUBLE", "type": "builtins.int"}}, "OPAQUE_KIND_CV_DRAW_PRIM": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.detail.OPAQUE_KIND_CV_DRAW_PRIM", "name": "OPAQUE_KIND_CV_DRAW_PRIM", "type": "builtins.int"}}, "OPAQUE_KIND_CV_FLOAT": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.detail.OPAQUE_KIND_CV_FLOAT", "name": "OPAQUE_KIND_CV_FLOAT", "type": "builtins.int"}}, "OPAQUE_KIND_CV_INT": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.detail.OPAQUE_KIND_CV_INT", "name": "OPAQUE_KIND_CV_INT", "type": "builtins.int"}}, "OPAQUE_KIND_CV_INT64": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.detail.OPAQUE_KIND_CV_INT64", "name": "OPAQUE_KIND_CV_INT64", "type": "builtins.int"}}, "OPAQUE_KIND_CV_MAT": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.detail.OPAQUE_KIND_CV_MAT", "name": "OPAQUE_KIND_CV_MAT", "type": "builtins.int"}}, "OPAQUE_KIND_CV_POINT": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.detail.OPAQUE_KIND_CV_POINT", "name": "OPAQUE_KIND_CV_POINT", "type": "builtins.int"}}, "OPAQUE_KIND_CV_POINT2F": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.detail.OPAQUE_KIND_CV_POINT2F", "name": "OPAQUE_KIND_CV_POINT2F", "type": "builtins.int"}}, "OPAQUE_KIND_CV_POINT3F": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.detail.OPAQUE_KIND_CV_POINT3F", "name": "OPAQUE_KIND_CV_POINT3F", "type": "builtins.int"}}, "OPAQUE_KIND_CV_RECT": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.detail.OPAQUE_KIND_CV_RECT", "name": "OPAQUE_KIND_CV_RECT", "type": "builtins.int"}}, "OPAQUE_KIND_CV_SCALAR": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.detail.OPAQUE_KIND_CV_SCALAR", "name": "OPAQUE_KIND_CV_SCALAR", "type": "builtins.int"}}, "OPAQUE_KIND_CV_SIZE": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.detail.OPAQUE_KIND_CV_SIZE", "name": "OPAQUE_KIND_CV_SIZE", "type": "builtins.int"}}, "OPAQUE_KIND_CV_STRING": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.detail.OPAQUE_KIND_CV_STRING", "name": "OPAQUE_KIND_CV_STRING", "type": "builtins.int"}}, "OPAQUE_KIND_CV_UINT64": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.detail.OPAQUE_KIND_CV_UINT64", "name": "OPAQUE_KIND_CV_UINT64", "type": "builtins.int"}}, "OPAQUE_KIND_CV_UNKNOWN": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.detail.OPAQUE_KIND_CV_UNKNOWN", "name": "OPAQUE_KIND_CV_UNKNOWN", "type": "builtins.int"}}, "OpaqueKind": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "cv2.detail.OpaqueKind", "line": 62, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "builtins.int"}}, "OpaqueKind_CV_BOOL": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.detail.OpaqueKind_CV_BOOL", "name": "OpaqueKind_CV_BOOL", "type": "builtins.int"}}, "OpaqueKind_CV_DOUBLE": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.detail.OpaqueKind_CV_DOUBLE", "name": "OpaqueKind_CV_DOUBLE", "type": "builtins.int"}}, "OpaqueKind_CV_DRAW_PRIM": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.detail.OpaqueKind_CV_DRAW_PRIM", "name": "OpaqueKind_CV_DRAW_PRIM", "type": "builtins.int"}}, "OpaqueKind_CV_FLOAT": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.detail.OpaqueKind_CV_FLOAT", "name": "OpaqueKind_CV_FLOAT", "type": "builtins.int"}}, "OpaqueKind_CV_INT": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.detail.OpaqueKind_CV_INT", "name": "OpaqueKind_CV_INT", "type": "builtins.int"}}, "OpaqueKind_CV_INT64": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.detail.OpaqueKind_CV_INT64", "name": "OpaqueKind_CV_INT64", "type": "builtins.int"}}, "OpaqueKind_CV_MAT": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.detail.OpaqueKind_CV_MAT", "name": "OpaqueKind_CV_MAT", "type": "builtins.int"}}, "OpaqueKind_CV_POINT": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.detail.OpaqueKind_CV_POINT", "name": "OpaqueKind_CV_POINT", "type": "builtins.int"}}, "OpaqueKind_CV_POINT2F": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.detail.OpaqueKind_CV_POINT2F", "name": "OpaqueKind_CV_POINT2F", "type": "builtins.int"}}, "OpaqueKind_CV_POINT3F": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.detail.OpaqueKind_CV_POINT3F", "name": "OpaqueKind_CV_POINT3F", "type": "builtins.int"}}, "OpaqueKind_CV_RECT": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.detail.OpaqueKind_CV_RECT", "name": "OpaqueKind_CV_RECT", "type": "builtins.int"}}, "OpaqueKind_CV_SCALAR": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.detail.OpaqueKind_CV_SCALAR", "name": "OpaqueKind_CV_SCALAR", "type": "builtins.int"}}, "OpaqueKind_CV_SIZE": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.detail.OpaqueKind_CV_SIZE", "name": "OpaqueKind_CV_SIZE", "type": "builtins.int"}}, "OpaqueKind_CV_STRING": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.detail.OpaqueKind_CV_STRING", "name": "OpaqueKind_CV_STRING", "type": "builtins.int"}}, "OpaqueKind_CV_UINT64": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.detail.OpaqueKind_CV_UINT64", "name": "OpaqueKind_CV_UINT64", "type": "builtins.int"}}, "OpaqueKind_CV_UNKNOWN": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.detail.OpaqueKind_CV_UNKNOWN", "name": "OpaqueKind_CV_UNKNOWN", "type": "builtins.int"}}, "PairwiseSeamFinder": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["cv2.detail.SeamFinder"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "cv2.detail.PairwiseSeamFinder", "name": "PairwiseSeamFinder", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "cv2.detail.PairwiseSeamFinder", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "cv2.detail", "mro": ["cv2.detail.PairwiseSeamFinder", "cv2.detail.SeamFinder", "builtins.object"], "names": {".class": "SymbolTable", "find": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "src", "corners", "masks"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.detail.PairwiseSeamFinder.find", "name": "find", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "src", "corners", "masks"], "arg_types": ["cv2.detail.PairwiseSeamFinder", {".class": "Instance", "args": ["cv2.UMat"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.Point"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "Instance", "args": ["cv2.UMat"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "find of PairwiseSeamFinder", "ret_type": {".class": "Instance", "args": ["cv2.UMat"], "extra_attrs": null, "type_ref": "typing.Sequence"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cv2.detail.PairwiseSeamFinder.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cv2.detail.PairwiseSeamFinder", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ProjectorBase": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "cv2.detail.ProjectorBase", "name": "ProjectorBase", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "cv2.detail.ProjectorBase", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "cv2.detail", "mro": ["cv2.detail.ProjectorBase", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cv2.detail.ProjectorBase.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cv2.detail.ProjectorBase", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SEAM_FINDER_DP_SEAM": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.detail.SEAM_FINDER_DP_SEAM", "name": "SEAM_FINDER_DP_SEAM", "type": "builtins.int"}}, "SEAM_FINDER_NO": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.detail.SEAM_FINDER_NO", "name": "SEAM_FINDER_NO", "type": "builtins.int"}}, "SEAM_FINDER_VORONOI_SEAM": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.detail.SEAM_FINDER_VORONOI_SEAM", "name": "SEAM_FINDER_VORONOI_SEAM", "type": "builtins.int"}}, "SeamFinder": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "cv2.detail.SeamFinder", "name": "SeamFinder", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "cv2.detail.SeamFinder", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "cv2.detail", "mro": ["cv2.detail.SeamFinder", "builtins.object"], "names": {".class": "SymbolTable", "createDefault": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "type"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "cv2.detail.SeamFinder.createDefault", "name": "createDefault", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "type"], "arg_types": [{".class": "TypeType", "item": "cv2.detail.SeamFinder"}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "createDefault of SeamFinder", "ret_type": "cv2.detail.SeamFinder", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "cv2.detail.SeamFinder.createDefault", "name": "createDefault", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "type"], "arg_types": [{".class": "TypeType", "item": "cv2.detail.SeamFinder"}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "createDefault of SeamFinder", "ret_type": "cv2.detail.SeamFinder", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "find": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "src", "corners", "masks"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.detail.SeamFinder.find", "name": "find", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "src", "corners", "masks"], "arg_types": ["cv2.detail.SeamFinder", {".class": "Instance", "args": ["cv2.UMat"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.Point"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "Instance", "args": ["cv2.UMat"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "find of Seam<PERSON>inder", "ret_type": {".class": "Instance", "args": ["cv2.UMat"], "extra_attrs": null, "type_ref": "typing.Sequence"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cv2.detail.SeamFinder.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cv2.detail.SeamFinder", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SeamFinder_DP_SEAM": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.detail.SeamFinder_DP_SEAM", "name": "SeamFinder_DP_SEAM", "type": "builtins.int"}}, "SeamFinder_NO": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.detail.SeamFinder_NO", "name": "SeamFinder_NO", "type": "builtins.int"}}, "SeamFinder_VORONOI_SEAM": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.detail.SeamFinder_VORONOI_SEAM", "name": "SeamFinder_VORONOI_SEAM", "type": "builtins.int"}}, "SphericalProjector": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["cv2.detail.ProjectorBase"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "cv2.detail.SphericalProjector", "name": "SphericalProjector", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "cv2.detail.SphericalProjector", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "cv2.detail", "mro": ["cv2.detail.SphericalProjector", "cv2.detail.ProjectorBase", "builtins.object"], "names": {".class": "SymbolTable", "mapBackward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "u", "v", "x", "y"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.detail.SphericalProjector.mapBackward", "name": "mapBackward", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "u", "v", "x", "y"], "arg_types": ["cv2.detail.SphericalProjector", "builtins.float", "builtins.float", "builtins.float", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "mapBackward of SphericalProjector", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "mapForward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "x", "y", "u", "v"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.detail.SphericalProjector.mapForward", "name": "mapForward", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "x", "y", "u", "v"], "arg_types": ["cv2.detail.SphericalProjector", "builtins.float", "builtins.float", "builtins.float", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "mapForward of SphericalProjector", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cv2.detail.SphericalProjector.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cv2.detail.SphericalProjector", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TEST_CUSTOM": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.detail.TEST_CUSTOM", "name": "TEST_CUSTOM", "type": "builtins.int"}}, "TEST_EQ": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.detail.TEST_EQ", "name": "TEST_EQ", "type": "builtins.int"}}, "TEST_GE": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.detail.TEST_GE", "name": "TEST_GE", "type": "builtins.int"}}, "TEST_GT": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.detail.TEST_GT", "name": "TEST_GT", "type": "builtins.int"}}, "TEST_LE": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.detail.TEST_LE", "name": "TEST_LE", "type": "builtins.int"}}, "TEST_LT": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.detail.TEST_LT", "name": "TEST_LT", "type": "builtins.int"}}, "TEST_NE": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.detail.TEST_NE", "name": "TEST_NE", "type": "builtins.int"}}, "TIMELAPSER_AS_IS": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.detail.TIMELAPSER_AS_IS", "name": "TIMELAPSER_AS_IS", "type": "builtins.int"}}, "TIMELAPSER_CROP": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.detail.TIMELAPSER_CROP", "name": "TIMELAPSER_CROP", "type": "builtins.int"}}, "TRACKER_SAMPLER_CSC_MODE_DETECT": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.detail.TRACKER_SAMPLER_CSC_MODE_DETECT", "name": "TRACKER_SAMPLER_CSC_MODE_DETECT", "type": "builtins.int"}}, "TRACKER_SAMPLER_CSC_MODE_INIT_NEG": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.detail.TRACKER_SAMPLER_CSC_MODE_INIT_NEG", "name": "TRACKER_SAMPLER_CSC_MODE_INIT_NEG", "type": "builtins.int"}}, "TRACKER_SAMPLER_CSC_MODE_INIT_POS": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.detail.TRACKER_SAMPLER_CSC_MODE_INIT_POS", "name": "TRACKER_SAMPLER_CSC_MODE_INIT_POS", "type": "builtins.int"}}, "TRACKER_SAMPLER_CSC_MODE_TRACK_NEG": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.detail.TRACKER_SAMPLER_CSC_MODE_TRACK_NEG", "name": "TRACKER_SAMPLER_CSC_MODE_TRACK_NEG", "type": "builtins.int"}}, "TRACKER_SAMPLER_CSC_MODE_TRACK_POS": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.detail.TRACKER_SAMPLER_CSC_MODE_TRACK_POS", "name": "TRACKER_SAMPLER_CSC_MODE_TRACK_POS", "type": "builtins.int"}}, "TestOp": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "cv2.detail.TestOp", "line": 21, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "builtins.int"}}, "Timelapser": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "cv2.detail.Timelapser", "name": "Timelapser", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "cv2.detail.Timelapser", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "cv2.detail", "mro": ["cv2.detail.Timelapser", "builtins.object"], "names": {".class": "SymbolTable", "createDefault": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "type"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "cv2.detail.Timelapser.createDefault", "name": "createDefault", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "type"], "arg_types": [{".class": "TypeType", "item": "cv2.detail.Timelapser"}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "createDefault of Timelapser", "ret_type": "cv2.detail.Timelapser", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "cv2.detail.Timelapser.createDefault", "name": "createDefault", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "type"], "arg_types": [{".class": "TypeType", "item": "cv2.detail.Timelapser"}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "createDefault of Timelapser", "ret_type": "cv2.detail.Timelapser", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "getDst": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.detail.Timelapser.getDst", "name": "getDst", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cv2.detail.Timelapser"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getDst of Timelapser", "ret_type": "cv2.UMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "initialize": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "corners", "sizes"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.detail.Timelapser.initialize", "name": "initialize", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "corners", "sizes"], "arg_types": ["cv2.detail.Timelapser", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.Point"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.<PERSON>.Size"}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "initialize of Timelapser", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "process": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "cv2.detail.Timelapser.process", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "img", "mask", "tl"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.detail.Timelapser.process", "name": "process", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "img", "mask", "tl"], "arg_types": ["cv2.detail.Timelapser", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.Point"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "process of Timelapser", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.detail.Timelapser.process", "name": "process", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "img", "mask", "tl"], "arg_types": ["cv2.detail.Timelapser", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.Point"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "process of Timelapser", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "img", "mask", "tl"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.detail.Timelapser.process", "name": "process", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "img", "mask", "tl"], "arg_types": ["cv2.detail.Timelapser", "cv2.UMat", "cv2.UMat", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.Point"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "process of Timelapser", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.detail.Timelapser.process", "name": "process", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "img", "mask", "tl"], "arg_types": ["cv2.detail.Timelapser", "cv2.UMat", "cv2.UMat", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.Point"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "process of Timelapser", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "img", "mask", "tl"], "arg_types": ["cv2.detail.Timelapser", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.Point"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "process of Timelapser", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "img", "mask", "tl"], "arg_types": ["cv2.detail.Timelapser", "cv2.UMat", "cv2.UMat", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.Point"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "process of Timelapser", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cv2.detail.Timelapser.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cv2.detail.Timelapser", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TimelapserCrop": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["cv2.detail.Timelapser"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "cv2.detail.TimelapserCrop", "name": "TimelapserCrop", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "cv2.detail.TimelapserCrop", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "cv2.detail", "mro": ["cv2.detail.TimelapserCrop", "cv2.detail.Timelapser", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cv2.detail.TimelapserCrop.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cv2.detail.TimelapserCrop", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Timelapser_AS_IS": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.detail.Timelapser_AS_IS", "name": "Timelapser_AS_IS", "type": "builtins.int"}}, "Timelapser_CROP": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.detail.Timelapser_CROP", "name": "Timelapser_CROP", "type": "builtins.int"}}, "TrackerSamplerCSC_MODE": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "cv2.detail.TrackerSamplerCSC_MODE", "line": 141, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "builtins.int"}}, "TrackerSamplerCSC_MODE_DETECT": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.detail.TrackerSamplerCSC_MODE_DETECT", "name": "TrackerSamplerCSC_MODE_DETECT", "type": "builtins.int"}}, "TrackerSamplerCSC_MODE_INIT_NEG": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.detail.TrackerSamplerCSC_MODE_INIT_NEG", "name": "TrackerSamplerCSC_MODE_INIT_NEG", "type": "builtins.int"}}, "TrackerSamplerCSC_MODE_INIT_POS": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.detail.TrackerSamplerCSC_MODE_INIT_POS", "name": "TrackerSamplerCSC_MODE_INIT_POS", "type": "builtins.int"}}, "TrackerSamplerCSC_MODE_TRACK_NEG": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.detail.TrackerSamplerCSC_MODE_TRACK_NEG", "name": "TrackerSamplerCSC_MODE_TRACK_NEG", "type": "builtins.int"}}, "TrackerSamplerCSC_MODE_TRACK_POS": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.detail.TrackerSamplerCSC_MODE_TRACK_POS", "name": "TrackerSamplerCSC_MODE_TRACK_POS", "type": "builtins.int"}}, "VoronoiSeamFinder": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["cv2.detail.PairwiseSeamFinder"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "cv2.detail.VoronoiSeamFinder", "name": "VoronoiSeamFinder", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "cv2.detail.VoronoiSeamFinder", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "cv2.detail", "mro": ["cv2.detail.VoronoiSeamFinder", "cv2.detail.PairwiseSeamFinder", "cv2.detail.SeamFinder", "builtins.object"], "names": {".class": "SymbolTable", "find": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "src", "corners", "masks"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.detail.VoronoiSeamFinder.find", "name": "find", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "src", "corners", "masks"], "arg_types": ["cv2.detail.VoronoiSeamFinder", {".class": "Instance", "args": ["cv2.UMat"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.Point"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "Instance", "args": ["cv2.UMat"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "find of VoronoiSeamFinder", "ret_type": {".class": "Instance", "args": ["cv2.UMat"], "extra_attrs": null, "type_ref": "typing.Sequence"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cv2.detail.VoronoiSeamFinder.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cv2.detail.VoronoiSeamFinder", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "WAVE_CORRECT_AUTO": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.detail.WAVE_CORRECT_AUTO", "name": "WAVE_CORRECT_AUTO", "type": "builtins.int"}}, "WAVE_CORRECT_HORIZ": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.detail.WAVE_CORRECT_HORIZ", "name": "WAVE_CORRECT_HORIZ", "type": "builtins.int"}}, "WAVE_CORRECT_VERT": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.detail.WAVE_CORRECT_VERT", "name": "WAVE_CORRECT_VERT", "type": "builtins.int"}}, "WaveCorrectKind": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "cv2.detail.WaveCorrectKind", "line": 27, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "builtins.int"}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "cv2.detail.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.detail.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.detail.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.detail.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.detail.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.detail.__package__", "name": "__package__", "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.detail.__path__", "name": "__path__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.detail.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_typing": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef", "module_hidden": true, "module_public": false}, "calibrateRotatingCamera": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["Hs", "K"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.detail.calibrateRotatingCamera", "name": "calibrateRotatingCamera", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["Hs", "K"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "calibrateRotatingCamera", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.bool", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "computeImageFeatures": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "cv2.detail.computeImageFeatures", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["featuresFinder", "images", "masks"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.detail.computeImageFeatures", "name": "computeImageFeatures", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["featuresFinder", "images", "masks"], "arg_types": ["cv2.Feature2D", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "computeImageFeatures", "ret_type": {".class": "Instance", "args": ["cv2.detail.ImageFeatures"], "extra_attrs": null, "type_ref": "typing.Sequence"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.detail.computeImageFeatures", "name": "computeImageFeatures", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["featuresFinder", "images", "masks"], "arg_types": ["cv2.Feature2D", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "computeImageFeatures", "ret_type": {".class": "Instance", "args": ["cv2.detail.ImageFeatures"], "extra_attrs": null, "type_ref": "typing.Sequence"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["featuresFinder", "images", "masks"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.detail.computeImageFeatures", "name": "computeImageFeatures", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["featuresFinder", "images", "masks"], "arg_types": ["cv2.Feature2D", {".class": "Instance", "args": ["cv2.UMat"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["cv2.UMat"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "computeImageFeatures", "ret_type": {".class": "Instance", "args": ["cv2.detail.ImageFeatures"], "extra_attrs": null, "type_ref": "typing.Sequence"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.detail.computeImageFeatures", "name": "computeImageFeatures", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["featuresFinder", "images", "masks"], "arg_types": ["cv2.Feature2D", {".class": "Instance", "args": ["cv2.UMat"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["cv2.UMat"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "computeImageFeatures", "ret_type": {".class": "Instance", "args": ["cv2.detail.ImageFeatures"], "extra_attrs": null, "type_ref": "typing.Sequence"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["featuresFinder", "images", "masks"], "arg_types": ["cv2.Feature2D", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "computeImageFeatures", "ret_type": {".class": "Instance", "args": ["cv2.detail.ImageFeatures"], "extra_attrs": null, "type_ref": "typing.Sequence"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["featuresFinder", "images", "masks"], "arg_types": ["cv2.Feature2D", {".class": "Instance", "args": ["cv2.UMat"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["cv2.UMat"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "computeImageFeatures", "ret_type": {".class": "Instance", "args": ["cv2.detail.ImageFeatures"], "extra_attrs": null, "type_ref": "typing.Sequence"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "computeImageFeatures2": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "cv2.detail.computeImageFeatures2", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["featuresFinder", "image", "mask"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.detail.computeImageFeatures2", "name": "computeImageFeatures2", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["featuresFinder", "image", "mask"], "arg_types": ["cv2.Feature2D", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "computeImageFeatures2", "ret_type": "cv2.detail.ImageFeatures", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.detail.computeImageFeatures2", "name": "computeImageFeatures2", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["featuresFinder", "image", "mask"], "arg_types": ["cv2.Feature2D", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "computeImageFeatures2", "ret_type": "cv2.detail.ImageFeatures", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["featuresFinder", "image", "mask"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.detail.computeImageFeatures2", "name": "computeImageFeatures2", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["featuresFinder", "image", "mask"], "arg_types": ["cv2.Feature2D", "cv2.UMat", {".class": "UnionType", "items": ["cv2.UMat", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "computeImageFeatures2", "ret_type": "cv2.detail.ImageFeatures", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.detail.computeImageFeatures2", "name": "computeImageFeatures2", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["featuresFinder", "image", "mask"], "arg_types": ["cv2.Feature2D", "cv2.UMat", {".class": "UnionType", "items": ["cv2.UMat", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "computeImageFeatures2", "ret_type": "cv2.detail.ImageFeatures", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["featuresFinder", "image", "mask"], "arg_types": ["cv2.Feature2D", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "computeImageFeatures2", "ret_type": "cv2.detail.ImageFeatures", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["featuresFinder", "image", "mask"], "arg_types": ["cv2.Feature2D", "cv2.UMat", {".class": "UnionType", "items": ["cv2.UMat", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "computeImageFeatures2", "ret_type": "cv2.detail.ImageFeatures", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "createLaplacePyr": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "cv2.detail.createLaplacePyr", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["img", "num_levels", "pyr"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.detail.createLaplacePyr", "name": "createLaplacePyr", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["img", "num_levels", "pyr"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, "builtins.int", {".class": "Instance", "args": ["cv2.UMat"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "createLaplacePyr", "ret_type": {".class": "Instance", "args": ["cv2.UMat"], "extra_attrs": null, "type_ref": "typing.Sequence"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.detail.createLaplacePyr", "name": "createLaplacePyr", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["img", "num_levels", "pyr"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, "builtins.int", {".class": "Instance", "args": ["cv2.UMat"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "createLaplacePyr", "ret_type": {".class": "Instance", "args": ["cv2.UMat"], "extra_attrs": null, "type_ref": "typing.Sequence"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["img", "num_levels", "pyr"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.detail.createLaplacePyr", "name": "createLaplacePyr", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["img", "num_levels", "pyr"], "arg_types": ["cv2.UMat", "builtins.int", {".class": "Instance", "args": ["cv2.UMat"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "createLaplacePyr", "ret_type": {".class": "Instance", "args": ["cv2.UMat"], "extra_attrs": null, "type_ref": "typing.Sequence"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.detail.createLaplacePyr", "name": "createLaplacePyr", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["img", "num_levels", "pyr"], "arg_types": ["cv2.UMat", "builtins.int", {".class": "Instance", "args": ["cv2.UMat"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "createLaplacePyr", "ret_type": {".class": "Instance", "args": ["cv2.UMat"], "extra_attrs": null, "type_ref": "typing.Sequence"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["img", "num_levels", "pyr"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, "builtins.int", {".class": "Instance", "args": ["cv2.UMat"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "createLaplacePyr", "ret_type": {".class": "Instance", "args": ["cv2.UMat"], "extra_attrs": null, "type_ref": "typing.Sequence"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["img", "num_levels", "pyr"], "arg_types": ["cv2.UMat", "builtins.int", {".class": "Instance", "args": ["cv2.UMat"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "createLaplacePyr", "ret_type": {".class": "Instance", "args": ["cv2.UMat"], "extra_attrs": null, "type_ref": "typing.Sequence"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "createLaplacePyrGpu": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "cv2.detail.createLaplacePyrGpu", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["img", "num_levels", "pyr"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.detail.createLaplacePyrGpu", "name": "createLaplacePyrGpu", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["img", "num_levels", "pyr"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, "builtins.int", {".class": "Instance", "args": ["cv2.UMat"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "createLaplacePyrGpu", "ret_type": {".class": "Instance", "args": ["cv2.UMat"], "extra_attrs": null, "type_ref": "typing.Sequence"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.detail.createLaplacePyrGpu", "name": "createLaplacePyrGpu", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["img", "num_levels", "pyr"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, "builtins.int", {".class": "Instance", "args": ["cv2.UMat"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "createLaplacePyrGpu", "ret_type": {".class": "Instance", "args": ["cv2.UMat"], "extra_attrs": null, "type_ref": "typing.Sequence"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["img", "num_levels", "pyr"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.detail.createLaplacePyrGpu", "name": "createLaplacePyrGpu", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["img", "num_levels", "pyr"], "arg_types": ["cv2.UMat", "builtins.int", {".class": "Instance", "args": ["cv2.UMat"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "createLaplacePyrGpu", "ret_type": {".class": "Instance", "args": ["cv2.UMat"], "extra_attrs": null, "type_ref": "typing.Sequence"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.detail.createLaplacePyrGpu", "name": "createLaplacePyrGpu", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["img", "num_levels", "pyr"], "arg_types": ["cv2.UMat", "builtins.int", {".class": "Instance", "args": ["cv2.UMat"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "createLaplacePyrGpu", "ret_type": {".class": "Instance", "args": ["cv2.UMat"], "extra_attrs": null, "type_ref": "typing.Sequence"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["img", "num_levels", "pyr"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, "builtins.int", {".class": "Instance", "args": ["cv2.UMat"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "createLaplacePyrGpu", "ret_type": {".class": "Instance", "args": ["cv2.UMat"], "extra_attrs": null, "type_ref": "typing.Sequence"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["img", "num_levels", "pyr"], "arg_types": ["cv2.UMat", "builtins.int", {".class": "Instance", "args": ["cv2.UMat"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "createLaplacePyrGpu", "ret_type": {".class": "Instance", "args": ["cv2.UMat"], "extra_attrs": null, "type_ref": "typing.Sequence"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "createWeightMap": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "cv2.detail.createWeightMap", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["mask", "sharpness", "weight"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.detail.createWeightMap", "name": "createWeightMap", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["mask", "sharpness", "weight"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, "builtins.float", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "createWeightMap", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.detail.createWeightMap", "name": "createWeightMap", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["mask", "sharpness", "weight"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, "builtins.float", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "createWeightMap", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["mask", "sharpness", "weight"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.detail.createWeightMap", "name": "createWeightMap", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["mask", "sharpness", "weight"], "arg_types": ["cv2.UMat", "builtins.float", "cv2.UMat"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "createWeightMap", "ret_type": "cv2.UMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.detail.createWeightMap", "name": "createWeightMap", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["mask", "sharpness", "weight"], "arg_types": ["cv2.UMat", "builtins.float", "cv2.UMat"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "createWeightMap", "ret_type": "cv2.UMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["mask", "sharpness", "weight"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, "builtins.float", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "createWeightMap", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["mask", "sharpness", "weight"], "arg_types": ["cv2.UMat", "builtins.float", "cv2.UMat"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "createWeightMap", "ret_type": "cv2.UMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "cv2": {".class": "SymbolTableNode", "cross_ref": "cv2", "kind": "Gdef", "module_hidden": true, "module_public": false}, "focalsFromHomography": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["H", "f0", "f1", "f0_ok", "f1_ok"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.detail.focalsFromHomography", "name": "focalsFromHomography", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["H", "f0", "f1", "f0_ok", "f1_ok"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, "builtins.float", "builtins.float", "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "focalsFromHomography", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "leaveBiggestComponent": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["features", "pairwise_matches", "conf_threshold"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.detail.leaveBiggestComponent", "name": "leaveBiggestComponent", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["features", "pairwise_matches", "conf_threshold"], "arg_types": [{".class": "Instance", "args": ["cv2.detail.ImageFeatures"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "Instance", "args": ["cv2.detail.MatchesInfo"], "extra_attrs": null, "type_ref": "typing.Sequence"}, "builtins.float"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "leaveBiggestComponent", "ret_type": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "typing.Sequence"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "matchesGraphAsString": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["paths", "pairwise_matches", "conf_threshold"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.detail.matchesGraphAsString", "name": "matchesGraphAsString", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["paths", "pairwise_matches", "conf_threshold"], "arg_types": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "Instance", "args": ["cv2.detail.MatchesInfo"], "extra_attrs": null, "type_ref": "typing.Sequence"}, "builtins.float"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "matchesGraphAsString", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "normalizeUsingWeightMap": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "cv2.detail.normalizeUsingWeightMap", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["weight", "src"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.detail.normalizeUsingWeightMap", "name": "normalizeUsingWeightMap", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["weight", "src"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "normalizeUsingWeightMap", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.detail.normalizeUsingWeightMap", "name": "normalizeUsingWeightMap", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["weight", "src"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "normalizeUsingWeightMap", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["weight", "src"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.detail.normalizeUsingWeightMap", "name": "normalizeUsingWeightMap", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["weight", "src"], "arg_types": ["cv2.UMat", "cv2.UMat"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "normalizeUsingWeightMap", "ret_type": "cv2.UMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.detail.normalizeUsingWeightMap", "name": "normalizeUsingWeightMap", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["weight", "src"], "arg_types": ["cv2.UMat", "cv2.UMat"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "normalizeUsingWeightMap", "ret_type": "cv2.UMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["weight", "src"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "normalizeUsingWeightMap", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["weight", "src"], "arg_types": ["cv2.UMat", "cv2.UMat"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "normalizeUsingWeightMap", "ret_type": "cv2.UMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "numpy": {".class": "SymbolTableNode", "cross_ref": "numpy", "kind": "Gdef", "module_hidden": true, "module_public": false}, "overlapRoi": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["tl1", "tl2", "sz1", "sz2", "roi"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.detail.overlapRoi", "name": "overlapRoi", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["tl1", "tl2", "sz1", "sz2", "roi"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.Point"}, {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.Point"}, {".class": "TypeAliasType", "args": [], "type_ref": "cv2.<PERSON>.Size"}, {".class": "TypeAliasType", "args": [], "type_ref": "cv2.<PERSON>.Size"}, {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.Rect"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "overlapRoi", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "restoreImageFromLaplacePyr": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["pyr"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.detail.restoreImageFromLaplacePyr", "name": "restoreImageFromLaplacePyr", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["pyr"], "arg_types": [{".class": "Instance", "args": ["cv2.UMat"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "restoreImageFromLaplacePyr", "ret_type": {".class": "Instance", "args": ["cv2.UMat"], "extra_attrs": null, "type_ref": "typing.Sequence"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "restoreImageFromLaplacePyrGpu": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["pyr"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.detail.restoreImageFromLaplacePyrGpu", "name": "restoreImageFromLaplacePyrGpu", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["pyr"], "arg_types": [{".class": "Instance", "args": ["cv2.UMat"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "restoreImageFromLaplacePyrGpu", "ret_type": {".class": "Instance", "args": ["cv2.UMat"], "extra_attrs": null, "type_ref": "typing.Sequence"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "resultRoi": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "cv2.detail.resultRoi", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["corners", "images"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.detail.resultRoi", "name": "resultRoi", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["corners", "images"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.Point"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "Instance", "args": ["cv2.UMat"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "resultRoi", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.Rect"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.detail.resultRoi", "name": "resultRoi", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["corners", "images"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.Point"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "Instance", "args": ["cv2.UMat"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "resultRoi", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.Rect"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["corners", "sizes"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.detail.resultRoi", "name": "resultRoi", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["corners", "sizes"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.Point"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.<PERSON>.Size"}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "resultRoi", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.Rect"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.detail.resultRoi", "name": "resultRoi", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["corners", "sizes"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.Point"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.<PERSON>.Size"}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "resultRoi", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.Rect"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["corners", "images"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.Point"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "Instance", "args": ["cv2.UMat"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "resultRoi", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.Rect"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["corners", "sizes"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.Point"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.<PERSON>.Size"}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "resultRoi", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.Rect"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "resultRoiIntersection": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["corners", "sizes"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.detail.resultRoiIntersection", "name": "resultRoiIntersection", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["corners", "sizes"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.Point"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.<PERSON>.Size"}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "resultRoiIntersection", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.Rect"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "resultTl": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["corners"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.detail.resultTl", "name": "resultTl", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["corners"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.Point"}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "resultTl", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.Point"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "selectRandomSubset": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["count", "size", "subset"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.detail.selectRandomSubset", "name": "selectRandomSubset", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["count", "size", "subset"], "arg_types": ["builtins.int", "builtins.int", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "selectRandomSubset", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "stitchingLogLevel": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.detail.stitchingLogLevel", "name": "stitchingLogLevel", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "stitchingLogLevel", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "strip": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "cv2.detail.strip", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["params"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.detail.strip", "name": "strip", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["params"], "arg_types": ["cv2.gapi.ie.PyParams"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "strip", "ret_type": "cv2.gapi.GNetParam", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.detail.strip", "name": "strip", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["params"], "arg_types": ["cv2.gapi.ie.PyParams"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "strip", "ret_type": "cv2.gapi.GNetParam", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["params"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.detail.strip", "name": "strip", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["params"], "arg_types": ["cv2.gapi.onnx.PyParams"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "strip", "ret_type": "cv2.gapi.GNetParam", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.detail.strip", "name": "strip", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["params"], "arg_types": ["cv2.gapi.onnx.PyParams"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "strip", "ret_type": "cv2.gapi.GNetParam", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["params"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.detail.strip", "name": "strip", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["params"], "arg_types": ["cv2.gapi.ov.PyParams"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "strip", "ret_type": "cv2.gapi.GNetParam", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.detail.strip", "name": "strip", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["params"], "arg_types": ["cv2.gapi.ov.PyParams"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "strip", "ret_type": "cv2.gapi.GNetParam", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": ["params"], "arg_types": ["cv2.gapi.ie.PyParams"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "strip", "ret_type": "cv2.gapi.GNetParam", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0], "arg_names": ["params"], "arg_types": ["cv2.gapi.onnx.PyParams"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "strip", "ret_type": "cv2.gapi.GNetParam", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0], "arg_names": ["params"], "arg_types": ["cv2.gapi.ov.PyParams"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "strip", "ret_type": "cv2.gapi.GNetParam", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "waveCorrect": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["rmats", "kind"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.detail.waveCorrect", "name": "waveCorrect", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["rmats", "kind"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "waveCorrect", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "path": "c:\\Python313\\Lib\\site-packages\\cv2\\detail\\__init__.pyi"}