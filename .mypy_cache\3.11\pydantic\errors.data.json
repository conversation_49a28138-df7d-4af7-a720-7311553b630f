{".class": "MypyFile", "_fullname": "pydantic.errors", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_public": false}, "ClassVar": {".class": "SymbolTableNode", "cross_ref": "typing.ClassVar", "kind": "Gdef", "module_public": false}, "DEV_ERROR_DOCS_URL": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pydantic.errors.DEV_ERROR_DOCS_URL", "name": "DEV_ERROR_DOCS_URL", "type": "builtins.str"}}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing.Literal", "kind": "Gdef", "module_public": false}, "PydanticErrorCodes": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "pydantic.errors.PydanticErrorCodes", "line": 30, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "class-not-fully-defined"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "custom-json-schema"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "decorator-missing-field"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "discriminator-no-field"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "discriminator-alias-type"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "discriminator-needs-literal"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "discriminator-alias"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "discriminator-validator"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "callable-discriminator-no-tag"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "typed-dict-version"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "model-field-overridden"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "model-field-missing-annotation"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "config-both"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "removed-kwargs"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "circular-reference-schema"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "invalid-for-json-schema"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "json-schema-already-used"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "base-model-instantiated"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "undefined-annotation"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "schema-for-unknown-type"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "import-error"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "create-model-field-definitions"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "validator-no-fields"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "validator-invalid-fields"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "validator-instance-method"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "validator-input-type"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "root-validator-pre-skip"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "model-serializer-instance-method"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "validator-field-config-info"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "validator-v1-signature"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "validator-signature"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "field-serializer-signature"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "model-serializer-signature"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "multiple-field-serializers"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "invalid-annotated-type"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "type-adapter-config-unused"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "root-model-extra"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "unevaluable-type-annotation"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "dataclass-init-false-extra-allow"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "clashing-init-and-init-var"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "model-config-invalid-field-name"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "with-config-on-model"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "dataclass-on-model"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "validate-call-type"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "unpack-typed-dict"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "overlapping-unpack-typed-dict"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "invalid-self-type"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "validate-by-alias-and-name-false"}], "uses_pep604_syntax": false}}}, "PydanticErrorMixin": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic.errors.PydanticErrorMixin", "name": "PydanticErrorMixin", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pydantic.errors.PydanticErrorMixin", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pydantic.errors", "mro": ["pydantic.errors.PydanticErrorMixin", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 3], "arg_names": ["self", "message", "code"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.errors.PydanticErrorMixin.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 3], "arg_names": ["self", "message", "code"], "arg_types": ["pydantic.errors.PydanticErrorMixin", "builtins.str", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.errors.PydanticErrorCodes"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of PydanticErrorMixin", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__str__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.errors.PydanticErrorMixin.__str__", "name": "__str__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["pydantic.errors.PydanticErrorMixin"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__str__ of PydanticErrorMixin", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "code": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pydantic.errors.PydanticErrorMixin.code", "name": "code", "type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.errors.PydanticErrorCodes"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "message": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pydantic.errors.PydanticErrorMixin.message", "name": "message", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.errors.PydanticErrorMixin.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.errors.PydanticErrorMixin", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "PydanticForbiddenQualifier": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["pydantic.errors.PydanticUserError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic.errors.PydanticForbiddenQualifier", "name": "PydanticForbiddenQualifier", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pydantic.errors.PydanticForbiddenQualifier", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pydantic.errors", "mro": ["pydantic.errors.PydanticForbiddenQualifier", "pydantic.errors.PydanticUserError", "pydantic.errors.PydanticErrorMixin", "builtins.TypeError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "qualifier", "annotation"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.errors.PydanticForbiddenQualifier.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "qualifier", "annotation"], "arg_types": ["pydantic.errors.PydanticForbiddenQualifier", {".class": "TypeAliasType", "args": [], "type_ref": "typing_inspection.introspection.Qualifier"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of PydanticForbiddenQualifier", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_qualifier_repr_map": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready", "has_explicit_value"], "fullname": "pydantic.errors.PydanticForbiddenQualifier._qualifier_repr_map", "name": "_qualifier_repr_map", "type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "typing_inspection.introspection.Qualifier"}, "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.errors.PydanticForbiddenQualifier.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.errors.PydanticForbiddenQualifier", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "PydanticImportError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["pydantic.errors.PydanticErrorMixin", "builtins.ImportError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic.errors.PydanticImportError", "name": "PydanticImportError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pydantic.errors.PydanticImportError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pydantic.errors", "mro": ["pydantic.errors.PydanticImportError", "pydantic.errors.PydanticErrorMixin", "builtins.ImportError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "message"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.errors.PydanticImportError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "message"], "arg_types": ["pydantic.errors.PydanticImportError", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of PydanticImportError", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.errors.PydanticImportError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.errors.PydanticImportError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "PydanticInvalidForJsonSchema": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["pydantic.errors.PydanticUserError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic.errors.PydanticInvalidForJsonSchema", "name": "PydanticInvalidForJsonSchema", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pydantic.errors.PydanticInvalidForJsonSchema", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pydantic.errors", "mro": ["pydantic.errors.PydanticInvalidForJsonSchema", "pydantic.errors.PydanticUserError", "pydantic.errors.PydanticErrorMixin", "builtins.TypeError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "message"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.errors.PydanticInvalidForJsonSchema.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "message"], "arg_types": ["pydantic.errors.PydanticInvalidForJsonSchema", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of PydanticInvalidForJsonSchema", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.errors.PydanticInvalidForJsonSchema.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.errors.PydanticInvalidForJsonSchema", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "PydanticSchemaGenerationError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["pydantic.errors.PydanticUserError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic.errors.PydanticSchemaGenerationError", "name": "PydanticSchemaGenerationError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pydantic.errors.PydanticSchemaGenerationError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pydantic.errors", "mro": ["pydantic.errors.PydanticSchemaGenerationError", "pydantic.errors.PydanticUserError", "pydantic.errors.PydanticErrorMixin", "builtins.TypeError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "message"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.errors.PydanticSchemaGenerationError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "message"], "arg_types": ["pydantic.errors.PydanticSchemaGenerationError", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of PydanticSchemaGenerationError", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.errors.PydanticSchemaGenerationError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.errors.PydanticSchemaGenerationError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "PydanticUndefinedAnnotation": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["pydantic.errors.PydanticErrorMixin", "builtins.NameError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic.errors.PydanticUndefinedAnnotation", "name": "PydanticUndefinedAnnotation", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pydantic.errors.PydanticUndefinedAnnotation", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pydantic.errors", "mro": ["pydantic.errors.PydanticUndefinedAnnotation", "pydantic.errors.PydanticErrorMixin", "builtins.NameError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "message"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.errors.PydanticUndefinedAnnotation.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "message"], "arg_types": ["pydantic.errors.PydanticUndefinedAnnotation", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of PydanticUndefinedAnnotation", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "from_name_error": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "name_error"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "pydantic.errors.PydanticUndefinedAnnotation.from_name_error", "name": "from_name_error", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "name_error"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.errors.PydanticUndefinedAnnotation.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.errors.PydanticUndefinedAnnotation", "values": [], "variance": 0}}, "builtins.NameError"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_name_error of PydanticUndefinedAnnotation", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.errors.PydanticUndefinedAnnotation.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.errors.PydanticUndefinedAnnotation", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.errors.PydanticUndefinedAnnotation.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.errors.PydanticUndefinedAnnotation", "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "pydantic.errors.PydanticUndefinedAnnotation.from_name_error", "name": "from_name_error", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "name_error"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.errors.PydanticUndefinedAnnotation.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.errors.PydanticUndefinedAnnotation", "values": [], "variance": 0}}, "builtins.NameError"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_name_error of PydanticUndefinedAnnotation", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.errors.PydanticUndefinedAnnotation.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.errors.PydanticUndefinedAnnotation", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.errors.PydanticUndefinedAnnotation.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.errors.PydanticUndefinedAnnotation", "values": [], "variance": 0}]}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.errors.PydanticUndefinedAnnotation.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.errors.PydanticUndefinedAnnotation", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "PydanticUserError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["pydantic.errors.PydanticErrorMixin", "builtins.TypeError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic.errors.PydanticUserError", "name": "PydanticUserError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pydantic.errors.PydanticUserError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pydantic.errors", "mro": ["pydantic.errors.PydanticUserError", "pydantic.errors.PydanticErrorMixin", "builtins.TypeError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.errors.PydanticUserError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.errors.PydanticUserError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Qualifier": {".class": "SymbolTableNode", "cross_ref": "typing_inspection.introspection.Qualifier", "kind": "Gdef", "module_public": false}, "Self": {".class": "SymbolTableNode", "cross_ref": "typing.Self", "kind": "Gdef", "module_public": false}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pydantic.errors.__all__", "name": "__all__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic.errors.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic.errors.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic.errors.__file__", "name": "__file__", "type": "builtins.str"}}, "__getattr__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pydantic.errors.__getattr__", "name": "__getattr__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic.errors.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic.errors.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic.errors.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef", "module_public": false}, "_repr": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._repr", "kind": "Gdef", "module_public": false}, "getattr_migration": {".class": "SymbolTableNode", "cross_ref": "pydantic._migration.getattr_migration", "kind": "Gdef", "module_public": false}, "re": {".class": "SymbolTableNode", "cross_ref": "re", "kind": "Gdef", "module_public": false}, "version_short": {".class": "SymbolTableNode", "cross_ref": "pydantic.version.version_short", "kind": "Gdef", "module_public": false}}, "path": "c:\\Python313\\Lib\\site-packages\\pydantic\\errors.py"}