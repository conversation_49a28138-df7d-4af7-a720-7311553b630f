{".class": "MypyFile", "_fullname": "numpy.lib", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "Arrayterator": {".class": "SymbolTableNode", "cross_ref": "numpy.lib._arrayterator_impl.Arrayterator", "kind": "Gdef"}, "NumpyVersion": {".class": "SymbolTableNode", "cross_ref": "numpy.lib._version.NumpyVersion", "kind": "Gdef"}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "numpy.lib.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.lib.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.lib.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.lib.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.lib.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.lib.__package__", "name": "__package__", "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.lib.__path__", "name": "__path__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.lib.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "add_docstring": {".class": "SymbolTableNode", "cross_ref": "numpy._core.multiarray.add_docstring", "kind": "Gdef"}, "add_newdoc": {".class": "SymbolTableNode", "cross_ref": "numpy._core.function_base.add_newdoc", "kind": "Gdef"}, "array_utils": {".class": "SymbolTableNode", "cross_ref": "numpy.lib.array_utils", "kind": "Gdef"}, "format": {".class": "SymbolTableNode", "cross_ref": "numpy.lib.format", "kind": "Gdef", "module_public": false}, "introspect": {".class": "SymbolTableNode", "cross_ref": "numpy.lib.introspect", "kind": "Gdef"}, "mixins": {".class": "SymbolTableNode", "cross_ref": "numpy.lib.mixins", "kind": "Gdef"}, "npyio": {".class": "SymbolTableNode", "cross_ref": "numpy.lib.npyio", "kind": "Gdef"}, "scimath": {".class": "SymbolTableNode", "cross_ref": "numpy.lib.scimath", "kind": "Gdef"}, "stride_tricks": {".class": "SymbolTableNode", "cross_ref": "numpy.lib.stride_tricks", "kind": "Gdef"}, "tracemalloc_domain": {".class": "SymbolTableNode", "cross_ref": "numpy._core.multiarray.tracemalloc_domain", "kind": "Gdef"}}, "path": "c:\\Python313\\Lib\\site-packages\\numpy\\lib\\__init__.pyi"}