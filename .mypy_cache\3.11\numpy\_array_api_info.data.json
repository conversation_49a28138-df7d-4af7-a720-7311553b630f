{".class": "MypyFile", "_fullname": "numpy._array_api_info", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "ClassVar": {".class": "SymbolTableNode", "cross_ref": "typing.ClassVar", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing.Literal", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Never": {".class": "SymbolTableNode", "cross_ref": "typing.Never", "kind": "Gdef", "module_hidden": true, "module_public": false}, "TypeAlias": {".class": "SymbolTableNode", "cross_ref": "typing.TypeAlias", "kind": "Gdef", "module_hidden": true, "module_public": false}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef", "module_hidden": true, "module_public": false}, "TypedDict": {".class": "SymbolTableNode", "cross_ref": "typing.TypedDict", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_Capabilities": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy._array_api_info._Capabilities", "name": "_Capabilities", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "numpy._array_api_info._Capabilities", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "numpy._array_api_info", "mro": ["numpy._array_api_info._Capabilities", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["boolean indexing", {".class": "LiteralType", "fallback": "builtins.bool", "value": true}], ["data-dependent shapes", {".class": "LiteralType", "fallback": "builtins.bool", "value": true}]], "readonly_keys": [], "required_keys": ["boolean indexing", "data-dependent shapes"]}}}, "_DTypes": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy._array_api_info._DTypes", "name": "_DTypes", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "numpy._array_api_info._DTypes", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "numpy._array_api_info", "mro": ["numpy._array_api_info._DTypes", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["complex64", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.complex64"}], "extra_attrs": null, "type_ref": "numpy.dtype"}], ["complex128", {".class": "Instance", "args": ["numpy.complex128"], "extra_attrs": null, "type_ref": "numpy.dtype"}], ["float32", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.float32"}], "extra_attrs": null, "type_ref": "numpy.dtype"}], ["float64", {".class": "Instance", "args": ["numpy.float64"], "extra_attrs": null, "type_ref": "numpy.dtype"}], ["uint8", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint8"}], "extra_attrs": null, "type_ref": "numpy.dtype"}], ["uint16", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint16"}], "extra_attrs": null, "type_ref": "numpy.dtype"}], ["uint32", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint32"}], "extra_attrs": null, "type_ref": "numpy.dtype"}], ["uint64", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint64"}], "extra_attrs": null, "type_ref": "numpy.dtype"}], ["int8", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.int8"}], "extra_attrs": null, "type_ref": "numpy.dtype"}], ["int16", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.int16"}], "extra_attrs": null, "type_ref": "numpy.dtype"}], ["int32", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.int32"}], "extra_attrs": null, "type_ref": "numpy.dtype"}], ["int64", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.int64"}], "extra_attrs": null, "type_ref": "numpy.dtype"}], ["bool", {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "numpy.bool"}], "extra_attrs": null, "type_ref": "numpy.dtype"}]], "readonly_keys": [], "required_keys": ["bool", "complex128", "complex64", "float32", "float64", "int16", "int32", "int64", "int8", "uint16", "uint32", "uint64", "uint8"]}}}, "_DTypesBool": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy._array_api_info._DTypesBool", "name": "_DTypesBool", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "numpy._array_api_info._DTypesBool", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "numpy._array_api_info", "mro": ["numpy._array_api_info._DTypesBool", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["bool", {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "numpy.bool"}], "extra_attrs": null, "type_ref": "numpy.dtype"}]], "readonly_keys": [], "required_keys": ["bool"]}}}, "_DTypesComplex": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy._array_api_info._DTypesComplex", "name": "_DTypesComplex", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "numpy._array_api_info._DTypesComplex", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "numpy._array_api_info", "mro": ["numpy._array_api_info._DTypesComplex", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["complex64", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.complex64"}], "extra_attrs": null, "type_ref": "numpy.dtype"}], ["complex128", {".class": "Instance", "args": ["numpy.complex128"], "extra_attrs": null, "type_ref": "numpy.dtype"}]], "readonly_keys": [], "required_keys": ["complex128", "complex64"]}}}, "_DTypesFloat": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy._array_api_info._DTypesFloat", "name": "_DTypesFloat", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "numpy._array_api_info._DTypesFloat", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "numpy._array_api_info", "mro": ["numpy._array_api_info._DTypesFloat", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["float32", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.float32"}], "extra_attrs": null, "type_ref": "numpy.dtype"}], ["float64", {".class": "Instance", "args": ["numpy.float64"], "extra_attrs": null, "type_ref": "numpy.dtype"}]], "readonly_keys": [], "required_keys": ["float32", "float64"]}}}, "_DTypesInt": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy._array_api_info._DTypesInt", "name": "_DTypesInt", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "numpy._array_api_info._DTypesInt", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "numpy._array_api_info", "mro": ["numpy._array_api_info._DTypesInt", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["int8", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.int8"}], "extra_attrs": null, "type_ref": "numpy.dtype"}], ["int16", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.int16"}], "extra_attrs": null, "type_ref": "numpy.dtype"}], ["int32", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.int32"}], "extra_attrs": null, "type_ref": "numpy.dtype"}], ["int64", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.int64"}], "extra_attrs": null, "type_ref": "numpy.dtype"}]], "readonly_keys": [], "required_keys": ["int16", "int32", "int64", "int8"]}}}, "_DTypesInteger": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy._array_api_info._DTypesInteger", "name": "_DTypesInteger", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "numpy._array_api_info._DTypesInteger", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "numpy._array_api_info", "mro": ["numpy._array_api_info._DTypesInteger", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["uint8", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint8"}], "extra_attrs": null, "type_ref": "numpy.dtype"}], ["uint16", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint16"}], "extra_attrs": null, "type_ref": "numpy.dtype"}], ["uint32", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint32"}], "extra_attrs": null, "type_ref": "numpy.dtype"}], ["uint64", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint64"}], "extra_attrs": null, "type_ref": "numpy.dtype"}], ["int8", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.int8"}], "extra_attrs": null, "type_ref": "numpy.dtype"}], ["int16", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.int16"}], "extra_attrs": null, "type_ref": "numpy.dtype"}], ["int32", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.int32"}], "extra_attrs": null, "type_ref": "numpy.dtype"}], ["int64", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.int64"}], "extra_attrs": null, "type_ref": "numpy.dtype"}]], "readonly_keys": [], "required_keys": ["int16", "int32", "int64", "int8", "uint16", "uint32", "uint64", "uint8"]}}}, "_DTypesNumber": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy._array_api_info._DTypesNumber", "name": "_DTypesNumber", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "numpy._array_api_info._DTypesNumber", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "numpy._array_api_info", "mro": ["numpy._array_api_info._DTypesNumber", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["complex64", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.complex64"}], "extra_attrs": null, "type_ref": "numpy.dtype"}], ["complex128", {".class": "Instance", "args": ["numpy.complex128"], "extra_attrs": null, "type_ref": "numpy.dtype"}], ["float32", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.float32"}], "extra_attrs": null, "type_ref": "numpy.dtype"}], ["float64", {".class": "Instance", "args": ["numpy.float64"], "extra_attrs": null, "type_ref": "numpy.dtype"}], ["uint8", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint8"}], "extra_attrs": null, "type_ref": "numpy.dtype"}], ["uint16", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint16"}], "extra_attrs": null, "type_ref": "numpy.dtype"}], ["uint32", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint32"}], "extra_attrs": null, "type_ref": "numpy.dtype"}], ["uint64", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint64"}], "extra_attrs": null, "type_ref": "numpy.dtype"}], ["int8", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.int8"}], "extra_attrs": null, "type_ref": "numpy.dtype"}], ["int16", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.int16"}], "extra_attrs": null, "type_ref": "numpy.dtype"}], ["int32", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.int32"}], "extra_attrs": null, "type_ref": "numpy.dtype"}], ["int64", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.int64"}], "extra_attrs": null, "type_ref": "numpy.dtype"}]], "readonly_keys": [], "required_keys": ["complex128", "complex64", "float32", "float64", "int16", "int32", "int64", "int8", "uint16", "uint32", "uint64", "uint8"]}}}, "_DTypesUInt": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy._array_api_info._DTypesUInt", "name": "_DTypesUInt", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "numpy._array_api_info._DTypesUInt", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "numpy._array_api_info", "mro": ["numpy._array_api_info._DTypesUInt", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["uint8", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint8"}], "extra_attrs": null, "type_ref": "numpy.dtype"}], ["uint16", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint16"}], "extra_attrs": null, "type_ref": "numpy.dtype"}], ["uint32", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint32"}], "extra_attrs": null, "type_ref": "numpy.dtype"}], ["uint64", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint64"}], "extra_attrs": null, "type_ref": "numpy.dtype"}]], "readonly_keys": [], "required_keys": ["uint16", "uint32", "uint64", "uint8"]}}}, "_DTypesUnion": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy._array_api_info._DTypesUnion", "name": "_DTypesUnion", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "numpy._array_api_info._DTypesUnion", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "numpy._array_api_info", "mro": ["numpy._array_api_info._DTypesUnion", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["bool", {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "numpy.bool"}], "extra_attrs": null, "type_ref": "numpy.dtype"}], ["int8", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.int8"}], "extra_attrs": null, "type_ref": "numpy.dtype"}], ["int16", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.int16"}], "extra_attrs": null, "type_ref": "numpy.dtype"}], ["int32", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.int32"}], "extra_attrs": null, "type_ref": "numpy.dtype"}], ["int64", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.int64"}], "extra_attrs": null, "type_ref": "numpy.dtype"}], ["uint8", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint8"}], "extra_attrs": null, "type_ref": "numpy.dtype"}], ["uint16", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint16"}], "extra_attrs": null, "type_ref": "numpy.dtype"}], ["uint32", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint32"}], "extra_attrs": null, "type_ref": "numpy.dtype"}], ["uint64", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint64"}], "extra_attrs": null, "type_ref": "numpy.dtype"}], ["float32", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.float32"}], "extra_attrs": null, "type_ref": "numpy.dtype"}], ["float64", {".class": "Instance", "args": ["numpy.float64"], "extra_attrs": null, "type_ref": "numpy.dtype"}], ["complex64", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.complex64"}], "extra_attrs": null, "type_ref": "numpy.dtype"}], ["complex128", {".class": "Instance", "args": ["numpy.complex128"], "extra_attrs": null, "type_ref": "numpy.dtype"}]], "readonly_keys": [], "required_keys": []}}}, "_DefaultDTypes": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy._array_api_info._DefaultDTypes", "name": "_DefaultDTypes", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "numpy._array_api_info._DefaultDTypes", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "numpy._array_api_info", "mro": ["numpy._array_api_info._DefaultDTypes", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["real floating", {".class": "Instance", "args": ["numpy.float64"], "extra_attrs": null, "type_ref": "numpy.dtype"}], ["complex floating", {".class": "Instance", "args": ["numpy.complex128"], "extra_attrs": null, "type_ref": "numpy.dtype"}], ["integral", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "extra_attrs": null, "type_ref": "numpy.dtype"}], ["indexing", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "extra_attrs": null, "type_ref": "numpy.dtype"}]], "readonly_keys": [], "required_keys": ["complex floating", "indexing", "integral", "real floating"]}}}, "_Device": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "numpy._array_api_info._Device", "line": 16, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "LiteralType", "fallback": "builtins.str", "value": "cpu"}}}, "_DeviceLike": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "numpy._array_api_info._DeviceLike", "line": 17, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._array_api_info._Device"}], "uses_pep604_syntax": true}}}, "_EmptyDict": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "numpy._array_api_info._EmptyDict", "line": 120, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "Instance", "args": [{".class": "UninhabitedType"}, {".class": "UninhabitedType"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_Kind": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "numpy._array_api_info._Kind", "line": 45, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._array_api_info._KindBool"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._array_api_info._KindInt"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._array_api_info._KindUInt"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._array_api_info._KindInteger"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._array_api_info._KindFloat"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._array_api_info._KindComplex"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._array_api_info._KindNumber"}], "uses_pep604_syntax": true}}}, "_KindBool": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "numpy._array_api_info._KindBool", "line": 38, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "LiteralType", "fallback": "builtins.str", "value": "bool"}}}, "_KindComplex": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "numpy._array_api_info._KindComplex", "line": 43, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "LiteralType", "fallback": "builtins.str", "value": "complex floating"}}}, "_KindFloat": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "numpy._array_api_info._KindFloat", "line": 42, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "LiteralType", "fallback": "builtins.str", "value": "real floating"}}}, "_KindInt": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "numpy._array_api_info._KindInt", "line": 39, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "LiteralType", "fallback": "builtins.str", "value": "signed integer"}}}, "_KindInteger": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "numpy._array_api_info._KindInteger", "line": 41, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "LiteralType", "fallback": "builtins.str", "value": "integral"}}}, "_KindNumber": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "numpy._array_api_info._KindNumber", "line": 44, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "LiteralType", "fallback": "builtins.str", "value": "numeric"}}}, "_KindUInt": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "numpy._array_api_info._KindUInt", "line": 40, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "LiteralType", "fallback": "builtins.str", "value": "unsigned integer"}}}, "_Permute1": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._array_api_info._T1", "id": 1, "name": "_T1", "namespace": "numpy._array_api_info._Permute1", "upper_bound": "builtins.object", "values": [], "variance": 0}], "column": 0, "fullname": "numpy._array_api_info._Permute1", "line": 59, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._array_api_info._T1", "id": 1, "name": "_T1", "namespace": "numpy._array_api_info._Permute1", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TupleType", "implicit": false, "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._array_api_info._T1", "id": 1, "name": "_T1", "namespace": "numpy._array_api_info._Permute1", "upper_bound": "builtins.object", "values": [], "variance": 0}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": true}}}, "_Permute2": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._array_api_info._T1", "id": 1, "name": "_T1", "namespace": "numpy._array_api_info._Permute2", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._array_api_info._T2", "id": 2, "name": "_T2", "namespace": "numpy._array_api_info._Permute2", "upper_bound": "builtins.object", "values": [], "variance": 0}], "column": 0, "fullname": "numpy._array_api_info._Permute2", "line": 60, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._array_api_info._T1", "id": 1, "name": "_T1", "namespace": "numpy._array_api_info._Permute2", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._array_api_info._T2", "id": 2, "name": "_T2", "namespace": "numpy._array_api_info._Permute2", "upper_bound": "builtins.object", "values": [], "variance": 0}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "TupleType", "implicit": false, "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._array_api_info._T2", "id": 2, "name": "_T2", "namespace": "numpy._array_api_info._Permute2", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._array_api_info._T1", "id": 1, "name": "_T1", "namespace": "numpy._array_api_info._Permute2", "upper_bound": "builtins.object", "values": [], "variance": 0}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": true}}}, "_Permute3": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._array_api_info._T1", "id": 1, "name": "_T1", "namespace": "numpy._array_api_info._Permute3", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._array_api_info._T2", "id": 2, "name": "_T2", "namespace": "numpy._array_api_info._Permute3", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._array_api_info._T3", "id": 3, "name": "_T3", "namespace": "numpy._array_api_info._Permute3", "upper_bound": "builtins.object", "values": [], "variance": 0}], "column": 0, "fullname": "numpy._array_api_info._Permute3", "line": 61, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._array_api_info._T1", "id": 1, "name": "_T1", "namespace": "numpy._array_api_info._Permute3", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._array_api_info._T2", "id": 2, "name": "_T2", "namespace": "numpy._array_api_info._Permute3", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._array_api_info._T3", "id": 3, "name": "_T3", "namespace": "numpy._array_api_info._Permute3", "upper_bound": "builtins.object", "values": [], "variance": 0}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "TupleType", "implicit": false, "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._array_api_info._T1", "id": 1, "name": "_T1", "namespace": "numpy._array_api_info._Permute3", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._array_api_info._T3", "id": 3, "name": "_T3", "namespace": "numpy._array_api_info._Permute3", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._array_api_info._T2", "id": 2, "name": "_T2", "namespace": "numpy._array_api_info._Permute3", "upper_bound": "builtins.object", "values": [], "variance": 0}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "TupleType", "implicit": false, "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._array_api_info._T2", "id": 2, "name": "_T2", "namespace": "numpy._array_api_info._Permute3", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._array_api_info._T1", "id": 1, "name": "_T1", "namespace": "numpy._array_api_info._Permute3", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._array_api_info._T3", "id": 3, "name": "_T3", "namespace": "numpy._array_api_info._Permute3", "upper_bound": "builtins.object", "values": [], "variance": 0}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "TupleType", "implicit": false, "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._array_api_info._T2", "id": 2, "name": "_T2", "namespace": "numpy._array_api_info._Permute3", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._array_api_info._T3", "id": 3, "name": "_T3", "namespace": "numpy._array_api_info._Permute3", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._array_api_info._T1", "id": 1, "name": "_T1", "namespace": "numpy._array_api_info._Permute3", "upper_bound": "builtins.object", "values": [], "variance": 0}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "TupleType", "implicit": false, "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._array_api_info._T3", "id": 3, "name": "_T3", "namespace": "numpy._array_api_info._Permute3", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._array_api_info._T1", "id": 1, "name": "_T1", "namespace": "numpy._array_api_info._Permute3", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._array_api_info._T2", "id": 2, "name": "_T2", "namespace": "numpy._array_api_info._Permute3", "upper_bound": "builtins.object", "values": [], "variance": 0}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "TupleType", "implicit": false, "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._array_api_info._T3", "id": 3, "name": "_T3", "namespace": "numpy._array_api_info._Permute3", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._array_api_info._T2", "id": 2, "name": "_T2", "namespace": "numpy._array_api_info._Permute3", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._array_api_info._T1", "id": 1, "name": "_T1", "namespace": "numpy._array_api_info._Permute3", "upper_bound": "builtins.object", "values": [], "variance": 0}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": true}}}, "_T1": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._array_api_info._T1", "name": "_T1", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "_T2": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._array_api_info._T2", "name": "_T2", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "_T3": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._array_api_info._T3", "name": "_T3", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy._array_api_info.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__array_namespace_info__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy._array_api_info.__array_namespace_info__", "name": "__array_namespace_info__", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_final"], "fullname": "numpy._array_api_info.__array_namespace_info__", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "numpy._array_api_info", "mro": ["numpy._array_api_info.__array_namespace_info__", "builtins.object"], "names": {".class": "SymbolTable", "__module__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready"], "fullname": "numpy._array_api_info.__array_namespace_info__.__module__", "name": "__module__", "type": {".class": "LiteralType", "fallback": "builtins.str", "value": "numpy"}}}, "capabilities": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy._array_api_info.__array_namespace_info__.capabilities", "name": "capabilities", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["numpy._array_api_info.__array_namespace_info__"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "capabilities of __array_namespace_info__", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy._array_api_info._Capabilities"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "default_device": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy._array_api_info.__array_namespace_info__.default_device", "name": "default_device", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["numpy._array_api_info.__array_namespace_info__"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "default_device of __array_namespace_info__", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy._array_api_info._Device"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "default_dtypes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5], "arg_names": ["self", "device"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy._array_api_info.__array_namespace_info__.default_dtypes", "name": "default_dtypes", "type": {".class": "CallableType", "arg_kinds": [0, 5], "arg_names": ["self", "device"], "arg_types": ["numpy._array_api_info.__array_namespace_info__", {".class": "TypeAliasType", "args": [], "type_ref": "numpy._array_api_info._DeviceLike"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "default_dtypes of __array_namespace_info__", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy._array_api_info._DefaultDTypes"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "devices": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy._array_api_info.__array_namespace_info__.devices", "name": "devices", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["numpy._array_api_info.__array_namespace_info__"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "devices of __array_namespace_info__", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._array_api_info._Device"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "dtypes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "numpy._array_api_info.__array_namespace_info__.dtypes", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5], "arg_names": ["self", "device", "kind"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy._array_api_info.__array_namespace_info__.dtypes", "name": "dtypes", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5], "arg_names": ["self", "device", "kind"], "arg_types": ["numpy._array_api_info.__array_namespace_info__", {".class": "TypeAliasType", "args": [], "type_ref": "numpy._array_api_info._DeviceLike"}, {".class": "NoneType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "dtypes of __array_namespace_info__", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy._array_api_info._DTypes"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy._array_api_info.__array_namespace_info__.dtypes", "name": "dtypes", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5], "arg_names": ["self", "device", "kind"], "arg_types": ["numpy._array_api_info.__array_namespace_info__", {".class": "TypeAliasType", "args": [], "type_ref": "numpy._array_api_info._DeviceLike"}, {".class": "NoneType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "dtypes of __array_namespace_info__", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy._array_api_info._DTypes"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 3], "arg_names": ["self", "device", "kind"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy._array_api_info.__array_namespace_info__.dtypes", "name": "dtypes", "type": {".class": "CallableType", "arg_kinds": [0, 5, 3], "arg_names": ["self", "device", "kind"], "arg_types": ["numpy._array_api_info.__array_namespace_info__", {".class": "TypeAliasType", "args": [], "type_ref": "numpy._array_api_info._DeviceLike"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._array_api_info._KindBool"}], "type_ref": "numpy._array_api_info._Permute1"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "dtypes of __array_namespace_info__", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy._array_api_info._DTypesBool"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy._array_api_info.__array_namespace_info__.dtypes", "name": "dtypes", "type": {".class": "CallableType", "arg_kinds": [0, 5, 3], "arg_names": ["self", "device", "kind"], "arg_types": ["numpy._array_api_info.__array_namespace_info__", {".class": "TypeAliasType", "args": [], "type_ref": "numpy._array_api_info._DeviceLike"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._array_api_info._KindBool"}], "type_ref": "numpy._array_api_info._Permute1"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "dtypes of __array_namespace_info__", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy._array_api_info._DTypesBool"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 3], "arg_names": ["self", "device", "kind"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy._array_api_info.__array_namespace_info__.dtypes", "name": "dtypes", "type": {".class": "CallableType", "arg_kinds": [0, 5, 3], "arg_names": ["self", "device", "kind"], "arg_types": ["numpy._array_api_info.__array_namespace_info__", {".class": "TypeAliasType", "args": [], "type_ref": "numpy._array_api_info._DeviceLike"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._array_api_info._KindInt"}], "type_ref": "numpy._array_api_info._Permute1"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "dtypes of __array_namespace_info__", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy._array_api_info._DTypesInt"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy._array_api_info.__array_namespace_info__.dtypes", "name": "dtypes", "type": {".class": "CallableType", "arg_kinds": [0, 5, 3], "arg_names": ["self", "device", "kind"], "arg_types": ["numpy._array_api_info.__array_namespace_info__", {".class": "TypeAliasType", "args": [], "type_ref": "numpy._array_api_info._DeviceLike"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._array_api_info._KindInt"}], "type_ref": "numpy._array_api_info._Permute1"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "dtypes of __array_namespace_info__", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy._array_api_info._DTypesInt"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 3], "arg_names": ["self", "device", "kind"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy._array_api_info.__array_namespace_info__.dtypes", "name": "dtypes", "type": {".class": "CallableType", "arg_kinds": [0, 5, 3], "arg_names": ["self", "device", "kind"], "arg_types": ["numpy._array_api_info.__array_namespace_info__", {".class": "TypeAliasType", "args": [], "type_ref": "numpy._array_api_info._DeviceLike"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._array_api_info._KindUInt"}], "type_ref": "numpy._array_api_info._Permute1"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "dtypes of __array_namespace_info__", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy._array_api_info._DTypesUInt"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy._array_api_info.__array_namespace_info__.dtypes", "name": "dtypes", "type": {".class": "CallableType", "arg_kinds": [0, 5, 3], "arg_names": ["self", "device", "kind"], "arg_types": ["numpy._array_api_info.__array_namespace_info__", {".class": "TypeAliasType", "args": [], "type_ref": "numpy._array_api_info._DeviceLike"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._array_api_info._KindUInt"}], "type_ref": "numpy._array_api_info._Permute1"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "dtypes of __array_namespace_info__", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy._array_api_info._DTypesUInt"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 3], "arg_names": ["self", "device", "kind"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy._array_api_info.__array_namespace_info__.dtypes", "name": "dtypes", "type": {".class": "CallableType", "arg_kinds": [0, 5, 3], "arg_names": ["self", "device", "kind"], "arg_types": ["numpy._array_api_info.__array_namespace_info__", {".class": "TypeAliasType", "args": [], "type_ref": "numpy._array_api_info._DeviceLike"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._array_api_info._KindFloat"}], "type_ref": "numpy._array_api_info._Permute1"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "dtypes of __array_namespace_info__", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy._array_api_info._DTypesFloat"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy._array_api_info.__array_namespace_info__.dtypes", "name": "dtypes", "type": {".class": "CallableType", "arg_kinds": [0, 5, 3], "arg_names": ["self", "device", "kind"], "arg_types": ["numpy._array_api_info.__array_namespace_info__", {".class": "TypeAliasType", "args": [], "type_ref": "numpy._array_api_info._DeviceLike"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._array_api_info._KindFloat"}], "type_ref": "numpy._array_api_info._Permute1"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "dtypes of __array_namespace_info__", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy._array_api_info._DTypesFloat"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 3], "arg_names": ["self", "device", "kind"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy._array_api_info.__array_namespace_info__.dtypes", "name": "dtypes", "type": {".class": "CallableType", "arg_kinds": [0, 5, 3], "arg_names": ["self", "device", "kind"], "arg_types": ["numpy._array_api_info.__array_namespace_info__", {".class": "TypeAliasType", "args": [], "type_ref": "numpy._array_api_info._DeviceLike"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._array_api_info._KindComplex"}], "type_ref": "numpy._array_api_info._Permute1"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "dtypes of __array_namespace_info__", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy._array_api_info._DTypesComplex"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy._array_api_info.__array_namespace_info__.dtypes", "name": "dtypes", "type": {".class": "CallableType", "arg_kinds": [0, 5, 3], "arg_names": ["self", "device", "kind"], "arg_types": ["numpy._array_api_info.__array_namespace_info__", {".class": "TypeAliasType", "args": [], "type_ref": "numpy._array_api_info._DeviceLike"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._array_api_info._KindComplex"}], "type_ref": "numpy._array_api_info._Permute1"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "dtypes of __array_namespace_info__", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy._array_api_info._DTypesComplex"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 3], "arg_names": ["self", "device", "kind"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy._array_api_info.__array_namespace_info__.dtypes", "name": "dtypes", "type": {".class": "CallableType", "arg_kinds": [0, 5, 3], "arg_names": ["self", "device", "kind"], "arg_types": ["numpy._array_api_info.__array_namespace_info__", {".class": "TypeAliasType", "args": [], "type_ref": "numpy._array_api_info._DeviceLike"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._array_api_info._KindInteger"}], "type_ref": "numpy._array_api_info._Permute1"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._array_api_info._KindInt"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._array_api_info._KindUInt"}], "type_ref": "numpy._array_api_info._Permute2"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "dtypes of __array_namespace_info__", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy._array_api_info._DTypesInteger"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy._array_api_info.__array_namespace_info__.dtypes", "name": "dtypes", "type": {".class": "CallableType", "arg_kinds": [0, 5, 3], "arg_names": ["self", "device", "kind"], "arg_types": ["numpy._array_api_info.__array_namespace_info__", {".class": "TypeAliasType", "args": [], "type_ref": "numpy._array_api_info._DeviceLike"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._array_api_info._KindInteger"}], "type_ref": "numpy._array_api_info._Permute1"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._array_api_info._KindInt"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._array_api_info._KindUInt"}], "type_ref": "numpy._array_api_info._Permute2"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "dtypes of __array_namespace_info__", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy._array_api_info._DTypesInteger"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 3], "arg_names": ["self", "device", "kind"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy._array_api_info.__array_namespace_info__.dtypes", "name": "dtypes", "type": {".class": "CallableType", "arg_kinds": [0, 5, 3], "arg_names": ["self", "device", "kind"], "arg_types": ["numpy._array_api_info.__array_namespace_info__", {".class": "TypeAliasType", "args": [], "type_ref": "numpy._array_api_info._DeviceLike"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._array_api_info._KindNumber"}], "type_ref": "numpy._array_api_info._Permute1"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._array_api_info._KindInteger"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._array_api_info._KindFloat"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._array_api_info._KindComplex"}], "type_ref": "numpy._array_api_info._Permute3"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "dtypes of __array_namespace_info__", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy._array_api_info._DTypesNumber"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy._array_api_info.__array_namespace_info__.dtypes", "name": "dtypes", "type": {".class": "CallableType", "arg_kinds": [0, 5, 3], "arg_names": ["self", "device", "kind"], "arg_types": ["numpy._array_api_info.__array_namespace_info__", {".class": "TypeAliasType", "args": [], "type_ref": "numpy._array_api_info._DeviceLike"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._array_api_info._KindNumber"}], "type_ref": "numpy._array_api_info._Permute1"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._array_api_info._KindInteger"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._array_api_info._KindFloat"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._array_api_info._KindComplex"}], "type_ref": "numpy._array_api_info._Permute3"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "dtypes of __array_namespace_info__", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy._array_api_info._DTypesNumber"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 3], "arg_names": ["self", "device", "kind"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy._array_api_info.__array_namespace_info__.dtypes", "name": "dtypes", "type": {".class": "CallableType", "arg_kinds": [0, 5, 3], "arg_names": ["self", "device", "kind"], "arg_types": ["numpy._array_api_info.__array_namespace_info__", {".class": "TypeAliasType", "args": [], "type_ref": "numpy._array_api_info._DeviceLike"}, {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "dtypes of __array_namespace_info__", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy._array_api_info._EmptyDict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy._array_api_info.__array_namespace_info__.dtypes", "name": "dtypes", "type": {".class": "CallableType", "arg_kinds": [0, 5, 3], "arg_names": ["self", "device", "kind"], "arg_types": ["numpy._array_api_info.__array_namespace_info__", {".class": "TypeAliasType", "args": [], "type_ref": "numpy._array_api_info._DeviceLike"}, {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "dtypes of __array_namespace_info__", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy._array_api_info._EmptyDict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 3], "arg_names": ["self", "device", "kind"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy._array_api_info.__array_namespace_info__.dtypes", "name": "dtypes", "type": {".class": "CallableType", "arg_kinds": [0, 5, 3], "arg_names": ["self", "device", "kind"], "arg_types": ["numpy._array_api_info.__array_namespace_info__", {".class": "TypeAliasType", "args": [], "type_ref": "numpy._array_api_info._DeviceLike"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._array_api_info._Kind"}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "dtypes of __array_namespace_info__", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy._array_api_info._DTypesUnion"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy._array_api_info.__array_namespace_info__.dtypes", "name": "dtypes", "type": {".class": "CallableType", "arg_kinds": [0, 5, 3], "arg_names": ["self", "device", "kind"], "arg_types": ["numpy._array_api_info.__array_namespace_info__", {".class": "TypeAliasType", "args": [], "type_ref": "numpy._array_api_info._DeviceLike"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._array_api_info._Kind"}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "dtypes of __array_namespace_info__", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy._array_api_info._DTypesUnion"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 5, 5], "arg_names": ["self", "device", "kind"], "arg_types": ["numpy._array_api_info.__array_namespace_info__", {".class": "TypeAliasType", "args": [], "type_ref": "numpy._array_api_info._DeviceLike"}, {".class": "NoneType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "dtypes of __array_namespace_info__", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy._array_api_info._DTypes"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 5, 3], "arg_names": ["self", "device", "kind"], "arg_types": ["numpy._array_api_info.__array_namespace_info__", {".class": "TypeAliasType", "args": [], "type_ref": "numpy._array_api_info._DeviceLike"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._array_api_info._KindBool"}], "type_ref": "numpy._array_api_info._Permute1"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "dtypes of __array_namespace_info__", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy._array_api_info._DTypesBool"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 5, 3], "arg_names": ["self", "device", "kind"], "arg_types": ["numpy._array_api_info.__array_namespace_info__", {".class": "TypeAliasType", "args": [], "type_ref": "numpy._array_api_info._DeviceLike"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._array_api_info._KindInt"}], "type_ref": "numpy._array_api_info._Permute1"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "dtypes of __array_namespace_info__", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy._array_api_info._DTypesInt"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 5, 3], "arg_names": ["self", "device", "kind"], "arg_types": ["numpy._array_api_info.__array_namespace_info__", {".class": "TypeAliasType", "args": [], "type_ref": "numpy._array_api_info._DeviceLike"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._array_api_info._KindUInt"}], "type_ref": "numpy._array_api_info._Permute1"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "dtypes of __array_namespace_info__", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy._array_api_info._DTypesUInt"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 5, 3], "arg_names": ["self", "device", "kind"], "arg_types": ["numpy._array_api_info.__array_namespace_info__", {".class": "TypeAliasType", "args": [], "type_ref": "numpy._array_api_info._DeviceLike"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._array_api_info._KindFloat"}], "type_ref": "numpy._array_api_info._Permute1"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "dtypes of __array_namespace_info__", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy._array_api_info._DTypesFloat"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 5, 3], "arg_names": ["self", "device", "kind"], "arg_types": ["numpy._array_api_info.__array_namespace_info__", {".class": "TypeAliasType", "args": [], "type_ref": "numpy._array_api_info._DeviceLike"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._array_api_info._KindComplex"}], "type_ref": "numpy._array_api_info._Permute1"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "dtypes of __array_namespace_info__", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy._array_api_info._DTypesComplex"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 5, 3], "arg_names": ["self", "device", "kind"], "arg_types": ["numpy._array_api_info.__array_namespace_info__", {".class": "TypeAliasType", "args": [], "type_ref": "numpy._array_api_info._DeviceLike"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._array_api_info._KindInteger"}], "type_ref": "numpy._array_api_info._Permute1"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._array_api_info._KindInt"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._array_api_info._KindUInt"}], "type_ref": "numpy._array_api_info._Permute2"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "dtypes of __array_namespace_info__", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy._array_api_info._DTypesInteger"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 5, 3], "arg_names": ["self", "device", "kind"], "arg_types": ["numpy._array_api_info.__array_namespace_info__", {".class": "TypeAliasType", "args": [], "type_ref": "numpy._array_api_info._DeviceLike"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._array_api_info._KindNumber"}], "type_ref": "numpy._array_api_info._Permute1"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._array_api_info._KindInteger"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._array_api_info._KindFloat"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._array_api_info._KindComplex"}], "type_ref": "numpy._array_api_info._Permute3"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "dtypes of __array_namespace_info__", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy._array_api_info._DTypesNumber"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 5, 3], "arg_names": ["self", "device", "kind"], "arg_types": ["numpy._array_api_info.__array_namespace_info__", {".class": "TypeAliasType", "args": [], "type_ref": "numpy._array_api_info._DeviceLike"}, {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "dtypes of __array_namespace_info__", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy._array_api_info._EmptyDict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 5, 3], "arg_names": ["self", "device", "kind"], "arg_types": ["numpy._array_api_info.__array_namespace_info__", {".class": "TypeAliasType", "args": [], "type_ref": "numpy._array_api_info._DeviceLike"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._array_api_info._Kind"}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "dtypes of __array_namespace_info__", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy._array_api_info._DTypesUnion"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._array_api_info.__array_namespace_info__.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "numpy._array_api_info.__array_namespace_info__", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy._array_api_info.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy._array_api_info.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy._array_api_info.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy._array_api_info.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy._array_api_info.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "final": {".class": "SymbolTableNode", "cross_ref": "typing.final", "kind": "Gdef", "module_hidden": true, "module_public": false}, "np": {".class": "SymbolTableNode", "cross_ref": "numpy", "kind": "Gdef", "module_hidden": true, "module_public": false}, "overload": {".class": "SymbolTableNode", "cross_ref": "typing.overload", "kind": "Gdef", "module_hidden": true, "module_public": false}, "type_check_only": {".class": "SymbolTableNode", "cross_ref": "typing.type_check_only", "kind": "Gdef", "module_hidden": true, "module_public": false}}, "path": "c:\\Python313\\Lib\\site-packages\\numpy\\_array_api_info.pyi"}