{"data_mtime": 1750100862, "dep_lines": [12, 12, 12, 13, 14, 3, 5, 6, 7, 8, 10, 1, 1, 1], "dep_prios": [10, 10, 20, 5, 5, 5, 5, 5, 5, 5, 5, 5, 30, 30], "dependencies": ["pydantic._internal._decorators", "pydantic._internal._decorators_v1", "pydantic._internal", "pydantic.errors", "pydantic.warnings", "__future__", "functools", "types", "typing", "warnings", "typing_extensions", "builtins", "_frozen_importlib", "abc"], "hash": "b1c4b8e94673cb8b3b32e811191fb55007203d0e", "id": "pydantic.deprecated.class_validators", "ignore_all": true, "interface_hash": "ab2656c784984895ac7e759ad2d5ac881c6b7d04", "mtime": 1748795478, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Python313\\Lib\\site-packages\\pydantic\\deprecated\\class_validators.py", "plugin_data": null, "size": 10245, "suppressed": [], "version_id": "1.15.0"}