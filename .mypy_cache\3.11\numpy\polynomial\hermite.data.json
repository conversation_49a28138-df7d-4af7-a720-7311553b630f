{".class": "MypyFile", "_fullname": "numpy.polynomial.hermite", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "ABCPolyBase": {".class": "SymbolTableNode", "cross_ref": "numpy.polynomial._polybase.ABCPolyBase", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Final": {".class": "SymbolTableNode", "cross_ref": "typing.Final", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Hermite": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "H"}], "extra_attrs": null, "type_ref": "numpy.polynomial._polybase.ABCPolyBase"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy.polynomial.hermite.Hermite", "name": "Hermite", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "numpy.polynomial.hermite.Hermite", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "numpy.polynomial.hermite", "mro": ["numpy.polynomial.hermite.Hermite", "numpy.polynomial._polybase.ABCPolyBase", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial.hermite.Hermite.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "numpy.polynomial.hermite.Hermite", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "L": {".class": "SymbolTableNode", "cross_ref": "typing.Literal", "kind": "Gdef", "module_hidden": true, "module_public": false}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_Array1": {".class": "SymbolTableNode", "cross_ref": "numpy.polynomial._polytypes._Array1", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_Array2": {".class": "SymbolTableNode", "cross_ref": "numpy.polynomial._polytypes._Array2", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_FuncBinOp": {".class": "SymbolTableNode", "cross_ref": "numpy.polynomial._polytypes._FuncBinOp", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_FuncCompanion": {".class": "SymbolTableNode", "cross_ref": "numpy.polynomial._polytypes._FuncCompanion", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_FuncDer": {".class": "SymbolTableNode", "cross_ref": "numpy.polynomial._polytypes._FuncDer", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_FuncFit": {".class": "SymbolTableNode", "cross_ref": "numpy.polynomial._polytypes._FuncFit", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_FuncFromRoots": {".class": "SymbolTableNode", "cross_ref": "numpy.polynomial._polytypes._FuncFromRoots", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_FuncGauss": {".class": "SymbolTableNode", "cross_ref": "numpy.polynomial._polytypes._FuncGauss", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_FuncInteg": {".class": "SymbolTableNode", "cross_ref": "numpy.polynomial._polytypes._FuncInteg", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_FuncLine": {".class": "SymbolTableNode", "cross_ref": "numpy.polynomial._polytypes._FuncLine", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_FuncPoly2Ortho": {".class": "SymbolTableNode", "cross_ref": "numpy.polynomial._polytypes._FuncPoly2Ortho", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_FuncPow": {".class": "SymbolTableNode", "cross_ref": "numpy.polynomial._polytypes._FuncPow", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_FuncRoots": {".class": "SymbolTableNode", "cross_ref": "numpy.polynomial._polytypes._FuncRoots", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_FuncUnOp": {".class": "SymbolTableNode", "cross_ref": "numpy.polynomial._polytypes._FuncUnOp", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_FuncVal": {".class": "SymbolTableNode", "cross_ref": "numpy.polynomial._polytypes._FuncVal", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_FuncVal2D": {".class": "SymbolTableNode", "cross_ref": "numpy.polynomial._polytypes._FuncVal2D", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_FuncVal3D": {".class": "SymbolTableNode", "cross_ref": "numpy.polynomial._polytypes._FuncVal3D", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_FuncValFromRoots": {".class": "SymbolTableNode", "cross_ref": "numpy.polynomial._polytypes._FuncValFromRoots", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_FuncVander": {".class": "SymbolTableNode", "cross_ref": "numpy.polynomial._polytypes._FuncVander", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_FuncVander2D": {".class": "SymbolTableNode", "cross_ref": "numpy.polynomial._polytypes._FuncVander2D", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_FuncVander3D": {".class": "SymbolTableNode", "cross_ref": "numpy.polynomial._polytypes._FuncVander3D", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_FuncWeight": {".class": "SymbolTableNode", "cross_ref": "numpy.polynomial._polytypes._FuncWeight", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_ND": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial.hermite._ND", "name": "_ND", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "numpy.polynomial.hermite.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.hermite.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.hermite.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.hermite.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.hermite.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.hermite.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.hermite.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_normed_hermite_n": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["x", "n"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.polynomial.hermite._normed_hermite_n", "name": "_normed_hermite_n", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["x", "n"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial.hermite._ND", "id": -1, "name": "_ND", "namespace": "numpy.polynomial.hermite._normed_hermite_n", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "Instance", "args": ["numpy.float64"], "extra_attrs": null, "type_ref": "numpy.dtype"}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}, {".class": "UnionType", "items": ["builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_normed_hermite_n", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial.hermite._ND", "id": -1, "name": "_ND", "namespace": "numpy.polynomial.hermite._normed_hermite_n", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "Instance", "args": ["numpy.float64"], "extra_attrs": null, "type_ref": "numpy.dtype"}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial.hermite._ND", "id": -1, "name": "_ND", "namespace": "numpy.polynomial.hermite._normed_hermite_n", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}]}}}, "herm2poly": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.hermite.herm2poly", "name": "herm2poly", "type": {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "herm2poly"}], "extra_attrs": null, "type_ref": "numpy.polynomial._polytypes._FuncUnOp"}}}, "hermadd": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.hermite.hermadd", "name": "hermadd", "type": {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "hermadd"}], "extra_attrs": null, "type_ref": "numpy.polynomial._polytypes._FuncBinOp"}}}, "hermcompanion": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.hermite.hermcompanion", "name": "hermcompanion", "type": {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "hermcompanion"}], "extra_attrs": null, "type_ref": "numpy.polynomial._polytypes._FuncCompanion"}}}, "hermder": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.hermite.hermder", "name": "<PERSON><PERSON><PERSON>", "type": {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "<PERSON><PERSON><PERSON>"}], "extra_attrs": null, "type_ref": "numpy.polynomial._polytypes._FuncDer"}}}, "hermdiv": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.hermite.hermdiv", "name": "hermdiv", "type": {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "hermdiv"}], "extra_attrs": null, "type_ref": "numpy.polynomial._polytypes._FuncBinOp"}}}, "hermdomain": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "numpy.polynomial.hermite.hermdomain", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": {".class": "TypeAliasType", "args": ["numpy.float64"], "type_ref": "numpy.polynomial._polytypes._Array2"}}}, "hermfit": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.hermite.hermfit", "name": "hermfit", "type": {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "hermfit"}], "extra_attrs": null, "type_ref": "numpy.polynomial._polytypes._FuncFit"}}}, "hermfromroots": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.hermite.hermfromroots", "name": "hermfromroots", "type": {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "hermfromroots"}], "extra_attrs": null, "type_ref": "numpy.polynomial._polytypes._FuncFromRoots"}}}, "hermgauss": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.hermite.hermgauss", "name": "herm<PERSON>ss", "type": {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "herm<PERSON>ss"}], "extra_attrs": null, "type_ref": "numpy.polynomial._polytypes._FuncGauss"}}}, "hermgrid2d": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.hermite.hermgrid2d", "name": "hermgrid2d", "type": {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "hermgrid2d"}], "extra_attrs": null, "type_ref": "numpy.polynomial._polytypes._FuncVal2D"}}}, "hermgrid3d": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.hermite.hermgrid3d", "name": "hermgrid3d", "type": {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "hermgrid3d"}], "extra_attrs": null, "type_ref": "numpy.polynomial._polytypes._FuncVal3D"}}}, "hermint": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.hermite.hermint", "name": "hermint", "type": {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "hermint"}], "extra_attrs": null, "type_ref": "numpy.polynomial._polytypes._FuncInteg"}}}, "hermline": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.hermite.hermline", "name": "<PERSON><PERSON><PERSON>", "type": {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "<PERSON><PERSON><PERSON>"}], "extra_attrs": null, "type_ref": "numpy.polynomial._polytypes._FuncLine"}}}, "hermmul": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.hermite.hermmul", "name": "<PERSON><PERSON><PERSON>", "type": {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "<PERSON><PERSON><PERSON>"}], "extra_attrs": null, "type_ref": "numpy.polynomial._polytypes._FuncBinOp"}}}, "hermmulx": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.hermite.hermmulx", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "<PERSON><PERSON><PERSON><PERSON>"}], "extra_attrs": null, "type_ref": "numpy.polynomial._polytypes._FuncUnOp"}}}, "hermone": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "numpy.polynomial.hermite.hermone", "name": "hermone", "type": {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.int_"}], "type_ref": "numpy.polynomial._polytypes._Array1"}}}, "hermpow": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.hermite.hermpow", "name": "<PERSON><PERSON>w", "type": {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "<PERSON><PERSON>w"}], "extra_attrs": null, "type_ref": "numpy.polynomial._polytypes._FuncPow"}}}, "hermroots": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.hermite.hermroots", "name": "herm<PERSON>", "type": {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "herm<PERSON>"}], "extra_attrs": null, "type_ref": "numpy.polynomial._polytypes._FuncRoots"}}}, "hermsub": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.hermite.hermsub", "name": "hermsub", "type": {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "hermsub"}], "extra_attrs": null, "type_ref": "numpy.polynomial._polytypes._FuncBinOp"}}}, "hermtrim": {".class": "SymbolTableNode", "cross_ref": "numpy.polynomial.polyutils.trimcoef", "kind": "Gdef"}, "hermval": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.hermite.hermval", "name": "<PERSON><PERSON><PERSON>", "type": {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "<PERSON><PERSON><PERSON>"}], "extra_attrs": null, "type_ref": "numpy.polynomial._polytypes._FuncVal"}}}, "hermval2d": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.hermite.hermval2d", "name": "hermval2d", "type": {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "hermval2d"}], "extra_attrs": null, "type_ref": "numpy.polynomial._polytypes._FuncVal2D"}}}, "hermval3d": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.hermite.hermval3d", "name": "hermval3d", "type": {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "hermval3d"}], "extra_attrs": null, "type_ref": "numpy.polynomial._polytypes._FuncVal3D"}}}, "hermvalfromroots": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.hermite.hermvalfromroots", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "extra_attrs": null, "type_ref": "numpy.polynomial._polytypes._FuncValFromRoots"}}}, "hermvander": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.hermite.hermvander", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "<PERSON><PERSON><PERSON><PERSON>"}], "extra_attrs": null, "type_ref": "numpy.polynomial._polytypes._FuncVander"}}}, "hermvander2d": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.hermite.hermvander2d", "name": "hermvander2d", "type": {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "hermvander2d"}], "extra_attrs": null, "type_ref": "numpy.polynomial._polytypes._FuncVander2D"}}}, "hermvander3d": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.hermite.hermvander3d", "name": "hermvander3d", "type": {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "hermvander3d"}], "extra_attrs": null, "type_ref": "numpy.polynomial._polytypes._FuncVander3D"}}}, "hermweight": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.hermite.hermweight", "name": "hermweight", "type": {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "hermweight"}], "extra_attrs": null, "type_ref": "numpy.polynomial._polytypes._FuncWeight"}}}, "hermx": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "numpy.polynomial.hermite.hermx", "name": "hermx", "type": {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.int_"}], "type_ref": "numpy.polynomial._polytypes._Array2"}}}, "hermzero": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "numpy.polynomial.hermite.hermzero", "name": "hermzero", "type": {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.int_"}], "type_ref": "numpy.polynomial._polytypes._Array1"}}}, "np": {".class": "SymbolTableNode", "cross_ref": "numpy", "kind": "Gdef", "module_hidden": true, "module_public": false}, "poly2herm": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.hermite.poly2herm", "name": "poly2herm", "type": {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "poly2herm"}], "extra_attrs": null, "type_ref": "numpy.polynomial._polytypes._FuncPoly2Ortho"}}}}, "path": "c:\\Python313\\Lib\\site-packages\\numpy\\polynomial\\hermite.pyi"}