{"data_mtime": 1750100861, "dep_lines": [30, 31, 32, 38, 39, 40, 30, 1, 2, 12, 36, 1, 1, 1, 1, 35], "dep_prios": [10, 5, 5, 25, 25, 25, 20, 10, 5, 5, 25, 5, 30, 30, 30, 25], "dependencies": ["pydantic.v1.errors", "pydantic.v1.utils", "pydantic.v1.validators", "pydantic.v1.config", "pydantic.v1.fields", "pydantic.v1.typing", "pydantic.v1", "re", "ipaddress", "typing", "typing_extensions", "builtins", "_frozen_importlib", "abc", "enum"], "hash": "ac587133cb640e4c9a52aa2aadcaf43d1fe34dd9", "id": "pydantic.v1.networks", "ignore_all": true, "interface_hash": "727119f62e7f16e58b200ea95ed48907e9f0b125", "mtime": 1748795478, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Python313\\Lib\\site-packages\\pydantic\\v1\\networks.py", "plugin_data": null, "size": 22124, "suppressed": ["email_validator"], "version_id": "1.15.0"}