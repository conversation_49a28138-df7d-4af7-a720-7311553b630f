{"data_mtime": 1750100849, "dep_lines": [217, 41, 49, 49, 49, 57, 58, 59, 90, 221, 221, 221, 221, 221, 222, 1126, 1798, 27, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 42, 43, 44, 49, 216, 1, 1, 1, 1, 1, 1, 1, 1, 1, 219, 63], "dep_prios": [25, 5, 10, 10, 10, 5, 5, 5, 10, 20, 20, 20, 20, 20, 25, 20, 20, 5, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 5, 5, 5, 5, 25, 30, 30, 30, 30, 30, 30, 30, 30, 30, 25, 5], "dependencies": ["xml.etree.ElementTree", "collections.abc", "PIL.ExifTags", "PIL.ImageMode", "PIL.TiffTags", "PIL._binary", "PIL._deprecate", "PIL._util", "PIL._imaging", "PIL.ImageFile", "PIL.ImageFilter", "PIL.ImagePalette", "PIL.ImageQt", "PIL.TiffImagePlugin", "PIL._typing", "PIL.ImageCms", "PIL.ImageColor", "__future__", "abc", "atexit", "builtins", "io", "logging", "math", "os", "re", "struct", "sys", "tempfile", "warnings", "enum", "types", "typing", "PIL", "mmap", "PIL._imagingcms", "_frozen_importlib", "_io", "_typeshed", "_warnings", "genericpath", "numpy", "posixpath", "typing_extensions"], "hash": "1fd6b34e13b76c6d4cda50995716e66478e67e80", "id": "PIL.Image", "ignore_all": true, "interface_hash": "0f056af770754b58034019539fc5d52f3f36f656", "mtime": 1745696559, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Python313\\Lib\\site-packages\\PIL\\Image.py", "plugin_data": null, "size": 152063, "suppressed": ["IPython.lib.pretty", "defusedxml"], "version_id": "1.15.0"}