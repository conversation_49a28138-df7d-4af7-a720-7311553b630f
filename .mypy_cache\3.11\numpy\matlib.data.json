{".class": "MypyFile", "_fullname": "numpy.matlib", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_hidden": true, "module_public": false}, "False_": {".class": "SymbolTableNode", "cross_ref": "numpy.False_", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing.Literal", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ScalarType": {".class": "SymbolTableNode", "cross_ref": "numpy._core.numerictypes.ScalarType", "kind": "Gdef", "module_hidden": true, "module_public": false}, "True_": {".class": "SymbolTableNode", "cross_ref": "numpy.True_", "kind": "Gdef", "module_hidden": true, "module_public": false}, "TypeAlias": {".class": "SymbolTableNode", "cross_ref": "typing.TypeAlias", "kind": "Gdef", "module_hidden": true, "module_public": false}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_ArrayLike": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._array_like._ArrayLike", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_DTypeLike": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._dtype_like._DTypeLike", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_Matrix": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.matlib._T", "id": 1, "name": "_T", "namespace": "numpy.matlib._Matrix", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "column": 0, "fullname": "numpy.matlib._Matrix", "line": 513, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.matlib._T", "id": 1, "name": "_T", "namespace": "numpy.matlib._Matrix", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy.dtype"}], "extra_attrs": null, "type_ref": "numpy.matrix"}}}, "_Order": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "numpy.matlib._Order", "line": 514, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "C"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "F"}], "uses_pep604_syntax": false}}}, "_T": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.matlib._T", "name": "_T", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "numpy.matlib.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.matlib.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__array_namespace_info__": {".class": "SymbolTableNode", "cross_ref": "numpy._array_api_info.__array_namespace_info__", "kind": "Gdef", "module_hidden": true, "module_public": false}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.matlib.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.matlib.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.matlib.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.matlib.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.matlib.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "__version__": {".class": "SymbolTableNode", "cross_ref": "numpy.__version__", "kind": "Gdef", "module_hidden": true, "module_public": false}, "abs": {".class": "SymbolTableNode", "cross_ref": "numpy.abs", "kind": "Gdef", "module_hidden": true, "module_public": false}, "absolute": {".class": "SymbolTableNode", "cross_ref": "numpy.absolute", "kind": "Gdef", "module_hidden": true, "module_public": false}, "acos": {".class": "SymbolTableNode", "cross_ref": "numpy.acos", "kind": "Gdef", "module_hidden": true, "module_public": false}, "acosh": {".class": "SymbolTableNode", "cross_ref": "numpy.acosh", "kind": "Gdef", "module_hidden": true, "module_public": false}, "add": {".class": "SymbolTableNode", "cross_ref": "numpy.add", "kind": "Gdef", "module_hidden": true, "module_public": false}, "all": {".class": "SymbolTableNode", "cross_ref": "numpy._core.fromnumeric.all", "kind": "Gdef", "module_hidden": true, "module_public": false}, "allclose": {".class": "SymbolTableNode", "cross_ref": "numpy._core.numeric.allclose", "kind": "Gdef", "module_hidden": true, "module_public": false}, "amax": {".class": "SymbolTableNode", "cross_ref": "numpy._core.fromnumeric.amax", "kind": "Gdef", "module_hidden": true, "module_public": false}, "amin": {".class": "SymbolTableNode", "cross_ref": "numpy._core.fromnumeric.amin", "kind": "Gdef", "module_hidden": true, "module_public": false}, "angle": {".class": "SymbolTableNode", "cross_ref": "numpy.lib._function_base_impl.angle", "kind": "Gdef", "module_hidden": true, "module_public": false}, "any": {".class": "SymbolTableNode", "cross_ref": "numpy._core.fromnumeric.any", "kind": "Gdef", "module_hidden": true, "module_public": false}, "append": {".class": "SymbolTableNode", "cross_ref": "numpy.lib._function_base_impl.append", "kind": "Gdef", "module_hidden": true, "module_public": false}, "apply_along_axis": {".class": "SymbolTableNode", "cross_ref": "numpy.lib._shape_base_impl.apply_along_axis", "kind": "Gdef", "module_hidden": true, "module_public": false}, "apply_over_axes": {".class": "SymbolTableNode", "cross_ref": "numpy.lib._shape_base_impl.apply_over_axes", "kind": "Gdef", "module_hidden": true, "module_public": false}, "arange": {".class": "SymbolTableNode", "cross_ref": "numpy._core.multiarray.arange", "kind": "Gdef", "module_hidden": true, "module_public": false}, "arccos": {".class": "SymbolTableNode", "cross_ref": "numpy.arccos", "kind": "Gdef", "module_hidden": true, "module_public": false}, "arccosh": {".class": "SymbolTableNode", "cross_ref": "numpy.arc<PERSON>", "kind": "Gdef", "module_hidden": true, "module_public": false}, "arcsin": {".class": "SymbolTableNode", "cross_ref": "numpy.arcsin", "kind": "Gdef", "module_hidden": true, "module_public": false}, "arcsinh": {".class": "SymbolTableNode", "cross_ref": "numpy.arcsinh", "kind": "Gdef", "module_hidden": true, "module_public": false}, "arctan": {".class": "SymbolTableNode", "cross_ref": "numpy.arctan", "kind": "Gdef", "module_hidden": true, "module_public": false}, "arctan2": {".class": "SymbolTableNode", "cross_ref": "numpy.arctan2", "kind": "Gdef", "module_hidden": true, "module_public": false}, "arctanh": {".class": "SymbolTableNode", "cross_ref": "numpy.arctanh", "kind": "Gdef", "module_hidden": true, "module_public": false}, "argmax": {".class": "SymbolTableNode", "cross_ref": "numpy._core.fromnumeric.argmax", "kind": "Gdef", "module_hidden": true, "module_public": false}, "argmin": {".class": "SymbolTableNode", "cross_ref": "numpy._core.fromnumeric.argmin", "kind": "Gdef", "module_hidden": true, "module_public": false}, "argpartition": {".class": "SymbolTableNode", "cross_ref": "numpy._core.fromnumeric.argpartition", "kind": "Gdef", "module_hidden": true, "module_public": false}, "argsort": {".class": "SymbolTableNode", "cross_ref": "numpy._core.fromnumeric.argsort", "kind": "Gdef", "module_hidden": true, "module_public": false}, "argwhere": {".class": "SymbolTableNode", "cross_ref": "numpy._core.numeric.argwhere", "kind": "Gdef", "module_hidden": true, "module_public": false}, "around": {".class": "SymbolTableNode", "cross_ref": "numpy._core.fromnumeric.around", "kind": "Gdef", "module_hidden": true, "module_public": false}, "array": {".class": "SymbolTableNode", "cross_ref": "numpy._core.multiarray.array", "kind": "Gdef", "module_hidden": true, "module_public": false}, "array2string": {".class": "SymbolTableNode", "cross_ref": "numpy._core.arrayprint.array2string", "kind": "Gdef", "module_hidden": true, "module_public": false}, "array_equal": {".class": "SymbolTableNode", "cross_ref": "numpy._core.numeric.array_equal", "kind": "Gdef", "module_hidden": true, "module_public": false}, "array_equiv": {".class": "SymbolTableNode", "cross_ref": "numpy._core.numeric.array_equiv", "kind": "Gdef", "module_hidden": true, "module_public": false}, "array_repr": {".class": "SymbolTableNode", "cross_ref": "numpy._core.arrayprint.array_repr", "kind": "Gdef", "module_hidden": true, "module_public": false}, "array_split": {".class": "SymbolTableNode", "cross_ref": "numpy.lib._shape_base_impl.array_split", "kind": "Gdef", "module_hidden": true, "module_public": false}, "array_str": {".class": "SymbolTableNode", "cross_ref": "numpy._core.arrayprint.array_str", "kind": "Gdef", "module_hidden": true, "module_public": false}, "asanyarray": {".class": "SymbolTableNode", "cross_ref": "numpy._core.multiarray.asanyarray", "kind": "Gdef", "module_hidden": true, "module_public": false}, "asarray": {".class": "SymbolTableNode", "cross_ref": "numpy._core.multiarray.asarray", "kind": "Gdef", "module_hidden": true, "module_public": false}, "asarray_chkfinite": {".class": "SymbolTableNode", "cross_ref": "numpy.lib._function_base_impl.asarray_chkfinite", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ascontiguousarray": {".class": "SymbolTableNode", "cross_ref": "numpy._core.multiarray.ascontiguousarray", "kind": "Gdef", "module_hidden": true, "module_public": false}, "asfortranarray": {".class": "SymbolTableNode", "cross_ref": "numpy._core.multiarray.asfortranarray", "kind": "Gdef", "module_hidden": true, "module_public": false}, "asin": {".class": "SymbolTableNode", "cross_ref": "numpy.asin", "kind": "Gdef", "module_hidden": true, "module_public": false}, "asinh": {".class": "SymbolTableNode", "cross_ref": "numpy.asinh", "kind": "Gdef", "module_hidden": true, "module_public": false}, "asmatrix": {".class": "SymbolTableNode", "cross_ref": "numpy.matrixlib.defmatrix.asmatrix", "kind": "Gdef", "module_hidden": true, "module_public": false}, "astype": {".class": "SymbolTableNode", "cross_ref": "numpy._core.numeric.astype", "kind": "Gdef", "module_hidden": true, "module_public": false}, "atan": {".class": "SymbolTableNode", "cross_ref": "numpy.atan", "kind": "Gdef", "module_hidden": true, "module_public": false}, "atan2": {".class": "SymbolTableNode", "cross_ref": "numpy.atan2", "kind": "Gdef", "module_hidden": true, "module_public": false}, "atanh": {".class": "SymbolTableNode", "cross_ref": "numpy.atanh", "kind": "Gdef", "module_hidden": true, "module_public": false}, "atleast_1d": {".class": "SymbolTableNode", "cross_ref": "numpy._core.shape_base.atleast_1d", "kind": "Gdef", "module_hidden": true, "module_public": false}, "atleast_2d": {".class": "SymbolTableNode", "cross_ref": "numpy._core.shape_base.atleast_2d", "kind": "Gdef", "module_hidden": true, "module_public": false}, "atleast_3d": {".class": "SymbolTableNode", "cross_ref": "numpy._core.shape_base.atleast_3d", "kind": "Gdef", "module_hidden": true, "module_public": false}, "average": {".class": "SymbolTableNode", "cross_ref": "numpy.lib._function_base_impl.average", "kind": "Gdef", "module_hidden": true, "module_public": false}, "bartlett": {".class": "SymbolTableNode", "cross_ref": "numpy.lib._function_base_impl.bartlett", "kind": "Gdef", "module_hidden": true, "module_public": false}, "base_repr": {".class": "SymbolTableNode", "cross_ref": "numpy._core.numeric.base_repr", "kind": "Gdef", "module_hidden": true, "module_public": false}, "binary_repr": {".class": "SymbolTableNode", "cross_ref": "numpy._core.numeric.binary_repr", "kind": "Gdef", "module_hidden": true, "module_public": false}, "bincount": {".class": "SymbolTableNode", "cross_ref": "numpy._core.multiarray.bincount", "kind": "Gdef", "module_hidden": true, "module_public": false}, "bitwise_and": {".class": "SymbolTableNode", "cross_ref": "numpy.bitwise_and", "kind": "Gdef", "module_hidden": true, "module_public": false}, "bitwise_count": {".class": "SymbolTableNode", "cross_ref": "numpy.bitwise_count", "kind": "Gdef", "module_hidden": true, "module_public": false}, "bitwise_invert": {".class": "SymbolTableNode", "cross_ref": "numpy.bitwise_invert", "kind": "Gdef", "module_hidden": true, "module_public": false}, "bitwise_left_shift": {".class": "SymbolTableNode", "cross_ref": "numpy.bitwise_left_shift", "kind": "Gdef", "module_hidden": true, "module_public": false}, "bitwise_not": {".class": "SymbolTableNode", "cross_ref": "numpy.bitwise_not", "kind": "Gdef", "module_hidden": true, "module_public": false}, "bitwise_or": {".class": "SymbolTableNode", "cross_ref": "numpy.bitwise_or", "kind": "Gdef", "module_hidden": true, "module_public": false}, "bitwise_right_shift": {".class": "SymbolTableNode", "cross_ref": "numpy.bitwise_right_shift", "kind": "Gdef", "module_hidden": true, "module_public": false}, "bitwise_xor": {".class": "SymbolTableNode", "cross_ref": "numpy.bitwise_xor", "kind": "Gdef", "module_hidden": true, "module_public": false}, "blackman": {".class": "SymbolTableNode", "cross_ref": "numpy.lib._function_base_impl.blackman", "kind": "Gdef", "module_hidden": true, "module_public": false}, "block": {".class": "SymbolTableNode", "cross_ref": "numpy._core.shape_base.block", "kind": "Gdef", "module_hidden": true, "module_public": false}, "bmat": {".class": "SymbolTableNode", "cross_ref": "numpy.matrixlib.defmatrix.bmat", "kind": "Gdef", "module_hidden": true, "module_public": false}, "bool": {".class": "SymbolTableNode", "cross_ref": "numpy.bool", "kind": "Gdef", "module_hidden": true, "module_public": false}, "bool_": {".class": "SymbolTableNode", "cross_ref": "numpy.bool_", "kind": "Gdef", "module_hidden": true, "module_public": false}, "broadcast": {".class": "SymbolTableNode", "cross_ref": "numpy.broadcast", "kind": "Gdef", "module_hidden": true, "module_public": false}, "broadcast_arrays": {".class": "SymbolTableNode", "cross_ref": "numpy.lib._stride_tricks_impl.broadcast_arrays", "kind": "Gdef", "module_hidden": true, "module_public": false}, "broadcast_shapes": {".class": "SymbolTableNode", "cross_ref": "numpy.lib._stride_tricks_impl.broadcast_shapes", "kind": "Gdef", "module_hidden": true, "module_public": false}, "broadcast_to": {".class": "SymbolTableNode", "cross_ref": "numpy.lib._stride_tricks_impl.broadcast_to", "kind": "Gdef", "module_hidden": true, "module_public": false}, "busday_count": {".class": "SymbolTableNode", "cross_ref": "numpy._core.multiarray.busday_count", "kind": "Gdef", "module_hidden": true, "module_public": false}, "busday_offset": {".class": "SymbolTableNode", "cross_ref": "numpy._core.multiarray.busday_offset", "kind": "Gdef", "module_hidden": true, "module_public": false}, "busdaycalendar": {".class": "SymbolTableNode", "cross_ref": "numpy.busdaycalendar", "kind": "Gdef", "module_hidden": true, "module_public": false}, "byte": {".class": "SymbolTableNode", "cross_ref": "numpy.byte", "kind": "Gdef", "module_hidden": true, "module_public": false}, "bytes_": {".class": "SymbolTableNode", "cross_ref": "numpy.bytes_", "kind": "Gdef", "module_hidden": true, "module_public": false}, "c_": {".class": "SymbolTableNode", "cross_ref": "numpy.lib._index_tricks_impl.c_", "kind": "Gdef", "module_hidden": true, "module_public": false}, "can_cast": {".class": "SymbolTableNode", "cross_ref": "numpy._core.multiarray.can_cast", "kind": "Gdef", "module_hidden": true, "module_public": false}, "cbrt": {".class": "SymbolTableNode", "cross_ref": "numpy.cbrt", "kind": "Gdef", "module_hidden": true, "module_public": false}, "cdouble": {".class": "SymbolTableNode", "cross_ref": "numpy.cdouble", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ceil": {".class": "SymbolTableNode", "cross_ref": "numpy.ceil", "kind": "Gdef", "module_hidden": true, "module_public": false}, "char": {".class": "SymbolTableNode", "cross_ref": "numpy.char", "kind": "Gdef", "module_hidden": true, "module_public": false}, "character": {".class": "SymbolTableNode", "cross_ref": "numpy.character", "kind": "Gdef", "module_hidden": true, "module_public": false}, "choose": {".class": "SymbolTableNode", "cross_ref": "numpy._core.fromnumeric.choose", "kind": "Gdef", "module_hidden": true, "module_public": false}, "clip": {".class": "SymbolTableNode", "cross_ref": "numpy._core.fromnumeric.clip", "kind": "Gdef", "module_hidden": true, "module_public": false}, "clongdouble": {".class": "SymbolTableNode", "cross_ref": "numpy.clongdouble", "kind": "Gdef", "module_hidden": true, "module_public": false}, "column_stack": {".class": "SymbolTableNode", "cross_ref": "numpy.lib._shape_base_impl.column_stack", "kind": "Gdef", "module_hidden": true, "module_public": false}, "common_type": {".class": "SymbolTableNode", "cross_ref": "numpy.lib._type_check_impl.common_type", "kind": "Gdef", "module_hidden": true, "module_public": false}, "complex128": {".class": "SymbolTableNode", "cross_ref": "numpy.complex128", "kind": "Gdef", "module_hidden": true, "module_public": false}, "complex256": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._extended_precision.complex256", "kind": "Gdef", "module_hidden": true, "module_public": false}, "complex64": {".class": "SymbolTableNode", "cross_ref": "numpy.complex64", "kind": "Gdef", "module_hidden": true, "module_public": false}, "complexfloating": {".class": "SymbolTableNode", "cross_ref": "numpy.complexfloating", "kind": "Gdef", "module_hidden": true, "module_public": false}, "compress": {".class": "SymbolTableNode", "cross_ref": "numpy._core.fromnumeric.compress", "kind": "Gdef", "module_hidden": true, "module_public": false}, "concat": {".class": "SymbolTableNode", "cross_ref": "numpy.concat", "kind": "Gdef", "module_hidden": true, "module_public": false}, "concatenate": {".class": "SymbolTableNode", "cross_ref": "numpy._core.multiarray.concatenate", "kind": "Gdef", "module_hidden": true, "module_public": false}, "conj": {".class": "SymbolTableNode", "cross_ref": "numpy.conj", "kind": "Gdef", "module_hidden": true, "module_public": false}, "conjugate": {".class": "SymbolTableNode", "cross_ref": "numpy.conjugate", "kind": "Gdef", "module_hidden": true, "module_public": false}, "convolve": {".class": "SymbolTableNode", "cross_ref": "numpy._core.numeric.convolve", "kind": "Gdef", "module_hidden": true, "module_public": false}, "copy": {".class": "SymbolTableNode", "cross_ref": "numpy.lib._function_base_impl.copy", "kind": "Gdef", "module_hidden": true, "module_public": false}, "copysign": {".class": "SymbolTableNode", "cross_ref": "numpy.copysign", "kind": "Gdef", "module_hidden": true, "module_public": false}, "copyto": {".class": "SymbolTableNode", "cross_ref": "numpy._core.multiarray.copyto", "kind": "Gdef", "module_hidden": true, "module_public": false}, "core": {".class": "SymbolTableNode", "cross_ref": "numpy.core", "kind": "Gdef", "module_hidden": true, "module_public": false}, "corrcoef": {".class": "SymbolTableNode", "cross_ref": "numpy.lib._function_base_impl.corrcoef", "kind": "Gdef", "module_hidden": true, "module_public": false}, "correlate": {".class": "SymbolTableNode", "cross_ref": "numpy._core.numeric.correlate", "kind": "Gdef", "module_hidden": true, "module_public": false}, "cos": {".class": "SymbolTableNode", "cross_ref": "numpy.cos", "kind": "Gdef", "module_hidden": true, "module_public": false}, "cosh": {".class": "SymbolTableNode", "cross_ref": "numpy.cosh", "kind": "Gdef", "module_hidden": true, "module_public": false}, "count_nonzero": {".class": "SymbolTableNode", "cross_ref": "numpy._core.numeric.count_nonzero", "kind": "Gdef", "module_hidden": true, "module_public": false}, "cov": {".class": "SymbolTableNode", "cross_ref": "numpy.lib._function_base_impl.cov", "kind": "Gdef", "module_hidden": true, "module_public": false}, "cross": {".class": "SymbolTableNode", "cross_ref": "numpy._core.numeric.cross", "kind": "Gdef", "module_hidden": true, "module_public": false}, "csingle": {".class": "SymbolTableNode", "cross_ref": "numpy.c<PERSON>le", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ctypeslib": {".class": "SymbolTableNode", "cross_ref": "numpy.ctypeslib", "kind": "Gdef", "module_hidden": true, "module_public": false}, "cumprod": {".class": "SymbolTableNode", "cross_ref": "numpy._core.fromnumeric.cumprod", "kind": "Gdef", "module_hidden": true, "module_public": false}, "cumsum": {".class": "SymbolTableNode", "cross_ref": "numpy._core.fromnumeric.cumsum", "kind": "Gdef", "module_hidden": true, "module_public": false}, "cumulative_prod": {".class": "SymbolTableNode", "cross_ref": "numpy._core.fromnumeric.cumulative_prod", "kind": "Gdef", "module_hidden": true, "module_public": false}, "cumulative_sum": {".class": "SymbolTableNode", "cross_ref": "numpy._core.fromnumeric.cumulative_sum", "kind": "Gdef", "module_hidden": true, "module_public": false}, "datetime64": {".class": "SymbolTableNode", "cross_ref": "numpy.datetime64", "kind": "Gdef", "module_hidden": true, "module_public": false}, "datetime_as_string": {".class": "SymbolTableNode", "cross_ref": "numpy._core.multiarray.datetime_as_string", "kind": "Gdef", "module_hidden": true, "module_public": false}, "datetime_data": {".class": "SymbolTableNode", "cross_ref": "numpy._core.multiarray.datetime_data", "kind": "Gdef", "module_hidden": true, "module_public": false}, "deg2rad": {".class": "SymbolTableNode", "cross_ref": "numpy.deg2rad", "kind": "Gdef", "module_hidden": true, "module_public": false}, "degrees": {".class": "SymbolTableNode", "cross_ref": "numpy.degrees", "kind": "Gdef", "module_hidden": true, "module_public": false}, "delete": {".class": "SymbolTableNode", "cross_ref": "numpy.lib._function_base_impl.delete", "kind": "Gdef", "module_hidden": true, "module_public": false}, "diag": {".class": "SymbolTableNode", "cross_ref": "numpy.lib._twodim_base_impl.diag", "kind": "Gdef", "module_hidden": true, "module_public": false}, "diag_indices": {".class": "SymbolTableNode", "cross_ref": "numpy.lib._index_tricks_impl.diag_indices", "kind": "Gdef", "module_hidden": true, "module_public": false}, "diag_indices_from": {".class": "SymbolTableNode", "cross_ref": "numpy.lib._index_tricks_impl.diag_indices_from", "kind": "Gdef", "module_hidden": true, "module_public": false}, "diagflat": {".class": "SymbolTableNode", "cross_ref": "numpy.lib._twodim_base_impl.diagflat", "kind": "Gdef", "module_hidden": true, "module_public": false}, "diagonal": {".class": "SymbolTableNode", "cross_ref": "numpy._core.fromnumeric.diagonal", "kind": "Gdef", "module_hidden": true, "module_public": false}, "diff": {".class": "SymbolTableNode", "cross_ref": "numpy.lib._function_base_impl.diff", "kind": "Gdef", "module_hidden": true, "module_public": false}, "digitize": {".class": "SymbolTableNode", "cross_ref": "numpy.lib._function_base_impl.digitize", "kind": "Gdef", "module_hidden": true, "module_public": false}, "divide": {".class": "SymbolTableNode", "cross_ref": "numpy.divide", "kind": "Gdef", "module_hidden": true, "module_public": false}, "divmod": {".class": "SymbolTableNode", "cross_ref": "numpy.divmod", "kind": "Gdef", "module_hidden": true, "module_public": false}, "dot": {".class": "SymbolTableNode", "cross_ref": "numpy._core.multiarray.dot", "kind": "Gdef", "module_hidden": true, "module_public": false}, "double": {".class": "SymbolTableNode", "cross_ref": "numpy.double", "kind": "Gdef", "module_hidden": true, "module_public": false}, "dsplit": {".class": "SymbolTableNode", "cross_ref": "numpy.lib._shape_base_impl.dsplit", "kind": "Gdef", "module_hidden": true, "module_public": false}, "dstack": {".class": "SymbolTableNode", "cross_ref": "numpy.lib._shape_base_impl.dstack", "kind": "Gdef", "module_hidden": true, "module_public": false}, "dtype": {".class": "SymbolTableNode", "cross_ref": "numpy.dtype", "kind": "Gdef", "module_hidden": true, "module_public": false}, "dtypes": {".class": "SymbolTableNode", "cross_ref": "numpy.dtypes", "kind": "Gdef", "module_hidden": true, "module_public": false}, "e": {".class": "SymbolTableNode", "cross_ref": "numpy.e", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ediff1d": {".class": "SymbolTableNode", "cross_ref": "numpy.lib._arraysetops_impl.ediff1d", "kind": "Gdef", "module_hidden": true, "module_public": false}, "einsum": {".class": "SymbolTableNode", "cross_ref": "numpy._core.einsumfunc.einsum", "kind": "Gdef", "module_hidden": true, "module_public": false}, "einsum_path": {".class": "SymbolTableNode", "cross_ref": "numpy._core.einsumfunc.einsum_path", "kind": "Gdef", "module_hidden": true, "module_public": false}, "emath": {".class": "SymbolTableNode", "cross_ref": "numpy.lib.scimath", "kind": "Gdef", "module_hidden": true, "module_public": false}, "empty": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "numpy.matlib.empty", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["shape", "dtype", "order"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.matlib.empty", "name": "empty", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["shape", "dtype", "order"], "arg_types": [{".class": "UnionType", "items": ["builtins.int", {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": true}, {".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.matlib._Order"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "empty", "ret_type": {".class": "TypeAliasType", "args": ["numpy.float64"], "type_ref": "numpy.matlib._Matrix"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.matlib.empty", "name": "empty", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["shape", "dtype", "order"], "arg_types": [{".class": "UnionType", "items": ["builtins.int", {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": true}, {".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.matlib._Order"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "empty", "ret_type": {".class": "TypeAliasType", "args": ["numpy.float64"], "type_ref": "numpy.matlib._Matrix"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["shape", "dtype", "order"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.matlib.empty", "name": "empty", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["shape", "dtype", "order"], "arg_types": [{".class": "UnionType", "items": ["builtins.int", {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": true}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.matlib._T", "id": -1, "name": "_T", "namespace": "numpy.matlib.empty#1", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy._typing._dtype_like._DTypeLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.matlib._Order"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "empty", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.matlib._T", "id": -1, "name": "_T", "namespace": "numpy.matlib.empty#1", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy.matlib._Matrix"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.matlib._T", "id": -1, "name": "_T", "namespace": "numpy.matlib.empty#1", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.matlib.empty", "name": "empty", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["shape", "dtype", "order"], "arg_types": [{".class": "UnionType", "items": ["builtins.int", {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": true}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.matlib._T", "id": -1, "name": "_T", "namespace": "numpy.matlib.empty#1", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy._typing._dtype_like._DTypeLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.matlib._Order"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "empty", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.matlib._T", "id": -1, "name": "_T", "namespace": "numpy.matlib.empty#1", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy.matlib._Matrix"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.matlib._T", "id": -1, "name": "_T", "namespace": "numpy.matlib.empty#1", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["shape", "dtype", "order"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.matlib.empty", "name": "empty", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["shape", "dtype", "order"], "arg_types": [{".class": "UnionType", "items": ["builtins.int", {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": true}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._dtype_like.DTypeLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.matlib._Order"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "empty", "ret_type": {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy.matlib._Matrix"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.matlib.empty", "name": "empty", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["shape", "dtype", "order"], "arg_types": [{".class": "UnionType", "items": ["builtins.int", {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": true}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._dtype_like.DTypeLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.matlib._Order"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "empty", "ret_type": {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy.matlib._Matrix"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["shape", "dtype", "order"], "arg_types": [{".class": "UnionType", "items": ["builtins.int", {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": true}, {".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.matlib._Order"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "empty", "ret_type": {".class": "TypeAliasType", "args": ["numpy.float64"], "type_ref": "numpy.matlib._Matrix"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["shape", "dtype", "order"], "arg_types": [{".class": "UnionType", "items": ["builtins.int", {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": true}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.matlib._T", "id": -1, "name": "_T", "namespace": "numpy.matlib.empty#1", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy._typing._dtype_like._DTypeLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.matlib._Order"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "empty", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.matlib._T", "id": -1, "name": "_T", "namespace": "numpy.matlib.empty#1", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy.matlib._Matrix"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.matlib._T", "id": -1, "name": "_T", "namespace": "numpy.matlib.empty#1", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["shape", "dtype", "order"], "arg_types": [{".class": "UnionType", "items": ["builtins.int", {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": true}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._dtype_like.DTypeLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.matlib._Order"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "empty", "ret_type": {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy.matlib._Matrix"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "empty_like": {".class": "SymbolTableNode", "cross_ref": "numpy._core.multiarray.empty_like", "kind": "Gdef", "module_hidden": true, "module_public": false}, "equal": {".class": "SymbolTableNode", "cross_ref": "numpy.equal", "kind": "Gdef", "module_hidden": true, "module_public": false}, "errstate": {".class": "SymbolTableNode", "cross_ref": "numpy.errstate", "kind": "Gdef", "module_hidden": true, "module_public": false}, "euler_gamma": {".class": "SymbolTableNode", "cross_ref": "numpy.euler_gamma", "kind": "Gdef", "module_hidden": true, "module_public": false}, "exceptions": {".class": "SymbolTableNode", "cross_ref": "numpy.exceptions", "kind": "Gdef", "module_hidden": true, "module_public": false}, "exp": {".class": "SymbolTableNode", "cross_ref": "numpy.exp", "kind": "Gdef", "module_hidden": true, "module_public": false}, "exp2": {".class": "SymbolTableNode", "cross_ref": "numpy.exp2", "kind": "Gdef", "module_hidden": true, "module_public": false}, "expand_dims": {".class": "SymbolTableNode", "cross_ref": "numpy.lib._shape_base_impl.expand_dims", "kind": "Gdef", "module_hidden": true, "module_public": false}, "expm1": {".class": "SymbolTableNode", "cross_ref": "numpy.expm1", "kind": "Gdef", "module_hidden": true, "module_public": false}, "extract": {".class": "SymbolTableNode", "cross_ref": "numpy.lib._function_base_impl.extract", "kind": "Gdef", "module_hidden": true, "module_public": false}, "eye": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "numpy.matlib.eye", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["n", "M", "k", "dtype", "order"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.matlib.eye", "name": "eye", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["n", "M", "k", "dtype", "order"], "arg_types": ["builtins.int", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int", {".class": "UnionType", "items": [{".class": "TypeType", "item": "numpy.float64"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.matlib._Order"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "eye", "ret_type": {".class": "TypeAliasType", "args": ["numpy.float64"], "type_ref": "numpy.matlib._Matrix"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.matlib.eye", "name": "eye", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["n", "M", "k", "dtype", "order"], "arg_types": ["builtins.int", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int", {".class": "UnionType", "items": [{".class": "TypeType", "item": "numpy.float64"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.matlib._Order"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "eye", "ret_type": {".class": "TypeAliasType", "args": ["numpy.float64"], "type_ref": "numpy.matlib._Matrix"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["n", "M", "k", "dtype", "order"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.matlib.eye", "name": "eye", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["n", "M", "k", "dtype", "order"], "arg_types": ["builtins.int", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int", {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.matlib._T", "id": -1, "name": "_T", "namespace": "numpy.matlib.eye#1", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy._typing._dtype_like._DTypeLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.matlib._Order"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "eye", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.matlib._T", "id": -1, "name": "_T", "namespace": "numpy.matlib.eye#1", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy.matlib._Matrix"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.matlib._T", "id": -1, "name": "_T", "namespace": "numpy.matlib.eye#1", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.matlib.eye", "name": "eye", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["n", "M", "k", "dtype", "order"], "arg_types": ["builtins.int", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int", {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.matlib._T", "id": -1, "name": "_T", "namespace": "numpy.matlib.eye#1", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy._typing._dtype_like._DTypeLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.matlib._Order"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "eye", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.matlib._T", "id": -1, "name": "_T", "namespace": "numpy.matlib.eye#1", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy.matlib._Matrix"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.matlib._T", "id": -1, "name": "_T", "namespace": "numpy.matlib.eye#1", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 3, 5], "arg_names": ["n", "M", "k", "dtype", "order"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.matlib.eye", "name": "eye", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 3, 5], "arg_names": ["n", "M", "k", "dtype", "order"], "arg_types": ["builtins.int", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int", {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.matlib._T", "id": -1, "name": "_T", "namespace": "numpy.matlib.eye#2", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy._typing._dtype_like._DTypeLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.matlib._Order"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "eye", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.matlib._T", "id": -1, "name": "_T", "namespace": "numpy.matlib.eye#2", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy.matlib._Matrix"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.matlib._T", "id": -1, "name": "_T", "namespace": "numpy.matlib.eye#2", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.matlib.eye", "name": "eye", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 3, 5], "arg_names": ["n", "M", "k", "dtype", "order"], "arg_types": ["builtins.int", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int", {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.matlib._T", "id": -1, "name": "_T", "namespace": "numpy.matlib.eye#2", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy._typing._dtype_like._DTypeLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.matlib._Order"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "eye", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.matlib._T", "id": -1, "name": "_T", "namespace": "numpy.matlib.eye#2", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy.matlib._Matrix"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.matlib._T", "id": -1, "name": "_T", "namespace": "numpy.matlib.eye#2", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["n", "M", "k", "dtype", "order"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.matlib.eye", "name": "eye", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["n", "M", "k", "dtype", "order"], "arg_types": ["builtins.int", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._dtype_like.DTypeLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.matlib._Order"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "eye", "ret_type": {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy.matlib._Matrix"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.matlib.eye", "name": "eye", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["n", "M", "k", "dtype", "order"], "arg_types": ["builtins.int", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._dtype_like.DTypeLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.matlib._Order"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "eye", "ret_type": {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy.matlib._Matrix"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["n", "M", "k", "dtype", "order"], "arg_types": ["builtins.int", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int", {".class": "UnionType", "items": [{".class": "TypeType", "item": "numpy.float64"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.matlib._Order"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "eye", "ret_type": {".class": "TypeAliasType", "args": ["numpy.float64"], "type_ref": "numpy.matlib._Matrix"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["n", "M", "k", "dtype", "order"], "arg_types": ["builtins.int", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int", {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.matlib._T", "id": -1, "name": "_T", "namespace": "numpy.matlib.eye#1", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy._typing._dtype_like._DTypeLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.matlib._Order"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "eye", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.matlib._T", "id": -1, "name": "_T", "namespace": "numpy.matlib.eye#1", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy.matlib._Matrix"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.matlib._T", "id": -1, "name": "_T", "namespace": "numpy.matlib.eye#1", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0, 1, 1, 3, 5], "arg_names": ["n", "M", "k", "dtype", "order"], "arg_types": ["builtins.int", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int", {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.matlib._T", "id": -1, "name": "_T", "namespace": "numpy.matlib.eye#2", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy._typing._dtype_like._DTypeLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.matlib._Order"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "eye", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.matlib._T", "id": -1, "name": "_T", "namespace": "numpy.matlib.eye#2", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy.matlib._Matrix"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.matlib._T", "id": -1, "name": "_T", "namespace": "numpy.matlib.eye#2", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["n", "M", "k", "dtype", "order"], "arg_types": ["builtins.int", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._dtype_like.DTypeLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.matlib._Order"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "eye", "ret_type": {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy.matlib._Matrix"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "f2py": {".class": "SymbolTableNode", "cross_ref": "numpy.f2py", "kind": "Gdef", "module_hidden": true, "module_public": false}, "fabs": {".class": "SymbolTableNode", "cross_ref": "numpy.fabs", "kind": "Gdef", "module_hidden": true, "module_public": false}, "fft": {".class": "SymbolTableNode", "cross_ref": "numpy.fft", "kind": "Gdef", "module_hidden": true, "module_public": false}, "fill_diagonal": {".class": "SymbolTableNode", "cross_ref": "numpy.lib._index_tricks_impl.fill_diagonal", "kind": "Gdef", "module_hidden": true, "module_public": false}, "finfo": {".class": "SymbolTableNode", "cross_ref": "numpy.finfo", "kind": "Gdef", "module_hidden": true, "module_public": false}, "fix": {".class": "SymbolTableNode", "cross_ref": "numpy.lib._ufunclike_impl.fix", "kind": "Gdef", "module_hidden": true, "module_public": false}, "flatiter": {".class": "SymbolTableNode", "cross_ref": "numpy.flatiter", "kind": "Gdef", "module_hidden": true, "module_public": false}, "flatnonzero": {".class": "SymbolTableNode", "cross_ref": "numpy._core.numeric.flatnonzero", "kind": "Gdef", "module_hidden": true, "module_public": false}, "flexible": {".class": "SymbolTableNode", "cross_ref": "numpy.flexible", "kind": "Gdef", "module_hidden": true, "module_public": false}, "flip": {".class": "SymbolTableNode", "cross_ref": "numpy.lib._function_base_impl.flip", "kind": "Gdef", "module_hidden": true, "module_public": false}, "fliplr": {".class": "SymbolTableNode", "cross_ref": "numpy.lib._twodim_base_impl.fliplr", "kind": "Gdef", "module_hidden": true, "module_public": false}, "flipud": {".class": "SymbolTableNode", "cross_ref": "numpy.lib._twodim_base_impl.flipud", "kind": "Gdef", "module_hidden": true, "module_public": false}, "float128": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._extended_precision.float128", "kind": "Gdef", "module_hidden": true, "module_public": false}, "float16": {".class": "SymbolTableNode", "cross_ref": "numpy.float16", "kind": "Gdef", "module_hidden": true, "module_public": false}, "float32": {".class": "SymbolTableNode", "cross_ref": "numpy.float32", "kind": "Gdef", "module_hidden": true, "module_public": false}, "float64": {".class": "SymbolTableNode", "cross_ref": "numpy.float64", "kind": "Gdef", "module_hidden": true, "module_public": false}, "float_power": {".class": "SymbolTableNode", "cross_ref": "numpy.float_power", "kind": "Gdef", "module_hidden": true, "module_public": false}, "floating": {".class": "SymbolTableNode", "cross_ref": "numpy.floating", "kind": "Gdef", "module_hidden": true, "module_public": false}, "floor": {".class": "SymbolTableNode", "cross_ref": "numpy.floor", "kind": "Gdef", "module_hidden": true, "module_public": false}, "floor_divide": {".class": "SymbolTableNode", "cross_ref": "numpy.floor_divide", "kind": "Gdef", "module_hidden": true, "module_public": false}, "fmax": {".class": "SymbolTableNode", "cross_ref": "numpy.fmax", "kind": "Gdef", "module_hidden": true, "module_public": false}, "fmin": {".class": "SymbolTableNode", "cross_ref": "numpy.fmin", "kind": "Gdef", "module_hidden": true, "module_public": false}, "fmod": {".class": "SymbolTableNode", "cross_ref": "numpy.fmod", "kind": "Gdef", "module_hidden": true, "module_public": false}, "format_float_positional": {".class": "SymbolTableNode", "cross_ref": "numpy._core.arrayprint.format_float_positional", "kind": "Gdef", "module_hidden": true, "module_public": false}, "format_float_scientific": {".class": "SymbolTableNode", "cross_ref": "numpy._core.arrayprint.format_float_scientific", "kind": "Gdef", "module_hidden": true, "module_public": false}, "frexp": {".class": "SymbolTableNode", "cross_ref": "numpy.frexp", "kind": "Gdef", "module_hidden": true, "module_public": false}, "from_dlpack": {".class": "SymbolTableNode", "cross_ref": "numpy.from_dlpack", "kind": "Gdef", "module_hidden": true, "module_public": false}, "frombuffer": {".class": "SymbolTableNode", "cross_ref": "numpy._core.multiarray.frombuffer", "kind": "Gdef", "module_hidden": true, "module_public": false}, "fromfile": {".class": "SymbolTableNode", "cross_ref": "numpy._core.multiarray.fromfile", "kind": "Gdef", "module_hidden": true, "module_public": false}, "fromfunction": {".class": "SymbolTableNode", "cross_ref": "numpy._core.numeric.fromfunction", "kind": "Gdef", "module_hidden": true, "module_public": false}, "fromiter": {".class": "SymbolTableNode", "cross_ref": "numpy._core.multiarray.fromiter", "kind": "Gdef", "module_hidden": true, "module_public": false}, "frompyfunc": {".class": "SymbolTableNode", "cross_ref": "numpy._core.multiarray.frompyfunc", "kind": "Gdef", "module_hidden": true, "module_public": false}, "fromregex": {".class": "SymbolTableNode", "cross_ref": "numpy.lib._npyio_impl.fromregex", "kind": "Gdef", "module_hidden": true, "module_public": false}, "fromstring": {".class": "SymbolTableNode", "cross_ref": "numpy._core.multiarray.fromstring", "kind": "Gdef", "module_hidden": true, "module_public": false}, "full": {".class": "SymbolTableNode", "cross_ref": "numpy._core.numeric.full", "kind": "Gdef", "module_hidden": true, "module_public": false}, "full_like": {".class": "SymbolTableNode", "cross_ref": "numpy._core.numeric.full_like", "kind": "Gdef", "module_hidden": true, "module_public": false}, "gcd": {".class": "SymbolTableNode", "cross_ref": "numpy.gcd", "kind": "Gdef", "module_hidden": true, "module_public": false}, "generic": {".class": "SymbolTableNode", "cross_ref": "numpy.generic", "kind": "Gdef", "module_hidden": true, "module_public": false}, "genfromtxt": {".class": "SymbolTableNode", "cross_ref": "numpy.lib._npyio_impl.genfromtxt", "kind": "Gdef", "module_hidden": true, "module_public": false}, "geomspace": {".class": "SymbolTableNode", "cross_ref": "numpy._core.function_base.geomspace", "kind": "Gdef", "module_hidden": true, "module_public": false}, "get_include": {".class": "SymbolTableNode", "cross_ref": "numpy.lib._utils_impl.get_include", "kind": "Gdef", "module_hidden": true, "module_public": false}, "get_printoptions": {".class": "SymbolTableNode", "cross_ref": "numpy._core.arrayprint.get_printoptions", "kind": "Gdef", "module_hidden": true, "module_public": false}, "getbufsize": {".class": "SymbolTableNode", "cross_ref": "numpy._core._ufunc_config.getbufsize", "kind": "Gdef", "module_hidden": true, "module_public": false}, "geterr": {".class": "SymbolTableNode", "cross_ref": "numpy._core._ufunc_config.geterr", "kind": "Gdef", "module_hidden": true, "module_public": false}, "geterrcall": {".class": "SymbolTableNode", "cross_ref": "numpy._core._ufunc_config.geterrcall", "kind": "Gdef", "module_hidden": true, "module_public": false}, "gradient": {".class": "SymbolTableNode", "cross_ref": "numpy.lib._function_base_impl.gradient", "kind": "Gdef", "module_hidden": true, "module_public": false}, "greater": {".class": "SymbolTableNode", "cross_ref": "numpy.greater", "kind": "Gdef", "module_hidden": true, "module_public": false}, "greater_equal": {".class": "SymbolTableNode", "cross_ref": "numpy.greater_equal", "kind": "Gdef", "module_hidden": true, "module_public": false}, "half": {".class": "SymbolTableNode", "cross_ref": "numpy.half", "kind": "Gdef", "module_hidden": true, "module_public": false}, "hamming": {".class": "SymbolTableNode", "cross_ref": "numpy.lib._function_base_impl.hamming", "kind": "Gdef", "module_hidden": true, "module_public": false}, "hanning": {".class": "SymbolTableNode", "cross_ref": "numpy.lib._function_base_impl.hanning", "kind": "Gdef", "module_hidden": true, "module_public": false}, "heaviside": {".class": "SymbolTableNode", "cross_ref": "numpy.heaviside", "kind": "Gdef", "module_hidden": true, "module_public": false}, "histogram": {".class": "SymbolTableNode", "cross_ref": "numpy.lib._histograms_impl.histogram", "kind": "Gdef", "module_hidden": true, "module_public": false}, "histogram2d": {".class": "SymbolTableNode", "cross_ref": "numpy.lib._twodim_base_impl.histogram2d", "kind": "Gdef", "module_hidden": true, "module_public": false}, "histogram_bin_edges": {".class": "SymbolTableNode", "cross_ref": "numpy.lib._histograms_impl.histogram_bin_edges", "kind": "Gdef", "module_hidden": true, "module_public": false}, "histogramdd": {".class": "SymbolTableNode", "cross_ref": "numpy.lib._histograms_impl.histogramdd", "kind": "Gdef", "module_hidden": true, "module_public": false}, "hsplit": {".class": "SymbolTableNode", "cross_ref": "numpy.lib._shape_base_impl.hsplit", "kind": "Gdef", "module_hidden": true, "module_public": false}, "hstack": {".class": "SymbolTableNode", "cross_ref": "numpy._core.shape_base.hstack", "kind": "Gdef", "module_hidden": true, "module_public": false}, "hypot": {".class": "SymbolTableNode", "cross_ref": "numpy.hypot", "kind": "Gdef", "module_hidden": true, "module_public": false}, "i0": {".class": "SymbolTableNode", "cross_ref": "numpy.lib._function_base_impl.i0", "kind": "Gdef", "module_hidden": true, "module_public": false}, "identity": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "numpy.matlib.identity", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["n", "dtype"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.matlib.identity", "name": "identity", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["n", "dtype"], "arg_types": ["builtins.int", {".class": "NoneType"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "identity", "ret_type": {".class": "TypeAliasType", "args": ["numpy.float64"], "type_ref": "numpy.matlib._Matrix"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.matlib.identity", "name": "identity", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["n", "dtype"], "arg_types": ["builtins.int", {".class": "NoneType"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "identity", "ret_type": {".class": "TypeAliasType", "args": ["numpy.float64"], "type_ref": "numpy.matlib._Matrix"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["n", "dtype"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.matlib.identity", "name": "identity", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["n", "dtype"], "arg_types": ["builtins.int", {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.matlib._T", "id": -1, "name": "_T", "namespace": "numpy.matlib.identity#1", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy._typing._dtype_like._DTypeLike"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "identity", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.matlib._T", "id": -1, "name": "_T", "namespace": "numpy.matlib.identity#1", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy.matlib._Matrix"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.matlib._T", "id": -1, "name": "_T", "namespace": "numpy.matlib.identity#1", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.matlib.identity", "name": "identity", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["n", "dtype"], "arg_types": ["builtins.int", {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.matlib._T", "id": -1, "name": "_T", "namespace": "numpy.matlib.identity#1", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy._typing._dtype_like._DTypeLike"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "identity", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.matlib._T", "id": -1, "name": "_T", "namespace": "numpy.matlib.identity#1", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy.matlib._Matrix"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.matlib._T", "id": -1, "name": "_T", "namespace": "numpy.matlib.identity#1", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["n", "dtype"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.matlib.identity", "name": "identity", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["n", "dtype"], "arg_types": ["builtins.int", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._dtype_like.DTypeLike"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "identity", "ret_type": {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy.matlib._Matrix"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.matlib.identity", "name": "identity", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["n", "dtype"], "arg_types": ["builtins.int", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._dtype_like.DTypeLike"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "identity", "ret_type": {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy.matlib._Matrix"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["n", "dtype"], "arg_types": ["builtins.int", {".class": "NoneType"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "identity", "ret_type": {".class": "TypeAliasType", "args": ["numpy.float64"], "type_ref": "numpy.matlib._Matrix"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["n", "dtype"], "arg_types": ["builtins.int", {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.matlib._T", "id": -1, "name": "_T", "namespace": "numpy.matlib.identity#1", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy._typing._dtype_like._DTypeLike"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "identity", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.matlib._T", "id": -1, "name": "_T", "namespace": "numpy.matlib.identity#1", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy.matlib._Matrix"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.matlib._T", "id": -1, "name": "_T", "namespace": "numpy.matlib.identity#1", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["n", "dtype"], "arg_types": ["builtins.int", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._dtype_like.DTypeLike"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "identity", "ret_type": {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy.matlib._Matrix"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "iinfo": {".class": "SymbolTableNode", "cross_ref": "numpy.iinfo", "kind": "Gdef", "module_hidden": true, "module_public": false}, "imag": {".class": "SymbolTableNode", "cross_ref": "numpy.lib._type_check_impl.imag", "kind": "Gdef", "module_hidden": true, "module_public": false}, "in1d": {".class": "SymbolTableNode", "cross_ref": "numpy.lib._arraysetops_impl.in1d", "kind": "Gdef", "module_hidden": true, "module_public": false}, "index_exp": {".class": "SymbolTableNode", "cross_ref": "numpy.lib._index_tricks_impl.index_exp", "kind": "Gdef", "module_hidden": true, "module_public": false}, "indices": {".class": "SymbolTableNode", "cross_ref": "numpy._core.numeric.indices", "kind": "Gdef", "module_hidden": true, "module_public": false}, "inexact": {".class": "SymbolTableNode", "cross_ref": "numpy.inexact", "kind": "Gdef", "module_hidden": true, "module_public": false}, "inf": {".class": "SymbolTableNode", "cross_ref": "numpy.inf", "kind": "Gdef", "module_hidden": true, "module_public": false}, "info": {".class": "SymbolTableNode", "cross_ref": "numpy.lib._utils_impl.info", "kind": "Gdef", "module_hidden": true, "module_public": false}, "inner": {".class": "SymbolTableNode", "cross_ref": "numpy._core.multiarray.inner", "kind": "Gdef", "module_hidden": true, "module_public": false}, "insert": {".class": "SymbolTableNode", "cross_ref": "numpy.lib._function_base_impl.insert", "kind": "Gdef", "module_hidden": true, "module_public": false}, "int16": {".class": "SymbolTableNode", "cross_ref": "numpy.int16", "kind": "Gdef", "module_hidden": true, "module_public": false}, "int32": {".class": "SymbolTableNode", "cross_ref": "numpy.int32", "kind": "Gdef", "module_hidden": true, "module_public": false}, "int64": {".class": "SymbolTableNode", "cross_ref": "numpy.int64", "kind": "Gdef", "module_hidden": true, "module_public": false}, "int8": {".class": "SymbolTableNode", "cross_ref": "numpy.int8", "kind": "Gdef", "module_hidden": true, "module_public": false}, "int_": {".class": "SymbolTableNode", "cross_ref": "numpy.int_", "kind": "Gdef", "module_hidden": true, "module_public": false}, "intc": {".class": "SymbolTableNode", "cross_ref": "numpy.intc", "kind": "Gdef", "module_hidden": true, "module_public": false}, "integer": {".class": "SymbolTableNode", "cross_ref": "numpy.integer", "kind": "Gdef", "module_hidden": true, "module_public": false}, "interp": {".class": "SymbolTableNode", "cross_ref": "numpy.lib._function_base_impl.interp", "kind": "Gdef", "module_hidden": true, "module_public": false}, "intersect1d": {".class": "SymbolTableNode", "cross_ref": "numpy.lib._arraysetops_impl.intersect1d", "kind": "Gdef", "module_hidden": true, "module_public": false}, "intp": {".class": "SymbolTableNode", "cross_ref": "numpy.intp", "kind": "Gdef", "module_hidden": true, "module_public": false}, "invert": {".class": "SymbolTableNode", "cross_ref": "numpy.invert", "kind": "Gdef", "module_hidden": true, "module_public": false}, "is_busday": {".class": "SymbolTableNode", "cross_ref": "numpy._core.multiarray.is_busday", "kind": "Gdef", "module_hidden": true, "module_public": false}, "isclose": {".class": "SymbolTableNode", "cross_ref": "numpy._core.numeric.isclose", "kind": "Gdef", "module_hidden": true, "module_public": false}, "iscomplex": {".class": "SymbolTableNode", "cross_ref": "numpy.lib._type_check_impl.iscomplex", "kind": "Gdef", "module_hidden": true, "module_public": false}, "iscomplexobj": {".class": "SymbolTableNode", "cross_ref": "numpy.lib._type_check_impl.iscomplexobj", "kind": "Gdef", "module_hidden": true, "module_public": false}, "isdtype": {".class": "SymbolTableNode", "cross_ref": "numpy._core.numerictypes.isdtype", "kind": "Gdef", "module_hidden": true, "module_public": false}, "isfinite": {".class": "SymbolTableNode", "cross_ref": "numpy.isfinite", "kind": "Gdef", "module_hidden": true, "module_public": false}, "isfortran": {".class": "SymbolTableNode", "cross_ref": "numpy._core.numeric.isfortran", "kind": "Gdef", "module_hidden": true, "module_public": false}, "isin": {".class": "SymbolTableNode", "cross_ref": "numpy.lib._arraysetops_impl.isin", "kind": "Gdef", "module_hidden": true, "module_public": false}, "isinf": {".class": "SymbolTableNode", "cross_ref": "numpy.isinf", "kind": "Gdef", "module_hidden": true, "module_public": false}, "isnan": {".class": "SymbolTableNode", "cross_ref": "numpy.isnan", "kind": "Gdef", "module_hidden": true, "module_public": false}, "isnat": {".class": "SymbolTableNode", "cross_ref": "numpy.isnat", "kind": "Gdef", "module_hidden": true, "module_public": false}, "isneginf": {".class": "SymbolTableNode", "cross_ref": "numpy.lib._ufunclike_impl.isneginf", "kind": "Gdef", "module_hidden": true, "module_public": false}, "isposinf": {".class": "SymbolTableNode", "cross_ref": "numpy.lib._ufunclike_impl.isposinf", "kind": "Gdef", "module_hidden": true, "module_public": false}, "isreal": {".class": "SymbolTableNode", "cross_ref": "numpy.lib._type_check_impl.isreal", "kind": "Gdef", "module_hidden": true, "module_public": false}, "isrealobj": {".class": "SymbolTableNode", "cross_ref": "numpy.lib._type_check_impl.isrealobj", "kind": "Gdef", "module_hidden": true, "module_public": false}, "isscalar": {".class": "SymbolTableNode", "cross_ref": "numpy._core.numeric.isscalar", "kind": "Gdef", "module_hidden": true, "module_public": false}, "issubdtype": {".class": "SymbolTableNode", "cross_ref": "numpy._core.numerictypes.issubdtype", "kind": "Gdef", "module_hidden": true, "module_public": false}, "iterable": {".class": "SymbolTableNode", "cross_ref": "numpy.lib._function_base_impl.iterable", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ix_": {".class": "SymbolTableNode", "cross_ref": "numpy.lib._index_tricks_impl.ix_", "kind": "Gdef", "module_hidden": true, "module_public": false}, "kaiser": {".class": "SymbolTableNode", "cross_ref": "numpy.lib._function_base_impl.kaiser", "kind": "Gdef", "module_hidden": true, "module_public": false}, "kron": {".class": "SymbolTableNode", "cross_ref": "numpy.lib._shape_base_impl.kron", "kind": "Gdef", "module_hidden": true, "module_public": false}, "lcm": {".class": "SymbolTableNode", "cross_ref": "numpy.lcm", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ldexp": {".class": "SymbolTableNode", "cross_ref": "numpy.ldexp", "kind": "Gdef", "module_hidden": true, "module_public": false}, "left_shift": {".class": "SymbolTableNode", "cross_ref": "numpy.left_shift", "kind": "Gdef", "module_hidden": true, "module_public": false}, "less": {".class": "SymbolTableNode", "cross_ref": "numpy.less", "kind": "Gdef", "module_hidden": true, "module_public": false}, "less_equal": {".class": "SymbolTableNode", "cross_ref": "numpy.less_equal", "kind": "Gdef", "module_hidden": true, "module_public": false}, "lexsort": {".class": "SymbolTableNode", "cross_ref": "numpy._core.multiarray.lexsort", "kind": "Gdef", "module_hidden": true, "module_public": false}, "lib": {".class": "SymbolTableNode", "cross_ref": "numpy.lib", "kind": "Gdef", "module_hidden": true, "module_public": false}, "linalg": {".class": "SymbolTableNode", "cross_ref": "numpy.linalg", "kind": "Gdef", "module_hidden": true, "module_public": false}, "linspace": {".class": "SymbolTableNode", "cross_ref": "numpy._core.function_base.linspace", "kind": "Gdef", "module_hidden": true, "module_public": false}, "little_endian": {".class": "SymbolTableNode", "cross_ref": "numpy.little_endian", "kind": "Gdef", "module_hidden": true, "module_public": false}, "load": {".class": "SymbolTableNode", "cross_ref": "numpy.lib._npyio_impl.load", "kind": "Gdef", "module_hidden": true, "module_public": false}, "loadtxt": {".class": "SymbolTableNode", "cross_ref": "numpy.lib._npyio_impl.loadtxt", "kind": "Gdef", "module_hidden": true, "module_public": false}, "log": {".class": "SymbolTableNode", "cross_ref": "numpy.log", "kind": "Gdef", "module_hidden": true, "module_public": false}, "log10": {".class": "SymbolTableNode", "cross_ref": "numpy.log10", "kind": "Gdef", "module_hidden": true, "module_public": false}, "log1p": {".class": "SymbolTableNode", "cross_ref": "numpy.log1p", "kind": "Gdef", "module_hidden": true, "module_public": false}, "log2": {".class": "SymbolTableNode", "cross_ref": "numpy.log2", "kind": "Gdef", "module_hidden": true, "module_public": false}, "logaddexp": {".class": "SymbolTableNode", "cross_ref": "numpy.logaddexp", "kind": "Gdef", "module_hidden": true, "module_public": false}, "logaddexp2": {".class": "SymbolTableNode", "cross_ref": "numpy.logaddexp2", "kind": "Gdef", "module_hidden": true, "module_public": false}, "logical_and": {".class": "SymbolTableNode", "cross_ref": "numpy.logical_and", "kind": "Gdef", "module_hidden": true, "module_public": false}, "logical_not": {".class": "SymbolTableNode", "cross_ref": "numpy.logical_not", "kind": "Gdef", "module_hidden": true, "module_public": false}, "logical_or": {".class": "SymbolTableNode", "cross_ref": "numpy.logical_or", "kind": "Gdef", "module_hidden": true, "module_public": false}, "logical_xor": {".class": "SymbolTableNode", "cross_ref": "numpy.logical_xor", "kind": "Gdef", "module_hidden": true, "module_public": false}, "logspace": {".class": "SymbolTableNode", "cross_ref": "numpy._core.function_base.logspace", "kind": "Gdef", "module_hidden": true, "module_public": false}, "long": {".class": "SymbolTableNode", "cross_ref": "numpy.long", "kind": "Gdef", "module_hidden": true, "module_public": false}, "longdouble": {".class": "SymbolTableNode", "cross_ref": "numpy.longdouble", "kind": "Gdef", "module_hidden": true, "module_public": false}, "longlong": {".class": "SymbolTableNode", "cross_ref": "numpy.longlong", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ma": {".class": "SymbolTableNode", "cross_ref": "numpy.ma", "kind": "Gdef", "module_hidden": true, "module_public": false}, "mask_indices": {".class": "SymbolTableNode", "cross_ref": "numpy.lib._twodim_base_impl.mask_indices", "kind": "Gdef", "module_hidden": true, "module_public": false}, "matmul": {".class": "SymbolTableNode", "cross_ref": "numpy.matmul", "kind": "Gdef", "module_hidden": true, "module_public": false}, "matrix": {".class": "SymbolTableNode", "cross_ref": "numpy.matrix", "kind": "Gdef", "module_hidden": true, "module_public": false}, "matrix_transpose": {".class": "SymbolTableNode", "cross_ref": "numpy._core.fromnumeric.matrix_transpose", "kind": "Gdef", "module_hidden": true, "module_public": false}, "matvec": {".class": "SymbolTableNode", "cross_ref": "numpy.matvec", "kind": "Gdef", "module_hidden": true, "module_public": false}, "max": {".class": "SymbolTableNode", "cross_ref": "numpy._core.fromnumeric.max", "kind": "Gdef", "module_hidden": true, "module_public": false}, "maximum": {".class": "SymbolTableNode", "cross_ref": "numpy.maximum", "kind": "Gdef", "module_hidden": true, "module_public": false}, "may_share_memory": {".class": "SymbolTableNode", "cross_ref": "numpy._core.multiarray.may_share_memory", "kind": "Gdef", "module_hidden": true, "module_public": false}, "mean": {".class": "SymbolTableNode", "cross_ref": "numpy._core.fromnumeric.mean", "kind": "Gdef", "module_hidden": true, "module_public": false}, "median": {".class": "SymbolTableNode", "cross_ref": "numpy.lib._function_base_impl.median", "kind": "Gdef", "module_hidden": true, "module_public": false}, "memmap": {".class": "SymbolTableNode", "cross_ref": "numpy.memmap", "kind": "Gdef", "module_hidden": true, "module_public": false}, "meshgrid": {".class": "SymbolTableNode", "cross_ref": "numpy.lib._function_base_impl.meshgrid", "kind": "Gdef", "module_hidden": true, "module_public": false}, "mgrid": {".class": "SymbolTableNode", "cross_ref": "numpy.lib._index_tricks_impl.mgrid", "kind": "Gdef", "module_hidden": true, "module_public": false}, "min": {".class": "SymbolTableNode", "cross_ref": "numpy._core.fromnumeric.min", "kind": "Gdef", "module_hidden": true, "module_public": false}, "min_scalar_type": {".class": "SymbolTableNode", "cross_ref": "numpy._core.multiarray.min_scalar_type", "kind": "Gdef", "module_hidden": true, "module_public": false}, "minimum": {".class": "SymbolTableNode", "cross_ref": "numpy.minimum", "kind": "Gdef", "module_hidden": true, "module_public": false}, "mintypecode": {".class": "SymbolTableNode", "cross_ref": "numpy.lib._type_check_impl.mintypecode", "kind": "Gdef", "module_hidden": true, "module_public": false}, "mod": {".class": "SymbolTableNode", "cross_ref": "numpy.mod", "kind": "Gdef", "module_hidden": true, "module_public": false}, "modf": {".class": "SymbolTableNode", "cross_ref": "numpy.modf", "kind": "Gdef", "module_hidden": true, "module_public": false}, "moveaxis": {".class": "SymbolTableNode", "cross_ref": "numpy._core.numeric.moveaxis", "kind": "Gdef", "module_hidden": true, "module_public": false}, "multiply": {".class": "SymbolTableNode", "cross_ref": "numpy.multiply", "kind": "Gdef", "module_hidden": true, "module_public": false}, "nan": {".class": "SymbolTableNode", "cross_ref": "numpy.nan", "kind": "Gdef", "module_hidden": true, "module_public": false}, "nan_to_num": {".class": "SymbolTableNode", "cross_ref": "numpy.lib._type_check_impl.nan_to_num", "kind": "Gdef", "module_hidden": true, "module_public": false}, "nanargmax": {".class": "SymbolTableNode", "cross_ref": "numpy.lib._nanfunctions_impl.nanargmax", "kind": "Gdef", "module_hidden": true, "module_public": false}, "nanargmin": {".class": "SymbolTableNode", "cross_ref": "numpy.lib._nanfunctions_impl.nanargmin", "kind": "Gdef", "module_hidden": true, "module_public": false}, "nancumprod": {".class": "SymbolTableNode", "cross_ref": "numpy.lib._nanfunctions_impl.nancumprod", "kind": "Gdef", "module_hidden": true, "module_public": false}, "nancumsum": {".class": "SymbolTableNode", "cross_ref": "numpy.lib._nanfunctions_impl.nancumsum", "kind": "Gdef", "module_hidden": true, "module_public": false}, "nanmax": {".class": "SymbolTableNode", "cross_ref": "numpy.lib._nanfunctions_impl.nanmax", "kind": "Gdef", "module_hidden": true, "module_public": false}, "nanmean": {".class": "SymbolTableNode", "cross_ref": "numpy.lib._nanfunctions_impl.nanmean", "kind": "Gdef", "module_hidden": true, "module_public": false}, "nanmedian": {".class": "SymbolTableNode", "cross_ref": "numpy.lib._nanfunctions_impl.nanmedian", "kind": "Gdef", "module_hidden": true, "module_public": false}, "nanmin": {".class": "SymbolTableNode", "cross_ref": "numpy.lib._nanfunctions_impl.nanmin", "kind": "Gdef", "module_hidden": true, "module_public": false}, "nanpercentile": {".class": "SymbolTableNode", "cross_ref": "numpy.lib._nanfunctions_impl.nanpercentile", "kind": "Gdef", "module_hidden": true, "module_public": false}, "nanprod": {".class": "SymbolTableNode", "cross_ref": "numpy.lib._nanfunctions_impl.nanprod", "kind": "Gdef", "module_hidden": true, "module_public": false}, "nanquantile": {".class": "SymbolTableNode", "cross_ref": "numpy.lib._nanfunctions_impl.nanquantile", "kind": "Gdef", "module_hidden": true, "module_public": false}, "nanstd": {".class": "SymbolTableNode", "cross_ref": "numpy.lib._nanfunctions_impl.nanstd", "kind": "Gdef", "module_hidden": true, "module_public": false}, "nansum": {".class": "SymbolTableNode", "cross_ref": "numpy.lib._nanfunctions_impl.nansum", "kind": "Gdef", "module_hidden": true, "module_public": false}, "nanvar": {".class": "SymbolTableNode", "cross_ref": "numpy.lib._nanfunctions_impl.nanvar", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ndarray": {".class": "SymbolTableNode", "cross_ref": "numpy.n<PERSON><PERSON>", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ndenumerate": {".class": "SymbolTableNode", "cross_ref": "numpy.lib._index_tricks_impl.ndenumerate", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ndim": {".class": "SymbolTableNode", "cross_ref": "numpy._core.fromnumeric.ndim", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ndindex": {".class": "SymbolTableNode", "cross_ref": "numpy.lib._index_tricks_impl.ndindex", "kind": "Gdef", "module_hidden": true, "module_public": false}, "nditer": {".class": "SymbolTableNode", "cross_ref": "numpy.nditer", "kind": "Gdef", "module_hidden": true, "module_public": false}, "negative": {".class": "SymbolTableNode", "cross_ref": "numpy.negative", "kind": "Gdef", "module_hidden": true, "module_public": false}, "nested_iters": {".class": "SymbolTableNode", "cross_ref": "numpy._core.multiarray.nested_iters", "kind": "Gdef", "module_hidden": true, "module_public": false}, "newaxis": {".class": "SymbolTableNode", "cross_ref": "numpy.newaxis", "kind": "Gdef", "module_hidden": true, "module_public": false}, "nextafter": {".class": "SymbolTableNode", "cross_ref": "numpy.nextafter", "kind": "Gdef", "module_hidden": true, "module_public": false}, "nonzero": {".class": "SymbolTableNode", "cross_ref": "numpy._core.fromnumeric.nonzero", "kind": "Gdef", "module_hidden": true, "module_public": false}, "not_equal": {".class": "SymbolTableNode", "cross_ref": "numpy.not_equal", "kind": "Gdef", "module_hidden": true, "module_public": false}, "np": {".class": "SymbolTableNode", "cross_ref": "numpy", "kind": "Gdef", "module_hidden": true, "module_public": false}, "npt": {".class": "SymbolTableNode", "cross_ref": "numpy.typing", "kind": "Gdef", "module_hidden": true, "module_public": false}, "number": {".class": "SymbolTableNode", "cross_ref": "numpy.number", "kind": "Gdef", "module_hidden": true, "module_public": false}, "object_": {".class": "SymbolTableNode", "cross_ref": "numpy.object_", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ogrid": {".class": "SymbolTableNode", "cross_ref": "numpy.lib._index_tricks_impl.ogrid", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ones": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "numpy.matlib.ones", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["shape", "dtype", "order"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.matlib.ones", "name": "ones", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["shape", "dtype", "order"], "arg_types": [{".class": "UnionType", "items": ["builtins.int", {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": true}, {".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.matlib._Order"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ones", "ret_type": {".class": "TypeAliasType", "args": ["numpy.float64"], "type_ref": "numpy.matlib._Matrix"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.matlib.ones", "name": "ones", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["shape", "dtype", "order"], "arg_types": [{".class": "UnionType", "items": ["builtins.int", {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": true}, {".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.matlib._Order"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ones", "ret_type": {".class": "TypeAliasType", "args": ["numpy.float64"], "type_ref": "numpy.matlib._Matrix"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["shape", "dtype", "order"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.matlib.ones", "name": "ones", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["shape", "dtype", "order"], "arg_types": [{".class": "UnionType", "items": ["builtins.int", {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": true}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.matlib._T", "id": -1, "name": "_T", "namespace": "numpy.matlib.ones#1", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy._typing._dtype_like._DTypeLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.matlib._Order"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ones", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.matlib._T", "id": -1, "name": "_T", "namespace": "numpy.matlib.ones#1", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy.matlib._Matrix"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.matlib._T", "id": -1, "name": "_T", "namespace": "numpy.matlib.ones#1", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.matlib.ones", "name": "ones", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["shape", "dtype", "order"], "arg_types": [{".class": "UnionType", "items": ["builtins.int", {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": true}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.matlib._T", "id": -1, "name": "_T", "namespace": "numpy.matlib.ones#1", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy._typing._dtype_like._DTypeLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.matlib._Order"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ones", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.matlib._T", "id": -1, "name": "_T", "namespace": "numpy.matlib.ones#1", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy.matlib._Matrix"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.matlib._T", "id": -1, "name": "_T", "namespace": "numpy.matlib.ones#1", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["shape", "dtype", "order"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.matlib.ones", "name": "ones", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["shape", "dtype", "order"], "arg_types": [{".class": "UnionType", "items": ["builtins.int", {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": true}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._dtype_like.DTypeLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.matlib._Order"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ones", "ret_type": {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy.matlib._Matrix"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.matlib.ones", "name": "ones", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["shape", "dtype", "order"], "arg_types": [{".class": "UnionType", "items": ["builtins.int", {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": true}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._dtype_like.DTypeLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.matlib._Order"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ones", "ret_type": {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy.matlib._Matrix"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["shape", "dtype", "order"], "arg_types": [{".class": "UnionType", "items": ["builtins.int", {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": true}, {".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.matlib._Order"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ones", "ret_type": {".class": "TypeAliasType", "args": ["numpy.float64"], "type_ref": "numpy.matlib._Matrix"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["shape", "dtype", "order"], "arg_types": [{".class": "UnionType", "items": ["builtins.int", {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": true}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.matlib._T", "id": -1, "name": "_T", "namespace": "numpy.matlib.ones#1", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy._typing._dtype_like._DTypeLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.matlib._Order"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ones", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.matlib._T", "id": -1, "name": "_T", "namespace": "numpy.matlib.ones#1", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy.matlib._Matrix"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.matlib._T", "id": -1, "name": "_T", "namespace": "numpy.matlib.ones#1", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["shape", "dtype", "order"], "arg_types": [{".class": "UnionType", "items": ["builtins.int", {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": true}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._dtype_like.DTypeLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.matlib._Order"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ones", "ret_type": {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy.matlib._Matrix"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "ones_like": {".class": "SymbolTableNode", "cross_ref": "numpy._core.numeric.ones_like", "kind": "Gdef", "module_hidden": true, "module_public": false}, "outer": {".class": "SymbolTableNode", "cross_ref": "numpy._core.numeric.outer", "kind": "Gdef", "module_hidden": true, "module_public": false}, "overload": {".class": "SymbolTableNode", "cross_ref": "typing.overload", "kind": "Gdef", "module_hidden": true, "module_public": false}, "packbits": {".class": "SymbolTableNode", "cross_ref": "numpy._core.multiarray.packbits", "kind": "Gdef", "module_hidden": true, "module_public": false}, "pad": {".class": "SymbolTableNode", "cross_ref": "numpy.lib._arraypad_impl.pad", "kind": "Gdef", "module_hidden": true, "module_public": false}, "partition": {".class": "SymbolTableNode", "cross_ref": "numpy._core.fromnumeric.partition", "kind": "Gdef", "module_hidden": true, "module_public": false}, "percentile": {".class": "SymbolTableNode", "cross_ref": "numpy.lib._function_base_impl.percentile", "kind": "Gdef", "module_hidden": true, "module_public": false}, "permute_dims": {".class": "SymbolTableNode", "cross_ref": "numpy.permute_dims", "kind": "Gdef", "module_hidden": true, "module_public": false}, "pi": {".class": "SymbolTableNode", "cross_ref": "numpy.pi", "kind": "Gdef", "module_hidden": true, "module_public": false}, "piecewise": {".class": "SymbolTableNode", "cross_ref": "numpy.lib._function_base_impl.piecewise", "kind": "Gdef", "module_hidden": true, "module_public": false}, "place": {".class": "SymbolTableNode", "cross_ref": "numpy.lib._function_base_impl.place", "kind": "Gdef", "module_hidden": true, "module_public": false}, "poly": {".class": "SymbolTableNode", "cross_ref": "numpy.lib._polynomial_impl.poly", "kind": "Gdef", "module_hidden": true, "module_public": false}, "poly1d": {".class": "SymbolTableNode", "cross_ref": "numpy.poly1d", "kind": "Gdef", "module_hidden": true, "module_public": false}, "polyadd": {".class": "SymbolTableNode", "cross_ref": "numpy.lib._polynomial_impl.polyadd", "kind": "Gdef", "module_hidden": true, "module_public": false}, "polyder": {".class": "SymbolTableNode", "cross_ref": "numpy.lib._polynomial_impl.polyder", "kind": "Gdef", "module_hidden": true, "module_public": false}, "polydiv": {".class": "SymbolTableNode", "cross_ref": "numpy.lib._polynomial_impl.polydiv", "kind": "Gdef", "module_hidden": true, "module_public": false}, "polyfit": {".class": "SymbolTableNode", "cross_ref": "numpy.lib._polynomial_impl.polyfit", "kind": "Gdef", "module_hidden": true, "module_public": false}, "polyint": {".class": "SymbolTableNode", "cross_ref": "numpy.lib._polynomial_impl.polyint", "kind": "Gdef", "module_hidden": true, "module_public": false}, "polymul": {".class": "SymbolTableNode", "cross_ref": "numpy.lib._polynomial_impl.polymul", "kind": "Gdef", "module_hidden": true, "module_public": false}, "polynomial": {".class": "SymbolTableNode", "cross_ref": "numpy.polynomial", "kind": "Gdef", "module_hidden": true, "module_public": false}, "polysub": {".class": "SymbolTableNode", "cross_ref": "numpy.lib._polynomial_impl.polysub", "kind": "Gdef", "module_hidden": true, "module_public": false}, "polyval": {".class": "SymbolTableNode", "cross_ref": "numpy.lib._polynomial_impl.polyval", "kind": "Gdef", "module_hidden": true, "module_public": false}, "positive": {".class": "SymbolTableNode", "cross_ref": "numpy.positive", "kind": "Gdef", "module_hidden": true, "module_public": false}, "pow": {".class": "SymbolTableNode", "cross_ref": "numpy.pow", "kind": "Gdef", "module_hidden": true, "module_public": false}, "power": {".class": "SymbolTableNode", "cross_ref": "numpy.power", "kind": "Gdef", "module_hidden": true, "module_public": false}, "printoptions": {".class": "SymbolTableNode", "cross_ref": "numpy._core.arrayprint.printoptions", "kind": "Gdef", "module_hidden": true, "module_public": false}, "prod": {".class": "SymbolTableNode", "cross_ref": "numpy._core.fromnumeric.prod", "kind": "Gdef", "module_hidden": true, "module_public": false}, "promote_types": {".class": "SymbolTableNode", "cross_ref": "numpy._core.multiarray.promote_types", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ptp": {".class": "SymbolTableNode", "cross_ref": "numpy._core.fromnumeric.ptp", "kind": "Gdef", "module_hidden": true, "module_public": false}, "put": {".class": "SymbolTableNode", "cross_ref": "numpy._core.fromnumeric.put", "kind": "Gdef", "module_hidden": true, "module_public": false}, "put_along_axis": {".class": "SymbolTableNode", "cross_ref": "numpy.lib._shape_base_impl.put_along_axis", "kind": "Gdef", "module_hidden": true, "module_public": false}, "putmask": {".class": "SymbolTableNode", "cross_ref": "numpy._core.multiarray.putmask", "kind": "Gdef", "module_hidden": true, "module_public": false}, "quantile": {".class": "SymbolTableNode", "cross_ref": "numpy.lib._function_base_impl.quantile", "kind": "Gdef", "module_hidden": true, "module_public": false}, "r_": {".class": "SymbolTableNode", "cross_ref": "numpy.lib._index_tricks_impl.r_", "kind": "Gdef", "module_hidden": true, "module_public": false}, "rad2deg": {".class": "SymbolTableNode", "cross_ref": "numpy.rad2deg", "kind": "Gdef", "module_hidden": true, "module_public": false}, "radians": {".class": "SymbolTableNode", "cross_ref": "numpy.radians", "kind": "Gdef", "module_hidden": true, "module_public": false}, "rand": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "numpy.matlib.rand", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.matlib.rand", "name": "rand", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "UnionType", "items": ["builtins.int", {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "TupleType", "implicit": false, "items": ["builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "rand", "ret_type": {".class": "TypeAliasType", "args": ["numpy.float64"], "type_ref": "numpy.matlib._Matrix"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.matlib.rand", "name": "rand", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "UnionType", "items": ["builtins.int", {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "TupleType", "implicit": false, "items": ["builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "rand", "ret_type": {".class": "TypeAliasType", "args": ["numpy.float64"], "type_ref": "numpy.matlib._Matrix"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2], "arg_names": [null, "args"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.matlib.rand", "name": "rand", "type": {".class": "CallableType", "arg_kinds": [0, 2], "arg_names": [null, "args"], "arg_types": ["builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "rand", "ret_type": {".class": "TypeAliasType", "args": ["numpy.float64"], "type_ref": "numpy.matlib._Matrix"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.matlib.rand", "name": "rand", "type": {".class": "CallableType", "arg_kinds": [0, 2], "arg_names": [null, "args"], "arg_types": ["builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "rand", "ret_type": {".class": "TypeAliasType", "args": ["numpy.float64"], "type_ref": "numpy.matlib._Matrix"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "UnionType", "items": ["builtins.int", {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "TupleType", "implicit": false, "items": ["builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "rand", "ret_type": {".class": "TypeAliasType", "args": ["numpy.float64"], "type_ref": "numpy.matlib._Matrix"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 2], "arg_names": [null, "args"], "arg_types": ["builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "rand", "ret_type": {".class": "TypeAliasType", "args": ["numpy.float64"], "type_ref": "numpy.matlib._Matrix"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "randn": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "numpy.matlib.randn", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.matlib.randn", "name": "randn", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "UnionType", "items": ["builtins.int", {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "TupleType", "implicit": false, "items": ["builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "randn", "ret_type": {".class": "TypeAliasType", "args": ["numpy.float64"], "type_ref": "numpy.matlib._Matrix"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.matlib.randn", "name": "randn", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "UnionType", "items": ["builtins.int", {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "TupleType", "implicit": false, "items": ["builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "randn", "ret_type": {".class": "TypeAliasType", "args": ["numpy.float64"], "type_ref": "numpy.matlib._Matrix"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2], "arg_names": [null, "args"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.matlib.randn", "name": "randn", "type": {".class": "CallableType", "arg_kinds": [0, 2], "arg_names": [null, "args"], "arg_types": ["builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "randn", "ret_type": {".class": "TypeAliasType", "args": ["numpy.float64"], "type_ref": "numpy.matlib._Matrix"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.matlib.randn", "name": "randn", "type": {".class": "CallableType", "arg_kinds": [0, 2], "arg_names": [null, "args"], "arg_types": ["builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "randn", "ret_type": {".class": "TypeAliasType", "args": ["numpy.float64"], "type_ref": "numpy.matlib._Matrix"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "UnionType", "items": ["builtins.int", {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "TupleType", "implicit": false, "items": ["builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "randn", "ret_type": {".class": "TypeAliasType", "args": ["numpy.float64"], "type_ref": "numpy.matlib._Matrix"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 2], "arg_names": [null, "args"], "arg_types": ["builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "randn", "ret_type": {".class": "TypeAliasType", "args": ["numpy.float64"], "type_ref": "numpy.matlib._Matrix"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "random": {".class": "SymbolTableNode", "cross_ref": "numpy.random", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ravel": {".class": "SymbolTableNode", "cross_ref": "numpy._core.fromnumeric.ravel", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ravel_multi_index": {".class": "SymbolTableNode", "cross_ref": "numpy._core.multiarray.ravel_multi_index", "kind": "Gdef", "module_hidden": true, "module_public": false}, "real": {".class": "SymbolTableNode", "cross_ref": "numpy.lib._type_check_impl.real", "kind": "Gdef", "module_hidden": true, "module_public": false}, "real_if_close": {".class": "SymbolTableNode", "cross_ref": "numpy.lib._type_check_impl.real_if_close", "kind": "Gdef", "module_hidden": true, "module_public": false}, "rec": {".class": "SymbolTableNode", "cross_ref": "numpy.rec", "kind": "Gdef", "module_hidden": true, "module_public": false}, "recarray": {".class": "SymbolTableNode", "cross_ref": "numpy._core.records.recarray", "kind": "Gdef", "module_hidden": true, "module_public": false}, "reciprocal": {".class": "SymbolTableNode", "cross_ref": "numpy.reciprocal", "kind": "Gdef", "module_hidden": true, "module_public": false}, "record": {".class": "SymbolTableNode", "cross_ref": "numpy._core.records.record", "kind": "Gdef", "module_hidden": true, "module_public": false}, "remainder": {".class": "SymbolTableNode", "cross_ref": "numpy.remainder", "kind": "Gdef", "module_hidden": true, "module_public": false}, "repeat": {".class": "SymbolTableNode", "cross_ref": "numpy._core.fromnumeric.repeat", "kind": "Gdef", "module_hidden": true, "module_public": false}, "repmat": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "numpy.matlib.repmat", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["a", "m", "n"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.matlib.repmat", "name": "repmat", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["a", "m", "n"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.matlib._T", "id": -1, "name": "_T", "namespace": "numpy.matlib.repmat#0", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy.matlib._Matrix"}, "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "repmat", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.matlib._T", "id": -1, "name": "_T", "namespace": "numpy.matlib.repmat#0", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy.matlib._Matrix"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.matlib._T", "id": -1, "name": "_T", "namespace": "numpy.matlib.repmat#0", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.matlib.repmat", "name": "repmat", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["a", "m", "n"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.matlib._T", "id": -1, "name": "_T", "namespace": "numpy.matlib.repmat#0", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy.matlib._Matrix"}, "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "repmat", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.matlib._T", "id": -1, "name": "_T", "namespace": "numpy.matlib.repmat#0", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy.matlib._Matrix"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.matlib._T", "id": -1, "name": "_T", "namespace": "numpy.matlib.repmat#0", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["a", "m", "n"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.matlib.repmat", "name": "repmat", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["a", "m", "n"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.matlib._T", "id": -1, "name": "_T", "namespace": "numpy.matlib.repmat#1", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like._ArrayLike"}, "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "repmat", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.matlib._T", "id": -1, "name": "_T", "namespace": "numpy.matlib.repmat#1", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.matlib._T", "id": -1, "name": "_T", "namespace": "numpy.matlib.repmat#1", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.matlib.repmat", "name": "repmat", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["a", "m", "n"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.matlib._T", "id": -1, "name": "_T", "namespace": "numpy.matlib.repmat#1", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like._ArrayLike"}, "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "repmat", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.matlib._T", "id": -1, "name": "_T", "namespace": "numpy.matlib.repmat#1", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.matlib._T", "id": -1, "name": "_T", "namespace": "numpy.matlib.repmat#1", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["a", "m", "n"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.matlib.repmat", "name": "repmat", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["a", "m", "n"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "repmat", "ret_type": {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.matlib.repmat", "name": "repmat", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["a", "m", "n"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "repmat", "ret_type": {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["a", "m", "n"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.matlib._T", "id": -1, "name": "_T", "namespace": "numpy.matlib.repmat#0", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy.matlib._Matrix"}, "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "repmat", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.matlib._T", "id": -1, "name": "_T", "namespace": "numpy.matlib.repmat#0", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy.matlib._Matrix"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.matlib._T", "id": -1, "name": "_T", "namespace": "numpy.matlib.repmat#0", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["a", "m", "n"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.matlib._T", "id": -1, "name": "_T", "namespace": "numpy.matlib.repmat#1", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like._ArrayLike"}, "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "repmat", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.matlib._T", "id": -1, "name": "_T", "namespace": "numpy.matlib.repmat#1", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.matlib._T", "id": -1, "name": "_T", "namespace": "numpy.matlib.repmat#1", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["a", "m", "n"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "repmat", "ret_type": {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "require": {".class": "SymbolTableNode", "cross_ref": "numpy._core._asarray.require", "kind": "Gdef", "module_hidden": true, "module_public": false}, "reshape": {".class": "SymbolTableNode", "cross_ref": "numpy._core.fromnumeric.reshape", "kind": "Gdef", "module_hidden": true, "module_public": false}, "resize": {".class": "SymbolTableNode", "cross_ref": "numpy._core.fromnumeric.resize", "kind": "Gdef", "module_hidden": true, "module_public": false}, "result_type": {".class": "SymbolTableNode", "cross_ref": "numpy._core.multiarray.result_type", "kind": "Gdef", "module_hidden": true, "module_public": false}, "right_shift": {".class": "SymbolTableNode", "cross_ref": "numpy.right_shift", "kind": "Gdef", "module_hidden": true, "module_public": false}, "rint": {".class": "SymbolTableNode", "cross_ref": "numpy.rint", "kind": "Gdef", "module_hidden": true, "module_public": false}, "roll": {".class": "SymbolTableNode", "cross_ref": "numpy._core.numeric.roll", "kind": "Gdef", "module_hidden": true, "module_public": false}, "rollaxis": {".class": "SymbolTableNode", "cross_ref": "numpy._core.numeric.rollaxis", "kind": "Gdef", "module_hidden": true, "module_public": false}, "roots": {".class": "SymbolTableNode", "cross_ref": "numpy.lib._polynomial_impl.roots", "kind": "Gdef", "module_hidden": true, "module_public": false}, "rot90": {".class": "SymbolTableNode", "cross_ref": "numpy.lib._function_base_impl.rot90", "kind": "Gdef", "module_hidden": true, "module_public": false}, "round": {".class": "SymbolTableNode", "cross_ref": "numpy._core.fromnumeric.round", "kind": "Gdef", "module_hidden": true, "module_public": false}, "row_stack": {".class": "SymbolTableNode", "cross_ref": "numpy.lib._shape_base_impl.row_stack", "kind": "Gdef", "module_hidden": true, "module_public": false}, "s_": {".class": "SymbolTableNode", "cross_ref": "numpy.lib._index_tricks_impl.s_", "kind": "Gdef", "module_hidden": true, "module_public": false}, "save": {".class": "SymbolTableNode", "cross_ref": "numpy.lib._npyio_impl.save", "kind": "Gdef", "module_hidden": true, "module_public": false}, "savetxt": {".class": "SymbolTableNode", "cross_ref": "numpy.lib._npyio_impl.savetxt", "kind": "Gdef", "module_hidden": true, "module_public": false}, "savez": {".class": "SymbolTableNode", "cross_ref": "numpy.lib._npyio_impl.savez", "kind": "Gdef", "module_hidden": true, "module_public": false}, "savez_compressed": {".class": "SymbolTableNode", "cross_ref": "numpy.lib._npyio_impl.savez_compressed", "kind": "Gdef", "module_hidden": true, "module_public": false}, "sctypeDict": {".class": "SymbolTableNode", "cross_ref": "numpy._core._type_aliases.sctypeDict", "kind": "Gdef", "module_hidden": true, "module_public": false}, "searchsorted": {".class": "SymbolTableNode", "cross_ref": "numpy._core.fromnumeric.searchsorted", "kind": "Gdef", "module_hidden": true, "module_public": false}, "select": {".class": "SymbolTableNode", "cross_ref": "numpy.lib._function_base_impl.select", "kind": "Gdef", "module_hidden": true, "module_public": false}, "set_printoptions": {".class": "SymbolTableNode", "cross_ref": "numpy._core.arrayprint.set_printoptions", "kind": "Gdef", "module_hidden": true, "module_public": false}, "setbufsize": {".class": "SymbolTableNode", "cross_ref": "numpy._core._ufunc_config.setbufsize", "kind": "Gdef", "module_hidden": true, "module_public": false}, "setdiff1d": {".class": "SymbolTableNode", "cross_ref": "numpy.lib._arraysetops_impl.setdiff1d", "kind": "Gdef", "module_hidden": true, "module_public": false}, "seterr": {".class": "SymbolTableNode", "cross_ref": "numpy._core._ufunc_config.seterr", "kind": "Gdef", "module_hidden": true, "module_public": false}, "seterrcall": {".class": "SymbolTableNode", "cross_ref": "numpy._core._ufunc_config.seterrcall", "kind": "Gdef", "module_hidden": true, "module_public": false}, "setxor1d": {".class": "SymbolTableNode", "cross_ref": "numpy.lib._arraysetops_impl.setxor1d", "kind": "Gdef", "module_hidden": true, "module_public": false}, "shape": {".class": "SymbolTableNode", "cross_ref": "numpy._core.fromnumeric.shape", "kind": "Gdef", "module_hidden": true, "module_public": false}, "shares_memory": {".class": "SymbolTableNode", "cross_ref": "numpy._core.multiarray.shares_memory", "kind": "Gdef", "module_hidden": true, "module_public": false}, "short": {".class": "SymbolTableNode", "cross_ref": "numpy.short", "kind": "Gdef", "module_hidden": true, "module_public": false}, "show_config": {".class": "SymbolTableNode", "cross_ref": "numpy.__config__.show", "kind": "Gdef", "module_hidden": true, "module_public": false}, "show_runtime": {".class": "SymbolTableNode", "cross_ref": "numpy.lib._utils_impl.show_runtime", "kind": "Gdef", "module_hidden": true, "module_public": false}, "sign": {".class": "SymbolTableNode", "cross_ref": "numpy.sign", "kind": "Gdef", "module_hidden": true, "module_public": false}, "signbit": {".class": "SymbolTableNode", "cross_ref": "numpy.signbit", "kind": "Gdef", "module_hidden": true, "module_public": false}, "signedinteger": {".class": "SymbolTableNode", "cross_ref": "numpy.<PERSON><PERSON><PERSON>r", "kind": "Gdef", "module_hidden": true, "module_public": false}, "sin": {".class": "SymbolTableNode", "cross_ref": "numpy.sin", "kind": "Gdef", "module_hidden": true, "module_public": false}, "sinc": {".class": "SymbolTableNode", "cross_ref": "numpy.lib._function_base_impl.sinc", "kind": "Gdef", "module_hidden": true, "module_public": false}, "single": {".class": "SymbolTableNode", "cross_ref": "numpy.single", "kind": "Gdef", "module_hidden": true, "module_public": false}, "sinh": {".class": "SymbolTableNode", "cross_ref": "numpy.sinh", "kind": "Gdef", "module_hidden": true, "module_public": false}, "size": {".class": "SymbolTableNode", "cross_ref": "numpy._core.fromnumeric.size", "kind": "Gdef", "module_hidden": true, "module_public": false}, "sort": {".class": "SymbolTableNode", "cross_ref": "numpy._core.fromnumeric.sort", "kind": "Gdef", "module_hidden": true, "module_public": false}, "sort_complex": {".class": "SymbolTableNode", "cross_ref": "numpy.lib._function_base_impl.sort_complex", "kind": "Gdef", "module_hidden": true, "module_public": false}, "spacing": {".class": "SymbolTableNode", "cross_ref": "numpy.spacing", "kind": "Gdef", "module_hidden": true, "module_public": false}, "split": {".class": "SymbolTableNode", "cross_ref": "numpy.lib._shape_base_impl.split", "kind": "Gdef", "module_hidden": true, "module_public": false}, "sqrt": {".class": "SymbolTableNode", "cross_ref": "numpy.sqrt", "kind": "Gdef", "module_hidden": true, "module_public": false}, "square": {".class": "SymbolTableNode", "cross_ref": "numpy.square", "kind": "Gdef", "module_hidden": true, "module_public": false}, "squeeze": {".class": "SymbolTableNode", "cross_ref": "numpy._core.fromnumeric.squeeze", "kind": "Gdef", "module_hidden": true, "module_public": false}, "stack": {".class": "SymbolTableNode", "cross_ref": "numpy._core.shape_base.stack", "kind": "Gdef", "module_hidden": true, "module_public": false}, "std": {".class": "SymbolTableNode", "cross_ref": "numpy._core.fromnumeric.std", "kind": "Gdef", "module_hidden": true, "module_public": false}, "str_": {".class": "SymbolTableNode", "cross_ref": "numpy.str_", "kind": "Gdef", "module_hidden": true, "module_public": false}, "strings": {".class": "SymbolTableNode", "cross_ref": "numpy.strings", "kind": "Gdef", "module_hidden": true, "module_public": false}, "subtract": {".class": "SymbolTableNode", "cross_ref": "numpy.subtract", "kind": "Gdef", "module_hidden": true, "module_public": false}, "sum": {".class": "SymbolTableNode", "cross_ref": "numpy._core.fromnumeric.sum", "kind": "Gdef", "module_hidden": true, "module_public": false}, "swapaxes": {".class": "SymbolTableNode", "cross_ref": "numpy._core.fromnumeric.swapaxes", "kind": "Gdef", "module_hidden": true, "module_public": false}, "take": {".class": "SymbolTableNode", "cross_ref": "numpy._core.fromnumeric.take", "kind": "Gdef", "module_hidden": true, "module_public": false}, "take_along_axis": {".class": "SymbolTableNode", "cross_ref": "numpy.lib._shape_base_impl.take_along_axis", "kind": "Gdef", "module_hidden": true, "module_public": false}, "tan": {".class": "SymbolTableNode", "cross_ref": "numpy.tan", "kind": "Gdef", "module_hidden": true, "module_public": false}, "tanh": {".class": "SymbolTableNode", "cross_ref": "numpy.tanh", "kind": "Gdef", "module_hidden": true, "module_public": false}, "tensordot": {".class": "SymbolTableNode", "cross_ref": "numpy._core.numeric.tensordot", "kind": "Gdef", "module_hidden": true, "module_public": false}, "test": {".class": "SymbolTableNode", "cross_ref": "numpy.test", "kind": "Gdef", "module_hidden": true, "module_public": false}, "testing": {".class": "SymbolTableNode", "cross_ref": "numpy.testing", "kind": "Gdef", "module_hidden": true, "module_public": false}, "tile": {".class": "SymbolTableNode", "cross_ref": "numpy.lib._shape_base_impl.tile", "kind": "Gdef", "module_hidden": true, "module_public": false}, "timedelta64": {".class": "SymbolTableNode", "cross_ref": "numpy.timedelta64", "kind": "Gdef", "module_hidden": true, "module_public": false}, "trace": {".class": "SymbolTableNode", "cross_ref": "numpy._core.fromnumeric.trace", "kind": "Gdef", "module_hidden": true, "module_public": false}, "transpose": {".class": "SymbolTableNode", "cross_ref": "numpy._core.fromnumeric.transpose", "kind": "Gdef", "module_hidden": true, "module_public": false}, "trapezoid": {".class": "SymbolTableNode", "cross_ref": "numpy.lib._function_base_impl.trapezoid", "kind": "Gdef", "module_hidden": true, "module_public": false}, "trapz": {".class": "SymbolTableNode", "cross_ref": "numpy.lib._function_base_impl.trapz", "kind": "Gdef", "module_hidden": true, "module_public": false}, "tri": {".class": "SymbolTableNode", "cross_ref": "numpy.lib._twodim_base_impl.tri", "kind": "Gdef", "module_hidden": true, "module_public": false}, "tril": {".class": "SymbolTableNode", "cross_ref": "numpy.lib._twodim_base_impl.tril", "kind": "Gdef", "module_hidden": true, "module_public": false}, "tril_indices": {".class": "SymbolTableNode", "cross_ref": "numpy.lib._twodim_base_impl.tril_indices", "kind": "Gdef", "module_hidden": true, "module_public": false}, "tril_indices_from": {".class": "SymbolTableNode", "cross_ref": "numpy.lib._twodim_base_impl.tril_indices_from", "kind": "Gdef", "module_hidden": true, "module_public": false}, "trim_zeros": {".class": "SymbolTableNode", "cross_ref": "numpy.lib._function_base_impl.trim_zeros", "kind": "Gdef", "module_hidden": true, "module_public": false}, "triu": {".class": "SymbolTableNode", "cross_ref": "numpy.lib._twodim_base_impl.triu", "kind": "Gdef", "module_hidden": true, "module_public": false}, "triu_indices": {".class": "SymbolTableNode", "cross_ref": "numpy.lib._twodim_base_impl.triu_indices", "kind": "Gdef", "module_hidden": true, "module_public": false}, "triu_indices_from": {".class": "SymbolTableNode", "cross_ref": "numpy.lib._twodim_base_impl.triu_indices_from", "kind": "Gdef", "module_hidden": true, "module_public": false}, "true_divide": {".class": "SymbolTableNode", "cross_ref": "numpy.true_divide", "kind": "Gdef", "module_hidden": true, "module_public": false}, "trunc": {".class": "SymbolTableNode", "cross_ref": "numpy.trunc", "kind": "Gdef", "module_hidden": true, "module_public": false}, "typecodes": {".class": "SymbolTableNode", "cross_ref": "numpy._core.numerictypes.typecodes", "kind": "Gdef", "module_hidden": true, "module_public": false}, "typename": {".class": "SymbolTableNode", "cross_ref": "numpy.lib._type_check_impl.typename", "kind": "Gdef", "module_hidden": true, "module_public": false}, "typing": {".class": "SymbolTableNode", "cross_ref": "numpy.typing", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ubyte": {".class": "SymbolTableNode", "cross_ref": "numpy.ubyte", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ufunc": {".class": "SymbolTableNode", "cross_ref": "numpy.ufunc", "kind": "Gdef", "module_hidden": true, "module_public": false}, "uint": {".class": "SymbolTableNode", "cross_ref": "numpy.uint", "kind": "Gdef", "module_hidden": true, "module_public": false}, "uint16": {".class": "SymbolTableNode", "cross_ref": "numpy.uint16", "kind": "Gdef", "module_hidden": true, "module_public": false}, "uint32": {".class": "SymbolTableNode", "cross_ref": "numpy.uint32", "kind": "Gdef", "module_hidden": true, "module_public": false}, "uint64": {".class": "SymbolTableNode", "cross_ref": "numpy.uint64", "kind": "Gdef", "module_hidden": true, "module_public": false}, "uint8": {".class": "SymbolTableNode", "cross_ref": "numpy.uint8", "kind": "Gdef", "module_hidden": true, "module_public": false}, "uintc": {".class": "SymbolTableNode", "cross_ref": "numpy.uintc", "kind": "Gdef", "module_hidden": true, "module_public": false}, "uintp": {".class": "SymbolTableNode", "cross_ref": "numpy.uintp", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ulong": {".class": "SymbolTableNode", "cross_ref": "numpy.ulong", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ulonglong": {".class": "SymbolTableNode", "cross_ref": "numpy.ul<PERSON>", "kind": "Gdef", "module_hidden": true, "module_public": false}, "union1d": {".class": "SymbolTableNode", "cross_ref": "numpy.lib._arraysetops_impl.union1d", "kind": "Gdef", "module_hidden": true, "module_public": false}, "unique": {".class": "SymbolTableNode", "cross_ref": "numpy.lib._arraysetops_impl.unique", "kind": "Gdef", "module_hidden": true, "module_public": false}, "unique_all": {".class": "SymbolTableNode", "cross_ref": "numpy.lib._arraysetops_impl.unique_all", "kind": "Gdef", "module_hidden": true, "module_public": false}, "unique_counts": {".class": "SymbolTableNode", "cross_ref": "numpy.lib._arraysetops_impl.unique_counts", "kind": "Gdef", "module_hidden": true, "module_public": false}, "unique_inverse": {".class": "SymbolTableNode", "cross_ref": "numpy.lib._arraysetops_impl.unique_inverse", "kind": "Gdef", "module_hidden": true, "module_public": false}, "unique_values": {".class": "SymbolTableNode", "cross_ref": "numpy.lib._arraysetops_impl.unique_values", "kind": "Gdef", "module_hidden": true, "module_public": false}, "unpackbits": {".class": "SymbolTableNode", "cross_ref": "numpy._core.multiarray.unpackbits", "kind": "Gdef", "module_hidden": true, "module_public": false}, "unravel_index": {".class": "SymbolTableNode", "cross_ref": "numpy._core.multiarray.unravel_index", "kind": "Gdef", "module_hidden": true, "module_public": false}, "unsignedinteger": {".class": "SymbolTableNode", "cross_ref": "numpy.unsignedinteger", "kind": "Gdef", "module_hidden": true, "module_public": false}, "unstack": {".class": "SymbolTableNode", "cross_ref": "numpy._core.shape_base.unstack", "kind": "Gdef", "module_hidden": true, "module_public": false}, "unwrap": {".class": "SymbolTableNode", "cross_ref": "numpy.lib._function_base_impl.unwrap", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ushort": {".class": "SymbolTableNode", "cross_ref": "numpy.ushort", "kind": "Gdef", "module_hidden": true, "module_public": false}, "vander": {".class": "SymbolTableNode", "cross_ref": "numpy.lib._twodim_base_impl.vander", "kind": "Gdef", "module_hidden": true, "module_public": false}, "var": {".class": "SymbolTableNode", "cross_ref": "numpy._core.fromnumeric.var", "kind": "Gdef", "module_hidden": true, "module_public": false}, "vdot": {".class": "SymbolTableNode", "cross_ref": "numpy._core.multiarray.vdot", "kind": "Gdef", "module_hidden": true, "module_public": false}, "vecdot": {".class": "SymbolTableNode", "cross_ref": "numpy.vecdot", "kind": "Gdef", "module_hidden": true, "module_public": false}, "vecmat": {".class": "SymbolTableNode", "cross_ref": "numpy.vecmat", "kind": "Gdef", "module_hidden": true, "module_public": false}, "vectorize": {".class": "SymbolTableNode", "cross_ref": "numpy.vectorize", "kind": "Gdef", "module_hidden": true, "module_public": false}, "void": {".class": "SymbolTableNode", "cross_ref": "numpy.void", "kind": "Gdef", "module_hidden": true, "module_public": false}, "vsplit": {".class": "SymbolTableNode", "cross_ref": "numpy.lib._shape_base_impl.vsplit", "kind": "Gdef", "module_hidden": true, "module_public": false}, "vstack": {".class": "SymbolTableNode", "cross_ref": "numpy._core.shape_base.vstack", "kind": "Gdef", "module_hidden": true, "module_public": false}, "where": {".class": "SymbolTableNode", "cross_ref": "numpy._core.multiarray.where", "kind": "Gdef", "module_hidden": true, "module_public": false}, "zeros": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "numpy.matlib.zeros", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["shape", "dtype", "order"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.matlib.zeros", "name": "zeros", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["shape", "dtype", "order"], "arg_types": [{".class": "UnionType", "items": ["builtins.int", {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": true}, {".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.matlib._Order"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "zeros", "ret_type": {".class": "TypeAliasType", "args": ["numpy.float64"], "type_ref": "numpy.matlib._Matrix"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.matlib.zeros", "name": "zeros", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["shape", "dtype", "order"], "arg_types": [{".class": "UnionType", "items": ["builtins.int", {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": true}, {".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.matlib._Order"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "zeros", "ret_type": {".class": "TypeAliasType", "args": ["numpy.float64"], "type_ref": "numpy.matlib._Matrix"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["shape", "dtype", "order"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.matlib.zeros", "name": "zeros", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["shape", "dtype", "order"], "arg_types": [{".class": "UnionType", "items": ["builtins.int", {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": true}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.matlib._T", "id": -1, "name": "_T", "namespace": "numpy.matlib.zeros#1", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy._typing._dtype_like._DTypeLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.matlib._Order"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "zeros", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.matlib._T", "id": -1, "name": "_T", "namespace": "numpy.matlib.zeros#1", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy.matlib._Matrix"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.matlib._T", "id": -1, "name": "_T", "namespace": "numpy.matlib.zeros#1", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.matlib.zeros", "name": "zeros", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["shape", "dtype", "order"], "arg_types": [{".class": "UnionType", "items": ["builtins.int", {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": true}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.matlib._T", "id": -1, "name": "_T", "namespace": "numpy.matlib.zeros#1", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy._typing._dtype_like._DTypeLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.matlib._Order"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "zeros", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.matlib._T", "id": -1, "name": "_T", "namespace": "numpy.matlib.zeros#1", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy.matlib._Matrix"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.matlib._T", "id": -1, "name": "_T", "namespace": "numpy.matlib.zeros#1", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["shape", "dtype", "order"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.matlib.zeros", "name": "zeros", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["shape", "dtype", "order"], "arg_types": [{".class": "UnionType", "items": ["builtins.int", {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": true}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._dtype_like.DTypeLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.matlib._Order"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "zeros", "ret_type": {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy.matlib._Matrix"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.matlib.zeros", "name": "zeros", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["shape", "dtype", "order"], "arg_types": [{".class": "UnionType", "items": ["builtins.int", {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": true}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._dtype_like.DTypeLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.matlib._Order"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "zeros", "ret_type": {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy.matlib._Matrix"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["shape", "dtype", "order"], "arg_types": [{".class": "UnionType", "items": ["builtins.int", {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": true}, {".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.matlib._Order"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "zeros", "ret_type": {".class": "TypeAliasType", "args": ["numpy.float64"], "type_ref": "numpy.matlib._Matrix"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["shape", "dtype", "order"], "arg_types": [{".class": "UnionType", "items": ["builtins.int", {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": true}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.matlib._T", "id": -1, "name": "_T", "namespace": "numpy.matlib.zeros#1", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy._typing._dtype_like._DTypeLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.matlib._Order"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "zeros", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.matlib._T", "id": -1, "name": "_T", "namespace": "numpy.matlib.zeros#1", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy.matlib._Matrix"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.matlib._T", "id": -1, "name": "_T", "namespace": "numpy.matlib.zeros#1", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["shape", "dtype", "order"], "arg_types": [{".class": "UnionType", "items": ["builtins.int", {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": true}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._dtype_like.DTypeLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.matlib._Order"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "zeros", "ret_type": {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy.matlib._Matrix"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "zeros_like": {".class": "SymbolTableNode", "cross_ref": "numpy._core.numeric.zeros_like", "kind": "Gdef", "module_hidden": true, "module_public": false}}, "path": "c:\\Python313\\Lib\\site-packages\\numpy\\matlib.pyi"}