{"data_mtime": 1750100862, "dep_lines": [20, 22, 22, 23, 24, 25, 26, 27, 28, 29, 11, 19, 21, 22, 34, 35, 3, 5, 6, 7, 8, 11, 17, 32, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 10, 5, 5, 5, 5, 5, 5, 5, 10, 5, 5, 20, 25, 25, 5, 10, 5, 10, 5, 5, 5, 25, 5, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["pydantic.plugin._schema_validator", "pydantic._internal._config", "pydantic._internal._decorators", "pydantic._internal._fields", "pydantic._internal._generate_schema", "pydantic._internal._generics", "pydantic._internal._mock_val_ser", "pydantic._internal._namespace_utils", "pydantic._internal._signature", "pydantic._internal._utils", "pydantic_core.core_schema", "pydantic.errors", "pydantic.warnings", "pydantic._internal", "pydantic.config", "pydantic.fields", "__future__", "dataclasses", "typing", "warnings", "functools", "pydantic_core", "typing_extensions", "_typeshed", "builtins", "_frozen_importlib", "abc", "pydantic._internal._repr", "pydantic.aliases", "pydantic.plugin", "pydantic_core._pydantic_core", "re"], "hash": "db9d9d254ddd1e36726928c6fd8357fe41a3b0eb", "id": "pydantic._internal._dataclasses", "ignore_all": true, "interface_hash": "fea5761983bc10549450a6bc6ec4652b10981e62", "mtime": 1748795478, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Python313\\Lib\\site-packages\\pydantic\\_internal\\_dataclasses.py", "plugin_data": null, "size": 8909, "suppressed": [], "version_id": "1.15.0"}