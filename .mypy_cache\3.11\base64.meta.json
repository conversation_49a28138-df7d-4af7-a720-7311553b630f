{"data_mtime": 1750100859, "dep_lines": [1, 2, 3, 1, 1, 1, 1], "dep_prios": [10, 5, 5, 5, 30, 30, 30], "dependencies": ["sys", "_typeshed", "typing", "builtins", "_frozen_importlib", "abc", "typing_extensions"], "hash": "9fd42491376fcf2e79b8be5cd1822f815b127860", "id": "base64", "ignore_all": true, "interface_hash": "537a2b268f3483f06b5e274b1f9c3e3ea1b1cddb", "mtime": 1750100071, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\.vscode\\extensions\\ms-python.mypy-type-checker-2025.2.0\\bundled\\libs\\mypy\\typeshed\\stdlib\\base64.pyi", "plugin_data": null, "size": 2403, "suppressed": [], "version_id": "1.15.0"}