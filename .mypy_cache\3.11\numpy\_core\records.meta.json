{"data_mtime": 1750100846, "dep_lines": [3, 11, 4, 6, 7, 9, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["collections.abc", "numpy._typing", "typing", "_typeshed", "typing_extensions", "numpy", "builtins", "_frozen_importlib", "abc", "array", "mmap", "numpy._typing._array_like", "numpy._typing._dtype_like", "numpy._typing._nested_sequence", "os", "types"], "hash": "76f189f17365195365615196510b23b462d99a2f", "id": "numpy._core.records", "ignore_all": true, "interface_hash": "0e5dd8b63151c4f3f3a106220b376a0fda44b48e", "mtime": 1748795461, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Python313\\Lib\\site-packages\\numpy\\_core\\records.pyi", "plugin_data": null, "size": 9104, "suppressed": [], "version_id": "1.15.0"}