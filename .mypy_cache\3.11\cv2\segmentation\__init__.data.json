{".class": "MypyFile", "_fullname": "cv2.segmentation", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "IntelligentScissorsMB": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "cv2.segmentation.IntelligentScissorsMB", "name": "IntelligentScissorsMB", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "cv2.segmentation.IntelligentScissorsMB", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "cv2.segmentation", "mro": ["cv2.segmentation.IntelligentScissorsMB", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.segmentation.IntelligentScissorsMB.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cv2.segmentation.IntelligentScissorsMB"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of IntelligentScissorsMB", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "applyImage": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "cv2.segmentation.IntelligentScissorsMB.applyImage", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "image"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.segmentation.IntelligentScissorsMB.applyImage", "name": "applyImage", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "image"], "arg_types": ["cv2.segmentation.IntelligentScissorsMB", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "applyImage of IntelligentScissorsMB", "ret_type": "cv2.segmentation.IntelligentScissorsMB", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.segmentation.IntelligentScissorsMB.applyImage", "name": "applyImage", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "image"], "arg_types": ["cv2.segmentation.IntelligentScissorsMB", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "applyImage of IntelligentScissorsMB", "ret_type": "cv2.segmentation.IntelligentScissorsMB", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "image"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.segmentation.IntelligentScissorsMB.applyImage", "name": "applyImage", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "image"], "arg_types": ["cv2.segmentation.IntelligentScissorsMB", "cv2.UMat"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "applyImage of IntelligentScissorsMB", "ret_type": "cv2.segmentation.IntelligentScissorsMB", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.segmentation.IntelligentScissorsMB.applyImage", "name": "applyImage", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "image"], "arg_types": ["cv2.segmentation.IntelligentScissorsMB", "cv2.UMat"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "applyImage of IntelligentScissorsMB", "ret_type": "cv2.segmentation.IntelligentScissorsMB", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "image"], "arg_types": ["cv2.segmentation.IntelligentScissorsMB", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "applyImage of IntelligentScissorsMB", "ret_type": "cv2.segmentation.IntelligentScissorsMB", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "image"], "arg_types": ["cv2.segmentation.IntelligentScissorsMB", "cv2.UMat"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "applyImage of IntelligentScissorsMB", "ret_type": "cv2.segmentation.IntelligentScissorsMB", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "applyImageFeatures": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "cv2.segmentation.IntelligentScissorsMB.applyImageFeatures", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["self", "non_edge", "gradient_direction", "gradient_magnitude", "image"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.segmentation.IntelligentScissorsMB.applyImageFeatures", "name": "applyImageFeatures", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["self", "non_edge", "gradient_direction", "gradient_magnitude", "image"], "arg_types": ["cv2.segmentation.IntelligentScissorsMB", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "applyImageFeatures of IntelligentScissorsMB", "ret_type": "cv2.segmentation.IntelligentScissorsMB", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.segmentation.IntelligentScissorsMB.applyImageFeatures", "name": "applyImageFeatures", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["self", "non_edge", "gradient_direction", "gradient_magnitude", "image"], "arg_types": ["cv2.segmentation.IntelligentScissorsMB", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "applyImageFeatures of IntelligentScissorsMB", "ret_type": "cv2.segmentation.IntelligentScissorsMB", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["self", "non_edge", "gradient_direction", "gradient_magnitude", "image"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.segmentation.IntelligentScissorsMB.applyImageFeatures", "name": "applyImageFeatures", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["self", "non_edge", "gradient_direction", "gradient_magnitude", "image"], "arg_types": ["cv2.segmentation.IntelligentScissorsMB", "cv2.UMat", "cv2.UMat", "cv2.UMat", {".class": "UnionType", "items": ["cv2.UMat", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "applyImageFeatures of IntelligentScissorsMB", "ret_type": "cv2.segmentation.IntelligentScissorsMB", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.segmentation.IntelligentScissorsMB.applyImageFeatures", "name": "applyImageFeatures", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["self", "non_edge", "gradient_direction", "gradient_magnitude", "image"], "arg_types": ["cv2.segmentation.IntelligentScissorsMB", "cv2.UMat", "cv2.UMat", "cv2.UMat", {".class": "UnionType", "items": ["cv2.UMat", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "applyImageFeatures of IntelligentScissorsMB", "ret_type": "cv2.segmentation.IntelligentScissorsMB", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["self", "non_edge", "gradient_direction", "gradient_magnitude", "image"], "arg_types": ["cv2.segmentation.IntelligentScissorsMB", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "applyImageFeatures of IntelligentScissorsMB", "ret_type": "cv2.segmentation.IntelligentScissorsMB", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["self", "non_edge", "gradient_direction", "gradient_magnitude", "image"], "arg_types": ["cv2.segmentation.IntelligentScissorsMB", "cv2.UMat", "cv2.UMat", "cv2.UMat", {".class": "UnionType", "items": ["cv2.UMat", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "applyImageFeatures of IntelligentScissorsMB", "ret_type": "cv2.segmentation.IntelligentScissorsMB", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "buildMap": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "sourcePt"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.segmentation.IntelligentScissorsMB.buildMap", "name": "buildMap", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "sourcePt"], "arg_types": ["cv2.segmentation.IntelligentScissorsMB", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.Point"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "buildMap of IntelligentScissorsMB", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "getContour": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "cv2.segmentation.IntelligentScissorsMB.getContour", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "targetPt", "contour", "backward"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.segmentation.IntelligentScissorsMB.getContour", "name": "getContour", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "targetPt", "contour", "backward"], "arg_types": ["cv2.segmentation.IntelligentScissorsMB", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.Point"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getContour of IntelligentScissorsMB", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.segmentation.IntelligentScissorsMB.getContour", "name": "getContour", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "targetPt", "contour", "backward"], "arg_types": ["cv2.segmentation.IntelligentScissorsMB", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.Point"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getContour of IntelligentScissorsMB", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "targetPt", "contour", "backward"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.segmentation.IntelligentScissorsMB.getContour", "name": "getContour", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "targetPt", "contour", "backward"], "arg_types": ["cv2.segmentation.IntelligentScissorsMB", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.Point"}, {".class": "UnionType", "items": ["cv2.UMat", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getContour of IntelligentScissorsMB", "ret_type": "cv2.UMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.segmentation.IntelligentScissorsMB.getContour", "name": "getContour", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "targetPt", "contour", "backward"], "arg_types": ["cv2.segmentation.IntelligentScissorsMB", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.Point"}, {".class": "UnionType", "items": ["cv2.UMat", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getContour of IntelligentScissorsMB", "ret_type": "cv2.UMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "targetPt", "contour", "backward"], "arg_types": ["cv2.segmentation.IntelligentScissorsMB", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.Point"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getContour of IntelligentScissorsMB", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "targetPt", "contour", "backward"], "arg_types": ["cv2.segmentation.IntelligentScissorsMB", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.Point"}, {".class": "UnionType", "items": ["cv2.UMat", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getContour of IntelligentScissorsMB", "ret_type": "cv2.UMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "setEdgeFeatureCannyParameters": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "threshold1", "threshold2", "apertureSize", "L2gradient"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.segmentation.IntelligentScissorsMB.setEdgeFeatureCannyParameters", "name": "setEdgeFeatureCannyParameters", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "threshold1", "threshold2", "apertureSize", "L2gradient"], "arg_types": ["cv2.segmentation.IntelligentScissorsMB", "builtins.float", "builtins.float", "builtins.int", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "setEdgeFeatureCannyParameters of IntelligentScissorsMB", "ret_type": "cv2.segmentation.IntelligentScissorsMB", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "setEdgeFeatureZeroCrossingParameters": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "gradient_magnitude_min_value"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.segmentation.IntelligentScissorsMB.setEdgeFeatureZeroCrossingParameters", "name": "setEdgeFeatureZeroCrossingParameters", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "gradient_magnitude_min_value"], "arg_types": ["cv2.segmentation.IntelligentScissorsMB", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "setEdgeFeatureZeroCrossingParameters of IntelligentScissorsMB", "ret_type": "cv2.segmentation.IntelligentScissorsMB", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "setGradientMagnitudeMaxLimit": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "gradient_magnitude_threshold_max"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.segmentation.IntelligentScissorsMB.setGradientMagnitudeMaxLimit", "name": "setGradientMagnitudeMaxLimit", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "gradient_magnitude_threshold_max"], "arg_types": ["cv2.segmentation.IntelligentScissorsMB", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "setGradientMagnitudeMaxLimit of IntelligentScissorsMB", "ret_type": "cv2.segmentation.IntelligentScissorsMB", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "setWeights": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "weight_non_edge", "weight_gradient_direction", "weight_gradient_magnitude"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.segmentation.IntelligentScissorsMB.setWeights", "name": "setWeights", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "weight_non_edge", "weight_gradient_direction", "weight_gradient_magnitude"], "arg_types": ["cv2.segmentation.IntelligentScissorsMB", "builtins.float", "builtins.float", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "setWeights of IntelligentScissorsMB", "ret_type": "cv2.segmentation.IntelligentScissorsMB", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cv2.segmentation.IntelligentScissorsMB.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cv2.segmentation.IntelligentScissorsMB", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "cv2.segmentation.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.segmentation.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.segmentation.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.segmentation.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.segmentation.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.segmentation.__package__", "name": "__package__", "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.segmentation.__path__", "name": "__path__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.segmentation.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_typing": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef", "module_hidden": true, "module_public": false}, "cv2": {".class": "SymbolTableNode", "cross_ref": "cv2", "kind": "Gdef", "module_hidden": true, "module_public": false}}, "path": "c:\\Python313\\Lib\\site-packages\\cv2\\segmentation\\__init__.pyi"}