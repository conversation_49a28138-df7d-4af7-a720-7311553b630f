{"data_mtime": 1750100849, "dep_lines": [27, 24, 24, 8, 9, 10, 11, 12, 23, 24, 25, 1, 1, 1, 1, 1, 1, 15, 16, 17, 18], "dep_prios": [5, 10, 10, 10, 10, 5, 5, 5, 10, 20, 10, 5, 30, 30, 30, 30, 30, 10, 10, 10, 10], "dependencies": ["src.utils.logger", "PIL.ImageGrab", "PIL.Image", "time", "threading", "typing", "dataclasses", "pathlib", "numpy", "PIL", "cv2", "builtins", "_frozen_importlib", "_typeshed", "abc", "src.utils", "typing_extensions"], "hash": "b508974da0ff9e27d1e75c0c1cc05c78b042bff6", "id": "src.services.window_service", "ignore_all": true, "interface_hash": "55dea60a85a0dba88896b34bafe1abb69f344c20", "mtime": 1750099832, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "E:\\db\\0617\\src\\services\\window_service.py", "plugin_data": null, "size": 18646, "suppressed": ["win32gui", "win32process", "win32con", "win32api"], "version_id": "1.15.0"}