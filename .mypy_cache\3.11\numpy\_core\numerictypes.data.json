{".class": "MypyFile", "_fullname": "numpy._core.numerictypes", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DTypeLike": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._dtype_like.DTypeLike", "kind": "Gdef", "module_hidden": true, "module_public": false}, "L": {".class": "SymbolTableNode", "cross_ref": "typing.Literal", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ScalarType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy._core.numerictypes.ScalarType", "name": "ScalarType", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeType", "item": "builtins.int"}, {".class": "TypeType", "item": "builtins.float"}, {".class": "TypeType", "item": "builtins.complex"}, {".class": "TypeType", "item": "builtins.bool"}, {".class": "TypeType", "item": "builtins.bytes"}, {".class": "TypeType", "item": "builtins.str"}, {".class": "TypeType", "item": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.memoryview"}}, {".class": "TypeType", "item": {".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "numpy.bool"}}, {".class": "TypeType", "item": {".class": "Instance", "args": ["numpy._typing._nbit_base._32Bit", "numpy._typing._nbit_base._32Bit"], "extra_attrs": null, "type_ref": "numpy.complexfloating"}}, {".class": "TypeType", "item": {".class": "Instance", "args": ["numpy._typing._nbit_base._64Bit", "numpy._typing._nbit_base._64Bit"], "extra_attrs": null, "type_ref": "numpy.complexfloating"}}, {".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._nbit._NBitLongDouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._nbit._NBitLongDouble"}], "extra_attrs": null, "type_ref": "numpy.complexfloating"}}, {".class": "TypeType", "item": {".class": "Instance", "args": ["numpy._typing._nbit_base._16Bit"], "extra_attrs": null, "type_ref": "numpy.floating"}}, {".class": "TypeType", "item": {".class": "Instance", "args": ["numpy._typing._nbit_base._32Bit"], "extra_attrs": null, "type_ref": "numpy.floating"}}, {".class": "TypeType", "item": {".class": "Instance", "args": ["numpy._typing._nbit_base._64Bit"], "extra_attrs": null, "type_ref": "numpy.floating"}}, {".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._nbit._NBitLongDouble"}], "extra_attrs": null, "type_ref": "numpy.floating"}}, {".class": "TypeType", "item": {".class": "Instance", "args": ["numpy._typing._nbit_base._8Bit"], "extra_attrs": null, "type_ref": "numpy.<PERSON><PERSON><PERSON>r"}}, {".class": "TypeType", "item": {".class": "Instance", "args": ["numpy._typing._nbit_base._16Bit"], "extra_attrs": null, "type_ref": "numpy.<PERSON><PERSON><PERSON>r"}}, {".class": "TypeType", "item": {".class": "Instance", "args": ["numpy._typing._nbit_base._32Bit"], "extra_attrs": null, "type_ref": "numpy.<PERSON><PERSON><PERSON>r"}}, {".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._nbit._NBitLong"}], "extra_attrs": null, "type_ref": "numpy.<PERSON><PERSON><PERSON>r"}}, {".class": "TypeType", "item": {".class": "Instance", "args": ["numpy._typing._nbit_base._64Bit"], "extra_attrs": null, "type_ref": "numpy.<PERSON><PERSON><PERSON>r"}}, {".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "UnionType", "items": ["datetime.<PERSON><PERSON><PERSON>", "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.timedelta64"}}, {".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "UnionType", "items": ["datetime.date", "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.datetime64"}}, {".class": "TypeType", "item": "numpy.object_"}, {".class": "TypeType", "item": "numpy.bytes_"}, {".class": "TypeType", "item": "numpy.str_"}, {".class": "TypeType", "item": {".class": "Instance", "args": ["numpy._typing._nbit_base._8Bit"], "extra_attrs": null, "type_ref": "numpy.unsignedinteger"}}, {".class": "TypeType", "item": {".class": "Instance", "args": ["numpy._typing._nbit_base._16Bit"], "extra_attrs": null, "type_ref": "numpy.unsignedinteger"}}, {".class": "TypeType", "item": {".class": "Instance", "args": ["numpy._typing._nbit_base._32Bit"], "extra_attrs": null, "type_ref": "numpy.unsignedinteger"}}, {".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._nbit._NBitLong"}], "extra_attrs": null, "type_ref": "numpy.unsignedinteger"}}, {".class": "TypeType", "item": {".class": "Instance", "args": ["numpy._typing._nbit_base._64Bit"], "extra_attrs": null, "type_ref": "numpy.unsignedinteger"}}, {".class": "TypeType", "item": "numpy.void"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "TypedDict": {".class": "SymbolTableNode", "cross_ref": "typing.TypedDict", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_TypeCodes": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy._core.numerictypes._TypeCodes", "name": "_TypeCodes", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "numpy._core.numerictypes._TypeCodes", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "numpy._core.numerictypes", "mro": ["numpy._core.numerictypes._TypeCodes", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["Character", {".class": "LiteralType", "fallback": "builtins.str", "value": "c"}], ["Integer", {".class": "LiteralType", "fallback": "builtins.str", "value": "bhilqnp"}], ["UnsignedInteger", {".class": "LiteralType", "fallback": "builtins.str", "value": "BHILQNP"}], ["Float", {".class": "LiteralType", "fallback": "builtins.str", "value": "efdg"}], ["Complex", {".class": "LiteralType", "fallback": "builtins.str", "value": "FDG"}], ["AllInteger", {".class": "LiteralType", "fallback": "builtins.str", "value": "bBhHiIlLqQnNpP"}], ["AllFloat", {".class": "LiteralType", "fallback": "builtins.str", "value": "efdgFDG"}], ["Datetime", {".class": "LiteralType", "fallback": "builtins.str", "value": "Mm"}], ["All", {".class": "LiteralType", "fallback": "builtins.str", "value": "?bhilqnpBHILQNPefdgFDGSUVOMm"}]], "readonly_keys": [], "required_keys": ["All", "AllFloat", "AllInteger", "Character", "Complex", "Datetime", "Float", "Integer", "UnsignedInteger"]}}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "numpy._core.numerictypes.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy._core.numerictypes.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy._core.numerictypes.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy._core.numerictypes.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy._core.numerictypes.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy._core.numerictypes.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy._core.numerictypes.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "bool": {".class": "SymbolTableNode", "cross_ref": "numpy.bool", "kind": "Gdef"}, "bool_": {".class": "SymbolTableNode", "cross_ref": "numpy.bool_", "kind": "Gdef"}, "builtins": {".class": "SymbolTableNode", "cross_ref": "builtins", "kind": "Gdef", "module_hidden": true, "module_public": false}, "busday_count": {".class": "SymbolTableNode", "cross_ref": "numpy._core.multiarray.busday_count", "kind": "Gdef"}, "busday_offset": {".class": "SymbolTableNode", "cross_ref": "numpy._core.multiarray.busday_offset", "kind": "Gdef"}, "busdaycalendar": {".class": "SymbolTableNode", "cross_ref": "numpy.busdaycalendar", "kind": "Gdef"}, "byte": {".class": "SymbolTableNode", "cross_ref": "numpy.byte", "kind": "Gdef"}, "bytes_": {".class": "SymbolTableNode", "cross_ref": "numpy.bytes_", "kind": "Gdef"}, "cdouble": {".class": "SymbolTableNode", "cross_ref": "numpy.cdouble", "kind": "Gdef"}, "character": {".class": "SymbolTableNode", "cross_ref": "numpy.character", "kind": "Gdef"}, "clongdouble": {".class": "SymbolTableNode", "cross_ref": "numpy.clongdouble", "kind": "Gdef"}, "complex128": {".class": "SymbolTableNode", "cross_ref": "numpy.complex128", "kind": "Gdef"}, "complex160": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._extended_precision.complex160", "kind": "Gdef"}, "complex192": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._extended_precision.complex192", "kind": "Gdef"}, "complex256": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._extended_precision.complex256", "kind": "Gdef"}, "complex512": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._extended_precision.complex512", "kind": "Gdef"}, "complex64": {".class": "SymbolTableNode", "cross_ref": "numpy.complex64", "kind": "Gdef"}, "complexfloating": {".class": "SymbolTableNode", "cross_ref": "numpy.complexfloating", "kind": "Gdef"}, "csingle": {".class": "SymbolTableNode", "cross_ref": "numpy.c<PERSON>le", "kind": "Gdef"}, "datetime64": {".class": "SymbolTableNode", "cross_ref": "numpy.datetime64", "kind": "Gdef"}, "datetime_as_string": {".class": "SymbolTableNode", "cross_ref": "numpy._core.multiarray.datetime_as_string", "kind": "Gdef"}, "datetime_data": {".class": "SymbolTableNode", "cross_ref": "numpy._core.multiarray.datetime_data", "kind": "Gdef"}, "double": {".class": "SymbolTableNode", "cross_ref": "numpy.double", "kind": "Gdef"}, "dtype": {".class": "SymbolTableNode", "cross_ref": "numpy.dtype", "kind": "Gdef", "module_hidden": true, "module_public": false}, "flexible": {".class": "SymbolTableNode", "cross_ref": "numpy.flexible", "kind": "Gdef"}, "float128": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._extended_precision.float128", "kind": "Gdef"}, "float16": {".class": "SymbolTableNode", "cross_ref": "numpy.float16", "kind": "Gdef"}, "float256": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._extended_precision.float256", "kind": "Gdef"}, "float32": {".class": "SymbolTableNode", "cross_ref": "numpy.float32", "kind": "Gdef"}, "float64": {".class": "SymbolTableNode", "cross_ref": "numpy.float64", "kind": "Gdef"}, "float80": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._extended_precision.float80", "kind": "Gdef"}, "float96": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._extended_precision.float96", "kind": "Gdef"}, "floating": {".class": "SymbolTableNode", "cross_ref": "numpy.floating", "kind": "Gdef"}, "generic": {".class": "SymbolTableNode", "cross_ref": "numpy.generic", "kind": "Gdef"}, "half": {".class": "SymbolTableNode", "cross_ref": "numpy.half", "kind": "Gdef"}, "inexact": {".class": "SymbolTableNode", "cross_ref": "numpy.inexact", "kind": "Gdef"}, "int128": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._extended_precision.int128", "kind": "Gdef"}, "int16": {".class": "SymbolTableNode", "cross_ref": "numpy.int16", "kind": "Gdef"}, "int256": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._extended_precision.int256", "kind": "Gdef"}, "int32": {".class": "SymbolTableNode", "cross_ref": "numpy.int32", "kind": "Gdef"}, "int64": {".class": "SymbolTableNode", "cross_ref": "numpy.int64", "kind": "Gdef"}, "int8": {".class": "SymbolTableNode", "cross_ref": "numpy.int8", "kind": "Gdef"}, "int_": {".class": "SymbolTableNode", "cross_ref": "numpy.int_", "kind": "Gdef"}, "intc": {".class": "SymbolTableNode", "cross_ref": "numpy.intc", "kind": "Gdef"}, "integer": {".class": "SymbolTableNode", "cross_ref": "numpy.integer", "kind": "Gdef"}, "intp": {".class": "SymbolTableNode", "cross_ref": "numpy.intp", "kind": "Gdef"}, "is_busday": {".class": "SymbolTableNode", "cross_ref": "numpy._core.multiarray.is_busday", "kind": "Gdef"}, "isdtype": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["dtype", "kind"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy._core.numerictypes.isdtype", "name": "isdtype", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["dtype", "kind"], "arg_types": [{".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.dtype"}, {".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._dtype_like.DTypeLike"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._dtype_like.DTypeLike"}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "isdtype", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "issubdtype": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["arg1", "arg2"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy._core.numerictypes.issubdtype", "name": "issubdtype", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["arg1", "arg2"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._dtype_like.DTypeLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._dtype_like.DTypeLike"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "issubdtype", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "long": {".class": "SymbolTableNode", "cross_ref": "numpy.long", "kind": "Gdef"}, "longdouble": {".class": "SymbolTableNode", "cross_ref": "numpy.longdouble", "kind": "Gdef"}, "longlong": {".class": "SymbolTableNode", "cross_ref": "numpy.longlong", "kind": "Gdef"}, "np": {".class": "SymbolTableNode", "cross_ref": "numpy", "kind": "Gdef", "module_hidden": true, "module_public": false}, "number": {".class": "SymbolTableNode", "cross_ref": "numpy.number", "kind": "Gdef"}, "object_": {".class": "SymbolTableNode", "cross_ref": "numpy.object_", "kind": "Gdef"}, "sctypeDict": {".class": "SymbolTableNode", "cross_ref": "numpy._core._type_aliases.sctypeDict", "kind": "Gdef", "module_hidden": true, "module_public": false}, "short": {".class": "SymbolTableNode", "cross_ref": "numpy.short", "kind": "Gdef"}, "signedinteger": {".class": "SymbolTableNode", "cross_ref": "numpy.<PERSON><PERSON><PERSON>r", "kind": "Gdef"}, "single": {".class": "SymbolTableNode", "cross_ref": "numpy.single", "kind": "Gdef"}, "str_": {".class": "SymbolTableNode", "cross_ref": "numpy.str_", "kind": "Gdef"}, "timedelta64": {".class": "SymbolTableNode", "cross_ref": "numpy.timedelta64", "kind": "Gdef"}, "type_check_only": {".class": "SymbolTableNode", "cross_ref": "typing.type_check_only", "kind": "Gdef", "module_hidden": true, "module_public": false}, "typecodes": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy._core.numerictypes.typecodes", "name": "typecodes", "type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy._core.numerictypes._TypeCodes"}}}, "ubyte": {".class": "SymbolTableNode", "cross_ref": "numpy.ubyte", "kind": "Gdef"}, "uint": {".class": "SymbolTableNode", "cross_ref": "numpy.uint", "kind": "Gdef"}, "uint128": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._extended_precision.uint128", "kind": "Gdef"}, "uint16": {".class": "SymbolTableNode", "cross_ref": "numpy.uint16", "kind": "Gdef"}, "uint256": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._extended_precision.uint256", "kind": "Gdef"}, "uint32": {".class": "SymbolTableNode", "cross_ref": "numpy.uint32", "kind": "Gdef"}, "uint64": {".class": "SymbolTableNode", "cross_ref": "numpy.uint64", "kind": "Gdef"}, "uint8": {".class": "SymbolTableNode", "cross_ref": "numpy.uint8", "kind": "Gdef"}, "uintc": {".class": "SymbolTableNode", "cross_ref": "numpy.uintc", "kind": "Gdef"}, "uintp": {".class": "SymbolTableNode", "cross_ref": "numpy.uintp", "kind": "Gdef"}, "ulong": {".class": "SymbolTableNode", "cross_ref": "numpy.ulong", "kind": "Gdef"}, "ulonglong": {".class": "SymbolTableNode", "cross_ref": "numpy.ul<PERSON>", "kind": "Gdef"}, "unsignedinteger": {".class": "SymbolTableNode", "cross_ref": "numpy.unsignedinteger", "kind": "Gdef"}, "ushort": {".class": "SymbolTableNode", "cross_ref": "numpy.ushort", "kind": "Gdef"}, "void": {".class": "SymbolTableNode", "cross_ref": "numpy.void", "kind": "Gdef"}}, "path": "c:\\Python313\\Lib\\site-packages\\numpy\\_core\\numerictypes.pyi"}