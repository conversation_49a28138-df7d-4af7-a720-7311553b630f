#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图像识别服务模块

提供卡牌识别、图像处理等视觉相关功能
"""

import cv2
import numpy as np
from typing import List, Dict, Optional, Tuple, Any
from dataclasses import dataclass
from enum import Enum
from pathlib import Path
import asyncio

from ..utils.logger import LoggerMixin
from ..utils.config import ConfigManager

# 可选依赖检查
try:
    from ultralytics import YOLO
    HAS_ULTRALYTICS = True
except ImportError:
    HAS_ULTRALYTICS = False

try:
    import torch
    HAS_TORCH = True
except ImportError:
    HAS_TORCH = False


class Suit(Enum):
    """花色枚举"""
    SPADES = "黑桃"
    HEARTS = "红桃"
    DIAMONDS = "方块"
    CLUBS = "梅花"
    JOKER = "王"


class Rank(Enum):
    """牌面值枚举"""
    THREE = "3"
    FOUR = "4"
    FIVE = "5"
    SIX = "6"
    SEVEN = "7"
    EIGHT = "8"
    NINE = "9"
    TEN = "10"
    JACK = "J"
    QUEEN = "Q"
    KING = "K"
    ACE = "A"
    TWO = "2"
    SMALL_JOKER = "小王"
    BIG_JOKER = "大王"


@dataclass
class Card:
    """卡牌数据类"""
    suit: Suit
    rank: Rank
    
    def __str__(self):
        return f"{self.suit.value}{self.rank.value}"


@dataclass
class CardTemplate:
    """卡牌模板"""
    suit: Suit
    rank: Rank
    template: np.ndarray
    
    @property
    def key(self) -> str:
        return f"{self.suit.value}_{self.rank.value}"


@dataclass
class CardDetection:
    """卡牌检测结果"""
    card: Card
    bbox: Tuple[int, int, int, int]  # (x1, y1, x2, y2)
    confidence: float


@dataclass
class CardRecognitionResult:
    """卡牌识别结果"""
    cards: List[Card]
    confidence: float
    region: Dict[str, Any]


class RecognitionMethod(Enum):
    """识别方法枚举"""
    TEMPLATE_MATCHING = "模板匹配"
    YOLO_DETECTION = "YOLO检测"
    DEEP_LEARNING = "深度学习"
    HYBRID = "混合方法"


class VisionService(LoggerMixin):
    """图像识别服务"""
    
    def __init__(self, config_manager: ConfigManager):
        super().__init__()
        self.config = config_manager
        
        # 模型配置
        self.recognition_method = RecognitionMethod.TEMPLATE_MATCHING
        self.confidence_threshold = 0.7
        self.template_threshold = 0.8
        
        # 图像处理参数
        self.normalize_brightness = True
        self.enhance_contrast = True
        
        # 模型实例
        self.yolo_model = None
        self.classification_model = None
        
        # 模板库
        self.card_templates: Dict[str, CardTemplate] = {}
        
        self.logger.info("图像识别服务初始化完成")
    
    async def initialize_models(self) -> bool:
        """异步初始化模型"""
        try:
            # 初始化YOLO模型
            await self._load_yolo_model()
            
            # 初始化分类模型
            await self._load_classification_model()
            
            # 加载卡牌模板
            await self._load_card_templates()
            
            self.logger.info("所有视觉模型初始化完成")
            return True
            
        except Exception as e:
            self.logger.error(f"模型初始化失败: {e}")
            return False
    
    async def _load_yolo_model(self) -> None:
        """加载YOLO模型"""
        if not HAS_ULTRALYTICS:
            self.logger.warning("Ultralytics未安装，跳过YOLO模型加载")
            self.yolo_model = None
            return
        
        try:
            model_path = Path("models/card_detection.pt")
            if model_path.exists():
                # 加载自定义训练的模型
                self.yolo_model = YOLO(str(model_path))
                self.logger.info("加载自定义YOLO模型")
            else:
                # 使用预训练模型
                self.yolo_model = YOLO('yolov8n.pt')
                self.logger.info("加载预训练YOLO模型")
                
        except Exception as e:
            self.logger.error(f"YOLO模型加载失败: {e}")
            self.yolo_model = None
    
    async def _load_classification_model(self) -> None:
        """加载分类模型"""
        try:
            # 这里可以加载深度学习分类模型
            # 暂时使用模板匹配作为替代
            self.classification_model = None
            self.logger.info("分类模型加载完成(使用模板匹配)")
            
        except Exception as e:
            self.logger.error(f"分类模型加载失败: {e}")
            self.classification_model = None
    
    async def _load_card_templates(self) -> None:
        """加载卡牌模板"""
        templates_dir = Path("assets/templates")
        
        if not templates_dir.exists():
            self.logger.warning("模板目录不存在，将创建默认模板")
            await self._create_default_templates()
            return
        
        try:
            template_count = 0
            
            # 遍历模板文件
            for template_file in templates_dir.glob("*.png"):
                try:
                    # 解析文件名获取卡牌信息
                    card_info = self._parse_template_filename(template_file.stem)
                    if not card_info:
                        continue
                    
                    # 加载模板图像
                    template_img = cv2.imread(str(template_file), cv2.IMREAD_COLOR)
                    if template_img is None:
                        continue
                    
                    # 创建模板对象
                    template = CardTemplate(
                        suit=card_info['suit'],
                        rank=card_info['rank'],
                        template=template_img
                    )
                    
                    # 生成模板键
                    template_key = f"{card_info['suit'].value}_{card_info['rank'].value}"
                    self.card_templates[template_key] = template
                    template_count += 1
                    
                except Exception as e:
                    self.logger.warning(f"加载模板失败 {template_file}: {e}")
            
            self.logger.info(f"加载了{template_count} 个卡牌模板")
            
        except Exception as e:
            self.logger.error(f"加载卡牌模板失败: {e}")
    
    def _parse_template_filename(self, filename: str) -> Optional[Dict]:
        """解析模板文件名
        
        文件名格式: suit_rank.png
        例如: spades_ace.png, hearts_3.png, joker_small.png
        """
        try:
            parts = filename.split('_')
            if len(parts) != 2:
                return None
            
            suit_str, rank_str = parts
            
            # 映射花色
            suit_mapping = {
                'spades': Suit.SPADES,
                'hearts': Suit.HEARTS,
                'diamonds': Suit.DIAMONDS,
                'clubs': Suit.CLUBS,
                'joker': Suit.JOKER
            }
            
            # 映射牌面值
            rank_mapping = {
                '3': Rank.THREE, '4': Rank.FOUR, '5': Rank.FIVE,
                '6': Rank.SIX, '7': Rank.SEVEN, '8': Rank.EIGHT,
                '9': Rank.NINE, '10': Rank.TEN, 'j': Rank.JACK,
                'q': Rank.QUEEN, 'k': Rank.KING, 'a': Rank.ACE,
                '2': Rank.TWO, 'small': Rank.SMALL_JOKER, 'big': Rank.BIG_JOKER
            }
            
            suit = suit_mapping.get(suit_str.lower())
            rank = rank_mapping.get(rank_str.lower())
            
            if suit and rank:
                return {'suit': suit, 'rank': rank}
            
            return None
            
        except Exception as e:
            self.logger.debug(f"解析文件名失败{filename}: {e}")
            return None
    
    async def _create_default_templates(self) -> None:
        """创建默认模板(如果没有现成的模板)"""
        templates_dir = Path("assets/templates")
        templates_dir.mkdir(parents=True, exist_ok=True)
        
        # 这里可以生成一些基础的卡牌模板
        # 实际项目中应该使用真实的卡牌图像作为模板
        self.logger.info("请将卡牌模板图像放置在assets/templates/ 目录中")
    
    async def recognize_cards(
        self, 
        image: np.ndarray, 
        region_name: str = "unknown"
    ) -> CardRecognitionResult:
        """识别图像中的卡牌"""
        try:
            # 图像预处理
            processed_img = self._preprocess_image(image)
            
            # 根据配置选择识别方法
            if self.recognition_method == RecognitionMethod.YOLO_DETECTION:
                detections = self._detect_with_yolo(processed_img)
            elif self.recognition_method == RecognitionMethod.TEMPLATE_MATCHING:
                detections = self._detect_with_templates(processed_img)
            elif self.recognition_method == RecognitionMethod.DEEP_LEARNING:
                detections = self._detect_with_deep_learning(processed_img)
            else:  # HYBRID
                detections = self._detect_hybrid(processed_img)
            
            # 后处理和排序
            cards = self._post_process_detections(detections, region_name)
            
            # 计算总体置信度
            if cards:
                avg_confidence = sum(d.confidence for d in detections) / len(detections)
            else:
                avg_confidence = 0.0
            
            result = CardRecognitionResult(
                cards=[d.card for d in cards],
                confidence=avg_confidence,
                region={"name": region_name, "detections": len(cards)}
            )
            
            self.logger.debug(f"识别了{len(cards)} 张卡牌，平均置信度 {avg_confidence:.2f}")
            return result
            
        except Exception as e:
            self.logger.error(f"卡牌识别失败: {e}")
            return CardRecognitionResult(cards=[], confidence=0.0, region={"name": region_name, "error": str(e)})

    def _preprocess_image(self, image: np.ndarray) -> np.ndarray:
        """图像预处理"""
        processed = image.copy()

        try:
            # 亮度归一化
            if self.normalize_brightness:
                processed = self._normalize_brightness(processed)

            # 对比度增强
            if self.enhance_contrast:
                processed = self._enhance_contrast(processed)

            # 降噪
            processed = cv2.bilateralFilter(processed, 9, 75, 75)

            return processed

        except Exception as e:
            self.logger.warning(f"图像预处理失败: {e}")
            return image

    def _normalize_brightness(self, image: np.ndarray) -> np.ndarray:
        """亮度归一化"""
        # 转换到LAB色彩空间
        lab = cv2.cvtColor(image, cv2.COLOR_BGR2LAB)
        l, a, b = cv2.split(lab)

        # 对L通道进行CLAHE
        clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
        l = clahe.apply(l)

        # 合并通道并转换回BGR
        lab = cv2.merge([l, a, b])
        return cv2.cvtColor(lab, cv2.COLOR_LAB2BGR)

    def _enhance_contrast(self, image: np.ndarray) -> np.ndarray:
        """对比度增强"""
        # 使用自适应直方图均衡化
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        clahe = cv2.createCLAHE(clipLimit=3.0, tileGridSize=(8, 8))
        enhanced_gray = clahe.apply(gray)

        # 将增强后的灰度图转换回彩色
        enhanced = cv2.cvtColor(enhanced_gray, cv2.COLOR_GRAY2BGR)

        # 与原图像混合
        return cv2.addWeighted(image, 0.7, enhanced, 0.3, 0)

    def _detect_with_yolo(self, image: np.ndarray) -> List[CardDetection]:
        """使用YOLO进行卡牌检测"""
        if not self.yolo_model:
            return []

        try:
            results = self.yolo_model(image)
            detections = []

            for result in results:
                boxes = result.boxes
                if boxes is not None:
                    for box in boxes:
                        # 提取边界框信息
                        x1, y1, x2, y2 = box.xyxy[0].cpu().numpy()
                        confidence = float(box.conf[0])
                        class_id = int(box.cls[0])

                        if confidence >= self.confidence_threshold:
                            # 创建卡牌对象(这里需要根据class_id映射到具体卡牌)
                            card = self._create_card_from_class_id(class_id)

                            detection = CardDetection(
                                card=card,
                                bbox=(int(x1), int(y1), int(x2), int(y2)),
                                confidence=confidence
                            )
                            detections.append(detection)

            return detections

        except Exception as e:
            self.logger.error(f"YOLO检测失败: {e}")
            return []

    def _detect_with_templates(self, image: np.ndarray) -> List[CardDetection]:
        """使用模板匹配进行卡牌检测"""
        detections = []

        if not self.card_templates:
            self.logger.warning("没有可用的卡牌模板")
            return detections

        try:
            # 转换为灰度图
            gray_image = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

            for template_key, template_obj in self.card_templates.items():
                # 转换模板为灰度图
                template_gray = cv2.cvtColor(template_obj.template, cv2.COLOR_BGR2GRAY)

                # 模板匹配
                result = cv2.matchTemplate(gray_image, template_gray, cv2.TM_CCOEFF_NORMED)

                # 查找匹配位置
                locations = np.where(result >= self.template_threshold)

                for pt in zip(*locations[::-1]):
                    x, y = pt
                    h, w = template_gray.shape

                    # 创建检测结果
                    card = Card(suit=template_obj.suit, rank=template_obj.rank)
                    detection = CardDetection(
                        card=card,
                        bbox=(x, y, x + w, y + h),
                        confidence=float(result[y, x])
                    )
                    detections.append(detection)

            return detections

        except Exception as e:
            self.logger.error(f"模板匹配失败: {e}")
            return []

    def _detect_with_deep_learning(self, image: np.ndarray) -> List[CardDetection]:
        """使用深度学习进行卡牌检测"""
        # 暂时返回空列表，可以在这里实现深度学习检测
        self.logger.info("深度学习检测功能开发中...")
        return []

    def _detect_hybrid(self, image: np.ndarray) -> List[CardDetection]:
        """混合检测方法"""
        # 结合多种检测方法
        yolo_detections = self._detect_with_yolo(image)
        template_detections = self._detect_with_templates(image)

        # 简单合并，实际应用中需要更复杂的融合策略
        all_detections = yolo_detections + template_detections

        # 去重和筛选
        return self._remove_duplicate_detections(all_detections)

    def _create_card_from_class_id(self, class_id: int) -> Card:
        """根据类别ID创建卡牌对象"""
        # 这里需要根据训练模型的类别映射来实现
        # 暂时返回一个默认卡牌
        return Card(suit=Suit.SPADES, rank=Rank.ACE)

    def _post_process_detections(self, detections: List[CardDetection], region_name: str) -> List[CardDetection]:
        """后处理检测结果"""
        if not detections:
            return []

        # 去重
        filtered_detections = self._remove_duplicate_detections(detections)

        # 按置信度排序
        filtered_detections.sort(key=lambda x: x.confidence, reverse=True)

        return filtered_detections

    def _remove_duplicate_detections(self, detections: List[CardDetection]) -> List[CardDetection]:
        """移除重复检测"""
        if not detections:
            return []

        # 简单的重复检测移除，基于IoU
        filtered = []

        for detection in detections:
            is_duplicate = False

            for existing in filtered:
                if self._calculate_iou(detection.bbox, existing.bbox) > 0.5:
                    # 如果IoU > 0.5，认为是重复检测
                    if detection.confidence > existing.confidence:
                        # 如果新检测的置信度更高，替换
                        filtered.remove(existing)
                        filtered.append(detection)
                    is_duplicate = True
                    break

            if not is_duplicate:
                filtered.append(detection)

        return filtered

    def _calculate_iou(self, bbox1: Tuple[int, int, int, int], bbox2: Tuple[int, int, int, int]) -> float:
        """计算两个边界框的IoU"""
        x1_1, y1_1, x2_1, y2_1 = bbox1
        x1_2, y1_2, x2_2, y2_2 = bbox2

        # 计算交集
        x1_i = max(x1_1, x1_2)
        y1_i = max(y1_1, y1_2)
        x2_i = min(x2_1, x2_2)
        y2_i = min(y2_1, y2_2)

        if x2_i <= x1_i or y2_i <= y1_i:
            return 0.0

        intersection = (x2_i - x1_i) * (y2_i - y1_i)

        # 计算并集
        area1 = (x2_1 - x1_1) * (y2_1 - y1_1)
        area2 = (x2_2 - x1_2) * (y2_2 - y1_2)
        union = area1 + area2 - intersection

        return intersection / union if union > 0 else 0.0
