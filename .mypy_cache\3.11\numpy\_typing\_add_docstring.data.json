{".class": "MypyFile", "_fullname": "numpy._typing._add_docstring", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "NDArray": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._array_like.NDArray", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy._typing._add_docstring.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy._typing._add_docstring.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy._typing._add_docstring.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy._typing._add_docstring.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy._typing._add_docstring.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy._typing._add_docstring.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_docstrings": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "numpy._typing._add_docstring._docstrings", "name": "_docstrings", "type": "builtins.str"}}, "_docstrings_list": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "invalid_partial_type", "has_explicit_value"], "fullname": "numpy._typing._add_docstring._docstrings_list", "name": "_docstrings_list", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "_parse_docstrings": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy._typing._add_docstring._parse_docstrings", "name": "_parse_docstrings", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_parse_docstrings", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "add_newdoc": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["name", "value", "doc"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy._typing._add_docstring.add_newdoc", "name": "add_newdoc", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["name", "value", "doc"], "arg_types": ["builtins.str", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add_newdoc", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "re": {".class": "SymbolTableNode", "cross_ref": "re", "kind": "Gdef"}, "textwrap": {".class": "SymbolTableNode", "cross_ref": "textwrap", "kind": "Gdef"}}, "path": "c:\\Python313\\Lib\\site-packages\\numpy\\_typing\\_add_docstring.py"}