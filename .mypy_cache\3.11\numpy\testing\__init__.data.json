{".class": "MypyFile", "_fullname": "numpy.testing", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "HAS_LAPACK64": {".class": "SymbolTableNode", "cross_ref": "numpy.testing._private.utils.HAS_LAPACK64", "kind": "Gdef"}, "HAS_REFCOUNT": {".class": "SymbolTableNode", "cross_ref": "numpy.testing._private.utils.HAS_REFCOUNT", "kind": "Gdef"}, "IS_EDITABLE": {".class": "SymbolTableNode", "cross_ref": "numpy.testing._private.utils.IS_EDITABLE", "kind": "Gdef"}, "IS_INSTALLED": {".class": "SymbolTableNode", "cross_ref": "numpy.testing._private.utils.IS_INSTALLED", "kind": "Gdef"}, "IS_MUSL": {".class": "SymbolTableNode", "cross_ref": "numpy.testing._private.utils.IS_MUSL", "kind": "Gdef"}, "IS_PYPY": {".class": "SymbolTableNode", "cross_ref": "numpy.testing._private.utils.IS_PYPY", "kind": "Gdef"}, "IS_PYSTON": {".class": "SymbolTableNode", "cross_ref": "numpy.testing._private.utils.IS_PYSTON", "kind": "Gdef"}, "IS_WASM": {".class": "SymbolTableNode", "cross_ref": "numpy.testing._private.utils.IS_WASM", "kind": "Gdef"}, "IgnoreException": {".class": "SymbolTableNode", "cross_ref": "numpy.testing._private.utils.IgnoreException", "kind": "Gdef"}, "KnownFailureException": {".class": "SymbolTableNode", "cross_ref": "numpy.testing._private.utils.KnownFailureException", "kind": "Gdef"}, "NOGIL_BUILD": {".class": "SymbolTableNode", "cross_ref": "numpy.testing._private.utils.NOGIL_BUILD", "kind": "Gdef"}, "NUMPY_ROOT": {".class": "SymbolTableNode", "cross_ref": "numpy.testing._private.utils.NUMPY_ROOT", "kind": "Gdef"}, "SkipTest": {".class": "SymbolTableNode", "cross_ref": "unittest.case.SkipTest", "kind": "Gdef"}, "TestCase": {".class": "SymbolTableNode", "cross_ref": "unittest.case.TestCase", "kind": "Gdef"}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "numpy.testing.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.testing.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.testing.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.testing.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.testing.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.testing.__package__", "name": "__package__", "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.testing.__path__", "name": "__path__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.testing.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "assert_": {".class": "SymbolTableNode", "cross_ref": "numpy.testing._private.utils.assert_", "kind": "Gdef"}, "assert_allclose": {".class": "SymbolTableNode", "cross_ref": "numpy.testing._private.utils.assert_allclose", "kind": "Gdef"}, "assert_almost_equal": {".class": "SymbolTableNode", "cross_ref": "numpy.testing._private.utils.assert_almost_equal", "kind": "Gdef"}, "assert_approx_equal": {".class": "SymbolTableNode", "cross_ref": "numpy.testing._private.utils.assert_approx_equal", "kind": "Gdef"}, "assert_array_almost_equal": {".class": "SymbolTableNode", "cross_ref": "numpy.testing._private.utils.assert_array_almost_equal", "kind": "Gdef"}, "assert_array_almost_equal_nulp": {".class": "SymbolTableNode", "cross_ref": "numpy.testing._private.utils.assert_array_almost_equal_nulp", "kind": "Gdef"}, "assert_array_compare": {".class": "SymbolTableNode", "cross_ref": "numpy.testing._private.utils.assert_array_compare", "kind": "Gdef"}, "assert_array_equal": {".class": "SymbolTableNode", "cross_ref": "numpy.testing._private.utils.assert_array_equal", "kind": "Gdef"}, "assert_array_less": {".class": "SymbolTableNode", "cross_ref": "numpy.testing._private.utils.assert_array_less", "kind": "Gdef"}, "assert_array_max_ulp": {".class": "SymbolTableNode", "cross_ref": "numpy.testing._private.utils.assert_array_max_ulp", "kind": "Gdef"}, "assert_equal": {".class": "SymbolTableNode", "cross_ref": "numpy.testing._private.utils.assert_equal", "kind": "Gdef"}, "assert_no_gc_cycles": {".class": "SymbolTableNode", "cross_ref": "numpy.testing._private.utils.assert_no_gc_cycles", "kind": "Gdef"}, "assert_no_warnings": {".class": "SymbolTableNode", "cross_ref": "numpy.testing._private.utils.assert_no_warnings", "kind": "Gdef"}, "assert_raises": {".class": "SymbolTableNode", "cross_ref": "numpy.testing._private.utils.assert_raises", "kind": "Gdef"}, "assert_raises_regex": {".class": "SymbolTableNode", "cross_ref": "numpy.testing._private.utils.assert_raises_regex", "kind": "Gdef"}, "assert_string_equal": {".class": "SymbolTableNode", "cross_ref": "numpy.testing._private.utils.assert_string_equal", "kind": "Gdef"}, "assert_warns": {".class": "SymbolTableNode", "cross_ref": "numpy.testing._private.utils.assert_warns", "kind": "Gdef"}, "break_cycles": {".class": "SymbolTableNode", "cross_ref": "numpy.testing._private.utils.break_cycles", "kind": "Gdef"}, "build_err_msg": {".class": "SymbolTableNode", "cross_ref": "numpy.testing._private.utils.build_err_msg", "kind": "Gdef"}, "check_support_sve": {".class": "SymbolTableNode", "cross_ref": "numpy.testing._private.utils.check_support_sve", "kind": "Gdef"}, "clear_and_catch_warnings": {".class": "SymbolTableNode", "cross_ref": "numpy.testing._private.utils.clear_and_catch_warnings", "kind": "Gdef"}, "decorate_methods": {".class": "SymbolTableNode", "cross_ref": "numpy.testing._private.utils.decorate_methods", "kind": "Gdef"}, "jiffies": {".class": "SymbolTableNode", "cross_ref": "numpy.testing._private.utils.jiffies", "kind": "Gdef"}, "measure": {".class": "SymbolTableNode", "cross_ref": "numpy.testing._private.utils.measure", "kind": "Gdef"}, "memusage": {".class": "SymbolTableNode", "cross_ref": "numpy.testing._private.utils.memusage", "kind": "Gdef"}, "overrides": {".class": "SymbolTableNode", "cross_ref": "numpy.testing.overrides", "kind": "Gdef"}, "print_assert_equal": {".class": "SymbolTableNode", "cross_ref": "numpy.testing._private.utils.print_assert_equal", "kind": "Gdef"}, "run_threaded": {".class": "SymbolTableNode", "cross_ref": "numpy.testing._private.utils.run_threaded", "kind": "Gdef"}, "rundocs": {".class": "SymbolTableNode", "cross_ref": "numpy.testing._private.utils.rundocs", "kind": "Gdef"}, "runstring": {".class": "SymbolTableNode", "cross_ref": "numpy.testing._private.utils.runstring", "kind": "Gdef"}, "suppress_warnings": {".class": "SymbolTableNode", "cross_ref": "numpy.testing._private.utils.suppress_warnings", "kind": "Gdef"}, "tempdir": {".class": "SymbolTableNode", "cross_ref": "numpy.testing._private.utils.tempdir", "kind": "Gdef"}, "temppath": {".class": "SymbolTableNode", "cross_ref": "numpy.testing._private.utils.temppath", "kind": "Gdef"}, "verbose": {".class": "SymbolTableNode", "cross_ref": "numpy.testing._private.utils.verbose", "kind": "Gdef"}}, "path": "c:\\Python313\\Lib\\site-packages\\numpy\\testing\\__init__.pyi"}