{".class": "MypyFile", "_fullname": "cv2.gapi.streaming", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "SYNC_POLICY_DONT_SYNC": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.gapi.streaming.SYNC_POLICY_DONT_SYNC", "name": "SYNC_POLICY_DONT_SYNC", "type": "builtins.int"}}, "SYNC_POLICY_DROP": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.gapi.streaming.SYNC_POLICY_DROP", "name": "SYNC_POLICY_DROP", "type": "builtins.int"}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "cv2.gapi.streaming.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.gapi.streaming.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.gapi.streaming.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.gapi.streaming.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.gapi.streaming.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.gapi.streaming.__package__", "name": "__package__", "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.gapi.streaming.__path__", "name": "__path__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.gapi.streaming.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_typing": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef", "module_hidden": true, "module_public": false}, "cv2": {".class": "SymbolTableNode", "cross_ref": "cv2", "kind": "Gdef", "module_hidden": true, "module_public": false}, "desync": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["g"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.gapi.streaming.desync", "name": "desync", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["g"], "arg_types": ["cv2.GMat"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "desync", "ret_type": "cv2.GMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "queue_capacity": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "cv2.gapi.streaming.queue_capacity", "name": "queue_capacity", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "cv2.gapi.streaming.queue_capacity", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "cv2.gapi.streaming", "mro": ["cv2.gapi.streaming.queue_capacity", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "cap"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.gapi.streaming.queue_capacity.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "cap"], "arg_types": ["cv2.gapi.streaming.queue_capacity", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of queue_capacity", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "capacity": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "cv2.gapi.streaming.queue_capacity.capacity", "name": "capacity", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cv2.gapi.streaming.queue_capacity.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cv2.gapi.streaming.queue_capacity", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "seqNo": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["arg1"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.gapi.streaming.seqNo", "name": "seqNo", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["arg1"], "arg_types": ["cv2.GMat"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "seqNo", "ret_type": "cv2.GOpaqueT", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "seq_id": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["arg1"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.gapi.streaming.seq_id", "name": "seq_id", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["arg1"], "arg_types": ["cv2.GMat"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "seq_id", "ret_type": "cv2.GOpaqueT", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "size": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "cv2.gapi.streaming.size", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["src"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.gapi.streaming.size", "name": "size", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["src"], "arg_types": ["cv2.GMat"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "size", "ret_type": "cv2.GOpaqueT", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.gapi.streaming.size", "name": "size", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["src"], "arg_types": ["cv2.GMat"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "size", "ret_type": "cv2.GOpaqueT", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["r"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.gapi.streaming.size", "name": "size", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["r"], "arg_types": ["cv2.GOpaqueT"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "size", "ret_type": "cv2.GOpaqueT", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.gapi.streaming.size", "name": "size", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["r"], "arg_types": ["cv2.GOpaqueT"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "size", "ret_type": "cv2.GOpaqueT", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["src"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.gapi.streaming.size", "name": "size", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["src"], "arg_types": ["cv2.<PERSON><PERSON><PERSON><PERSON>"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "size", "ret_type": "cv2.GOpaqueT", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.gapi.streaming.size", "name": "size", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["src"], "arg_types": ["cv2.<PERSON><PERSON><PERSON><PERSON>"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "size", "ret_type": "cv2.GOpaqueT", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": ["src"], "arg_types": ["cv2.GMat"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "size", "ret_type": "cv2.GOpaqueT", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0], "arg_names": ["r"], "arg_types": ["cv2.GOpaqueT"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "size", "ret_type": "cv2.GOpaqueT", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0], "arg_names": ["src"], "arg_types": ["cv2.<PERSON><PERSON><PERSON><PERSON>"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "size", "ret_type": "cv2.GOpaqueT", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "sync_policy": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "cv2.gapi.streaming.sync_policy", "line": 12, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "builtins.int"}}, "sync_policy_dont_sync": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.gapi.streaming.sync_policy_dont_sync", "name": "sync_policy_dont_sync", "type": "builtins.int"}}, "sync_policy_drop": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.gapi.streaming.sync_policy_drop", "name": "sync_policy_drop", "type": "builtins.int"}}, "timestamp": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["arg1"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.gapi.streaming.timestamp", "name": "timestamp", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["arg1"], "arg_types": ["cv2.GMat"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "timestamp", "ret_type": "cv2.GOpaqueT", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "path": "c:\\Python313\\Lib\\site-packages\\cv2\\gapi\\streaming\\__init__.pyi"}