{"data_mtime": 1750101514, "dep_lines": [17, 18, 9, 10, 11, 12, 13, 14, 15, 1, 1, 1, 1, 1, 1, 22, 28], "dep_prios": [5, 5, 10, 10, 5, 5, 5, 5, 10, 5, 30, 30, 30, 30, 30, 5, 10], "dependencies": ["src.utils.logger", "src.utils.config", "cv2", "numpy", "typing", "dataclasses", "enum", "pathlib", "asyncio", "builtins", "_frozen_importlib", "abc", "os", "src.utils", "typing_extensions"], "hash": "4731c0285545628ebd13c72a604fe9040be39e78", "id": "src.services.vision_service", "ignore_all": true, "interface_hash": "11dba47782518bbd176eb526df9306a1c0a9075d", "mtime": 1750101406, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "E:\\db\\0617\\src\\services\\vision_service.py", "plugin_data": null, "size": 17771, "suppressed": ["ultralytics", "torch"], "version_id": "1.15.0"}