#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单窗口选择器
用于选择游戏窗口的界面
"""

import flet as ft
from typing import Optional, Callable, List, Dict, Any
from ..utils.logger import LoggerMixin


class SimpleWindowSelector(ft.Column, LoggerMixin):
    """简单窗口选择器"""
    
    def __init__(
        self,
        page: ft.Page,
        window_service=None,
        config_manager=None,
        on_window_selected: Optional[Callable] = None,
        on_close: Optional[Callable] = None
    ):
        super().__init__()
        self.page = page
        self.window_service = window_service
        self.config = config_manager
        self.on_window_selected = on_window_selected
        self.on_close = on_close
        
        # 窗口列表
        self.windows_list = []
        self.selected_window = None
        
        # 界面组件
        self.window_list_view = None
        self.search_field = None
        self.refresh_button = None
        
        # 构建界面
        self.controls = [self.build()]
        
        # 加载窗口列表
        self._load_windows()
        
        self.logger.info("简单窗口选择器初始化完成")
    
    def build(self) -> ft.Control:
        """构建界面"""
        try:
            self.logger.info("开始构建窗口选择器界面")

            header = self._build_header()
            self.logger.info("头部构建完成")

            search_bar = self._build_search_bar()
            self.logger.info("搜索栏构建完成")

            window_list = self._build_window_list()
            self.logger.info("窗口列表构建完成")

            footer = self._build_footer()
            self.logger.info("底部按钮构建完成")

            result = ft.Container(
                content=ft.Column([
                    # 头部
                    header,
                    ft.Divider(height=2),

                    # 搜索和刷新
                    search_bar,
                    ft.Divider(height=1),

                    # 窗口列表
                    ft.Container(
                        content=ft.Column([
                            ft.Text("可用窗口", size=16, weight=ft.FontWeight.BOLD),
                            window_list,
                        ]),
                        expand=True,
                        padding=10
                    ),

                    # 底部按钮
                    footer,

                ]),
                padding=20,
                expand=True
            )

            self.logger.info("窗口选择器界面构建完成")
            return result

        except Exception as e:
            self.logger.error(f"构建窗口选择器界面失败: {e}")
            # 返回一个简单的错误界面
            return ft.Container(
                content=ft.Column([
                    ft.Text("窗口选择器加载失败", size=20, color=ft.Colors.RED),
                    ft.Text(f"错误: {e}", size=12),
                    ft.ElevatedButton(
                        text="返回",
                        on_click=lambda _: self.on_close() if self.on_close else None
                    )
                ], horizontal_alignment=ft.CrossAxisAlignment.CENTER),
                alignment=ft.alignment.center,
                expand=True
            )
    
    def _build_header(self) -> ft.Control:
        """构建头部"""
        return ft.Row([
            ft.IconButton(
                icon=ft.Icons.CLOSE,
                tooltip="关闭",
                on_click=lambda _: self.on_close() if self.on_close else None
            ),
            ft.Text(
                "选择游戏窗口",
                size=24,
                weight=ft.FontWeight.BOLD,
                color=ft.Colors.BLUE
            ),
            ft.Container(expand=True),
            ft.Icon(ft.Icons.WINDOW, size=32, color=ft.Colors.BLUE)
        ])
    
    def _build_search_bar(self) -> ft.Control:
        """构建搜索栏"""
        self.search_field = ft.TextField(
            hint_text="搜索窗口标题...",
            prefix_icon=ft.Icons.SEARCH,
            on_change=self._on_search_change,
            width=300
        )
        
        self.refresh_button = ft.ElevatedButton(
            text="刷新",
            icon=ft.Icons.REFRESH,
            on_click=self._refresh_windows
        )
        
        return ft.Row([
            self.search_field,
            ft.Container(width=10),
            self.refresh_button,
            ft.Container(expand=True),
            ft.Text(f"找到 {len(self.windows_list)} 个窗口", size=12, color=ft.Colors.GREY_600)
        ])
    
    def _build_window_list(self) -> ft.Control:
        """构建窗口列表"""
        self.window_list_view = ft.ListView(
            expand=True,
            spacing=5,
            padding=ft.padding.all(10)
        )

        # 在构建阶段只填充窗口列表，不调用页面更新
        self._populate_window_list()

        return ft.Container(
            content=self.window_list_view,
            height=400,
            bgcolor=ft.Colors.GREY_50,
            border_radius=10,
            padding=5
        )
    
    def _build_footer(self) -> ft.Control:
        """构建底部按钮"""
        return ft.Row([
            ft.Text("提示: 双击窗口项目可直接选择", size=12, color=ft.Colors.GREY_600),
            ft.Container(expand=True),
            ft.ElevatedButton(
                text="取消",
                on_click=lambda _: self.on_close() if self.on_close else None
            ),
            ft.ElevatedButton(
                text="选择窗口",
                icon=ft.Icons.CHECK,
                bgcolor=ft.Colors.BLUE,
                color=ft.Colors.WHITE,
                on_click=self._select_window,
                disabled=True  # 初始禁用，选择窗口后启用
            )
        ])
    
    def _load_windows(self):
        """加载窗口列表"""
        try:
            if self.window_service:
                self.windows_list = self.window_service.get_all_windows()
                self.logger.info(f"加载了 {len(self.windows_list)} 个窗口")
            else:
                # 模拟窗口数据
                self.windows_list = [
                    {"title": "斗地主游戏", "handle": 12345, "process": "game.exe"},
                    {"title": "QQ游戏大厅", "handle": 12346, "process": "qqgame.exe"},
                    {"title": "欢乐斗地主", "handle": 12347, "process": "happy.exe"},
                    {"title": "记事本", "handle": 12348, "process": "notepad.exe"},
                    {"title": "浏览器", "handle": 12349, "process": "chrome.exe"},
                ]
                self.logger.info(f"使用模拟数据，共 {len(self.windows_list)} 个窗口")
        except Exception as e:
            self.logger.error(f"加载窗口列表失败: {e}")
            self.windows_list = []

    def _populate_window_list(self):
        """填充窗口列表（构建阶段使用，不触发页面更新）"""
        if not self.window_list_view:
            return

        # 清空现有列表
        self.window_list_view.controls.clear()

        # 过滤和显示窗口
        for window in self.windows_list:
            # 处理WindowInfo对象或字典
            if hasattr(window, 'title'):
                # WindowInfo对象
                title = window.title
                process = window.process_name
                handle = window.hwnd
            else:
                # 字典格式（兼容性）
                title = window.get("title", "未知窗口")
                process = window.get("process", "未知进程")
                handle = window.get("handle", 0)

            # 创建窗口项目
            window_item = self._create_window_item(window, title, process, handle)
            self.window_list_view.controls.append(window_item)

    def _update_window_list(self):
        """更新窗口列表显示"""
        if not self.window_list_view:
            return

        # 清空现有列表
        self.window_list_view.controls.clear()

        # 获取搜索关键词
        search_text = ""
        if self.search_field and self.search_field.value:
            search_text = self.search_field.value.lower()

        # 过滤和显示窗口
        for window in self.windows_list:
            # 处理WindowInfo对象或字典
            if hasattr(window, 'title'):
                # WindowInfo对象
                title = window.title
                process = window.process_name
                handle = window.hwnd
            else:
                # 字典格式（兼容性）
                title = window.get("title", "未知窗口")
                process = window.get("process", "未知进程")
                handle = window.get("handle", 0)

            # 搜索过滤
            if search_text and search_text not in title.lower() and search_text not in process.lower():
                continue

            # 创建窗口项目
            window_item = self._create_window_item(window, title, process, handle)
            self.window_list_view.controls.append(window_item)

        # 更新页面
        if self.page:
            self.page.update()
    
    def _create_window_item(self, window_data: Dict, title: str, process: str, handle: int) -> ft.Control:
        """创建窗口项目"""
        # 判断是否是游戏窗口
        is_game = any(keyword in title.lower() for keyword in ["斗地主", "游戏", "game", "poker", "card"])
        
        # 设置图标和颜色
        if is_game:
            icon = ft.Icons.CASINO
            icon_color = ft.Colors.GREEN
            bg_color = ft.Colors.GREEN_50
        else:
            icon = ft.Icons.WINDOW
            icon_color = ft.Colors.BLUE
            bg_color = ft.Colors.BLUE_50
        
        return ft.Container(
            content=ft.ListTile(
                leading=ft.Icon(icon, color=icon_color),
                title=ft.Text(title, weight=ft.FontWeight.BOLD),
                subtitle=ft.Text(f"进程: {process} | 句柄: {handle}", size=12),
                trailing=ft.Icon(ft.Icons.ARROW_FORWARD_IOS, size=16),
                on_click=lambda e, w=window_data: self._on_window_click(w),
                selected=False
            ),
            bgcolor=bg_color,
            border_radius=8,
            margin=ft.margin.only(bottom=5),
            padding=5
        )
    
    def _on_window_click(self, window_data):
        """窗口点击事件"""
        self.selected_window = window_data

        # 获取窗口标题
        if hasattr(window_data, 'title'):
            window_title = window_data.title
        else:
            window_title = window_data.get('title', '未知') if isinstance(window_data, dict) else '未知'

        # 更新选择状态
        for control in self.window_list_view.controls:
            if hasattr(control, 'content') and hasattr(control.content, 'selected'):
                control.content.selected = False
                control.bgcolor = ft.Colors.BLUE_50

        # 高亮选中的窗口
        for control in self.window_list_view.controls:
            if hasattr(control, 'content') and hasattr(control.content, 'on_click'):
                # 这里需要更复杂的逻辑来标识选中的项目
                pass

        # 启用选择按钮
        footer_row = self.controls[0].content.controls[-1]  # 获取底部按钮行
        if hasattr(footer_row, 'controls') and len(footer_row.controls) >= 3:
            select_button = footer_row.controls[-1]  # 最后一个按钮是选择按钮
            select_button.disabled = False

        self._show_message(f"已选择窗口: {window_title}")

        if self.page:
            self.page.update()
    
    def _on_search_change(self, e):
        """搜索框内容改变"""
        self._update_window_list()
    
    def _refresh_windows(self, e):
        """刷新窗口列表"""
        self._show_message("正在刷新窗口列表...")
        self._load_windows()
        self._update_window_list()
        
        # 更新计数
        search_bar = self.controls[0].content.controls[2]  # 搜索栏
        if hasattr(search_bar, 'controls') and len(search_bar.controls) >= 4:
            count_text = search_bar.controls[-1]
            count_text.value = f"找到 {len(self.windows_list)} 个窗口"
        
        if self.page:
            self.page.update()
    
    def _select_window(self, e):
        """选择窗口"""
        if not self.selected_window:
            self._show_message("请先选择一个窗口")
            return

        try:
            # 获取窗口标题
            if hasattr(self.selected_window, 'title'):
                window_title = self.selected_window.title
            else:
                window_title = self.selected_window.get('title', '未知') if isinstance(self.selected_window, dict) else '未知'

            # 调用回调函数
            if self.on_window_selected:
                self.on_window_selected(self.selected_window)

            self._show_message(f"已选择窗口: {window_title}")

            # 关闭窗口选择器
            if self.on_close:
                self.on_close()

        except Exception as ex:
            self.logger.error(f"选择窗口失败: {ex}")
            self._show_message(f"选择窗口失败: {ex}")
    
    def _show_message(self, message: str):
        """显示消息"""
        if self.page:
            snack_bar = ft.SnackBar(
                content=ft.Text(message),
                duration=3000
            )
            self.page.snack_bar = snack_bar
            snack_bar.open = True
            self.page.update()
