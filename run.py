#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
斗地主AI助手 - 启动脚本

这个脚本提供了多种启动选项和环境检查功能。

使用方法:
    python run.py              # 正常启动
    python run.py --debug      # 调试模式
    python run.py --check      # 检查依赖
    python run.py --install    # 安装依赖
    python run.py --test       # 运行测试
"""

import sys
import os
import subprocess
import argparse
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def check_python_version():
    """检查Python版本"""
    if sys.version_info < (3, 8):
        print("❌ 错误: 需要Python 3.8或更高版本")
        print(f"当前版本: {sys.version}")
        return False
    print(f"✅ Python版本: {sys.version.split()[0]}")
    return True

def check_dependencies():
    """检查依赖包"""
    print("\n🔍 检查依赖包...")
    
    # 包名映射：pip包名 -> 导入名
    required_packages = {
        'flet': 'flet',
        'numpy': 'numpy', 
        'opencv-python': 'cv2',
        'Pillow': 'PIL',
        'pywin32': 'win32gui',
        'loguru': 'loguru',
        'pydantic': 'pydantic',
        'requests': 'requests',
        'psutil': 'psutil'
    }
    
    # 可选包（需要编译工具）
    optional_packages = {
        'paddleocr': 'paddleocr',
        'torch': 'torch'
    }
    
    missing_required = []
    missing_optional = []
    
    # 检查必需包
    print("必需依赖:")
    for package, import_name in required_packages.items():
        try:
            __import__(import_name)
            print(f"  ✅ {package}")
        except ImportError:
            print(f"  ❌ {package} (未安装)")
            missing_required.append(package)
    
    # 检查可选包
    print("\n可选依赖 (OCR和AI功能):")
    for package, import_name in optional_packages.items():
        try:
            __import__(import_name)
            print(f"  ✅ {package}")
        except ImportError:
            print(f"  ⚠️  {package} (未安装，部分功能可能不可用)")
            missing_optional.append(package)
    
    if missing_required:
        print(f"\n❌ 缺少 {len(missing_required)} 个必需依赖包")
        print("运行以下命令安装:")
        print(f"pip install {' '.join(missing_required)}")
        return False
    
    if missing_optional:
        print(f"\n💡 提示: {len(missing_optional)} 个可选包未安装，部分高级功能可能不可用")
        print("如需完整功能，请安装 Microsoft Visual C++ Build Tools 后运行:")
        print(f"pip install {' '.join(missing_optional)}")
    
    print("\n✅ 核心依赖包已安装，应用可以启动")
    return True

def install_dependencies():
    """安装依赖包"""
    print("\n📦 安装依赖包...")
    
    requirements_file = project_root / "requirements.txt"
    if not requirements_file.exists():
        print("❌ 找不到 requirements.txt 文件")
        return False
    
    try:
        # 尝试使用国内镜像源
        cmd = [
            sys.executable, "-m", "pip", "install", "-r", str(requirements_file),
            "-i", "https://pypi.tuna.tsinghua.edu.cn/simple/"
        ]
        
        print("使用清华镜像源安装...")
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode != 0:
            print("镜像源安装失败，尝试官方源...")
            cmd = [sys.executable, "-m", "pip", "install", "-r", str(requirements_file)]
            result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ 依赖包安装成功")
            return True
        else:
            print(f"❌ 安装失败: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ 安装过程中出错: {e}")
        return False

def run_tests():
    """运行测试"""
    print("\n🧪 运行测试...")
    
    tests_dir = project_root / "tests"
    if not tests_dir.exists():
        print("❌ 找不到测试目录")
        return False
    
    try:
        cmd = [sys.executable, "-m", "pytest", str(tests_dir), "-v"]
        result = subprocess.run(cmd)
        return result.returncode == 0
    except FileNotFoundError:
        print("❌ pytest未安装，请先安装: pip install pytest")
        return False
    except Exception as e:
        print(f"❌ 运行测试时出错: {e}")
        return False

def check_config():
    """检查配置文件"""
    print("\n⚙️  检查配置...")
    
    config_dir = project_root / "config"
    if not config_dir.exists():
        print("⚠️  配置目录不存在，将在首次运行时创建")
        return True
    
    config_file = config_dir / "config.yaml"
    if config_file.exists():
        print("✅ 配置文件存在")
    else:
        print("⚠️  配置文件不存在，将使用默认配置")
    
    return True

def start_application(debug_mode=False):
    """启动应用程序"""
    print("\n🚀 启动斗地主AI助手...")
    
    try:
        # 设置环境变量
        if debug_mode:
            os.environ['DEBUG'] = '1'
            print("🐛 调试模式已启用")
        
        # 导入并运行主程序
        from main import main
        main()
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        print("请确保所有依赖包已正确安装")
        return False
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        return False
    
    return True

def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="斗地主AI助手启动脚本",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例:
  python run.py              # 正常启动
  python run.py --debug      # 调试模式启动
  python run.py --check      # 检查环境和依赖
  python run.py --install    # 安装依赖包
  python run.py --test       # 运行测试
        """
    )
    
    parser.add_argument('--debug', action='store_true', help='启用调试模式')
    parser.add_argument('--check', action='store_true', help='检查环境和依赖')
    parser.add_argument('--install', action='store_true', help='安装依赖包')
    parser.add_argument('--test', action='store_true', help='运行测试')
    parser.add_argument('--version', action='version', version='斗地主AI助手 v1.0.0')
    
    args = parser.parse_args()
    
    print("🎮 斗地主AI助手 - 启动脚本")
    print("=" * 40)
    
    # 检查Python版本
    if not check_python_version():
        sys.exit(1)
    
    # 处理命令行参数
    if args.install:
        success = install_dependencies()
        sys.exit(0 if success else 1)
    
    if args.check:
        env_ok = check_dependencies() and check_config()
        if env_ok:
            print("\n✅ 环境检查通过，可以正常启动应用")
        else:
            print("\n❌ 环境检查失败，请解决上述问题")
        sys.exit(0 if env_ok else 1)
    
    if args.test:
        success = run_tests()
        sys.exit(0 if success else 1)
    
    # 正常启动流程
    print("\n🔍 启动前检查...")
    
    # 检查依赖
    if not check_dependencies():
        print("\n💡 提示: 运行 'python run.py --install' 安装依赖")
        sys.exit(1)
    
    # 检查配置
    check_config()
    
    # 启动应用
    success = start_application(debug_mode=args.debug)
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()