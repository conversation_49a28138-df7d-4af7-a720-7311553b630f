{".class": "MypyFile", "_fullname": "numpy.dtypes", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_hidden": true, "module_public": false}, "BoolDType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "b"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "?"}, {".class": "LiteralType", "fallback": "builtins.int", "value": 0}], "extra_attrs": null, "type_ref": "numpy.dtypes._TypeCodes"}, "numpy.dtypes._8Bit", {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "numpy.bool"}], "extra_attrs": null, "type_ref": "numpy.dtypes._LiteralDType"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy.dtypes.BoolDType", "name": "BoolDType", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_final"], "fullname": "numpy.dtypes.BoolDType", "has_param_spec_type": false, "metaclass_type": "numpy._DTypeMeta", "metadata": {}, "module_name": "numpy.dtypes", "mro": ["numpy.dtypes.BoolDType", "numpy.dtypes._TypeCodes", "numpy.dtypes._8Bit", "numpy.dtypes._NoOrder", "numpy.dtypes._NBit", "numpy.dtypes._LiteralDType", "numpy.dtypes._SimpleDType", "numpy.dtype", "builtins.object"], "names": {".class": "SymbolTable", "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "numpy.dtypes.BoolDType.name", "name": "name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["numpy.dtypes.BoolDType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "name of BoolDType", "ret_type": {".class": "LiteralType", "fallback": "builtins.str", "value": "bool"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "numpy.dtypes.BoolDType.name", "name": "name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["numpy.dtypes.BoolDType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "name of BoolDType", "ret_type": {".class": "LiteralType", "fallback": "builtins.str", "value": "bool"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "str": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "numpy.dtypes.BoolDType.str", "name": "str", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["numpy.dtypes.BoolDType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "str of BoolDType", "ret_type": {".class": "LiteralType", "fallback": "builtins.str", "value": "|b1"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "numpy.dtypes.BoolDType.str", "name": "str", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["numpy.dtypes.BoolDType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "str of BoolDType", "ret_type": {".class": "LiteralType", "fallback": "builtins.str", "value": "|b1"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.dtypes.BoolDType.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "numpy.dtypes.BoolDType", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ByteDType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "numpy.dtypes.ByteDType", "line": 233, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "numpy.dtypes.Int8DType"}}, "BytesDType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "S"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "S"}, {".class": "LiteralType", "fallback": "builtins.int", "value": 18}], "extra_attrs": null, "type_ref": "numpy.dtypes._TypeCodes"}, "numpy.dtypes._NoOrder", {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.int", "value": 1}, {".class": "TypeVarType", "default": "builtins.int", "fullname": "numpy.dtypes._ItemSize_co", "id": 1, "name": "_ItemSize_co", "namespace": "numpy.dtypes.BytesDType", "upper_bound": "builtins.int", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.dtypes._NBit"}, {".class": "Instance", "args": ["numpy.bytes_"], "extra_attrs": null, "type_ref": "numpy.dtypes._SimpleDType"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy.dtypes.BytesDType", "name": "BytesDType", "type_vars": [{".class": "TypeVarType", "default": "builtins.int", "fullname": "numpy.dtypes._ItemSize_co", "id": 1, "name": "_ItemSize_co", "namespace": "numpy.dtypes.BytesDType", "upper_bound": "builtins.int", "values": [], "variance": 1}]}, "deletable_attributes": [], "deprecated": null, "flags": ["is_final"], "fullname": "numpy.dtypes.BytesDType", "has_param_spec_type": false, "metaclass_type": "numpy._DTypeMeta", "metadata": {}, "module_name": "numpy.dtypes", "mro": ["numpy.dtypes.BytesDType", "numpy.dtypes._TypeCodes", "numpy.dtypes._NoOrder", "numpy.dtypes._NBit", "numpy.dtypes._SimpleDType", "numpy.dtype", "builtins.object"], "names": {".class": "SymbolTable", "__new__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static"], "fullname": "numpy.dtypes.BytesDType.__new__", "name": "__new__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.int", "fullname": "numpy.dtypes._ItemSize_co", "id": 1, "name": "_ItemSize_co", "namespace": "numpy.dtypes.BytesDType", "upper_bound": "builtins.int", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.dtypes.BytesDType"}}, {".class": "TypeVarType", "default": "builtins.int", "fullname": "numpy.dtypes._ItemSize_co", "id": 1, "name": "_ItemSize_co", "namespace": "numpy.dtypes.BytesDType", "upper_bound": "builtins.int", "values": [], "variance": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__new__ of BytesDType", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.int", "fullname": "numpy.dtypes._ItemSize_co", "id": 1, "name": "_ItemSize_co", "namespace": "numpy.dtypes.BytesDType", "upper_bound": "builtins.int", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.dtypes.BytesDType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "hasobject": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "numpy.dtypes.BytesDType.hasobject", "name": "hasobject", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.int", "fullname": "numpy.dtypes._ItemSize_co", "id": 1, "name": "_ItemSize_co", "namespace": "numpy.dtypes.BytesDType", "upper_bound": "builtins.int", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.dtypes.BytesDType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "hasobject of BytesDType", "ret_type": {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "numpy.dtypes.BytesDType.hasobject", "name": "hasobject", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.int", "fullname": "numpy.dtypes._ItemSize_co", "id": 1, "name": "_ItemSize_co", "namespace": "numpy.dtypes.BytesDType", "upper_bound": "builtins.int", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.dtypes.BytesDType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "hasobject of BytesDType", "ret_type": {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "numpy.dtypes.BytesDType.name", "name": "name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.int", "fullname": "numpy.dtypes._ItemSize_co", "id": 1, "name": "_ItemSize_co", "namespace": "numpy.dtypes.BytesDType", "upper_bound": "builtins.int", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.dtypes.BytesDType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "name of BytesDType", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "numpy.dtypes.BytesDType.name", "name": "name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.int", "fullname": "numpy.dtypes._ItemSize_co", "id": 1, "name": "_ItemSize_co", "namespace": "numpy.dtypes.BytesDType", "upper_bound": "builtins.int", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.dtypes.BytesDType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "name of BytesDType", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "str": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "numpy.dtypes.BytesDType.str", "name": "str", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.int", "fullname": "numpy.dtypes._ItemSize_co", "id": 1, "name": "_ItemSize_co", "namespace": "numpy.dtypes.BytesDType", "upper_bound": "builtins.int", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.dtypes.BytesDType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "str of BytesDType", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "numpy.dtypes.BytesDType.str", "name": "str", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.int", "fullname": "numpy.dtypes._ItemSize_co", "id": 1, "name": "_ItemSize_co", "namespace": "numpy.dtypes.BytesDType", "upper_bound": "builtins.int", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.dtypes.BytesDType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "str of BytesDType", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.dtypes.BytesDType.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.int", "fullname": "numpy.dtypes._ItemSize_co", "id": 1, "name": "_ItemSize_co", "namespace": "numpy.dtypes.BytesDType", "upper_bound": "builtins.int", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.dtypes.BytesDType"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["_ItemSize_co"], "typeddict_type": null}}, "CLongDoubleDType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "c"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "G"}, {".class": "LiteralType", "fallback": "builtins.int", "value": 16}], "extra_attrs": null, "type_ref": "numpy.dtypes._TypeCodes"}, "numpy.dtypes._NativeOrder", {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.int", "value": 8}, {".class": "LiteralType", "fallback": "builtins.int", "value": 12}, {".class": "LiteralType", "fallback": "builtins.int", "value": 16}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.int", "value": 16}, {".class": "LiteralType", "fallback": "builtins.int", "value": 24}, {".class": "LiteralType", "fallback": "builtins.int", "value": 32}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.dtypes._NBit"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.clongdouble"}], "extra_attrs": null, "type_ref": "numpy.dtypes._LiteralDType"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy.dtypes.CLongDoubleDType", "name": "CLongDoubleDType", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_final"], "fullname": "numpy.dtypes.CLongDoubleDType", "has_param_spec_type": false, "metaclass_type": "numpy._DTypeMeta", "metadata": {}, "module_name": "numpy.dtypes", "mro": ["numpy.dtypes.CLongDoubleDType", "numpy.dtypes._TypeCodes", "numpy.dtypes._NativeOrder", "numpy.dtypes._NBit", "numpy.dtypes._LiteralDType", "numpy.dtypes._SimpleDType", "numpy.dtype", "builtins.object"], "names": {".class": "SymbolTable", "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "numpy.dtypes.CLongDoubleDType.name", "name": "name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["numpy.dtypes.CLongDoubleDType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "name of CLongDoubleDType", "ret_type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "complex128"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "complex192"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "complex256"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "numpy.dtypes.CLongDoubleDType.name", "name": "name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["numpy.dtypes.CLongDoubleDType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "name of CLongDoubleDType", "ret_type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "complex128"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "complex192"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "complex256"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "str": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "numpy.dtypes.CLongDoubleDType.str", "name": "str", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["numpy.dtypes.CLongDoubleDType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "str of CLongDoubleDType", "ret_type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "<c16"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">c16"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "<c24"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">c24"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "<c32"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">c32"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "numpy.dtypes.CLongDoubleDType.str", "name": "str", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["numpy.dtypes.CLongDoubleDType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "str of CLongDoubleDType", "ret_type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "<c16"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">c16"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "<c24"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">c24"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "<c32"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">c32"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.dtypes.CLongDoubleDType.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "numpy.dtypes.CLongDoubleDType", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ClassVar": {".class": "SymbolTableNode", "cross_ref": "typing.ClassVar", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Complex128DType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "c"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "D"}, {".class": "LiteralType", "fallback": "builtins.int", "value": 15}], "extra_attrs": null, "type_ref": "numpy.dtypes._TypeCodes"}, "numpy.dtypes._NativeOrder", {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.int", "value": 8}, {".class": "LiteralType", "fallback": "builtins.int", "value": 16}], "extra_attrs": null, "type_ref": "numpy.dtypes._NBit"}, {".class": "Instance", "args": ["numpy.complex128"], "extra_attrs": null, "type_ref": "numpy.dtypes._LiteralDType"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy.dtypes.Complex128DType", "name": "Complex128DType", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_final"], "fullname": "numpy.dtypes.Complex128DType", "has_param_spec_type": false, "metaclass_type": "numpy._DTypeMeta", "metadata": {}, "module_name": "numpy.dtypes", "mro": ["numpy.dtypes.Complex128DType", "numpy.dtypes._TypeCodes", "numpy.dtypes._NativeOrder", "numpy.dtypes._NBit", "numpy.dtypes._LiteralDType", "numpy.dtypes._SimpleDType", "numpy.dtype", "builtins.object"], "names": {".class": "SymbolTable", "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "numpy.dtypes.Complex128DType.name", "name": "name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["numpy.dtypes.Complex128DType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "name of Complex128DType", "ret_type": {".class": "LiteralType", "fallback": "builtins.str", "value": "complex128"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "numpy.dtypes.Complex128DType.name", "name": "name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["numpy.dtypes.Complex128DType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "name of Complex128DType", "ret_type": {".class": "LiteralType", "fallback": "builtins.str", "value": "complex128"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "str": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "numpy.dtypes.Complex128DType.str", "name": "str", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["numpy.dtypes.Complex128DType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "str of Complex128DType", "ret_type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "<c16"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">c16"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "numpy.dtypes.Complex128DType.str", "name": "str", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["numpy.dtypes.Complex128DType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "str of Complex128DType", "ret_type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "<c16"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">c16"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.dtypes.Complex128DType.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "numpy.dtypes.Complex128DType", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Complex64DType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "c"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "F"}, {".class": "LiteralType", "fallback": "builtins.int", "value": 14}], "extra_attrs": null, "type_ref": "numpy.dtypes._TypeCodes"}, "numpy.dtypes._NativeOrder", {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.int", "value": 4}, {".class": "LiteralType", "fallback": "builtins.int", "value": 8}], "extra_attrs": null, "type_ref": "numpy.dtypes._NBit"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.complex64"}], "extra_attrs": null, "type_ref": "numpy.dtypes._LiteralDType"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy.dtypes.Complex64DType", "name": "Complex64DType", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_final"], "fullname": "numpy.dtypes.Complex64DType", "has_param_spec_type": false, "metaclass_type": "numpy._DTypeMeta", "metadata": {}, "module_name": "numpy.dtypes", "mro": ["numpy.dtypes.Complex64DType", "numpy.dtypes._TypeCodes", "numpy.dtypes._NativeOrder", "numpy.dtypes._NBit", "numpy.dtypes._LiteralDType", "numpy.dtypes._SimpleDType", "numpy.dtype", "builtins.object"], "names": {".class": "SymbolTable", "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "numpy.dtypes.Complex64DType.name", "name": "name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["numpy.dtypes.Complex64DType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "name of Complex64DType", "ret_type": {".class": "LiteralType", "fallback": "builtins.str", "value": "complex64"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "numpy.dtypes.Complex64DType.name", "name": "name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["numpy.dtypes.Complex64DType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "name of Complex64DType", "ret_type": {".class": "LiteralType", "fallback": "builtins.str", "value": "complex64"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "str": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "numpy.dtypes.Complex64DType.str", "name": "str", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["numpy.dtypes.Complex64DType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "str of Complex64DType", "ret_type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "<c8"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">c8"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "numpy.dtypes.Complex64DType.str", "name": "str", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["numpy.dtypes.Complex64DType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "str of Complex64DType", "ret_type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "<c8"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">c8"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.dtypes.Complex64DType.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "numpy.dtypes.Complex64DType", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DateTime64DType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "M"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "M"}, {".class": "LiteralType", "fallback": "builtins.int", "value": 21}], "extra_attrs": null, "type_ref": "numpy.dtypes._TypeCodes"}, "numpy.dtypes._NativeOrder", {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.int", "value": 8}, {".class": "LiteralType", "fallback": "builtins.int", "value": 8}], "extra_attrs": null, "type_ref": "numpy.dtypes._NBit"}, {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["datetime.date", "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.datetime64"}], "extra_attrs": null, "type_ref": "numpy.dtypes._LiteralDType"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy.dtypes.DateTime64DType", "name": "DateTime64DType", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_final"], "fullname": "numpy.dtypes.DateTime64DType", "has_param_spec_type": false, "metaclass_type": "numpy._DTypeMeta", "metadata": {}, "module_name": "numpy.dtypes", "mro": ["numpy.dtypes.DateTime64DType", "numpy.dtypes._TypeCodes", "numpy.dtypes._NativeOrder", "numpy.dtypes._NBit", "numpy.dtypes._LiteralDType", "numpy.dtypes._SimpleDType", "numpy.dtype", "builtins.object"], "names": {".class": "SymbolTable", "__new__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static"], "fullname": "numpy.dtypes.DateTime64DType.__new__", "name": "__new__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "TypeType", "item": "numpy.dtypes.DateTime64DType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.dtypes._DateTimeUnit"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__new__ of DateTime64DType", "ret_type": {".class": "UninhabitedType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "numpy.dtypes.DateTime64DType.name", "name": "name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["numpy.dtypes.DateTime64DType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "name of DateTime64DType", "ret_type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "datetime64"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "datetime64[Y]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "datetime64[M]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "datetime64[W]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "datetime64[D]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "datetime64[h]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "datetime64[m]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "datetime64[s]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "datetime64[ms]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "datetime64[us]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "datetime64[ns]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "datetime64[ps]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "datetime64[fs]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "datetime64[as]"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "numpy.dtypes.DateTime64DType.name", "name": "name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["numpy.dtypes.DateTime64DType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "name of DateTime64DType", "ret_type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "datetime64"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "datetime64[Y]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "datetime64[M]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "datetime64[W]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "datetime64[D]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "datetime64[h]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "datetime64[m]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "datetime64[s]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "datetime64[ms]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "datetime64[us]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "datetime64[ns]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "datetime64[ps]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "datetime64[fs]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "datetime64[as]"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "str": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "numpy.dtypes.DateTime64DType.str", "name": "str", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["numpy.dtypes.DateTime64DType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "str of DateTime64DType", "ret_type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "<M8"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">M8"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "<M8[Y]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">M8[Y]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "<M8[M]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">M8[M]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "<M8[W]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">M8[W]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "<M8[D]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">M8[D]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "<M8[h]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">M8[h]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "<M8[m]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">M8[m]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "<M8[s]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">M8[s]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "<M8[ms]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">M8[ms]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "<M8[us]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">M8[us]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "<M8[ns]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">M8[ns]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "<M8[ps]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">M8[ps]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "<M8[fs]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">M8[fs]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "<M8[as]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">M8[as]"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "numpy.dtypes.DateTime64DType.str", "name": "str", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["numpy.dtypes.DateTime64DType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "str of DateTime64DType", "ret_type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "<M8"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">M8"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "<M8[Y]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">M8[Y]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "<M8[M]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">M8[M]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "<M8[W]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">M8[W]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "<M8[D]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">M8[D]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "<M8[h]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">M8[h]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "<M8[m]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">M8[m]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "<M8[s]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">M8[s]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "<M8[ms]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">M8[ms]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "<M8[us]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">M8[us]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "<M8[ns]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">M8[ns]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "<M8[ps]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">M8[ps]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "<M8[fs]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">M8[fs]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "<M8[as]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">M8[as]"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.dtypes.DateTime64DType.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "numpy.dtypes.DateTime64DType", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Float16DType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "f"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "e"}, {".class": "LiteralType", "fallback": "builtins.int", "value": 23}], "extra_attrs": null, "type_ref": "numpy.dtypes._TypeCodes"}, "numpy.dtypes._NativeOrder", {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.int", "value": 2}, {".class": "LiteralType", "fallback": "builtins.int", "value": 2}], "extra_attrs": null, "type_ref": "numpy.dtypes._NBit"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.float16"}], "extra_attrs": null, "type_ref": "numpy.dtypes._LiteralDType"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy.dtypes.Float16DType", "name": "Float16DType", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_final"], "fullname": "numpy.dtypes.Float16DType", "has_param_spec_type": false, "metaclass_type": "numpy._DTypeMeta", "metadata": {}, "module_name": "numpy.dtypes", "mro": ["numpy.dtypes.Float16DType", "numpy.dtypes._TypeCodes", "numpy.dtypes._NativeOrder", "numpy.dtypes._NBit", "numpy.dtypes._LiteralDType", "numpy.dtypes._SimpleDType", "numpy.dtype", "builtins.object"], "names": {".class": "SymbolTable", "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "numpy.dtypes.Float16DType.name", "name": "name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["numpy.dtypes.Float16DType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "name of Float16DType", "ret_type": {".class": "LiteralType", "fallback": "builtins.str", "value": "float16"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "numpy.dtypes.Float16DType.name", "name": "name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["numpy.dtypes.Float16DType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "name of Float16DType", "ret_type": {".class": "LiteralType", "fallback": "builtins.str", "value": "float16"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "str": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "numpy.dtypes.Float16DType.str", "name": "str", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["numpy.dtypes.Float16DType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "str of Float16DType", "ret_type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "<f2"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">f2"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "numpy.dtypes.Float16DType.str", "name": "str", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["numpy.dtypes.Float16DType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "str of Float16DType", "ret_type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "<f2"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">f2"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.dtypes.Float16DType.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "numpy.dtypes.Float16DType", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Float32DType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "f"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "f"}, {".class": "LiteralType", "fallback": "builtins.int", "value": 11}], "extra_attrs": null, "type_ref": "numpy.dtypes._TypeCodes"}, "numpy.dtypes._NativeOrder", {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.int", "value": 4}, {".class": "LiteralType", "fallback": "builtins.int", "value": 4}], "extra_attrs": null, "type_ref": "numpy.dtypes._NBit"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.float32"}], "extra_attrs": null, "type_ref": "numpy.dtypes._LiteralDType"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy.dtypes.Float32DType", "name": "Float32DType", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_final"], "fullname": "numpy.dtypes.Float32DType", "has_param_spec_type": false, "metaclass_type": "numpy._DTypeMeta", "metadata": {}, "module_name": "numpy.dtypes", "mro": ["numpy.dtypes.Float32DType", "numpy.dtypes._TypeCodes", "numpy.dtypes._NativeOrder", "numpy.dtypes._NBit", "numpy.dtypes._LiteralDType", "numpy.dtypes._SimpleDType", "numpy.dtype", "builtins.object"], "names": {".class": "SymbolTable", "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "numpy.dtypes.Float32DType.name", "name": "name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["numpy.dtypes.Float32DType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "name of Float32DType", "ret_type": {".class": "LiteralType", "fallback": "builtins.str", "value": "float32"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "numpy.dtypes.Float32DType.name", "name": "name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["numpy.dtypes.Float32DType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "name of Float32DType", "ret_type": {".class": "LiteralType", "fallback": "builtins.str", "value": "float32"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "str": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "numpy.dtypes.Float32DType.str", "name": "str", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["numpy.dtypes.Float32DType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "str of Float32DType", "ret_type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "<f4"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">f4"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "numpy.dtypes.Float32DType.str", "name": "str", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["numpy.dtypes.Float32DType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "str of Float32DType", "ret_type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "<f4"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">f4"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.dtypes.Float32DType.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "numpy.dtypes.Float32DType", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Float64DType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "f"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "d"}, {".class": "LiteralType", "fallback": "builtins.int", "value": 12}], "extra_attrs": null, "type_ref": "numpy.dtypes._TypeCodes"}, "numpy.dtypes._NativeOrder", {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.int", "value": 8}, {".class": "LiteralType", "fallback": "builtins.int", "value": 8}], "extra_attrs": null, "type_ref": "numpy.dtypes._NBit"}, {".class": "Instance", "args": ["numpy.float64"], "extra_attrs": null, "type_ref": "numpy.dtypes._LiteralDType"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy.dtypes.Float64DType", "name": "Float64DType", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_final"], "fullname": "numpy.dtypes.Float64DType", "has_param_spec_type": false, "metaclass_type": "numpy._DTypeMeta", "metadata": {}, "module_name": "numpy.dtypes", "mro": ["numpy.dtypes.Float64DType", "numpy.dtypes._TypeCodes", "numpy.dtypes._NativeOrder", "numpy.dtypes._NBit", "numpy.dtypes._LiteralDType", "numpy.dtypes._SimpleDType", "numpy.dtype", "builtins.object"], "names": {".class": "SymbolTable", "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "numpy.dtypes.Float64DType.name", "name": "name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["numpy.dtypes.Float64DType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "name of Float64DType", "ret_type": {".class": "LiteralType", "fallback": "builtins.str", "value": "float64"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "numpy.dtypes.Float64DType.name", "name": "name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["numpy.dtypes.Float64DType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "name of Float64DType", "ret_type": {".class": "LiteralType", "fallback": "builtins.str", "value": "float64"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "str": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "numpy.dtypes.Float64DType.str", "name": "str", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["numpy.dtypes.Float64DType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "str of Float64DType", "ret_type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "<f8"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">f8"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "numpy.dtypes.Float64DType.str", "name": "str", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["numpy.dtypes.Float64DType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "str of Float64DType", "ret_type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "<f8"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">f8"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.dtypes.Float64DType.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "numpy.dtypes.Float64DType", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Generic": {".class": "SymbolTableNode", "cross_ref": "typing.Generic", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Int16DType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "i"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "h"}, {".class": "LiteralType", "fallback": "builtins.int", "value": 3}], "extra_attrs": null, "type_ref": "numpy.dtypes._TypeCodes"}, "numpy.dtypes._NativeOrder", {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.int", "value": 2}, {".class": "LiteralType", "fallback": "builtins.int", "value": 2}], "extra_attrs": null, "type_ref": "numpy.dtypes._NBit"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.int16"}], "extra_attrs": null, "type_ref": "numpy.dtypes._LiteralDType"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy.dtypes.Int16DType", "name": "Int16DType", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_final"], "fullname": "numpy.dtypes.Int16DType", "has_param_spec_type": false, "metaclass_type": "numpy._DTypeMeta", "metadata": {}, "module_name": "numpy.dtypes", "mro": ["numpy.dtypes.Int16DType", "numpy.dtypes._TypeCodes", "numpy.dtypes._NativeOrder", "numpy.dtypes._NBit", "numpy.dtypes._LiteralDType", "numpy.dtypes._SimpleDType", "numpy.dtype", "builtins.object"], "names": {".class": "SymbolTable", "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "numpy.dtypes.Int16DType.name", "name": "name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["numpy.dtypes.Int16DType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "name of Int16DType", "ret_type": {".class": "LiteralType", "fallback": "builtins.str", "value": "int16"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "numpy.dtypes.Int16DType.name", "name": "name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["numpy.dtypes.Int16DType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "name of Int16DType", "ret_type": {".class": "LiteralType", "fallback": "builtins.str", "value": "int16"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "str": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "numpy.dtypes.Int16DType.str", "name": "str", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["numpy.dtypes.Int16DType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "str of Int16DType", "ret_type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "<i2"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">i2"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "numpy.dtypes.Int16DType.str", "name": "str", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["numpy.dtypes.Int16DType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "str of Int16DType", "ret_type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "<i2"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">i2"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.dtypes.Int16DType.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "numpy.dtypes.Int16DType", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Int32DType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "i"}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "i"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "l"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.int", "value": 5}, {".class": "LiteralType", "fallback": "builtins.int", "value": 7}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.dtypes._TypeCodes"}, "numpy.dtypes._NativeOrder", {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.int", "value": 4}, {".class": "LiteralType", "fallback": "builtins.int", "value": 4}], "extra_attrs": null, "type_ref": "numpy.dtypes._NBit"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.int32"}], "extra_attrs": null, "type_ref": "numpy.dtypes._LiteralDType"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy.dtypes.Int32DType", "name": "Int32DType", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_final"], "fullname": "numpy.dtypes.Int32DType", "has_param_spec_type": false, "metaclass_type": "numpy._DTypeMeta", "metadata": {}, "module_name": "numpy.dtypes", "mro": ["numpy.dtypes.Int32DType", "numpy.dtypes._TypeCodes", "numpy.dtypes._NativeOrder", "numpy.dtypes._NBit", "numpy.dtypes._LiteralDType", "numpy.dtypes._SimpleDType", "numpy.dtype", "builtins.object"], "names": {".class": "SymbolTable", "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "numpy.dtypes.Int32DType.name", "name": "name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["numpy.dtypes.Int32DType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "name of Int32DType", "ret_type": {".class": "LiteralType", "fallback": "builtins.str", "value": "int32"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "numpy.dtypes.Int32DType.name", "name": "name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["numpy.dtypes.Int32DType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "name of Int32DType", "ret_type": {".class": "LiteralType", "fallback": "builtins.str", "value": "int32"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "str": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "numpy.dtypes.Int32DType.str", "name": "str", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["numpy.dtypes.Int32DType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "str of Int32DType", "ret_type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "<i4"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">i4"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "numpy.dtypes.Int32DType.str", "name": "str", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["numpy.dtypes.Int32DType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "str of Int32DType", "ret_type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "<i4"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">i4"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.dtypes.Int32DType.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "numpy.dtypes.Int32DType", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Int64DType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "i"}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "l"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "q"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.int", "value": 7}, {".class": "LiteralType", "fallback": "builtins.int", "value": 9}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.dtypes._TypeCodes"}, "numpy.dtypes._NativeOrder", {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.int", "value": 8}, {".class": "LiteralType", "fallback": "builtins.int", "value": 8}], "extra_attrs": null, "type_ref": "numpy.dtypes._NBit"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.int64"}], "extra_attrs": null, "type_ref": "numpy.dtypes._LiteralDType"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy.dtypes.Int64DType", "name": "Int64DType", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_final"], "fullname": "numpy.dtypes.Int64DType", "has_param_spec_type": false, "metaclass_type": "numpy._DTypeMeta", "metadata": {}, "module_name": "numpy.dtypes", "mro": ["numpy.dtypes.Int64DType", "numpy.dtypes._TypeCodes", "numpy.dtypes._NativeOrder", "numpy.dtypes._NBit", "numpy.dtypes._LiteralDType", "numpy.dtypes._SimpleDType", "numpy.dtype", "builtins.object"], "names": {".class": "SymbolTable", "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "numpy.dtypes.Int64DType.name", "name": "name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["numpy.dtypes.Int64DType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "name of Int64DType", "ret_type": {".class": "LiteralType", "fallback": "builtins.str", "value": "int64"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "numpy.dtypes.Int64DType.name", "name": "name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["numpy.dtypes.Int64DType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "name of Int64DType", "ret_type": {".class": "LiteralType", "fallback": "builtins.str", "value": "int64"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "str": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "numpy.dtypes.Int64DType.str", "name": "str", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["numpy.dtypes.Int64DType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "str of Int64DType", "ret_type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "<i8"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">i8"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "numpy.dtypes.Int64DType.str", "name": "str", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["numpy.dtypes.Int64DType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "str of Int64DType", "ret_type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "<i8"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">i8"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.dtypes.Int64DType.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "numpy.dtypes.Int64DType", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Int8DType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "i"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "b"}, {".class": "LiteralType", "fallback": "builtins.int", "value": 1}], "extra_attrs": null, "type_ref": "numpy.dtypes._TypeCodes"}, "numpy.dtypes._8Bit", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.int8"}], "extra_attrs": null, "type_ref": "numpy.dtypes._LiteralDType"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy.dtypes.Int8DType", "name": "Int8DType", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_final"], "fullname": "numpy.dtypes.Int8DType", "has_param_spec_type": false, "metaclass_type": "numpy._DTypeMeta", "metadata": {}, "module_name": "numpy.dtypes", "mro": ["numpy.dtypes.Int8DType", "numpy.dtypes._TypeCodes", "numpy.dtypes._8Bit", "numpy.dtypes._NoOrder", "numpy.dtypes._NBit", "numpy.dtypes._LiteralDType", "numpy.dtypes._SimpleDType", "numpy.dtype", "builtins.object"], "names": {".class": "SymbolTable", "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "numpy.dtypes.Int8DType.name", "name": "name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["numpy.dtypes.Int8DType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "name of Int8DType", "ret_type": {".class": "LiteralType", "fallback": "builtins.str", "value": "int8"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "numpy.dtypes.Int8DType.name", "name": "name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["numpy.dtypes.Int8DType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "name of Int8DType", "ret_type": {".class": "LiteralType", "fallback": "builtins.str", "value": "int8"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "str": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "numpy.dtypes.Int8DType.str", "name": "str", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["numpy.dtypes.Int8DType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "str of Int8DType", "ret_type": {".class": "LiteralType", "fallback": "builtins.str", "value": "|i1"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "numpy.dtypes.Int8DType.str", "name": "str", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["numpy.dtypes.Int8DType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "str of Int8DType", "ret_type": {".class": "LiteralType", "fallback": "builtins.str", "value": "|i1"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.dtypes.Int8DType.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "numpy.dtypes.Int8DType", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "IntDType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "i"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "i"}, {".class": "LiteralType", "fallback": "builtins.int", "value": 5}], "extra_attrs": null, "type_ref": "numpy.dtypes._TypeCodes"}, "numpy.dtypes._NativeOrder", {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.int", "value": 4}, {".class": "LiteralType", "fallback": "builtins.int", "value": 4}], "extra_attrs": null, "type_ref": "numpy.dtypes._NBit"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intc"}], "extra_attrs": null, "type_ref": "numpy.dtypes._LiteralDType"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy.dtypes.IntDType", "name": "IntDType", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_final"], "fullname": "numpy.dtypes.IntDType", "has_param_spec_type": false, "metaclass_type": "numpy._DTypeMeta", "metadata": {}, "module_name": "numpy.dtypes", "mro": ["numpy.dtypes.IntDType", "numpy.dtypes._TypeCodes", "numpy.dtypes._NativeOrder", "numpy.dtypes._NBit", "numpy.dtypes._LiteralDType", "numpy.dtypes._SimpleDType", "numpy.dtype", "builtins.object"], "names": {".class": "SymbolTable", "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "numpy.dtypes.IntDType.name", "name": "name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["numpy.dtypes.IntDType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "name of IntDType", "ret_type": {".class": "LiteralType", "fallback": "builtins.str", "value": "int32"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "numpy.dtypes.IntDType.name", "name": "name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["numpy.dtypes.IntDType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "name of IntDType", "ret_type": {".class": "LiteralType", "fallback": "builtins.str", "value": "int32"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "str": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "numpy.dtypes.IntDType.str", "name": "str", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["numpy.dtypes.IntDType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "str of IntDType", "ret_type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "<i4"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">i4"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "numpy.dtypes.IntDType.str", "name": "str", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["numpy.dtypes.IntDType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "str of IntDType", "ret_type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "<i4"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">i4"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.dtypes.IntDType.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "numpy.dtypes.IntDType", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "L": {".class": "SymbolTableNode", "cross_ref": "typing.Literal", "kind": "Gdef", "module_hidden": true, "module_public": false}, "LiteralString": {".class": "SymbolTableNode", "cross_ref": "typing.LiteralString", "kind": "Gdef", "module_hidden": true, "module_public": false}, "LongDType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "i"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "l"}, {".class": "LiteralType", "fallback": "builtins.int", "value": 7}], "extra_attrs": null, "type_ref": "numpy.dtypes._TypeCodes"}, "numpy.dtypes._NativeOrder", {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.int", "value": 4}, {".class": "LiteralType", "fallback": "builtins.int", "value": 8}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.int", "value": 4}, {".class": "LiteralType", "fallback": "builtins.int", "value": 8}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.dtypes._NBit"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.long"}], "extra_attrs": null, "type_ref": "numpy.dtypes._LiteralDType"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy.dtypes.LongDType", "name": "LongDType", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_final"], "fullname": "numpy.dtypes.LongDType", "has_param_spec_type": false, "metaclass_type": "numpy._DTypeMeta", "metadata": {}, "module_name": "numpy.dtypes", "mro": ["numpy.dtypes.LongDType", "numpy.dtypes._TypeCodes", "numpy.dtypes._NativeOrder", "numpy.dtypes._NBit", "numpy.dtypes._LiteralDType", "numpy.dtypes._SimpleDType", "numpy.dtype", "builtins.object"], "names": {".class": "SymbolTable", "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "numpy.dtypes.LongDType.name", "name": "name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["numpy.dtypes.LongDType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "name of LongDType", "ret_type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "int32"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "int64"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "numpy.dtypes.LongDType.name", "name": "name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["numpy.dtypes.LongDType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "name of LongDType", "ret_type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "int32"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "int64"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "str": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "numpy.dtypes.LongDType.str", "name": "str", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["numpy.dtypes.LongDType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "str of LongDType", "ret_type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "<i4"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">i4"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "<i8"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">i8"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "numpy.dtypes.LongDType.str", "name": "str", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["numpy.dtypes.LongDType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "str of LongDType", "ret_type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "<i4"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">i4"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "<i8"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">i8"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.dtypes.LongDType.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "numpy.dtypes.LongDType", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "LongDoubleDType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "f"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "g"}, {".class": "LiteralType", "fallback": "builtins.int", "value": 13}], "extra_attrs": null, "type_ref": "numpy.dtypes._TypeCodes"}, "numpy.dtypes._NativeOrder", {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.int", "value": 8}, {".class": "LiteralType", "fallback": "builtins.int", "value": 12}, {".class": "LiteralType", "fallback": "builtins.int", "value": 16}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.int", "value": 8}, {".class": "LiteralType", "fallback": "builtins.int", "value": 12}, {".class": "LiteralType", "fallback": "builtins.int", "value": 16}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.dtypes._NBit"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.longdouble"}], "extra_attrs": null, "type_ref": "numpy.dtypes._LiteralDType"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy.dtypes.LongDoubleDType", "name": "LongDoubleDType", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_final"], "fullname": "numpy.dtypes.LongDoubleDType", "has_param_spec_type": false, "metaclass_type": "numpy._DTypeMeta", "metadata": {}, "module_name": "numpy.dtypes", "mro": ["numpy.dtypes.LongDoubleDType", "numpy.dtypes._TypeCodes", "numpy.dtypes._NativeOrder", "numpy.dtypes._NBit", "numpy.dtypes._LiteralDType", "numpy.dtypes._SimpleDType", "numpy.dtype", "builtins.object"], "names": {".class": "SymbolTable", "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "numpy.dtypes.LongDoubleDType.name", "name": "name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["numpy.dtypes.LongDoubleDType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "name of LongDoubleDType", "ret_type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "float64"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "float96"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "float128"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "numpy.dtypes.LongDoubleDType.name", "name": "name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["numpy.dtypes.LongDoubleDType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "name of LongDoubleDType", "ret_type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "float64"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "float96"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "float128"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "str": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "numpy.dtypes.LongDoubleDType.str", "name": "str", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["numpy.dtypes.LongDoubleDType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "str of LongDoubleDType", "ret_type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "<f8"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">f8"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "<f12"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">f12"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "<f16"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">f16"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "numpy.dtypes.LongDoubleDType.str", "name": "str", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["numpy.dtypes.LongDoubleDType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "str of LongDoubleDType", "ret_type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "<f8"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">f8"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "<f12"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">f12"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "<f16"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">f16"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.dtypes.LongDoubleDType.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "numpy.dtypes.LongDoubleDType", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "LongLongDType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "i"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "q"}, {".class": "LiteralType", "fallback": "builtins.int", "value": 9}], "extra_attrs": null, "type_ref": "numpy.dtypes._TypeCodes"}, "numpy.dtypes._NativeOrder", {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.int", "value": 8}, {".class": "LiteralType", "fallback": "builtins.int", "value": 8}], "extra_attrs": null, "type_ref": "numpy.dtypes._NBit"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.longlong"}], "extra_attrs": null, "type_ref": "numpy.dtypes._LiteralDType"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy.dtypes.LongLongDType", "name": "LongLongDType", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_final"], "fullname": "numpy.dtypes.LongLongDType", "has_param_spec_type": false, "metaclass_type": "numpy._DTypeMeta", "metadata": {}, "module_name": "numpy.dtypes", "mro": ["numpy.dtypes.LongLongDType", "numpy.dtypes._TypeCodes", "numpy.dtypes._NativeOrder", "numpy.dtypes._NBit", "numpy.dtypes._LiteralDType", "numpy.dtypes._SimpleDType", "numpy.dtype", "builtins.object"], "names": {".class": "SymbolTable", "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "numpy.dtypes.LongLongDType.name", "name": "name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["numpy.dtypes.LongLongDType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "name of LongLongDType", "ret_type": {".class": "LiteralType", "fallback": "builtins.str", "value": "int64"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "numpy.dtypes.LongLongDType.name", "name": "name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["numpy.dtypes.LongLongDType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "name of LongLongDType", "ret_type": {".class": "LiteralType", "fallback": "builtins.str", "value": "int64"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "str": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "numpy.dtypes.LongLongDType.str", "name": "str", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["numpy.dtypes.LongLongDType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "str of LongLongDType", "ret_type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "<i8"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">i8"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "numpy.dtypes.LongLongDType.str", "name": "str", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["numpy.dtypes.LongLongDType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "str of LongLongDType", "ret_type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "<i8"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">i8"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.dtypes.LongLongDType.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "numpy.dtypes.LongLongDType", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "MemberDescriptorType": {".class": "SymbolTableNode", "cross_ref": "types.MemberDescriptorType", "kind": "Gdef", "module_hidden": true, "module_public": false}, "NoReturn": {".class": "SymbolTableNode", "cross_ref": "typing.NoReturn", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ObjectDType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "O"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "O"}, {".class": "LiteralType", "fallback": "builtins.int", "value": 17}], "extra_attrs": null, "type_ref": "numpy.dtypes._TypeCodes"}, "numpy.dtypes._NoOrder", {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.int", "value": 8}, {".class": "LiteralType", "fallback": "builtins.int", "value": 8}], "extra_attrs": null, "type_ref": "numpy.dtypes._NBit"}, {".class": "Instance", "args": ["numpy.object_"], "extra_attrs": null, "type_ref": "numpy.dtypes._SimpleDType"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy.dtypes.ObjectDType", "name": "ObjectDType", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_final"], "fullname": "numpy.dtypes.ObjectDType", "has_param_spec_type": false, "metaclass_type": "numpy._DTypeMeta", "metadata": {}, "module_name": "numpy.dtypes", "mro": ["numpy.dtypes.ObjectDType", "numpy.dtypes._TypeCodes", "numpy.dtypes._NoOrder", "numpy.dtypes._NBit", "numpy.dtypes._SimpleDType", "numpy.dtype", "builtins.object"], "names": {".class": "SymbolTable", "hasobject": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "numpy.dtypes.ObjectDType.hasobject", "name": "hasobject", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["numpy.dtypes.ObjectDType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "hasobject of ObjectDType", "ret_type": {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "numpy.dtypes.ObjectDType.hasobject", "name": "hasobject", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["numpy.dtypes.ObjectDType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "hasobject of ObjectDType", "ret_type": {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "numpy.dtypes.ObjectDType.name", "name": "name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["numpy.dtypes.ObjectDType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "name of ObjectDType", "ret_type": {".class": "LiteralType", "fallback": "builtins.str", "value": "object"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "numpy.dtypes.ObjectDType.name", "name": "name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["numpy.dtypes.ObjectDType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "name of ObjectDType", "ret_type": {".class": "LiteralType", "fallback": "builtins.str", "value": "object"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "str": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "numpy.dtypes.ObjectDType.str", "name": "str", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["numpy.dtypes.ObjectDType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "str of ObjectDType", "ret_type": {".class": "LiteralType", "fallback": "builtins.str", "value": "|O"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "numpy.dtypes.ObjectDType.str", "name": "str", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["numpy.dtypes.ObjectDType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "str of ObjectDType", "ret_type": {".class": "LiteralType", "fallback": "builtins.str", "value": "|O"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.dtypes.ObjectDType.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "numpy.dtypes.ObjectDType", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Self": {".class": "SymbolTableNode", "cross_ref": "typing.Self", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ShortDType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "numpy.dtypes.ShortDType", "line": 235, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "numpy.dtypes.Int16DType"}}, "StrDType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "U"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "U"}, {".class": "LiteralType", "fallback": "builtins.int", "value": 19}], "extra_attrs": null, "type_ref": "numpy.dtypes._TypeCodes"}, "numpy.dtypes._NativeOrder", {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.int", "value": 4}, {".class": "TypeVarType", "default": "builtins.int", "fullname": "numpy.dtypes._ItemSize_co", "id": 1, "name": "_ItemSize_co", "namespace": "numpy.dtypes.StrDType", "upper_bound": "builtins.int", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.dtypes._NBit"}, {".class": "Instance", "args": ["numpy.str_"], "extra_attrs": null, "type_ref": "numpy.dtypes._SimpleDType"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy.dtypes.StrDType", "name": "StrDType", "type_vars": [{".class": "TypeVarType", "default": "builtins.int", "fullname": "numpy.dtypes._ItemSize_co", "id": 1, "name": "_ItemSize_co", "namespace": "numpy.dtypes.StrDType", "upper_bound": "builtins.int", "values": [], "variance": 1}]}, "deletable_attributes": [], "deprecated": null, "flags": ["is_final"], "fullname": "numpy.dtypes.StrDType", "has_param_spec_type": false, "metaclass_type": "numpy._DTypeMeta", "metadata": {}, "module_name": "numpy.dtypes", "mro": ["numpy.dtypes.StrDType", "numpy.dtypes._TypeCodes", "numpy.dtypes._NativeOrder", "numpy.dtypes._NBit", "numpy.dtypes._SimpleDType", "numpy.dtype", "builtins.object"], "names": {".class": "SymbolTable", "__new__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static"], "fullname": "numpy.dtypes.StrDType.__new__", "name": "__new__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.int", "fullname": "numpy.dtypes._ItemSize_co", "id": 1, "name": "_ItemSize_co", "namespace": "numpy.dtypes.StrDType", "upper_bound": "builtins.int", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.dtypes.StrDType"}}, {".class": "TypeVarType", "default": "builtins.int", "fullname": "numpy.dtypes._ItemSize_co", "id": 1, "name": "_ItemSize_co", "namespace": "numpy.dtypes.StrDType", "upper_bound": "builtins.int", "values": [], "variance": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__new__ of StrDType", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.int", "fullname": "numpy.dtypes._ItemSize_co", "id": 1, "name": "_ItemSize_co", "namespace": "numpy.dtypes.StrDType", "upper_bound": "builtins.int", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.dtypes.StrDType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "hasobject": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "numpy.dtypes.StrDType.hasobject", "name": "hasobject", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.int", "fullname": "numpy.dtypes._ItemSize_co", "id": 1, "name": "_ItemSize_co", "namespace": "numpy.dtypes.StrDType", "upper_bound": "builtins.int", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.dtypes.StrDType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "hasobject of StrDType", "ret_type": {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "numpy.dtypes.StrDType.hasobject", "name": "hasobject", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.int", "fullname": "numpy.dtypes._ItemSize_co", "id": 1, "name": "_ItemSize_co", "namespace": "numpy.dtypes.StrDType", "upper_bound": "builtins.int", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.dtypes.StrDType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "hasobject of StrDType", "ret_type": {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "numpy.dtypes.StrDType.name", "name": "name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.int", "fullname": "numpy.dtypes._ItemSize_co", "id": 1, "name": "_ItemSize_co", "namespace": "numpy.dtypes.StrDType", "upper_bound": "builtins.int", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.dtypes.StrDType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "name of StrDType", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "numpy.dtypes.StrDType.name", "name": "name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.int", "fullname": "numpy.dtypes._ItemSize_co", "id": 1, "name": "_ItemSize_co", "namespace": "numpy.dtypes.StrDType", "upper_bound": "builtins.int", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.dtypes.StrDType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "name of StrDType", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "str": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "numpy.dtypes.StrDType.str", "name": "str", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.int", "fullname": "numpy.dtypes._ItemSize_co", "id": 1, "name": "_ItemSize_co", "namespace": "numpy.dtypes.StrDType", "upper_bound": "builtins.int", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.dtypes.StrDType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "str of StrDType", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "numpy.dtypes.StrDType.str", "name": "str", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.int", "fullname": "numpy.dtypes._ItemSize_co", "id": 1, "name": "_ItemSize_co", "namespace": "numpy.dtypes.StrDType", "upper_bound": "builtins.int", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.dtypes.StrDType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "str of StrDType", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.dtypes.StrDType.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.int", "fullname": "numpy.dtypes._ItemSize_co", "id": 1, "name": "_ItemSize_co", "namespace": "numpy.dtypes.StrDType", "upper_bound": "builtins.int", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.dtypes.StrDType"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["_ItemSize_co"], "typeddict_type": null}}, "StringDType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "T"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "T"}, {".class": "LiteralType", "fallback": "builtins.int", "value": 2056}], "extra_attrs": null, "type_ref": "numpy.dtypes._TypeCodes"}, "numpy.dtypes._NativeOrder", {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.int", "value": 8}, {".class": "LiteralType", "fallback": "builtins.int", "value": 16}], "extra_attrs": null, "type_ref": "numpy.dtypes._NBit"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "numpy.dtype"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy.dtypes.StringDType", "name": "StringDType", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_final"], "fullname": "numpy.dtypes.StringDType", "has_param_spec_type": false, "metaclass_type": "numpy._DTypeMeta", "metadata": {}, "module_name": "numpy.dtypes", "mro": ["numpy.dtypes.StringDType", "numpy.dtypes._TypeCodes", "numpy.dtypes._NativeOrder", "numpy.dtypes._NBit", "numpy.dtype", "builtins.object"], "names": {".class": "SymbolTable", "__getitem__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.dtypes.StringDType.__getitem__", "name": "__getitem__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["numpy.dtypes.StringDType", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__getitem__ of StringDType", "ret_type": {".class": "UninhabitedType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__new__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static"], "fullname": "numpy.dtypes.StringDType.__new__", "name": "__new__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeType", "item": "numpy.dtypes.StringDType"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__new__ of StringDType", "ret_type": "numpy.dtypes.StringDType", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "base": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "numpy.dtypes.StringDType.base", "name": "base", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["numpy.dtypes.StringDType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "base of StringDType", "ret_type": "numpy.dtypes.StringDType", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "numpy.dtypes.StringDType.base", "name": "base", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["numpy.dtypes.StringDType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "base of StringDType", "ret_type": "numpy.dtypes.StringDType", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "coerce": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "numpy.dtypes.StringDType.coerce", "name": "coerce", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["numpy.dtypes.StringDType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "coerce of StringDType", "ret_type": {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "numpy.dtypes.StringDType.coerce", "name": "coerce", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["numpy.dtypes.StringDType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "coerce of StringDType", "ret_type": {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "numpy.dtypes.StringDType.fields", "name": "fields", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["numpy.dtypes.StringDType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "fields of StringDType", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "numpy.dtypes.StringDType.fields", "name": "fields", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["numpy.dtypes.StringDType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "fields of StringDType", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "hasobject": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "numpy.dtypes.StringDType.hasobject", "name": "hasobject", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["numpy.dtypes.StringDType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "hasobject of StringDType", "ret_type": {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "numpy.dtypes.StringDType.hasobject", "name": "hasobject", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["numpy.dtypes.StringDType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "hasobject of StringDType", "ret_type": {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "isalignedstruct": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "numpy.dtypes.StringDType.isalignedstruct", "name": "isalignedstruct", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["numpy.dtypes.StringDType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "isalignedstruct of StringDType", "ret_type": {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "numpy.dtypes.StringDType.isalignedstruct", "name": "isalignedstruct", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["numpy.dtypes.StringDType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "isalignedstruct of StringDType", "ret_type": {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "isnative": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "numpy.dtypes.StringDType.isnative", "name": "isnative", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["numpy.dtypes.StringDType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "isnative of StringDType", "ret_type": {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "numpy.dtypes.StringDType.isnative", "name": "isnative", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["numpy.dtypes.StringDType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "isnative of StringDType", "ret_type": {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "na_object": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready"], "fullname": "numpy.dtypes.StringDType.na_object", "name": "na_object", "type": "types.MemberDescriptorType"}}, "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "numpy.dtypes.StringDType.name", "name": "name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["numpy.dtypes.StringDType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "name of StringDType", "ret_type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "StringDType64"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "StringDType128"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "numpy.dtypes.StringDType.name", "name": "name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["numpy.dtypes.StringDType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "name of StringDType", "ret_type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "StringDType64"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "StringDType128"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "ndim": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "numpy.dtypes.StringDType.ndim", "name": "ndim", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["numpy.dtypes.StringDType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ndim of StringDType", "ret_type": {".class": "LiteralType", "fallback": "builtins.int", "value": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "numpy.dtypes.StringDType.ndim", "name": "ndim", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["numpy.dtypes.StringDType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ndim of StringDType", "ret_type": {".class": "LiteralType", "fallback": "builtins.int", "value": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "shape": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "numpy.dtypes.StringDType.shape", "name": "shape", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["numpy.dtypes.StringDType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "shape of StringDType", "ret_type": {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "numpy.dtypes.StringDType.shape", "name": "shape", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["numpy.dtypes.StringDType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "shape of StringDType", "ret_type": {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "str": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "numpy.dtypes.StringDType.str", "name": "str", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["numpy.dtypes.StringDType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "str of StringDType", "ret_type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "|T8"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "|T16"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "numpy.dtypes.StringDType.str", "name": "str", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["numpy.dtypes.StringDType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "str of StringDType", "ret_type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "|T8"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "|T16"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "subdtype": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "numpy.dtypes.StringDType.subdtype", "name": "subdtype", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["numpy.dtypes.StringDType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "subdtype of StringDType", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "numpy.dtypes.StringDType.subdtype", "name": "subdtype", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["numpy.dtypes.StringDType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "subdtype of StringDType", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "numpy.dtypes.StringDType.type", "name": "type", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["numpy.dtypes.StringDType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "type of StringDType", "ret_type": {".class": "TypeType", "item": {".class": "UnboundType", "args": [], "expr": null, "expr_fallback": null, "name": "str"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "numpy.dtypes.StringDType.type", "name": "type", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["numpy.dtypes.StringDType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "type of StringDType", "ret_type": {".class": "TypeType", "item": {".class": "UnboundType", "args": [], "expr": null, "expr_fallback": null, "name": "str"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.dtypes.StringDType.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "numpy.dtypes.StringDType", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TimeDelta64DType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "m"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "m"}, {".class": "LiteralType", "fallback": "builtins.int", "value": 22}], "extra_attrs": null, "type_ref": "numpy.dtypes._TypeCodes"}, "numpy.dtypes._NativeOrder", {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.int", "value": 8}, {".class": "LiteralType", "fallback": "builtins.int", "value": 8}], "extra_attrs": null, "type_ref": "numpy.dtypes._NBit"}, {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["datetime.<PERSON><PERSON><PERSON>", "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.timedelta64"}], "extra_attrs": null, "type_ref": "numpy.dtypes._LiteralDType"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy.dtypes.TimeDelta64DType", "name": "TimeDelta64DType", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_final"], "fullname": "numpy.dtypes.TimeDelta64DType", "has_param_spec_type": false, "metaclass_type": "numpy._DTypeMeta", "metadata": {}, "module_name": "numpy.dtypes", "mro": ["numpy.dtypes.TimeDelta64DType", "numpy.dtypes._TypeCodes", "numpy.dtypes._NativeOrder", "numpy.dtypes._NBit", "numpy.dtypes._LiteralDType", "numpy.dtypes._SimpleDType", "numpy.dtype", "builtins.object"], "names": {".class": "SymbolTable", "__new__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static"], "fullname": "numpy.dtypes.TimeDelta64DType.__new__", "name": "__new__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "TypeType", "item": "numpy.dtypes.TimeDelta64DType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.dtypes._DateTimeUnit"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__new__ of TimeDelta64DType", "ret_type": {".class": "UninhabitedType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "numpy.dtypes.TimeDelta64DType.name", "name": "name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["numpy.dtypes.TimeDelta64DType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "name of TimeDelta64DType", "ret_type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "timedelta64"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "timedelta64[Y]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "timedelta64[M]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "timedelta64[W]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "timedelta64[D]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "timedelta64[h]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "timedelta64[m]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "timedelta64[s]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "timedelta64[ms]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "timedelta64[us]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "timedelta64[ns]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "timedelta64[ps]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "timedelta64[fs]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "timedelta64[as]"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "numpy.dtypes.TimeDelta64DType.name", "name": "name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["numpy.dtypes.TimeDelta64DType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "name of TimeDelta64DType", "ret_type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "timedelta64"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "timedelta64[Y]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "timedelta64[M]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "timedelta64[W]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "timedelta64[D]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "timedelta64[h]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "timedelta64[m]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "timedelta64[s]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "timedelta64[ms]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "timedelta64[us]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "timedelta64[ns]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "timedelta64[ps]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "timedelta64[fs]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "timedelta64[as]"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "str": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "numpy.dtypes.TimeDelta64DType.str", "name": "str", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["numpy.dtypes.TimeDelta64DType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "str of TimeDelta64DType", "ret_type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "<m8"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">m8"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "<m8[Y]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">m8[Y]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "<m8[M]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">m8[M]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "<m8[W]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">m8[W]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "<m8[D]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">m8[D]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "<m8[h]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">m8[h]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "<m8[m]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">m8[m]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "<m8[s]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">m8[s]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "<m8[ms]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">m8[ms]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "<m8[us]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">m8[us]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "<m8[ns]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">m8[ns]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "<m8[ps]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">m8[ps]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "<m8[fs]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">m8[fs]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "<m8[as]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">m8[as]"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "numpy.dtypes.TimeDelta64DType.str", "name": "str", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["numpy.dtypes.TimeDelta64DType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "str of TimeDelta64DType", "ret_type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "<m8"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">m8"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "<m8[Y]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">m8[Y]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "<m8[M]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">m8[M]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "<m8[W]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">m8[W]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "<m8[D]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">m8[D]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "<m8[h]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">m8[h]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "<m8[m]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">m8[m]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "<m8[s]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">m8[s]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "<m8[ms]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">m8[ms]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "<m8[us]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">m8[us]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "<m8[ns]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">m8[ns]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "<m8[ps]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">m8[ps]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "<m8[fs]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">m8[fs]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "<m8[as]"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">m8[as]"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.dtypes.TimeDelta64DType.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "numpy.dtypes.TimeDelta64DType", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TypeAlias": {".class": "SymbolTableNode", "cross_ref": "typing.TypeAlias", "kind": "Gdef", "module_hidden": true, "module_public": false}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.TypeVar", "kind": "Gdef", "module_hidden": true, "module_public": false}, "UByteDType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "numpy.dtypes.UByteDType", "line": 234, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "numpy.dtypes.UInt8DType"}}, "UInt16DType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "u"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "H"}, {".class": "LiteralType", "fallback": "builtins.int", "value": 4}], "extra_attrs": null, "type_ref": "numpy.dtypes._TypeCodes"}, "numpy.dtypes._NativeOrder", {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.int", "value": 2}, {".class": "LiteralType", "fallback": "builtins.int", "value": 2}], "extra_attrs": null, "type_ref": "numpy.dtypes._NBit"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint16"}], "extra_attrs": null, "type_ref": "numpy.dtypes._LiteralDType"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy.dtypes.UInt16DType", "name": "UInt16DType", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_final"], "fullname": "numpy.dtypes.UInt16DType", "has_param_spec_type": false, "metaclass_type": "numpy._DTypeMeta", "metadata": {}, "module_name": "numpy.dtypes", "mro": ["numpy.dtypes.UInt16DType", "numpy.dtypes._TypeCodes", "numpy.dtypes._NativeOrder", "numpy.dtypes._NBit", "numpy.dtypes._LiteralDType", "numpy.dtypes._SimpleDType", "numpy.dtype", "builtins.object"], "names": {".class": "SymbolTable", "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "numpy.dtypes.UInt16DType.name", "name": "name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["numpy.dtypes.UInt16DType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "name of UInt16DType", "ret_type": {".class": "LiteralType", "fallback": "builtins.str", "value": "uint16"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "numpy.dtypes.UInt16DType.name", "name": "name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["numpy.dtypes.UInt16DType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "name of UInt16DType", "ret_type": {".class": "LiteralType", "fallback": "builtins.str", "value": "uint16"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "str": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "numpy.dtypes.UInt16DType.str", "name": "str", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["numpy.dtypes.UInt16DType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "str of UInt16DType", "ret_type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "<u2"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">u2"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "numpy.dtypes.UInt16DType.str", "name": "str", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["numpy.dtypes.UInt16DType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "str of UInt16DType", "ret_type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "<u2"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">u2"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.dtypes.UInt16DType.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "numpy.dtypes.UInt16DType", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "UInt32DType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "u"}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "I"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "L"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.int", "value": 6}, {".class": "LiteralType", "fallback": "builtins.int", "value": 8}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.dtypes._TypeCodes"}, "numpy.dtypes._NativeOrder", {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.int", "value": 4}, {".class": "LiteralType", "fallback": "builtins.int", "value": 4}], "extra_attrs": null, "type_ref": "numpy.dtypes._NBit"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint32"}], "extra_attrs": null, "type_ref": "numpy.dtypes._LiteralDType"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy.dtypes.UInt32DType", "name": "UInt32DType", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_final"], "fullname": "numpy.dtypes.UInt32DType", "has_param_spec_type": false, "metaclass_type": "numpy._DTypeMeta", "metadata": {}, "module_name": "numpy.dtypes", "mro": ["numpy.dtypes.UInt32DType", "numpy.dtypes._TypeCodes", "numpy.dtypes._NativeOrder", "numpy.dtypes._NBit", "numpy.dtypes._LiteralDType", "numpy.dtypes._SimpleDType", "numpy.dtype", "builtins.object"], "names": {".class": "SymbolTable", "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "numpy.dtypes.UInt32DType.name", "name": "name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["numpy.dtypes.UInt32DType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "name of UInt32DType", "ret_type": {".class": "LiteralType", "fallback": "builtins.str", "value": "uint32"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "numpy.dtypes.UInt32DType.name", "name": "name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["numpy.dtypes.UInt32DType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "name of UInt32DType", "ret_type": {".class": "LiteralType", "fallback": "builtins.str", "value": "uint32"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "str": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "numpy.dtypes.UInt32DType.str", "name": "str", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["numpy.dtypes.UInt32DType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "str of UInt32DType", "ret_type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "<u4"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">u4"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "numpy.dtypes.UInt32DType.str", "name": "str", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["numpy.dtypes.UInt32DType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "str of UInt32DType", "ret_type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "<u4"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">u4"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.dtypes.UInt32DType.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "numpy.dtypes.UInt32DType", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "UInt64DType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "u"}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "L"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "Q"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.int", "value": 8}, {".class": "LiteralType", "fallback": "builtins.int", "value": 10}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.dtypes._TypeCodes"}, "numpy.dtypes._NativeOrder", {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.int", "value": 8}, {".class": "LiteralType", "fallback": "builtins.int", "value": 8}], "extra_attrs": null, "type_ref": "numpy.dtypes._NBit"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint64"}], "extra_attrs": null, "type_ref": "numpy.dtypes._LiteralDType"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy.dtypes.UInt64DType", "name": "UInt64DType", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_final"], "fullname": "numpy.dtypes.UInt64DType", "has_param_spec_type": false, "metaclass_type": "numpy._DTypeMeta", "metadata": {}, "module_name": "numpy.dtypes", "mro": ["numpy.dtypes.UInt64DType", "numpy.dtypes._TypeCodes", "numpy.dtypes._NativeOrder", "numpy.dtypes._NBit", "numpy.dtypes._LiteralDType", "numpy.dtypes._SimpleDType", "numpy.dtype", "builtins.object"], "names": {".class": "SymbolTable", "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "numpy.dtypes.UInt64DType.name", "name": "name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["numpy.dtypes.UInt64DType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "name of UInt64DType", "ret_type": {".class": "LiteralType", "fallback": "builtins.str", "value": "uint64"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "numpy.dtypes.UInt64DType.name", "name": "name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["numpy.dtypes.UInt64DType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "name of UInt64DType", "ret_type": {".class": "LiteralType", "fallback": "builtins.str", "value": "uint64"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "str": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "numpy.dtypes.UInt64DType.str", "name": "str", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["numpy.dtypes.UInt64DType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "str of UInt64DType", "ret_type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "<u8"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">u8"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "numpy.dtypes.UInt64DType.str", "name": "str", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["numpy.dtypes.UInt64DType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "str of UInt64DType", "ret_type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "<u8"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">u8"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.dtypes.UInt64DType.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "numpy.dtypes.UInt64DType", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "UInt8DType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "u"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "B"}, {".class": "LiteralType", "fallback": "builtins.int", "value": 2}], "extra_attrs": null, "type_ref": "numpy.dtypes._TypeCodes"}, "numpy.dtypes._8Bit", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint8"}], "extra_attrs": null, "type_ref": "numpy.dtypes._LiteralDType"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy.dtypes.UInt8DType", "name": "UInt8DType", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_final"], "fullname": "numpy.dtypes.UInt8DType", "has_param_spec_type": false, "metaclass_type": "numpy._DTypeMeta", "metadata": {}, "module_name": "numpy.dtypes", "mro": ["numpy.dtypes.UInt8DType", "numpy.dtypes._TypeCodes", "numpy.dtypes._8Bit", "numpy.dtypes._NoOrder", "numpy.dtypes._NBit", "numpy.dtypes._LiteralDType", "numpy.dtypes._SimpleDType", "numpy.dtype", "builtins.object"], "names": {".class": "SymbolTable", "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "numpy.dtypes.UInt8DType.name", "name": "name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["numpy.dtypes.UInt8DType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "name of UInt8DType", "ret_type": {".class": "LiteralType", "fallback": "builtins.str", "value": "uint8"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "numpy.dtypes.UInt8DType.name", "name": "name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["numpy.dtypes.UInt8DType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "name of UInt8DType", "ret_type": {".class": "LiteralType", "fallback": "builtins.str", "value": "uint8"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "str": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "numpy.dtypes.UInt8DType.str", "name": "str", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["numpy.dtypes.UInt8DType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "str of UInt8DType", "ret_type": {".class": "LiteralType", "fallback": "builtins.str", "value": "|u1"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "numpy.dtypes.UInt8DType.str", "name": "str", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["numpy.dtypes.UInt8DType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "str of UInt8DType", "ret_type": {".class": "LiteralType", "fallback": "builtins.str", "value": "|u1"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.dtypes.UInt8DType.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "numpy.dtypes.UInt8DType", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "UIntDType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "u"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "I"}, {".class": "LiteralType", "fallback": "builtins.int", "value": 6}], "extra_attrs": null, "type_ref": "numpy.dtypes._TypeCodes"}, "numpy.dtypes._NativeOrder", {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.int", "value": 4}, {".class": "LiteralType", "fallback": "builtins.int", "value": 4}], "extra_attrs": null, "type_ref": "numpy.dtypes._NBit"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.uintc"}], "extra_attrs": null, "type_ref": "numpy.dtypes._LiteralDType"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy.dtypes.UIntDType", "name": "UIntDType", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_final"], "fullname": "numpy.dtypes.UIntDType", "has_param_spec_type": false, "metaclass_type": "numpy._DTypeMeta", "metadata": {}, "module_name": "numpy.dtypes", "mro": ["numpy.dtypes.UIntDType", "numpy.dtypes._TypeCodes", "numpy.dtypes._NativeOrder", "numpy.dtypes._NBit", "numpy.dtypes._LiteralDType", "numpy.dtypes._SimpleDType", "numpy.dtype", "builtins.object"], "names": {".class": "SymbolTable", "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "numpy.dtypes.UIntDType.name", "name": "name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["numpy.dtypes.UIntDType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "name of UIntDType", "ret_type": {".class": "LiteralType", "fallback": "builtins.str", "value": "uint32"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "numpy.dtypes.UIntDType.name", "name": "name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["numpy.dtypes.UIntDType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "name of UIntDType", "ret_type": {".class": "LiteralType", "fallback": "builtins.str", "value": "uint32"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "str": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "numpy.dtypes.UIntDType.str", "name": "str", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["numpy.dtypes.UIntDType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "str of UIntDType", "ret_type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "<u4"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">u4"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "numpy.dtypes.UIntDType.str", "name": "str", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["numpy.dtypes.UIntDType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "str of UIntDType", "ret_type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "<u4"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">u4"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.dtypes.UIntDType.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "numpy.dtypes.UIntDType", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ULongDType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "u"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "L"}, {".class": "LiteralType", "fallback": "builtins.int", "value": 8}], "extra_attrs": null, "type_ref": "numpy.dtypes._TypeCodes"}, "numpy.dtypes._NativeOrder", {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.int", "value": 4}, {".class": "LiteralType", "fallback": "builtins.int", "value": 8}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.int", "value": 4}, {".class": "LiteralType", "fallback": "builtins.int", "value": 8}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.dtypes._NBit"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.ulong"}], "extra_attrs": null, "type_ref": "numpy.dtypes._LiteralDType"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy.dtypes.ULongDType", "name": "ULongDType", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_final"], "fullname": "numpy.dtypes.ULongDType", "has_param_spec_type": false, "metaclass_type": "numpy._DTypeMeta", "metadata": {}, "module_name": "numpy.dtypes", "mro": ["numpy.dtypes.ULongDType", "numpy.dtypes._TypeCodes", "numpy.dtypes._NativeOrder", "numpy.dtypes._NBit", "numpy.dtypes._LiteralDType", "numpy.dtypes._SimpleDType", "numpy.dtype", "builtins.object"], "names": {".class": "SymbolTable", "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "numpy.dtypes.ULongDType.name", "name": "name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["numpy.dtypes.ULongDType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "name of ULongDType", "ret_type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "uint32"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "uint64"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "numpy.dtypes.ULongDType.name", "name": "name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["numpy.dtypes.ULongDType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "name of ULongDType", "ret_type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "uint32"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "uint64"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "str": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "numpy.dtypes.ULongDType.str", "name": "str", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["numpy.dtypes.ULongDType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "str of ULongDType", "ret_type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "<u4"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">u4"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "<u8"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">u8"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "numpy.dtypes.ULongDType.str", "name": "str", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["numpy.dtypes.ULongDType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "str of ULongDType", "ret_type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "<u4"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">u4"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "<u8"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">u8"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.dtypes.ULongDType.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "numpy.dtypes.ULongDType", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ULongLongDType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "u"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "Q"}, {".class": "LiteralType", "fallback": "builtins.int", "value": 10}], "extra_attrs": null, "type_ref": "numpy.dtypes._TypeCodes"}, "numpy.dtypes._NativeOrder", {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.int", "value": 8}, {".class": "LiteralType", "fallback": "builtins.int", "value": 8}], "extra_attrs": null, "type_ref": "numpy.dtypes._NBit"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.ul<PERSON>"}], "extra_attrs": null, "type_ref": "numpy.dtypes._LiteralDType"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy.dtypes.ULongLongDType", "name": "ULongLongDType", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_final"], "fullname": "numpy.dtypes.ULongLongDType", "has_param_spec_type": false, "metaclass_type": "numpy._DTypeMeta", "metadata": {}, "module_name": "numpy.dtypes", "mro": ["numpy.dtypes.ULongLongDType", "numpy.dtypes._TypeCodes", "numpy.dtypes._NativeOrder", "numpy.dtypes._NBit", "numpy.dtypes._LiteralDType", "numpy.dtypes._SimpleDType", "numpy.dtype", "builtins.object"], "names": {".class": "SymbolTable", "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "numpy.dtypes.ULongLongDType.name", "name": "name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["numpy.dtypes.ULongLongDType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "name of ULongLongDType", "ret_type": {".class": "LiteralType", "fallback": "builtins.str", "value": "uint64"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "numpy.dtypes.ULongLongDType.name", "name": "name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["numpy.dtypes.ULongLongDType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "name of ULongLongDType", "ret_type": {".class": "LiteralType", "fallback": "builtins.str", "value": "uint64"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "str": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "numpy.dtypes.ULongLongDType.str", "name": "str", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["numpy.dtypes.ULongLongDType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "str of ULongLongDType", "ret_type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "<u8"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">u8"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "numpy.dtypes.ULongLongDType.str", "name": "str", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["numpy.dtypes.ULongLongDType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "str of ULongLongDType", "ret_type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "<u8"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ">u8"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.dtypes.ULongLongDType.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "numpy.dtypes.ULongLongDType", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "UShortDType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "numpy.dtypes.UShortDType", "line": 236, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "numpy.dtypes.UInt16DType"}}, "VoidDType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "V"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "V"}, {".class": "LiteralType", "fallback": "builtins.int", "value": 20}], "extra_attrs": null, "type_ref": "numpy.dtypes._TypeCodes"}, "numpy.dtypes._NoOrder", {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.int", "value": 1}, {".class": "TypeVarType", "default": "builtins.int", "fullname": "numpy.dtypes._ItemSize_co", "id": 1, "name": "_ItemSize_co", "namespace": "numpy.dtypes.VoidDType", "upper_bound": "builtins.int", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.dtypes._NBit"}, {".class": "Instance", "args": ["numpy.void"], "extra_attrs": null, "type_ref": "numpy.dtype"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy.dtypes.VoidDType", "name": "VoidDType", "type_vars": [{".class": "TypeVarType", "default": "builtins.int", "fullname": "numpy.dtypes._ItemSize_co", "id": 1, "name": "_ItemSize_co", "namespace": "numpy.dtypes.VoidDType", "upper_bound": "builtins.int", "values": [], "variance": 1}]}, "deletable_attributes": [], "deprecated": null, "flags": ["is_final"], "fullname": "numpy.dtypes.VoidDType", "has_param_spec_type": false, "metaclass_type": "numpy._DTypeMeta", "metadata": {}, "module_name": "numpy.dtypes", "mro": ["numpy.dtypes.VoidDType", "numpy.dtypes._TypeCodes", "numpy.dtypes._NoOrder", "numpy.dtypes._NBit", "numpy.dtype", "builtins.object"], "names": {".class": "SymbolTable", "__new__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static"], "fullname": "numpy.dtypes.VoidDType.__new__", "name": "__new__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.int", "fullname": "numpy.dtypes._ItemSize_co", "id": 1, "name": "_ItemSize_co", "namespace": "numpy.dtypes.VoidDType", "upper_bound": "builtins.int", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.dtypes.VoidDType"}}, {".class": "TypeVarType", "default": "builtins.int", "fullname": "numpy.dtypes._ItemSize_co", "id": 1, "name": "_ItemSize_co", "namespace": "numpy.dtypes.VoidDType", "upper_bound": "builtins.int", "values": [], "variance": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__new__ of VoidDType", "ret_type": {".class": "UninhabitedType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "base": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "numpy.dtypes.VoidDType.base", "name": "base", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.dtypes.VoidDType.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.int", "fullname": "numpy.dtypes._ItemSize_co", "id": 1, "name": "_ItemSize_co", "namespace": "numpy.dtypes.VoidDType", "upper_bound": "builtins.int", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.dtypes.VoidDType"}, "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "base of VoidDType", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.int", "fullname": "numpy.dtypes._ItemSize_co", "id": 1, "name": "_ItemSize_co", "namespace": "numpy.dtypes.VoidDType", "upper_bound": "builtins.int", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.dtypes.VoidDType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.dtypes.VoidDType.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.int", "fullname": "numpy.dtypes._ItemSize_co", "id": 1, "name": "_ItemSize_co", "namespace": "numpy.dtypes.VoidDType", "upper_bound": "builtins.int", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.dtypes.VoidDType"}, "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "numpy.dtypes.VoidDType.base", "name": "base", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.dtypes.VoidDType.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.int", "fullname": "numpy.dtypes._ItemSize_co", "id": 1, "name": "_ItemSize_co", "namespace": "numpy.dtypes.VoidDType", "upper_bound": "builtins.int", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.dtypes.VoidDType"}, "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "base of VoidDType", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.int", "fullname": "numpy.dtypes._ItemSize_co", "id": 1, "name": "_ItemSize_co", "namespace": "numpy.dtypes.VoidDType", "upper_bound": "builtins.int", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.dtypes.VoidDType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.dtypes.VoidDType.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.int", "fullname": "numpy.dtypes._ItemSize_co", "id": 1, "name": "_ItemSize_co", "namespace": "numpy.dtypes.VoidDType", "upper_bound": "builtins.int", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.dtypes.VoidDType"}, "values": [], "variance": 0}]}}}}, "isalignedstruct": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "numpy.dtypes.VoidDType.isalignedstruct", "name": "isalignedstruct", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.int", "fullname": "numpy.dtypes._ItemSize_co", "id": 1, "name": "_ItemSize_co", "namespace": "numpy.dtypes.VoidDType", "upper_bound": "builtins.int", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.dtypes.VoidDType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "isalignedstruct of VoidDType", "ret_type": {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "numpy.dtypes.VoidDType.isalignedstruct", "name": "isalignedstruct", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.int", "fullname": "numpy.dtypes._ItemSize_co", "id": 1, "name": "_ItemSize_co", "namespace": "numpy.dtypes.VoidDType", "upper_bound": "builtins.int", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.dtypes.VoidDType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "isalignedstruct of VoidDType", "ret_type": {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "isnative": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "numpy.dtypes.VoidDType.isnative", "name": "isnative", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.int", "fullname": "numpy.dtypes._ItemSize_co", "id": 1, "name": "_ItemSize_co", "namespace": "numpy.dtypes.VoidDType", "upper_bound": "builtins.int", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.dtypes.VoidDType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "isnative of VoidDType", "ret_type": {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "numpy.dtypes.VoidDType.isnative", "name": "isnative", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.int", "fullname": "numpy.dtypes._ItemSize_co", "id": 1, "name": "_ItemSize_co", "namespace": "numpy.dtypes.VoidDType", "upper_bound": "builtins.int", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.dtypes.VoidDType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "isnative of VoidDType", "ret_type": {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "numpy.dtypes.VoidDType.name", "name": "name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.int", "fullname": "numpy.dtypes._ItemSize_co", "id": 1, "name": "_ItemSize_co", "namespace": "numpy.dtypes.VoidDType", "upper_bound": "builtins.int", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.dtypes.VoidDType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "name of VoidDType", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "numpy.dtypes.VoidDType.name", "name": "name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.int", "fullname": "numpy.dtypes._ItemSize_co", "id": 1, "name": "_ItemSize_co", "namespace": "numpy.dtypes.VoidDType", "upper_bound": "builtins.int", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.dtypes.VoidDType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "name of VoidDType", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "ndim": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "numpy.dtypes.VoidDType.ndim", "name": "ndim", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.int", "fullname": "numpy.dtypes._ItemSize_co", "id": 1, "name": "_ItemSize_co", "namespace": "numpy.dtypes.VoidDType", "upper_bound": "builtins.int", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.dtypes.VoidDType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ndim of VoidDType", "ret_type": {".class": "LiteralType", "fallback": "builtins.int", "value": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "numpy.dtypes.VoidDType.ndim", "name": "ndim", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.int", "fullname": "numpy.dtypes._ItemSize_co", "id": 1, "name": "_ItemSize_co", "namespace": "numpy.dtypes.VoidDType", "upper_bound": "builtins.int", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.dtypes.VoidDType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ndim of VoidDType", "ret_type": {".class": "LiteralType", "fallback": "builtins.int", "value": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "shape": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "numpy.dtypes.VoidDType.shape", "name": "shape", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.int", "fullname": "numpy.dtypes._ItemSize_co", "id": 1, "name": "_ItemSize_co", "namespace": "numpy.dtypes.VoidDType", "upper_bound": "builtins.int", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.dtypes.VoidDType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "shape of VoidDType", "ret_type": {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "numpy.dtypes.VoidDType.shape", "name": "shape", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.int", "fullname": "numpy.dtypes._ItemSize_co", "id": 1, "name": "_ItemSize_co", "namespace": "numpy.dtypes.VoidDType", "upper_bound": "builtins.int", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.dtypes.VoidDType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "shape of VoidDType", "ret_type": {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "str": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "numpy.dtypes.VoidDType.str", "name": "str", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.int", "fullname": "numpy.dtypes._ItemSize_co", "id": 1, "name": "_ItemSize_co", "namespace": "numpy.dtypes.VoidDType", "upper_bound": "builtins.int", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.dtypes.VoidDType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "str of VoidDType", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "numpy.dtypes.VoidDType.str", "name": "str", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.int", "fullname": "numpy.dtypes._ItemSize_co", "id": 1, "name": "_ItemSize_co", "namespace": "numpy.dtypes.VoidDType", "upper_bound": "builtins.int", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.dtypes.VoidDType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "str of VoidDType", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "subdtype": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "numpy.dtypes.VoidDType.subdtype", "name": "subdtype", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.int", "fullname": "numpy.dtypes._ItemSize_co", "id": 1, "name": "_ItemSize_co", "namespace": "numpy.dtypes.VoidDType", "upper_bound": "builtins.int", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.dtypes.VoidDType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "subdtype of VoidDType", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "numpy.dtypes.VoidDType.subdtype", "name": "subdtype", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.int", "fullname": "numpy.dtypes._ItemSize_co", "id": 1, "name": "_ItemSize_co", "namespace": "numpy.dtypes.VoidDType", "upper_bound": "builtins.int", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.dtypes.VoidDType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "subdtype of VoidDType", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.dtypes.VoidDType.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.int", "fullname": "numpy.dtypes._ItemSize_co", "id": 1, "name": "_ItemSize_co", "namespace": "numpy.dtypes.VoidDType", "upper_bound": "builtins.int", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.dtypes.VoidDType"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["_ItemSize_co"], "typeddict_type": null}}, "_8Bit": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["numpy.dtypes._NoOrder", {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.int", "value": 1}, {".class": "LiteralType", "fallback": "builtins.int", "value": 1}], "extra_attrs": null, "type_ref": "numpy.dtypes._NBit"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy.dtypes._8Bit", "name": "_8Bit", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "numpy.dtypes._8Bit", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "numpy.dtypes", "mro": ["numpy.dtypes._8Bit", "numpy.dtypes._NoOrder", "numpy.dtypes._NBit", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.dtypes._8Bit.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "numpy.dtypes._8Bit", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_CharT_co": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.dtypes._CharT_co", "name": "_CharT_co", "upper_bound": "builtins.str", "values": [], "variance": 1}}, "_DataSize_co": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.dtypes._DataSize_co", "name": "_DataSize_co", "upper_bound": "builtins.int", "values": [], "variance": 1}}, "_DateTimeUnit": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "numpy.dtypes._DateTimeUnit", "line": 479, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.dtypes._DateUnit"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.dtypes._TimeUnit"}], "uses_pep604_syntax": true}}}, "_DateUnit": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "numpy.dtypes._DateUnit", "line": 477, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "Y"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "M"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "W"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "D"}], "uses_pep604_syntax": false}}}, "_ItemSize_co": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeVarExpr", "default": "builtins.int", "fullname": "numpy.dtypes._ItemSize_co", "name": "_ItemSize_co", "upper_bound": "builtins.int", "values": [], "variance": 1}}, "_KindT_co": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.dtypes._KindT_co", "name": "_KindT_co", "upper_bound": "builtins.str", "values": [], "variance": 1}}, "_LiteralDType": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.dtypes._SCT_co", "id": 1, "name": "_SCT_co", "namespace": "numpy.dtypes._LiteralDType", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.dtypes._SimpleDType"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy.dtypes._LiteralDType", "name": "_LiteralDType", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.dtypes._SCT_co", "id": 1, "name": "_SCT_co", "namespace": "numpy.dtypes._LiteralDType", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 1}]}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "numpy.dtypes._LiteralDType", "has_param_spec_type": false, "metaclass_type": "numpy._DTypeMeta", "metadata": {}, "module_name": "numpy.dtypes", "mro": ["numpy.dtypes._LiteralDType", "numpy.dtypes._SimpleDType", "numpy.dtype", "builtins.object"], "names": {".class": "SymbolTable", "flags": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "numpy.dtypes._LiteralDType.flags", "name": "flags", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.dtypes._SCT_co", "id": 1, "name": "_SCT_co", "namespace": "numpy.dtypes._LiteralDType", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.dtypes._LiteralDType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "flags of _LiteralDType", "ret_type": {".class": "LiteralType", "fallback": "builtins.int", "value": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "numpy.dtypes._LiteralDType.flags", "name": "flags", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.dtypes._SCT_co", "id": 1, "name": "_SCT_co", "namespace": "numpy.dtypes._LiteralDType", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.dtypes._LiteralDType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "flags of _LiteralDType", "ret_type": {".class": "LiteralType", "fallback": "builtins.int", "value": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "hasobject": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "numpy.dtypes._LiteralDType.hasobject", "name": "hasobject", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.dtypes._SCT_co", "id": 1, "name": "_SCT_co", "namespace": "numpy.dtypes._LiteralDType", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.dtypes._LiteralDType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "hasobject of _LiteralDType", "ret_type": {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "numpy.dtypes._LiteralDType.hasobject", "name": "hasobject", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.dtypes._SCT_co", "id": 1, "name": "_SCT_co", "namespace": "numpy.dtypes._LiteralDType", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.dtypes._LiteralDType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "hasobject of _LiteralDType", "ret_type": {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.dtypes._LiteralDType.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.dtypes._SCT_co", "id": 1, "name": "_SCT_co", "namespace": "numpy.dtypes._LiteralDType", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.dtypes._LiteralDType"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["_SCT_co"], "typeddict_type": null}}, "_NBit": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy.dtypes._NBit", "name": "_NBit", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.dtypes._DataSize_co", "id": 1, "name": "_DataSize_co", "namespace": "numpy.dtypes._NBit", "upper_bound": "builtins.int", "values": [], "variance": 1}, {".class": "TypeVarType", "default": "builtins.int", "fullname": "numpy.dtypes._ItemSize_co", "id": 2, "name": "_ItemSize_co", "namespace": "numpy.dtypes._NBit", "upper_bound": "builtins.int", "values": [], "variance": 1}]}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "numpy.dtypes._NBit", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "numpy.dtypes", "mro": ["numpy.dtypes._NBit", "builtins.object"], "names": {".class": "SymbolTable", "alignment": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_final", "is_decorated"], "fullname": "numpy.dtypes._NBit.alignment", "name": "alignment", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.dtypes._DataSize_co", "id": 1, "name": "_DataSize_co", "namespace": "numpy.dtypes._NBit", "upper_bound": "builtins.int", "values": [], "variance": 1}, {".class": "TypeVarType", "default": "builtins.int", "fullname": "numpy.dtypes._ItemSize_co", "id": 2, "name": "_ItemSize_co", "namespace": "numpy.dtypes._NBit", "upper_bound": "builtins.int", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.dtypes._NBit"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "alignment of _NBit", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.dtypes._DataSize_co", "id": 1, "name": "_DataSize_co", "namespace": "numpy.dtypes._NBit", "upper_bound": "builtins.int", "values": [], "variance": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_final", "is_ready", "is_inferred"], "fullname": "numpy.dtypes._NBit.alignment", "name": "alignment", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.dtypes._DataSize_co", "id": 1, "name": "_DataSize_co", "namespace": "numpy.dtypes._NBit", "upper_bound": "builtins.int", "values": [], "variance": 1}, {".class": "TypeVarType", "default": "builtins.int", "fullname": "numpy.dtypes._ItemSize_co", "id": 2, "name": "_ItemSize_co", "namespace": "numpy.dtypes._NBit", "upper_bound": "builtins.int", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.dtypes._NBit"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "alignment of _NBit", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.dtypes._DataSize_co", "id": 1, "name": "_DataSize_co", "namespace": "numpy.dtypes._NBit", "upper_bound": "builtins.int", "values": [], "variance": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "itemsize": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_final", "is_decorated"], "fullname": "numpy.dtypes._NBit.itemsize", "name": "itemsize", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.dtypes._DataSize_co", "id": 1, "name": "_DataSize_co", "namespace": "numpy.dtypes._NBit", "upper_bound": "builtins.int", "values": [], "variance": 1}, {".class": "TypeVarType", "default": "builtins.int", "fullname": "numpy.dtypes._ItemSize_co", "id": 2, "name": "_ItemSize_co", "namespace": "numpy.dtypes._NBit", "upper_bound": "builtins.int", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.dtypes._NBit"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "itemsize of _NBit", "ret_type": {".class": "TypeVarType", "default": "builtins.int", "fullname": "numpy.dtypes._ItemSize_co", "id": 2, "name": "_ItemSize_co", "namespace": "numpy.dtypes._NBit", "upper_bound": "builtins.int", "values": [], "variance": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_final", "is_ready", "is_inferred"], "fullname": "numpy.dtypes._NBit.itemsize", "name": "itemsize", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.dtypes._DataSize_co", "id": 1, "name": "_DataSize_co", "namespace": "numpy.dtypes._NBit", "upper_bound": "builtins.int", "values": [], "variance": 1}, {".class": "TypeVarType", "default": "builtins.int", "fullname": "numpy.dtypes._ItemSize_co", "id": 2, "name": "_ItemSize_co", "namespace": "numpy.dtypes._NBit", "upper_bound": "builtins.int", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.dtypes._NBit"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "itemsize of _NBit", "ret_type": {".class": "TypeVarType", "default": "builtins.int", "fullname": "numpy.dtypes._ItemSize_co", "id": 2, "name": "_ItemSize_co", "namespace": "numpy.dtypes._NBit", "upper_bound": "builtins.int", "values": [], "variance": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.dtypes._NBit.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.dtypes._DataSize_co", "id": 1, "name": "_DataSize_co", "namespace": "numpy.dtypes._NBit", "upper_bound": "builtins.int", "values": [], "variance": 1}, {".class": "TypeVarType", "default": "builtins.int", "fullname": "numpy.dtypes._ItemSize_co", "id": 2, "name": "_ItemSize_co", "namespace": "numpy.dtypes._NBit", "upper_bound": "builtins.int", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.dtypes._NBit"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["_DataSize_co", "_ItemSize_co"], "typeddict_type": null}}, "_NativeOrder": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy.dtypes._NativeOrder", "name": "_NativeOrder", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "numpy.dtypes._NativeOrder", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "numpy.dtypes", "mro": ["numpy.dtypes._NativeOrder", "builtins.object"], "names": {".class": "SymbolTable", "byteorder": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_final", "is_decorated"], "fullname": "numpy.dtypes._NativeOrder.byteorder", "name": "byteorder", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["numpy.dtypes._NativeOrder"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "byteorder of _NativeOrder", "ret_type": {".class": "LiteralType", "fallback": "builtins.str", "value": "="}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_final", "is_ready", "is_inferred"], "fullname": "numpy.dtypes._NativeOrder.byteorder", "name": "byteorder", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["numpy.dtypes._NativeOrder"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "byteorder of _NativeOrder", "ret_type": {".class": "LiteralType", "fallback": "builtins.str", "value": "="}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.dtypes._NativeOrder.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "numpy.dtypes._NativeOrder", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_NoOrder": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy.dtypes._NoOrder", "name": "_NoOrder", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "numpy.dtypes._NoOrder", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "numpy.dtypes", "mro": ["numpy.dtypes._NoOrder", "builtins.object"], "names": {".class": "SymbolTable", "byteorder": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_final", "is_decorated"], "fullname": "numpy.dtypes._NoOrder.byteorder", "name": "byteorder", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["numpy.dtypes._NoOrder"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "byteorder of _NoOrder", "ret_type": {".class": "LiteralType", "fallback": "builtins.str", "value": "|"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_final", "is_ready", "is_inferred"], "fullname": "numpy.dtypes._NoOrder.byteorder", "name": "byteorder", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["numpy.dtypes._NoOrder"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "byteorder of _NoOrder", "ret_type": {".class": "LiteralType", "fallback": "builtins.str", "value": "|"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.dtypes._NoOrder.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "numpy.dtypes._NoOrder", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_NumT_co": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.dtypes._NumT_co", "name": "_NumT_co", "upper_bound": "builtins.int", "values": [], "variance": 1}}, "_SCT_co": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.dtypes._SCT_co", "name": "_SCT_co", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 1}}, "_SimpleDType": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.dtypes._SCT_co", "id": 1, "name": "_SCT_co", "namespace": "numpy.dtypes._SimpleDType", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.dtype"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy.dtypes._SimpleDType", "name": "_SimpleDType", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.dtypes._SCT_co", "id": 1, "name": "_SCT_co", "namespace": "numpy.dtypes._SimpleDType", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 1}]}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "numpy.dtypes._SimpleDType", "has_param_spec_type": false, "metaclass_type": "numpy._DTypeMeta", "metadata": {}, "module_name": "numpy.dtypes", "mro": ["numpy.dtypes._SimpleDType", "numpy.dtype", "builtins.object"], "names": {".class": "SymbolTable", "__getitem__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.dtypes._SimpleDType.__getitem__", "name": "__getitem__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.dtypes._SCT_co", "id": 1, "name": "_SCT_co", "namespace": "numpy.dtypes._SimpleDType", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.dtypes._SimpleDType"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__getitem__ of _SimpleDType", "ret_type": {".class": "UninhabitedType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__new__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static"], "fullname": "numpy.dtypes._SimpleDType.__new__", "name": "__new__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.dtypes._SimpleDType.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.dtypes._SCT_co", "id": 1, "name": "_SCT_co", "namespace": "numpy.dtypes._SimpleDType", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.dtypes._SimpleDType"}, "values": [], "variance": 0}}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__new__ of _SimpleDType", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.dtypes._SimpleDType.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.dtypes._SCT_co", "id": 1, "name": "_SCT_co", "namespace": "numpy.dtypes._SimpleDType", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.dtypes._SimpleDType"}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.dtypes._SimpleDType.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.dtypes._SCT_co", "id": 1, "name": "_SCT_co", "namespace": "numpy.dtypes._SimpleDType", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.dtypes._SimpleDType"}, "values": [], "variance": 0}]}}}, "base": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "numpy.dtypes._SimpleDType.base", "name": "base", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.dtypes._SCT_co", "id": 1, "name": "_SCT_co", "namespace": "numpy.dtypes._SimpleDType", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.dtypes._SimpleDType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "base of _SimpleDType", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.dtypes._SCT_co", "id": 1, "name": "_SCT_co", "namespace": "numpy.dtypes._SimpleDType", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.dtype"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "numpy.dtypes._SimpleDType.base", "name": "base", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.dtypes._SCT_co", "id": 1, "name": "_SCT_co", "namespace": "numpy.dtypes._SimpleDType", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.dtypes._SimpleDType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "base of _SimpleDType", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.dtypes._SCT_co", "id": 1, "name": "_SCT_co", "namespace": "numpy.dtypes._SimpleDType", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.dtype"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "numpy.dtypes._SimpleDType.fields", "name": "fields", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.dtypes._SCT_co", "id": 1, "name": "_SCT_co", "namespace": "numpy.dtypes._SimpleDType", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.dtypes._SimpleDType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "fields of _SimpleDType", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "numpy.dtypes._SimpleDType.fields", "name": "fields", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.dtypes._SCT_co", "id": 1, "name": "_SCT_co", "namespace": "numpy.dtypes._SimpleDType", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.dtypes._SimpleDType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "fields of _SimpleDType", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "isalignedstruct": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "numpy.dtypes._SimpleDType.isalignedstruct", "name": "isalignedstruct", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.dtypes._SCT_co", "id": 1, "name": "_SCT_co", "namespace": "numpy.dtypes._SimpleDType", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.dtypes._SimpleDType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "isalignedstruct of _SimpleDType", "ret_type": {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "numpy.dtypes._SimpleDType.isalignedstruct", "name": "isalignedstruct", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.dtypes._SCT_co", "id": 1, "name": "_SCT_co", "namespace": "numpy.dtypes._SimpleDType", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.dtypes._SimpleDType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "isalignedstruct of _SimpleDType", "ret_type": {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "isnative": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "numpy.dtypes._SimpleDType.isnative", "name": "isnative", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.dtypes._SCT_co", "id": 1, "name": "_SCT_co", "namespace": "numpy.dtypes._SimpleDType", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.dtypes._SimpleDType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "isnative of _SimpleDType", "ret_type": {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "numpy.dtypes._SimpleDType.isnative", "name": "isnative", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.dtypes._SCT_co", "id": 1, "name": "_SCT_co", "namespace": "numpy.dtypes._SimpleDType", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.dtypes._SimpleDType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "isnative of _SimpleDType", "ret_type": {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "names": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "numpy.dtypes._SimpleDType.names", "name": "names", "type": {".class": "NoneType"}}}, "ndim": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "numpy.dtypes._SimpleDType.ndim", "name": "ndim", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.dtypes._SCT_co", "id": 1, "name": "_SCT_co", "namespace": "numpy.dtypes._SimpleDType", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.dtypes._SimpleDType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ndim of _SimpleDType", "ret_type": {".class": "LiteralType", "fallback": "builtins.int", "value": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "numpy.dtypes._SimpleDType.ndim", "name": "ndim", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.dtypes._SCT_co", "id": 1, "name": "_SCT_co", "namespace": "numpy.dtypes._SimpleDType", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.dtypes._SimpleDType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ndim of _SimpleDType", "ret_type": {".class": "LiteralType", "fallback": "builtins.int", "value": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "shape": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "numpy.dtypes._SimpleDType.shape", "name": "shape", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.dtypes._SCT_co", "id": 1, "name": "_SCT_co", "namespace": "numpy.dtypes._SimpleDType", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.dtypes._SimpleDType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "shape of _SimpleDType", "ret_type": {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "numpy.dtypes._SimpleDType.shape", "name": "shape", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.dtypes._SCT_co", "id": 1, "name": "_SCT_co", "namespace": "numpy.dtypes._SimpleDType", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.dtypes._SimpleDType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "shape of _SimpleDType", "ret_type": {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "subdtype": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "numpy.dtypes._SimpleDType.subdtype", "name": "subdtype", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.dtypes._SCT_co", "id": 1, "name": "_SCT_co", "namespace": "numpy.dtypes._SimpleDType", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.dtypes._SimpleDType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "subdtype of _SimpleDType", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "numpy.dtypes._SimpleDType.subdtype", "name": "subdtype", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.dtypes._SCT_co", "id": 1, "name": "_SCT_co", "namespace": "numpy.dtypes._SimpleDType", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.dtypes._SimpleDType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "subdtype of _SimpleDType", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.dtypes._SimpleDType.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.dtypes._SCT_co", "id": 1, "name": "_SCT_co", "namespace": "numpy.dtypes._SimpleDType", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.dtypes._SimpleDType"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["_SCT_co"], "typeddict_type": null}}, "_TimeUnit": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "numpy.dtypes._TimeUnit", "line": 478, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "h"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "m"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "s"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "ms"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "us"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "ns"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "ps"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "fs"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "as"}], "uses_pep604_syntax": false}}}, "_TypeCodes": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy.dtypes._TypeCodes", "name": "_TypeCodes", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.dtypes._KindT_co", "id": 1, "name": "_KindT_co", "namespace": "numpy.dtypes._TypeCodes", "upper_bound": "builtins.str", "values": [], "variance": 1}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.dtypes._CharT_co", "id": 2, "name": "_CharT_co", "namespace": "numpy.dtypes._TypeCodes", "upper_bound": "builtins.str", "values": [], "variance": 1}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.dtypes._NumT_co", "id": 3, "name": "_NumT_co", "namespace": "numpy.dtypes._TypeCodes", "upper_bound": "builtins.int", "values": [], "variance": 1}]}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "numpy.dtypes._TypeCodes", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "numpy.dtypes", "mro": ["numpy.dtypes._TypeCodes", "builtins.object"], "names": {".class": "SymbolTable", "char": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_final", "is_decorated"], "fullname": "numpy.dtypes._TypeCodes.char", "name": "char", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.dtypes._KindT_co", "id": 1, "name": "_KindT_co", "namespace": "numpy.dtypes._TypeCodes", "upper_bound": "builtins.str", "values": [], "variance": 1}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.dtypes._CharT_co", "id": 2, "name": "_CharT_co", "namespace": "numpy.dtypes._TypeCodes", "upper_bound": "builtins.str", "values": [], "variance": 1}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.dtypes._NumT_co", "id": 3, "name": "_NumT_co", "namespace": "numpy.dtypes._TypeCodes", "upper_bound": "builtins.int", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.dtypes._TypeCodes"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "char of _TypeCodes", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.dtypes._CharT_co", "id": 2, "name": "_CharT_co", "namespace": "numpy.dtypes._TypeCodes", "upper_bound": "builtins.str", "values": [], "variance": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_final", "is_ready", "is_inferred"], "fullname": "numpy.dtypes._TypeCodes.char", "name": "char", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.dtypes._KindT_co", "id": 1, "name": "_KindT_co", "namespace": "numpy.dtypes._TypeCodes", "upper_bound": "builtins.str", "values": [], "variance": 1}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.dtypes._CharT_co", "id": 2, "name": "_CharT_co", "namespace": "numpy.dtypes._TypeCodes", "upper_bound": "builtins.str", "values": [], "variance": 1}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.dtypes._NumT_co", "id": 3, "name": "_NumT_co", "namespace": "numpy.dtypes._TypeCodes", "upper_bound": "builtins.int", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.dtypes._TypeCodes"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "char of _TypeCodes", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.dtypes._CharT_co", "id": 2, "name": "_CharT_co", "namespace": "numpy.dtypes._TypeCodes", "upper_bound": "builtins.str", "values": [], "variance": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "kind": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_final", "is_decorated"], "fullname": "numpy.dtypes._TypeCodes.kind", "name": "kind", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.dtypes._KindT_co", "id": 1, "name": "_KindT_co", "namespace": "numpy.dtypes._TypeCodes", "upper_bound": "builtins.str", "values": [], "variance": 1}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.dtypes._CharT_co", "id": 2, "name": "_CharT_co", "namespace": "numpy.dtypes._TypeCodes", "upper_bound": "builtins.str", "values": [], "variance": 1}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.dtypes._NumT_co", "id": 3, "name": "_NumT_co", "namespace": "numpy.dtypes._TypeCodes", "upper_bound": "builtins.int", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.dtypes._TypeCodes"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "kind of _TypeCodes", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.dtypes._KindT_co", "id": 1, "name": "_KindT_co", "namespace": "numpy.dtypes._TypeCodes", "upper_bound": "builtins.str", "values": [], "variance": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_final", "is_ready", "is_inferred"], "fullname": "numpy.dtypes._TypeCodes.kind", "name": "kind", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.dtypes._KindT_co", "id": 1, "name": "_KindT_co", "namespace": "numpy.dtypes._TypeCodes", "upper_bound": "builtins.str", "values": [], "variance": 1}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.dtypes._CharT_co", "id": 2, "name": "_CharT_co", "namespace": "numpy.dtypes._TypeCodes", "upper_bound": "builtins.str", "values": [], "variance": 1}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.dtypes._NumT_co", "id": 3, "name": "_NumT_co", "namespace": "numpy.dtypes._TypeCodes", "upper_bound": "builtins.int", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.dtypes._TypeCodes"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "kind of _TypeCodes", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.dtypes._KindT_co", "id": 1, "name": "_KindT_co", "namespace": "numpy.dtypes._TypeCodes", "upper_bound": "builtins.str", "values": [], "variance": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "num": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_final", "is_decorated"], "fullname": "numpy.dtypes._TypeCodes.num", "name": "num", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.dtypes._KindT_co", "id": 1, "name": "_KindT_co", "namespace": "numpy.dtypes._TypeCodes", "upper_bound": "builtins.str", "values": [], "variance": 1}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.dtypes._CharT_co", "id": 2, "name": "_CharT_co", "namespace": "numpy.dtypes._TypeCodes", "upper_bound": "builtins.str", "values": [], "variance": 1}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.dtypes._NumT_co", "id": 3, "name": "_NumT_co", "namespace": "numpy.dtypes._TypeCodes", "upper_bound": "builtins.int", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.dtypes._TypeCodes"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "num of _TypeCodes", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.dtypes._NumT_co", "id": 3, "name": "_NumT_co", "namespace": "numpy.dtypes._TypeCodes", "upper_bound": "builtins.int", "values": [], "variance": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_final", "is_ready", "is_inferred"], "fullname": "numpy.dtypes._TypeCodes.num", "name": "num", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.dtypes._KindT_co", "id": 1, "name": "_KindT_co", "namespace": "numpy.dtypes._TypeCodes", "upper_bound": "builtins.str", "values": [], "variance": 1}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.dtypes._CharT_co", "id": 2, "name": "_CharT_co", "namespace": "numpy.dtypes._TypeCodes", "upper_bound": "builtins.str", "values": [], "variance": 1}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.dtypes._NumT_co", "id": 3, "name": "_NumT_co", "namespace": "numpy.dtypes._TypeCodes", "upper_bound": "builtins.int", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.dtypes._TypeCodes"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "num of _TypeCodes", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.dtypes._NumT_co", "id": 3, "name": "_NumT_co", "namespace": "numpy.dtypes._TypeCodes", "upper_bound": "builtins.int", "values": [], "variance": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.dtypes._TypeCodes.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.dtypes._KindT_co", "id": 1, "name": "_KindT_co", "namespace": "numpy.dtypes._TypeCodes", "upper_bound": "builtins.str", "values": [], "variance": 1}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.dtypes._CharT_co", "id": 2, "name": "_CharT_co", "namespace": "numpy.dtypes._TypeCodes", "upper_bound": "builtins.str", "values": [], "variance": 1}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.dtypes._NumT_co", "id": 3, "name": "_NumT_co", "namespace": "numpy.dtypes._TypeCodes", "upper_bound": "builtins.int", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.dtypes._TypeCodes"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["_KindT_co", "_CharT_co", "_NumT_co"], "typeddict_type": null}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "numpy.dtypes.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.dtypes.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.dtypes.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.dtypes.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.dtypes.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.dtypes.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.dtypes.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "final": {".class": "SymbolTableNode", "cross_ref": "typing.final", "kind": "Gdef", "module_hidden": true, "module_public": false}, "np": {".class": "SymbolTableNode", "cross_ref": "numpy", "kind": "Gdef", "module_hidden": true, "module_public": false}, "type_check_only": {".class": "SymbolTableNode", "cross_ref": "typing.type_check_only", "kind": "Gdef", "module_hidden": true, "module_public": false}}, "path": "c:\\Python313\\Lib\\site-packages\\numpy\\dtypes.pyi"}