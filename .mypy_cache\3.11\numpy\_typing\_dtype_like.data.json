{".class": "MypyFile", "_fullname": "numpy._typing._dtype_like", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "DTypeLike": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "numpy._typing._dtype_like.DTypeLike", "line": 117, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "numpy.dtype"}, {".class": "NoneType"}, {".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}}, {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "numpy.dtype"}], "extra_attrs": null, "type_ref": "numpy._typing._dtype_like._SupportsDType"}, "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._dtype_like._VoidDTypeLike"}], "uses_pep604_syntax": true}}}, "Protocol": {".class": "SymbolTableNode", "cross_ref": "typing.Protocol", "kind": "Gdef"}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef"}, "TypeAlias": {".class": "SymbolTableNode", "cross_ref": "typing.TypeAlias", "kind": "Gdef"}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef"}, "TypedDict": {".class": "SymbolTableNode", "cross_ref": "typing.TypedDict", "kind": "Gdef"}, "_BoolCodes": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._char_codes._BoolCodes", "kind": "Gdef"}, "_ByteCodes": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._char_codes._ByteCodes", "kind": "Gdef"}, "_BytesCodes": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._char_codes._BytesCodes", "kind": "Gdef"}, "_CDoubleCodes": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._char_codes._CDoubleCodes", "kind": "Gdef"}, "_CLongDoubleCodes": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._char_codes._CLongDoubleCodes", "kind": "Gdef"}, "_CSingleCodes": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._char_codes._CSingleCodes", "kind": "Gdef"}, "_Complex128Codes": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._char_codes._Complex128Codes", "kind": "Gdef"}, "_Complex64Codes": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._char_codes._Complex64Codes", "kind": "Gdef"}, "_DT64Codes": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._char_codes._DT64Codes", "kind": "Gdef"}, "_DTypeDict": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy._typing._dtype_like._DTypeDict", "name": "_DTypeDict", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "numpy._typing._dtype_like._DTypeDict", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "numpy._typing._dtype_like", "mro": ["numpy._typing._dtype_like._DTypeDict", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["names", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}], ["formats", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._dtype_like._DTypeLikeNested"}], "extra_attrs": null, "type_ref": "typing.Sequence"}], ["offsets", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "typing.Sequence"}], ["titles", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Sequence"}], ["itemsize", "builtins.int"], ["aligned", "builtins.bool"]], "readonly_keys": [], "required_keys": ["formats", "names"]}}}, "_DTypeDictBase": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy._typing._dtype_like._DTypeDictBase", "name": "_DTypeDictBase", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "numpy._typing._dtype_like._DTypeDictBase", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "numpy._typing._dtype_like", "mro": ["numpy._typing._dtype_like._DTypeDictBase", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["names", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}], ["formats", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._dtype_like._DTypeLikeNested"}], "extra_attrs": null, "type_ref": "typing.Sequence"}]], "readonly_keys": [], "required_keys": ["formats", "names"]}}}, "_DTypeLike": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._dtype_like._SCT", "id": 1, "name": "_SCT", "namespace": "numpy._typing._dtype_like._DTypeLike", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "column": 0, "fullname": "numpy._typing._dtype_like._DTypeLike", "line": 89, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._dtype_like._SCT", "id": 1, "name": "_SCT", "namespace": "numpy._typing._dtype_like._DTypeLike", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy.dtype"}, {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._dtype_like._SCT", "id": 1, "name": "_SCT", "namespace": "numpy._typing._dtype_like._DTypeLike", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._dtype_like._SCT", "id": 1, "name": "_SCT", "namespace": "numpy._typing._dtype_like._DTypeLike", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy.dtype"}], "extra_attrs": null, "type_ref": "numpy._typing._dtype_like._SupportsDType"}], "uses_pep604_syntax": true}}}, "_DTypeLikeBool": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "numpy._typing._dtype_like._DTypeLikeBool", "line": 139, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "TypeType", "item": "builtins.bool"}, {".class": "TypeType", "item": {".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "numpy.bool"}}, {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "numpy.bool"}], "extra_attrs": null, "type_ref": "numpy.dtype"}, {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "numpy.bool"}], "extra_attrs": null, "type_ref": "numpy.dtype"}], "extra_attrs": null, "type_ref": "numpy._typing._dtype_like._SupportsDType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._char_codes._BoolCodes"}], "uses_pep604_syntax": true}}}, "_DTypeLikeBytes": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "numpy._typing._dtype_like._DTypeLikeBytes", "line": 222, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "TypeType", "item": "builtins.bytes"}, {".class": "TypeType", "item": "numpy.bytes_"}, {".class": "Instance", "args": ["numpy.bytes_"], "extra_attrs": null, "type_ref": "numpy.dtype"}, {".class": "Instance", "args": [{".class": "Instance", "args": ["numpy.bytes_"], "extra_attrs": null, "type_ref": "numpy.dtype"}], "extra_attrs": null, "type_ref": "numpy._typing._dtype_like._SupportsDType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._char_codes._BytesCodes"}], "uses_pep604_syntax": true}}}, "_DTypeLikeComplex": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "numpy._typing._dtype_like._DTypeLikeComplex", "line": 192, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "TypeType", "item": "builtins.complex"}, {".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "numpy.complexfloating"}}, {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "numpy.complexfloating"}], "extra_attrs": null, "type_ref": "numpy.dtype"}, {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "numpy.complexfloating"}], "extra_attrs": null, "type_ref": "numpy.dtype"}], "extra_attrs": null, "type_ref": "numpy._typing._dtype_like._SupportsDType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._char_codes._Complex64Codes"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._char_codes._Complex128Codes"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._char_codes._CSingleCodes"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._char_codes._CDoubleCodes"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._char_codes._CLongDoubleCodes"}], "uses_pep604_syntax": true}}}, "_DTypeLikeComplex_co": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "numpy._typing._dtype_like._DTypeLikeComplex_co", "line": 243, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._dtype_like._DTypeLikeBool"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._dtype_like._DTypeLikeUInt"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._dtype_like._DTypeLikeInt"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._dtype_like._DTypeLikeFloat"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._dtype_like._DTypeLikeComplex"}], "uses_pep604_syntax": true}}}, "_DTypeLikeDT64": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "numpy._typing._dtype_like._DTypeLikeDT64", "line": 203, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "UnionType", "items": ["datetime.<PERSON><PERSON><PERSON>", "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.timedelta64"}}, {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["datetime.<PERSON><PERSON><PERSON>", "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.timedelta64"}], "extra_attrs": null, "type_ref": "numpy.dtype"}, {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["datetime.<PERSON><PERSON><PERSON>", "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.timedelta64"}], "extra_attrs": null, "type_ref": "numpy.dtype"}], "extra_attrs": null, "type_ref": "numpy._typing._dtype_like._SupportsDType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._char_codes._TD64Codes"}], "uses_pep604_syntax": true}}}, "_DTypeLikeFloat": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "numpy._typing._dtype_like._DTypeLikeFloat", "line": 179, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "TypeType", "item": "builtins.float"}, {".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "numpy.floating"}}, {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "numpy.floating"}], "extra_attrs": null, "type_ref": "numpy.dtype"}, {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "numpy.floating"}], "extra_attrs": null, "type_ref": "numpy.dtype"}], "extra_attrs": null, "type_ref": "numpy._typing._dtype_like._SupportsDType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._char_codes._Float16Codes"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._char_codes._Float32Codes"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._char_codes._Float64Codes"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._char_codes._HalfCodes"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._char_codes._SingleCodes"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._char_codes._DoubleCodes"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._char_codes._LongDoubleCodes"}], "uses_pep604_syntax": true}}}, "_DTypeLikeInt": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "numpy._typing._dtype_like._DTypeLikeInt", "line": 162, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "TypeType", "item": "builtins.int"}, {".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "numpy.<PERSON><PERSON><PERSON>r"}}, {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "numpy.<PERSON><PERSON><PERSON>r"}], "extra_attrs": null, "type_ref": "numpy.dtype"}, {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "numpy.<PERSON><PERSON><PERSON>r"}], "extra_attrs": null, "type_ref": "numpy.dtype"}], "extra_attrs": null, "type_ref": "numpy._typing._dtype_like._SupportsDType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._char_codes._Int8Codes"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._char_codes._Int16Codes"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._char_codes._Int32Codes"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._char_codes._Int64Codes"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._char_codes._ByteCodes"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._char_codes._ShortCodes"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._char_codes._IntCCodes"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._char_codes._LongCodes"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._char_codes._LongLongCodes"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._char_codes._IntPCodes"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._char_codes._IntCodes"}], "uses_pep604_syntax": true}}}, "_DTypeLikeNested": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "numpy._typing._dtype_like._DTypeLikeNested", "line": 62, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}}}, "_DTypeLikeObject": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "numpy._typing._dtype_like._DTypeLikeObject", "line": 236, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": ["builtins.type", {".class": "Instance", "args": ["numpy.object_"], "extra_attrs": null, "type_ref": "numpy.dtype"}, {".class": "Instance", "args": [{".class": "Instance", "args": ["numpy.object_"], "extra_attrs": null, "type_ref": "numpy.dtype"}], "extra_attrs": null, "type_ref": "numpy._typing._dtype_like._SupportsDType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._char_codes._ObjectCodes"}], "uses_pep604_syntax": true}}}, "_DTypeLikeStr": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "numpy._typing._dtype_like._DTypeLikeStr", "line": 215, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "TypeType", "item": "builtins.str"}, {".class": "TypeType", "item": "numpy.str_"}, {".class": "Instance", "args": ["numpy.str_"], "extra_attrs": null, "type_ref": "numpy.dtype"}, {".class": "Instance", "args": [{".class": "Instance", "args": ["numpy.str_"], "extra_attrs": null, "type_ref": "numpy.dtype"}], "extra_attrs": null, "type_ref": "numpy._typing._dtype_like._SupportsDType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._char_codes._StrCodes"}], "uses_pep604_syntax": true}}}, "_DTypeLikeTD64": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "numpy._typing._dtype_like._DTypeLikeTD64", "line": 209, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "UnionType", "items": ["datetime.date", "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.datetime64"}}, {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["datetime.date", "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.datetime64"}], "extra_attrs": null, "type_ref": "numpy.dtype"}, {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["datetime.date", "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.datetime64"}], "extra_attrs": null, "type_ref": "numpy.dtype"}], "extra_attrs": null, "type_ref": "numpy._typing._dtype_like._SupportsDType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._char_codes._DT64Codes"}], "uses_pep604_syntax": true}}}, "_DTypeLikeUInt": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "numpy._typing._dtype_like._DTypeLikeUInt", "line": 146, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "numpy.unsignedinteger"}}, {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "numpy.unsignedinteger"}], "extra_attrs": null, "type_ref": "numpy.dtype"}, {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "numpy.unsignedinteger"}], "extra_attrs": null, "type_ref": "numpy.dtype"}], "extra_attrs": null, "type_ref": "numpy._typing._dtype_like._SupportsDType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._char_codes._UInt8Codes"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._char_codes._UInt16Codes"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._char_codes._UInt32Codes"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._char_codes._UInt64Codes"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._char_codes._UByteCodes"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._char_codes._UShortCodes"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._char_codes._UIntCCodes"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._char_codes._LongCodes"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._char_codes._ULongLongCodes"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._char_codes._UIntPCodes"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._char_codes._UIntCodes"}], "uses_pep604_syntax": true}}}, "_DTypeLikeVoid": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "numpy._typing._dtype_like._DTypeLikeVoid", "line": 229, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "TypeType", "item": "numpy.void"}, {".class": "Instance", "args": ["numpy.void"], "extra_attrs": null, "type_ref": "numpy.dtype"}, {".class": "Instance", "args": [{".class": "Instance", "args": ["numpy.void"], "extra_attrs": null, "type_ref": "numpy.dtype"}], "extra_attrs": null, "type_ref": "numpy._typing._dtype_like._SupportsDType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._char_codes._VoidCodes"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._dtype_like._VoidDTypeLike"}], "uses_pep604_syntax": true}}}, "_DType_co": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._dtype_like._DType_co", "name": "_DType_co", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.dtype"}, "values": [], "variance": 1}}, "_DoubleCodes": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._char_codes._DoubleCodes", "kind": "Gdef"}, "_Float16Codes": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._char_codes._Float16Codes", "kind": "Gdef"}, "_Float32Codes": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._char_codes._Float32Codes", "kind": "Gdef"}, "_Float64Codes": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._char_codes._Float64Codes", "kind": "Gdef"}, "_HalfCodes": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._char_codes._HalfCodes", "kind": "Gdef"}, "_Int16Codes": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._char_codes._Int16Codes", "kind": "Gdef"}, "_Int32Codes": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._char_codes._Int32Codes", "kind": "Gdef"}, "_Int64Codes": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._char_codes._Int64Codes", "kind": "Gdef"}, "_Int8Codes": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._char_codes._Int8Codes", "kind": "Gdef"}, "_IntCCodes": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._char_codes._IntCCodes", "kind": "Gdef"}, "_IntCodes": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._char_codes._IntCodes", "kind": "Gdef"}, "_IntPCodes": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._char_codes._IntPCodes", "kind": "Gdef"}, "_LongCodes": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._char_codes._LongCodes", "kind": "Gdef"}, "_LongDoubleCodes": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._char_codes._LongDoubleCodes", "kind": "Gdef"}, "_LongLongCodes": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._char_codes._LongLongCodes", "kind": "Gdef"}, "_ObjectCodes": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._char_codes._ObjectCodes", "kind": "Gdef"}, "_SCT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._dtype_like._SCT", "name": "_SCT", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}}, "_ShapeLike": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._shape._ShapeLike", "kind": "Gdef"}, "_ShortCodes": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._char_codes._ShortCodes", "kind": "Gdef"}, "_SingleCodes": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._char_codes._SingleCodes", "kind": "Gdef"}, "_StrCodes": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._char_codes._StrCodes", "kind": "Gdef"}, "_SupportsDType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["dtype", 2]], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy._typing._dtype_like._SupportsDType", "name": "_SupportsDType", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._dtype_like._DType_co", "id": 1, "name": "_DType_co", "namespace": "numpy._typing._dtype_like._SupportsDType", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.dtype"}, "values": [], "variance": 1}]}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract", "is_protocol", "runtime_protocol"], "fullname": "numpy._typing._dtype_like._SupportsDType", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "numpy._typing._dtype_like", "mro": ["numpy._typing._dtype_like._SupportsDType", "builtins.object"], "names": {".class": "SymbolTable", "dtype": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_body"], "fullname": "numpy._typing._dtype_like._SupportsDType.dtype", "name": "dtype", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._dtype_like._DType_co", "id": 1, "name": "_DType_co", "namespace": "numpy._typing._dtype_like._SupportsDType", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.dtype"}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy._typing._dtype_like._SupportsDType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "dtype of _SupportsDType", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._dtype_like._DType_co", "id": 1, "name": "_DType_co", "namespace": "numpy._typing._dtype_like._SupportsDType", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.dtype"}, "values": [], "variance": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "numpy._typing._dtype_like._SupportsDType.dtype", "name": "dtype", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._dtype_like._DType_co", "id": 1, "name": "_DType_co", "namespace": "numpy._typing._dtype_like._SupportsDType", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.dtype"}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy._typing._dtype_like._SupportsDType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "dtype of _SupportsDType", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._dtype_like._DType_co", "id": 1, "name": "_DType_co", "namespace": "numpy._typing._dtype_like._SupportsDType", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.dtype"}, "values": [], "variance": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._dtype_like._SupportsDType.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._dtype_like._DType_co", "id": 1, "name": "_DType_co", "namespace": "numpy._typing._dtype_like._SupportsDType", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.dtype"}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy._typing._dtype_like._SupportsDType"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["_DType_co"], "typeddict_type": null}}, "_TD64Codes": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._char_codes._TD64Codes", "kind": "Gdef"}, "_UByteCodes": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._char_codes._UByteCodes", "kind": "Gdef"}, "_UInt16Codes": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._char_codes._UInt16Codes", "kind": "Gdef"}, "_UInt32Codes": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._char_codes._UInt32Codes", "kind": "Gdef"}, "_UInt64Codes": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._char_codes._UInt64Codes", "kind": "Gdef"}, "_UInt8Codes": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._char_codes._UInt8Codes", "kind": "Gdef"}, "_UIntCCodes": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._char_codes._UIntCCodes", "kind": "Gdef"}, "_UIntCodes": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._char_codes._UIntCodes", "kind": "Gdef"}, "_UIntPCodes": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._char_codes._UIntPCodes", "kind": "Gdef"}, "_ULongCodes": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._char_codes._ULongCodes", "kind": "Gdef"}, "_ULongLongCodes": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._char_codes._ULongLongCodes", "kind": "Gdef"}, "_UShortCodes": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._char_codes._UShortCodes", "kind": "Gdef"}, "_VoidCodes": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._char_codes._VoidCodes", "kind": "Gdef"}, "_VoidDTypeLike": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "numpy._typing._dtype_like._VoidDTypeLike", "line": 97, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._dtype_like._DTypeLikeNested"}, "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._dtype_like._DTypeLikeNested"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._shape._ShapeLike"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._dtype_like._DTypeDict"}, {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._dtype_like._DTypeLikeNested"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._dtype_like._DTypeLikeNested"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": true}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy._typing._dtype_like.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy._typing._dtype_like.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy._typing._dtype_like.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy._typing._dtype_like.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy._typing._dtype_like.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy._typing._dtype_like.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "np": {".class": "SymbolTableNode", "cross_ref": "numpy", "kind": "Gdef"}, "runtime_checkable": {".class": "SymbolTableNode", "cross_ref": "typing.runtime_checkable", "kind": "Gdef"}}, "path": "c:\\Python313\\Lib\\site-packages\\numpy\\_typing\\_dtype_like.py"}