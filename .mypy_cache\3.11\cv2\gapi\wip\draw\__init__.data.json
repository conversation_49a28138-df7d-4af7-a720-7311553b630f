{".class": "MypyFile", "_fullname": "cv2.gapi.wip.draw", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "Circle": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "cv2.gapi.wip.draw.Circle", "name": "Circle", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "cv2.gapi.wip.draw.Circle", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "cv2.gapi.wip.draw", "mro": ["cv2.gapi.wip.draw.Circle", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "cv2.gapi.wip.draw.Circle.__init__", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1, 1, 1], "arg_names": ["self", "center_", "radius_", "color_", "thick_", "lt_", "shift_"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.gapi.wip.draw.Circle.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1, 1, 1], "arg_names": ["self", "center_", "radius_", "color_", "thick_", "lt_", "shift_"], "arg_types": ["cv2.gapi.wip.draw.Circle", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.Point"}, "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.<PERSON>.<PERSON>"}, "builtins.int", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Circle", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.gapi.wip.draw.Circle.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1, 1, 1], "arg_names": ["self", "center_", "radius_", "color_", "thick_", "lt_", "shift_"], "arg_types": ["cv2.gapi.wip.draw.Circle", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.Point"}, "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.<PERSON>.<PERSON>"}, "builtins.int", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Circle", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.gapi.wip.draw.Circle.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cv2.gapi.wip.draw.Circle"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Circle", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.gapi.wip.draw.Circle.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cv2.gapi.wip.draw.Circle"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Circle", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1, 1, 1], "arg_names": ["self", "center_", "radius_", "color_", "thick_", "lt_", "shift_"], "arg_types": ["cv2.gapi.wip.draw.Circle", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.Point"}, "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.<PERSON>.<PERSON>"}, "builtins.int", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Circle", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cv2.gapi.wip.draw.Circle"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Circle", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "center": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "cv2.gapi.wip.draw.Circle.center", "name": "center", "type": {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.Point"}}}, "color": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "cv2.gapi.wip.draw.Circle.color", "name": "color", "type": {".class": "TypeAliasType", "args": [], "type_ref": "cv2.<PERSON>.<PERSON>"}}}, "lt": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "cv2.gapi.wip.draw.Circle.lt", "name": "lt", "type": "builtins.int"}}, "radius": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "cv2.gapi.wip.draw.Circle.radius", "name": "radius", "type": "builtins.int"}}, "shift": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "cv2.gapi.wip.draw.Circle.shift", "name": "shift", "type": "builtins.int"}}, "thick": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "cv2.gapi.wip.draw.Circle.thick", "name": "thick", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cv2.gapi.wip.draw.Circle.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cv2.gapi.wip.draw.Circle", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Image": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "cv2.gapi.wip.draw.Image", "name": "Image", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "cv2.gapi.wip.draw.Image", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "cv2.gapi.wip.draw", "mro": ["cv2.gapi.wip.draw.Image", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "cv2.gapi.wip.draw.Image.__init__", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "org_", "img_", "alpha_"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.gapi.wip.draw.Image.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "org_", "img_", "alpha_"], "arg_types": ["cv2.gapi.wip.draw.Image", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.Point"}, {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Image", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.gapi.wip.draw.Image.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "org_", "img_", "alpha_"], "arg_types": ["cv2.gapi.wip.draw.Image", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.Point"}, {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Image", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.gapi.wip.draw.Image.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cv2.gapi.wip.draw.Image"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Image", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.gapi.wip.draw.Image.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cv2.gapi.wip.draw.Image"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Image", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "org_", "img_", "alpha_"], "arg_types": ["cv2.gapi.wip.draw.Image", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.Point"}, {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Image", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cv2.gapi.wip.draw.Image"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Image", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "alpha": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "cv2.gapi.wip.draw.Image.alpha", "name": "alpha", "type": {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}}}, "img": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "cv2.gapi.wip.draw.Image.img", "name": "img", "type": {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}}}, "org": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "cv2.gapi.wip.draw.Image.org", "name": "org", "type": {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.Point"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cv2.gapi.wip.draw.Image.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cv2.gapi.wip.draw.Image", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Line": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "cv2.gapi.wip.draw.Line", "name": "Line", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "cv2.gapi.wip.draw.Line", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "cv2.gapi.wip.draw", "mro": ["cv2.gapi.wip.draw.Line", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "cv2.gapi.wip.draw.Line.__init__", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1, 1, 1], "arg_names": ["self", "pt1_", "pt2_", "color_", "thick_", "lt_", "shift_"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.gapi.wip.draw.Line.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1, 1, 1], "arg_names": ["self", "pt1_", "pt2_", "color_", "thick_", "lt_", "shift_"], "arg_types": ["cv2.gapi.wip.draw.Line", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.Point"}, {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.Point"}, {".class": "TypeAliasType", "args": [], "type_ref": "cv2.<PERSON>.<PERSON>"}, "builtins.int", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Line", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.gapi.wip.draw.Line.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1, 1, 1], "arg_names": ["self", "pt1_", "pt2_", "color_", "thick_", "lt_", "shift_"], "arg_types": ["cv2.gapi.wip.draw.Line", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.Point"}, {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.Point"}, {".class": "TypeAliasType", "args": [], "type_ref": "cv2.<PERSON>.<PERSON>"}, "builtins.int", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Line", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.gapi.wip.draw.Line.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cv2.gapi.wip.draw.Line"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Line", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.gapi.wip.draw.Line.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cv2.gapi.wip.draw.Line"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Line", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1, 1, 1], "arg_names": ["self", "pt1_", "pt2_", "color_", "thick_", "lt_", "shift_"], "arg_types": ["cv2.gapi.wip.draw.Line", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.Point"}, {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.Point"}, {".class": "TypeAliasType", "args": [], "type_ref": "cv2.<PERSON>.<PERSON>"}, "builtins.int", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Line", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cv2.gapi.wip.draw.Line"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Line", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "color": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "cv2.gapi.wip.draw.Line.color", "name": "color", "type": {".class": "TypeAliasType", "args": [], "type_ref": "cv2.<PERSON>.<PERSON>"}}}, "lt": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "cv2.gapi.wip.draw.Line.lt", "name": "lt", "type": "builtins.int"}}, "pt1": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "cv2.gapi.wip.draw.Line.pt1", "name": "pt1", "type": {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.Point"}}}, "pt2": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "cv2.gapi.wip.draw.Line.pt2", "name": "pt2", "type": {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.Point"}}}, "shift": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "cv2.gapi.wip.draw.Line.shift", "name": "shift", "type": "builtins.int"}}, "thick": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "cv2.gapi.wip.draw.Line.thick", "name": "thick", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cv2.gapi.wip.draw.Line.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cv2.gapi.wip.draw.Line", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Mosaic": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "cv2.gapi.wip.draw.Mosaic", "name": "Mosaic", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "cv2.gapi.wip.draw.Mosaic", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "cv2.gapi.wip.draw", "mro": ["cv2.gapi.wip.draw.Mosaic", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "cv2.gapi.wip.draw.Mosaic.__init__", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.gapi.wip.draw.Mosaic.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cv2.gapi.wip.draw.Mosaic"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Mosaic", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.gapi.wip.draw.Mosaic.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cv2.gapi.wip.draw.Mosaic"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Mosaic", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "mos_", "cellSz_", "decim_"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.gapi.wip.draw.Mosaic.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "mos_", "cellSz_", "decim_"], "arg_types": ["cv2.gapi.wip.draw.Mosaic", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.Rect2i"}, "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Mosaic", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.gapi.wip.draw.Mosaic.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "mos_", "cellSz_", "decim_"], "arg_types": ["cv2.gapi.wip.draw.Mosaic", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.Rect2i"}, "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Mosaic", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cv2.gapi.wip.draw.Mosaic"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Mosaic", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "mos_", "cellSz_", "decim_"], "arg_types": ["cv2.gapi.wip.draw.Mosaic", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.Rect2i"}, "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Mosaic", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "cellSz": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "cv2.gapi.wip.draw.Mosaic.cellSz", "name": "cellSz", "type": "builtins.int"}}, "decim": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "cv2.gapi.wip.draw.Mosaic.decim", "name": "decim", "type": "builtins.int"}}, "mos": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "cv2.gapi.wip.draw.Mosaic.mos", "name": "mos", "type": {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.Rect"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cv2.gapi.wip.draw.Mosaic.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cv2.gapi.wip.draw.Mosaic", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Poly": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "cv2.gapi.wip.draw.Poly", "name": "Poly", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "cv2.gapi.wip.draw.Poly", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "cv2.gapi.wip.draw", "mro": ["cv2.gapi.wip.draw.Poly", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "cv2.gapi.wip.draw.Poly.__init__", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1], "arg_names": ["self", "points_", "color_", "thick_", "lt_", "shift_"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.gapi.wip.draw.Poly.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1], "arg_names": ["self", "points_", "color_", "thick_", "lt_", "shift_"], "arg_types": ["cv2.gapi.wip.draw.Poly", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.Point"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "TypeAliasType", "args": [], "type_ref": "cv2.<PERSON>.<PERSON>"}, "builtins.int", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Poly", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.gapi.wip.draw.Poly.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1], "arg_names": ["self", "points_", "color_", "thick_", "lt_", "shift_"], "arg_types": ["cv2.gapi.wip.draw.Poly", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.Point"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "TypeAliasType", "args": [], "type_ref": "cv2.<PERSON>.<PERSON>"}, "builtins.int", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Poly", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.gapi.wip.draw.Poly.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cv2.gapi.wip.draw.Poly"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Poly", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.gapi.wip.draw.Poly.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cv2.gapi.wip.draw.Poly"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Poly", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1], "arg_names": ["self", "points_", "color_", "thick_", "lt_", "shift_"], "arg_types": ["cv2.gapi.wip.draw.Poly", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.Point"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "TypeAliasType", "args": [], "type_ref": "cv2.<PERSON>.<PERSON>"}, "builtins.int", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Poly", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cv2.gapi.wip.draw.Poly"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Poly", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "color": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "cv2.gapi.wip.draw.Poly.color", "name": "color", "type": {".class": "TypeAliasType", "args": [], "type_ref": "cv2.<PERSON>.<PERSON>"}}}, "lt": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "cv2.gapi.wip.draw.Poly.lt", "name": "lt", "type": "builtins.int"}}, "points": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "cv2.gapi.wip.draw.Poly.points", "name": "points", "type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.Point"}], "extra_attrs": null, "type_ref": "typing.Sequence"}}}, "shift": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "cv2.gapi.wip.draw.Poly.shift", "name": "shift", "type": "builtins.int"}}, "thick": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "cv2.gapi.wip.draw.Poly.thick", "name": "thick", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cv2.gapi.wip.draw.Poly.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cv2.gapi.wip.draw.Poly", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Rect": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "cv2.gapi.wip.draw.Rect", "name": "Rect", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "cv2.gapi.wip.draw.Rect", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "cv2.gapi.wip.draw", "mro": ["cv2.gapi.wip.draw.Rect", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "cv2.gapi.wip.draw.Rect.__init__", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.gapi.wip.draw.Rect.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cv2.gapi.wip.draw.Rect"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Rect", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.gapi.wip.draw.Rect.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cv2.gapi.wip.draw.Rect"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Rect", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1], "arg_names": ["self", "rect_", "color_", "thick_", "lt_", "shift_"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.gapi.wip.draw.Rect.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1], "arg_names": ["self", "rect_", "color_", "thick_", "lt_", "shift_"], "arg_types": ["cv2.gapi.wip.draw.Rect", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.Rect2i"}, {".class": "TypeAliasType", "args": [], "type_ref": "cv2.<PERSON>.<PERSON>"}, "builtins.int", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Rect", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.gapi.wip.draw.Rect.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1], "arg_names": ["self", "rect_", "color_", "thick_", "lt_", "shift_"], "arg_types": ["cv2.gapi.wip.draw.Rect", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.Rect2i"}, {".class": "TypeAliasType", "args": [], "type_ref": "cv2.<PERSON>.<PERSON>"}, "builtins.int", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Rect", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cv2.gapi.wip.draw.Rect"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Rect", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1], "arg_names": ["self", "rect_", "color_", "thick_", "lt_", "shift_"], "arg_types": ["cv2.gapi.wip.draw.Rect", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.Rect2i"}, {".class": "TypeAliasType", "args": [], "type_ref": "cv2.<PERSON>.<PERSON>"}, "builtins.int", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Rect", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "color": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "cv2.gapi.wip.draw.Rect.color", "name": "color", "type": {".class": "TypeAliasType", "args": [], "type_ref": "cv2.<PERSON>.<PERSON>"}}}, "lt": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "cv2.gapi.wip.draw.Rect.lt", "name": "lt", "type": "builtins.int"}}, "rect": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "cv2.gapi.wip.draw.Rect.rect", "name": "rect", "type": {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.Rect"}}}, "shift": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "cv2.gapi.wip.draw.Rect.shift", "name": "shift", "type": "builtins.int"}}, "thick": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "cv2.gapi.wip.draw.Rect.thick", "name": "thick", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cv2.gapi.wip.draw.Rect.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cv2.gapi.wip.draw.Rect", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Text": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "cv2.gapi.wip.draw.Text", "name": "Text", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "cv2.gapi.wip.draw.Text", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "cv2.gapi.wip.draw", "mro": ["cv2.gapi.wip.draw.Text", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "cv2.gapi.wip.draw.Text.__init__", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 1, 1, 1], "arg_names": ["self", "text_", "org_", "ff_", "fs_", "color_", "thick_", "lt_", "bottom_left_origin_"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.gapi.wip.draw.Text.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 1, 1, 1], "arg_names": ["self", "text_", "org_", "ff_", "fs_", "color_", "thick_", "lt_", "bottom_left_origin_"], "arg_types": ["cv2.gapi.wip.draw.Text", "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.Point"}, "builtins.int", "builtins.float", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.<PERSON>.<PERSON>"}, "builtins.int", "builtins.int", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Text", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.gapi.wip.draw.Text.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 1, 1, 1], "arg_names": ["self", "text_", "org_", "ff_", "fs_", "color_", "thick_", "lt_", "bottom_left_origin_"], "arg_types": ["cv2.gapi.wip.draw.Text", "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.Point"}, "builtins.int", "builtins.float", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.<PERSON>.<PERSON>"}, "builtins.int", "builtins.int", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Text", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.gapi.wip.draw.Text.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cv2.gapi.wip.draw.Text"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Text", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.gapi.wip.draw.Text.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cv2.gapi.wip.draw.Text"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Text", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 1, 1, 1], "arg_names": ["self", "text_", "org_", "ff_", "fs_", "color_", "thick_", "lt_", "bottom_left_origin_"], "arg_types": ["cv2.gapi.wip.draw.Text", "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.Point"}, "builtins.int", "builtins.float", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.<PERSON>.<PERSON>"}, "builtins.int", "builtins.int", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Text", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cv2.gapi.wip.draw.Text"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Text", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "bottom_left_origin": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "cv2.gapi.wip.draw.Text.bottom_left_origin", "name": "bottom_left_origin", "type": "builtins.bool"}}, "color": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "cv2.gapi.wip.draw.Text.color", "name": "color", "type": {".class": "TypeAliasType", "args": [], "type_ref": "cv2.<PERSON>.<PERSON>"}}}, "ff": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "cv2.gapi.wip.draw.Text.ff", "name": "ff", "type": "builtins.int"}}, "fs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "cv2.gapi.wip.draw.Text.fs", "name": "fs", "type": "builtins.float"}}, "lt": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "cv2.gapi.wip.draw.Text.lt", "name": "lt", "type": "builtins.int"}}, "org": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "cv2.gapi.wip.draw.Text.org", "name": "org", "type": {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.Point"}}}, "text": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "cv2.gapi.wip.draw.Text.text", "name": "text", "type": "builtins.str"}}, "thick": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "cv2.gapi.wip.draw.Text.thick", "name": "thick", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cv2.gapi.wip.draw.Text.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cv2.gapi.wip.draw.Text", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "cv2.gapi.wip.draw.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.gapi.wip.draw.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.gapi.wip.draw.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.gapi.wip.draw.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.gapi.wip.draw.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.gapi.wip.draw.__package__", "name": "__package__", "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.gapi.wip.draw.__path__", "name": "__path__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.gapi.wip.draw.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_typing": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef", "module_hidden": true, "module_public": false}, "cv2": {".class": "SymbolTableNode", "cross_ref": "cv2", "kind": "Gdef", "module_hidden": true, "module_public": false}, "render": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "cv2.gapi.wip.draw.render", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["bgr", "prims", "args"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.gapi.wip.draw.render", "name": "render", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["bgr", "prims", "args"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.Prim"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "Instance", "args": ["cv2.GCompileArg"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "render", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.gapi.wip.draw.render", "name": "render", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["bgr", "prims", "args"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.Prim"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "Instance", "args": ["cv2.GCompileArg"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "render", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["y_plane", "uv_plane", "prims", "args"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.gapi.wip.draw.render", "name": "render", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["y_plane", "uv_plane", "prims", "args"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.Prim"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "Instance", "args": ["cv2.GCompileArg"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "render", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.gapi.wip.draw.render", "name": "render", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["y_plane", "uv_plane", "prims", "args"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.Prim"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "Instance", "args": ["cv2.GCompileArg"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "render", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["bgr", "prims", "args"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.Prim"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "Instance", "args": ["cv2.GCompileArg"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "render", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["y_plane", "uv_plane", "prims", "args"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.Prim"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "Instance", "args": ["cv2.GCompileArg"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "render", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "render3ch": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["src", "prims"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.gapi.wip.draw.render3ch", "name": "render3ch", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["src", "prims"], "arg_types": ["cv2.GMat", "cv2.GArrayT"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "render3ch", "ret_type": "cv2.GMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "renderNV12": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["y", "uv", "prims"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.gapi.wip.draw.renderNV12", "name": "renderNV12", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["y", "uv", "prims"], "arg_types": ["cv2.GMat", "cv2.GMat", "cv2.GArrayT"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "renderNV12", "ret_type": {".class": "TupleType", "implicit": false, "items": ["cv2.GMat", "cv2.GMat"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "path": "c:\\Python313\\Lib\\site-packages\\cv2\\gapi\\wip\\draw\\__init__.pyi"}