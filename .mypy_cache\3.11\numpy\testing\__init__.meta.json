{"data_mtime": 1750100846, "dep_lines": [4, 3, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 5, 5, 30, 30, 30], "dependencies": ["numpy.testing._private.utils", "numpy.testing.overrides", "unittest", "builtins", "_frozen_importlib", "abc", "typing"], "hash": "80f178f1985ade35808ec508db9ea3dd3a32ce64", "id": "numpy.testing", "ignore_all": true, "interface_hash": "0f57176c8844c4919717b22ebb26be7f28583b72", "mtime": 1748795461, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Python313\\Lib\\site-packages\\numpy\\testing\\__init__.pyi", "plugin_data": null, "size": 2147, "suppressed": [], "version_id": "1.15.0"}