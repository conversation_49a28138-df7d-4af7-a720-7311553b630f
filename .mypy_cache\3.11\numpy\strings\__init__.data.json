{".class": "MypyFile", "_fullname": "numpy.strings", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "numpy.strings.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.strings.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.strings.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.strings.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.strings.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.strings.__package__", "name": "__package__", "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.strings.__path__", "name": "__path__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.strings.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "add": {".class": "SymbolTableNode", "cross_ref": "numpy._core.strings.add", "kind": "Gdef"}, "capitalize": {".class": "SymbolTableNode", "cross_ref": "numpy._core.strings.capitalize", "kind": "Gdef"}, "center": {".class": "SymbolTableNode", "cross_ref": "numpy._core.strings.center", "kind": "Gdef"}, "count": {".class": "SymbolTableNode", "cross_ref": "numpy._core.strings.count", "kind": "Gdef"}, "decode": {".class": "SymbolTableNode", "cross_ref": "numpy._core.strings.decode", "kind": "Gdef"}, "encode": {".class": "SymbolTableNode", "cross_ref": "numpy._core.strings.encode", "kind": "Gdef"}, "endswith": {".class": "SymbolTableNode", "cross_ref": "numpy._core.strings.endswith", "kind": "Gdef"}, "equal": {".class": "SymbolTableNode", "cross_ref": "numpy._core.strings.equal", "kind": "Gdef"}, "expandtabs": {".class": "SymbolTableNode", "cross_ref": "numpy._core.strings.expandtabs", "kind": "Gdef"}, "find": {".class": "SymbolTableNode", "cross_ref": "numpy._core.strings.find", "kind": "Gdef"}, "greater": {".class": "SymbolTableNode", "cross_ref": "numpy._core.strings.greater", "kind": "Gdef"}, "greater_equal": {".class": "SymbolTableNode", "cross_ref": "numpy._core.strings.greater_equal", "kind": "Gdef"}, "index": {".class": "SymbolTableNode", "cross_ref": "numpy._core.strings.index", "kind": "Gdef"}, "isalnum": {".class": "SymbolTableNode", "cross_ref": "numpy._core.strings.isalnum", "kind": "Gdef"}, "isalpha": {".class": "SymbolTableNode", "cross_ref": "numpy._core.strings.isalpha", "kind": "Gdef"}, "isdecimal": {".class": "SymbolTableNode", "cross_ref": "numpy._core.strings.isdecimal", "kind": "Gdef"}, "isdigit": {".class": "SymbolTableNode", "cross_ref": "numpy._core.strings.isdigit", "kind": "Gdef"}, "islower": {".class": "SymbolTableNode", "cross_ref": "numpy._core.strings.islower", "kind": "Gdef"}, "isnumeric": {".class": "SymbolTableNode", "cross_ref": "numpy._core.strings.isnumeric", "kind": "Gdef"}, "isspace": {".class": "SymbolTableNode", "cross_ref": "numpy._core.strings.isspace", "kind": "Gdef"}, "istitle": {".class": "SymbolTableNode", "cross_ref": "numpy._core.strings.istitle", "kind": "Gdef"}, "isupper": {".class": "SymbolTableNode", "cross_ref": "numpy._core.strings.isupper", "kind": "Gdef"}, "less": {".class": "SymbolTableNode", "cross_ref": "numpy._core.strings.less", "kind": "Gdef"}, "less_equal": {".class": "SymbolTableNode", "cross_ref": "numpy._core.strings.less_equal", "kind": "Gdef"}, "ljust": {".class": "SymbolTableNode", "cross_ref": "numpy._core.strings.ljust", "kind": "Gdef"}, "lower": {".class": "SymbolTableNode", "cross_ref": "numpy._core.strings.lower", "kind": "Gdef"}, "lstrip": {".class": "SymbolTableNode", "cross_ref": "numpy._core.strings.lstrip", "kind": "Gdef"}, "mod": {".class": "SymbolTableNode", "cross_ref": "numpy._core.strings.mod", "kind": "Gdef"}, "multiply": {".class": "SymbolTableNode", "cross_ref": "numpy._core.strings.multiply", "kind": "Gdef"}, "not_equal": {".class": "SymbolTableNode", "cross_ref": "numpy._core.strings.not_equal", "kind": "Gdef"}, "partition": {".class": "SymbolTableNode", "cross_ref": "numpy._core.strings.partition", "kind": "Gdef"}, "replace": {".class": "SymbolTableNode", "cross_ref": "numpy._core.strings.replace", "kind": "Gdef"}, "rfind": {".class": "SymbolTableNode", "cross_ref": "numpy._core.strings.rfind", "kind": "Gdef"}, "rindex": {".class": "SymbolTableNode", "cross_ref": "numpy._core.strings.rindex", "kind": "Gdef"}, "rjust": {".class": "SymbolTableNode", "cross_ref": "numpy._core.strings.rjust", "kind": "Gdef"}, "rpartition": {".class": "SymbolTableNode", "cross_ref": "numpy._core.strings.rpartition", "kind": "Gdef"}, "rstrip": {".class": "SymbolTableNode", "cross_ref": "numpy._core.strings.rstrip", "kind": "Gdef"}, "startswith": {".class": "SymbolTableNode", "cross_ref": "numpy._core.strings.startswith", "kind": "Gdef"}, "str_len": {".class": "SymbolTableNode", "cross_ref": "numpy._core.strings.str_len", "kind": "Gdef"}, "strip": {".class": "SymbolTableNode", "cross_ref": "numpy._core.strings.strip", "kind": "Gdef"}, "swapcase": {".class": "SymbolTableNode", "cross_ref": "numpy._core.strings.swapcase", "kind": "Gdef"}, "title": {".class": "SymbolTableNode", "cross_ref": "numpy._core.strings.title", "kind": "Gdef"}, "translate": {".class": "SymbolTableNode", "cross_ref": "numpy._core.strings.translate", "kind": "Gdef"}, "upper": {".class": "SymbolTableNode", "cross_ref": "numpy._core.strings.upper", "kind": "Gdef"}, "zfill": {".class": "SymbolTableNode", "cross_ref": "numpy._core.strings.zfill", "kind": "Gdef"}}, "path": "c:\\Python313\\Lib\\site-packages\\numpy\\strings\\__init__.pyi"}