{"data_mtime": 1750100861, "dep_lines": [6, 7, 8, 9, 10, 11, 1, 2, 3, 4, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 10, 10, 5, 5, 5, 30, 30, 30, 30], "dependencies": ["pydantic.v1.config", "pydantic.v1.fields", "pydantic.v1.main", "pydantic.v1.types", "pydantic.v1.typing", "pydantic.v1.utils", "os", "warnings", "pathlib", "typing", "builtins", "_frozen_importlib", "abc", "enum", "typing_extensions"], "hash": "57945b11348a6253aae0421eed6edf464655452e", "id": "pydantic.v1.env_settings", "ignore_all": true, "interface_hash": "e3de58993cd1a87439a01be9b2e99fa499a48c76", "mtime": 1748795478, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Python313\\Lib\\site-packages\\pydantic\\v1\\env_settings.py", "plugin_data": null, "size": 14105, "suppressed": [], "version_id": "1.15.0"}