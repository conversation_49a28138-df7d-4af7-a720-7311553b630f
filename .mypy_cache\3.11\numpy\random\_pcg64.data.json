{".class": "MypyFile", "_fullname": "numpy.random._pcg64", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "BitGenerator": {".class": "SymbolTableNode", "cross_ref": "numpy.random.bit_generator.BitGenerator", "kind": "Gdef", "module_hidden": true, "module_public": false}, "PCG64": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["numpy.random.bit_generator.BitGenerator"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy.random._pcg64.PCG64", "name": "PCG64", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "numpy.random._pcg64.PCG64", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "numpy.random._pcg64", "mro": ["numpy.random._pcg64.PCG64", "numpy.random.bit_generator.BitGenerator", "numpy.random.bit_generator._CythonMixin", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "seed"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.random._pcg64.PCG64.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "seed"], "arg_types": ["numpy.random._pcg64.PCG64", {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}, "numpy.random.bit_generator.SeedSequence"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of PCG64", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "advance": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "delta"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.random._pcg64.PCG64.advance", "name": "advance", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "delta"], "arg_types": ["numpy.random._pcg64.PCG64", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "advance of PCG64", "ret_type": "numpy.random._pcg64.PCG64", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "jumped": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "jumps"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.random._pcg64.PCG64.jumped", "name": "jumped", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "jumps"], "arg_types": ["numpy.random._pcg64.PCG64", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "jumped of PCG64", "ret_type": "numpy.random._pcg64.PCG64", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "state": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": ["is_property"], "fullname": "numpy.random._pcg64.PCG64.state", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_overload", "is_decorated"], "fullname": "numpy.random._pcg64.PCG64.state", "name": "state", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["numpy.random._pcg64.PCG64"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "state of PCG64", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy.random._pcg64._PCG64State"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_settable_property", "is_ready", "is_inferred"], "fullname": "numpy.random._pcg64.PCG64.state", "name": "state", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["numpy.random._pcg64.PCG64"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "state of PCG64", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy.random._pcg64._PCG64State"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "numpy.random._pcg64.PCG64.state", "name": "state", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "value"], "arg_types": ["numpy.random._pcg64.PCG64", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.random._pcg64._PCG64State"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "state of PCG64", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_inferred"], "fullname": "", "name": "state", "type": null}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["numpy.random._pcg64.PCG64"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "state of PCG64", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy.random._pcg64._PCG64State"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.random._pcg64.PCG64.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "numpy.random._pcg64.PCG64", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "PCG64DXSM": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["numpy.random.bit_generator.BitGenerator"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy.random._pcg64.PCG64DXSM", "name": "PCG64DXSM", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "numpy.random._pcg64.PCG64DXSM", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "numpy.random._pcg64", "mro": ["numpy.random._pcg64.PCG64DXSM", "numpy.random.bit_generator.BitGenerator", "numpy.random.bit_generator._CythonMixin", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "seed"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.random._pcg64.PCG64DXSM.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "seed"], "arg_types": ["numpy.random._pcg64.PCG64DXSM", {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}, "numpy.random.bit_generator.SeedSequence"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of PCG64DXSM", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "advance": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "delta"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.random._pcg64.PCG64DXSM.advance", "name": "advance", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "delta"], "arg_types": ["numpy.random._pcg64.PCG64DXSM", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "advance of PCG64DXSM", "ret_type": "numpy.random._pcg64.PCG64DXSM", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "jumped": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "jumps"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.random._pcg64.PCG64DXSM.jumped", "name": "jumped", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "jumps"], "arg_types": ["numpy.random._pcg64.PCG64DXSM", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "jumped of PCG64DXSM", "ret_type": "numpy.random._pcg64.PCG64DXSM", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "state": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": ["is_property"], "fullname": "numpy.random._pcg64.PCG64DXSM.state", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_overload", "is_decorated"], "fullname": "numpy.random._pcg64.PCG64DXSM.state", "name": "state", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["numpy.random._pcg64.PCG64DXSM"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "state of PCG64DXSM", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy.random._pcg64._PCG64State"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_settable_property", "is_ready", "is_inferred"], "fullname": "numpy.random._pcg64.PCG64DXSM.state", "name": "state", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["numpy.random._pcg64.PCG64DXSM"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "state of PCG64DXSM", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy.random._pcg64._PCG64State"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "numpy.random._pcg64.PCG64DXSM.state", "name": "state", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "value"], "arg_types": ["numpy.random._pcg64.PCG64DXSM", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.random._pcg64._PCG64State"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "state of PCG64DXSM", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_inferred"], "fullname": "", "name": "state", "type": null}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["numpy.random._pcg64.PCG64DXSM"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "state of PCG64DXSM", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy.random._pcg64._PCG64State"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.random._pcg64.PCG64DXSM.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "numpy.random._pcg64.PCG64DXSM", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SeedSequence": {".class": "SymbolTableNode", "cross_ref": "numpy.random.bit_generator.SeedSequence", "kind": "Gdef", "module_hidden": true, "module_public": false}, "TypedDict": {".class": "SymbolTableNode", "cross_ref": "typing.TypedDict", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_ArrayLikeInt_co": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._array_like._ArrayLikeInt_co", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_PCG64Internal": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy.random._pcg64._PCG64Internal", "name": "_PCG64Internal", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "numpy.random._pcg64._PCG64Internal", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "numpy.random._pcg64", "mro": ["numpy.random._pcg64._PCG64Internal", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["state", "builtins.int"], ["inc", "builtins.int"]], "readonly_keys": [], "required_keys": ["inc", "state"]}}}, "_PCG64State": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy.random._pcg64._PCG64State", "name": "_PCG64State", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "numpy.random._pcg64._PCG64State", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "numpy.random._pcg64", "mro": ["numpy.random._pcg64._PCG64State", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["bit_generator", "builtins.str"], ["state", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.random._pcg64._PCG64Internal"}], ["has_uint32", "builtins.int"], ["<PERSON><PERSON><PERSON><PERSON>", "builtins.int"]], "readonly_keys": [], "required_keys": ["bit_generator", "has_uint32", "state", "<PERSON><PERSON><PERSON><PERSON>"]}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.random._pcg64.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.random._pcg64.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.random._pcg64.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.random._pcg64.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.random._pcg64.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.random._pcg64.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "type_check_only": {".class": "SymbolTableNode", "cross_ref": "typing.type_check_only", "kind": "Gdef", "module_hidden": true, "module_public": false}}, "path": "c:\\Python313\\Lib\\site-packages\\numpy\\random\\_pcg64.pyi"}