{"data_mtime": 1750100859, "dep_lines": [7, 14, 3, 5, 6, 8, 9, 10, 12, 14, 1, 1, 1, 1], "dep_prios": [5, 10, 5, 10, 10, 5, 5, 5, 5, 20, 5, 30, 30, 30], "dependencies": ["collections.abc", "typing_inspection.typing_objects", "__future__", "sys", "types", "dataclasses", "enum", "typing", "typing_extensions", "typing_inspection", "builtins", "_frozen_importlib", "_typeshed", "abc"], "hash": "d02b8c9f32a9f45e624c03df4531e3e5d1d22dae", "id": "typing_inspection.introspection", "ignore_all": true, "interface_hash": "eb3e0fadfb6adc3ede29ec58041abfba2bcd0828", "mtime": 1748795465, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Python313\\Lib\\site-packages\\typing_inspection\\introspection.py", "plugin_data": null, "size": 22534, "suppressed": [], "version_id": "1.15.0"}