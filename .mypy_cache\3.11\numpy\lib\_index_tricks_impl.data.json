{".class": "MypyFile", "_fullname": "numpy.lib._index_tricks_impl", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ArrayLike": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._array_like.ArrayLike", "kind": "Gdef", "module_hidden": true, "module_public": false}, "AxisConcatenator": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy.lib._index_tricks_impl.AxisConcatenator", "name": "AxisConcatenator", "type_vars": [{".class": "TypeVarType", "default": {".class": "LiteralType", "fallback": "builtins.int", "value": 0}, "fullname": "numpy.lib._index_tricks_impl._AxisT_co", "id": 1, "name": "_AxisT_co", "namespace": "numpy.lib._index_tricks_impl.AxisConcatenator", "upper_bound": "builtins.int", "values": [], "variance": 1}, {".class": "TypeVarType", "default": {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, "fullname": "numpy.lib._index_tricks_impl._MatrixT_co", "id": 2, "name": "_MatrixT_co", "namespace": "numpy.lib._index_tricks_impl.AxisConcatenator", "upper_bound": "builtins.bool", "values": [], "variance": 1}, {".class": "TypeVarType", "default": {".class": "LiteralType", "fallback": "builtins.int", "value": 1}, "fullname": "numpy.lib._index_tricks_impl._NDMinT_co", "id": 3, "name": "_NDMinT_co", "namespace": "numpy.lib._index_tricks_impl.AxisConcatenator", "upper_bound": "builtins.int", "values": [], "variance": 1}, {".class": "TypeVarType", "default": {".class": "LiteralType", "fallback": "builtins.int", "value": -1}, "fullname": "numpy.lib._index_tricks_impl._Trans1DT_co", "id": 4, "name": "_Trans1DT_co", "namespace": "numpy.lib._index_tricks_impl.AxisConcatenator", "upper_bound": "builtins.int", "values": [], "variance": 1}]}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "numpy.lib._index_tricks_impl.AxisConcatenator", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "numpy.lib._index_tricks_impl", "mro": ["numpy.lib._index_tricks_impl.AxisConcatenator", "builtins.object"], "names": {".class": "SymbolTable", "__getitem__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.lib._index_tricks_impl.AxisConcatenator.__getitem__", "name": "__getitem__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "LiteralType", "fallback": "builtins.int", "value": 0}, "fullname": "numpy.lib._index_tricks_impl._AxisT_co", "id": 1, "name": "_AxisT_co", "namespace": "numpy.lib._index_tricks_impl.AxisConcatenator", "upper_bound": "builtins.int", "values": [], "variance": 1}, {".class": "TypeVarType", "default": {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, "fullname": "numpy.lib._index_tricks_impl._MatrixT_co", "id": 2, "name": "_MatrixT_co", "namespace": "numpy.lib._index_tricks_impl.AxisConcatenator", "upper_bound": "builtins.bool", "values": [], "variance": 1}, {".class": "TypeVarType", "default": {".class": "LiteralType", "fallback": "builtins.int", "value": 1}, "fullname": "numpy.lib._index_tricks_impl._NDMinT_co", "id": 3, "name": "_NDMinT_co", "namespace": "numpy.lib._index_tricks_impl.AxisConcatenator", "upper_bound": "builtins.int", "values": [], "variance": 1}, {".class": "TypeVarType", "default": {".class": "LiteralType", "fallback": "builtins.int", "value": -1}, "fullname": "numpy.lib._index_tricks_impl._Trans1DT_co", "id": 4, "name": "_Trans1DT_co", "namespace": "numpy.lib._index_tricks_impl.AxisConcatenator", "upper_bound": "builtins.int", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.lib._index_tricks_impl.AxisConcatenator"}, {".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.Incomplete"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__getitem__ of AxisConcatenator", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.Incomplete"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1], "arg_names": [null, "axis", "matrix", "ndmin", "trans1d"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.lib._index_tricks_impl.AxisConcatenator.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1], "arg_names": [null, "axis", "matrix", "ndmin", "trans1d"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "LiteralType", "fallback": "builtins.int", "value": 0}, "fullname": "numpy.lib._index_tricks_impl._AxisT_co", "id": 1, "name": "_AxisT_co", "namespace": "numpy.lib._index_tricks_impl.AxisConcatenator", "upper_bound": "builtins.int", "values": [], "variance": 1}, {".class": "TypeVarType", "default": {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, "fullname": "numpy.lib._index_tricks_impl._MatrixT_co", "id": 2, "name": "_MatrixT_co", "namespace": "numpy.lib._index_tricks_impl.AxisConcatenator", "upper_bound": "builtins.bool", "values": [], "variance": 1}, {".class": "TypeVarType", "default": {".class": "LiteralType", "fallback": "builtins.int", "value": 1}, "fullname": "numpy.lib._index_tricks_impl._NDMinT_co", "id": 3, "name": "_NDMinT_co", "namespace": "numpy.lib._index_tricks_impl.AxisConcatenator", "upper_bound": "builtins.int", "values": [], "variance": 1}, {".class": "TypeVarType", "default": {".class": "LiteralType", "fallback": "builtins.int", "value": -1}, "fullname": "numpy.lib._index_tricks_impl._Trans1DT_co", "id": 4, "name": "_Trans1DT_co", "namespace": "numpy.lib._index_tricks_impl.AxisConcatenator", "upper_bound": "builtins.int", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.lib._index_tricks_impl.AxisConcatenator"}, {".class": "TypeVarType", "default": {".class": "LiteralType", "fallback": "builtins.int", "value": 0}, "fullname": "numpy.lib._index_tricks_impl._AxisT_co", "id": 1, "name": "_AxisT_co", "namespace": "numpy.lib._index_tricks_impl.AxisConcatenator", "upper_bound": "builtins.int", "values": [], "variance": 1}, {".class": "TypeVarType", "default": {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, "fullname": "numpy.lib._index_tricks_impl._MatrixT_co", "id": 2, "name": "_MatrixT_co", "namespace": "numpy.lib._index_tricks_impl.AxisConcatenator", "upper_bound": "builtins.bool", "values": [], "variance": 1}, {".class": "TypeVarType", "default": {".class": "LiteralType", "fallback": "builtins.int", "value": 1}, "fullname": "numpy.lib._index_tricks_impl._NDMinT_co", "id": 3, "name": "_NDMinT_co", "namespace": "numpy.lib._index_tricks_impl.AxisConcatenator", "upper_bound": "builtins.int", "values": [], "variance": 1}, {".class": "TypeVarType", "default": {".class": "LiteralType", "fallback": "builtins.int", "value": -1}, "fullname": "numpy.lib._index_tricks_impl._Trans1DT_co", "id": 4, "name": "_Trans1DT_co", "namespace": "numpy.lib._index_tricks_impl.AxisConcatenator", "upper_bound": "builtins.int", "values": [], "variance": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of AxisConcatenator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__len__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.lib._index_tricks_impl.AxisConcatenator.__len__", "name": "__len__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "LiteralType", "fallback": "builtins.int", "value": 0}, "fullname": "numpy.lib._index_tricks_impl._AxisT_co", "id": 1, "name": "_AxisT_co", "namespace": "numpy.lib._index_tricks_impl.AxisConcatenator", "upper_bound": "builtins.int", "values": [], "variance": 1}, {".class": "TypeVarType", "default": {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, "fullname": "numpy.lib._index_tricks_impl._MatrixT_co", "id": 2, "name": "_MatrixT_co", "namespace": "numpy.lib._index_tricks_impl.AxisConcatenator", "upper_bound": "builtins.bool", "values": [], "variance": 1}, {".class": "TypeVarType", "default": {".class": "LiteralType", "fallback": "builtins.int", "value": 1}, "fullname": "numpy.lib._index_tricks_impl._NDMinT_co", "id": 3, "name": "_NDMinT_co", "namespace": "numpy.lib._index_tricks_impl.AxisConcatenator", "upper_bound": "builtins.int", "values": [], "variance": 1}, {".class": "TypeVarType", "default": {".class": "LiteralType", "fallback": "builtins.int", "value": -1}, "fullname": "numpy.lib._index_tricks_impl._Trans1DT_co", "id": 4, "name": "_Trans1DT_co", "namespace": "numpy.lib._index_tricks_impl.AxisConcatenator", "upper_bound": "builtins.int", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.lib._index_tricks_impl.AxisConcatenator"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__len__ of AxisConcatenator", "ret_type": {".class": "LiteralType", "fallback": "builtins.int", "value": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "numpy.lib._index_tricks_impl.AxisConcatenator.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "axis": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "numpy.lib._index_tricks_impl.AxisConcatenator.axis", "name": "axis", "type": {".class": "TypeVarType", "default": {".class": "LiteralType", "fallback": "builtins.int", "value": 0}, "fullname": "numpy.lib._index_tricks_impl._AxisT_co", "id": 1, "name": "_AxisT_co", "namespace": "numpy.lib._index_tricks_impl.AxisConcatenator", "upper_bound": "builtins.int", "values": [], "variance": 1}}}, "concatenate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": ["is_static"], "fullname": "numpy.lib._index_tricks_impl.AxisConcatenator.concatenate", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [2, 5, 3], "arg_names": ["a", "axis", "out"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_overload", "is_decorated"], "fullname": "numpy.lib._index_tricks_impl.AxisConcatenator.concatenate", "name": "concatenate", "type": {".class": "CallableType", "arg_kinds": [2, 5, 3], "arg_names": ["a", "axis", "out"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "UnionType", "items": ["typing.SupportsIndex", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._index_tricks_impl._ArrayT", "id": -1, "name": "_ArrayT", "namespace": "numpy.lib._index_tricks_impl.AxisConcatenator.concatenate#0", "upper_bound": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._shape._Shape"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.dtype"}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}, "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "concatenate of AxisConcatenator", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._index_tricks_impl._ArrayT", "id": -1, "name": "_ArrayT", "namespace": "numpy.lib._index_tricks_impl.AxisConcatenator.concatenate#0", "upper_bound": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._shape._Shape"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.dtype"}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._index_tricks_impl._ArrayT", "id": -1, "name": "_ArrayT", "namespace": "numpy.lib._index_tricks_impl.AxisConcatenator.concatenate#0", "upper_bound": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._shape._Shape"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.dtype"}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}, "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready", "is_inferred"], "fullname": "numpy.lib._index_tricks_impl.AxisConcatenator.concatenate", "name": "concatenate", "type": {".class": "CallableType", "arg_kinds": [2, 5, 3], "arg_names": ["a", "axis", "out"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "UnionType", "items": ["typing.SupportsIndex", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._index_tricks_impl._ArrayT", "id": -1, "name": "_ArrayT", "namespace": "numpy.lib._index_tricks_impl.AxisConcatenator.concatenate#0", "upper_bound": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._shape._Shape"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.dtype"}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}, "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "concatenate of AxisConcatenator", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._index_tricks_impl._ArrayT", "id": -1, "name": "_ArrayT", "namespace": "numpy.lib._index_tricks_impl.AxisConcatenator.concatenate#0", "upper_bound": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._shape._Shape"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.dtype"}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._index_tricks_impl._ArrayT", "id": -1, "name": "_ArrayT", "namespace": "numpy.lib._index_tricks_impl.AxisConcatenator.concatenate#0", "upper_bound": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._shape._Shape"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.dtype"}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}, "values": [], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [2, 5, 5], "arg_names": ["a", "axis", "out"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_overload", "is_decorated"], "fullname": "numpy.lib._index_tricks_impl.AxisConcatenator.concatenate", "name": "concatenate", "type": {".class": "CallableType", "arg_kinds": [2, 5, 5], "arg_names": ["a", "axis", "out"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "UnionType", "items": ["typing.SupportsIndex", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "NoneType"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "concatenate of AxisConcatenator", "ret_type": {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready", "is_inferred"], "fullname": "numpy.lib._index_tricks_impl.AxisConcatenator.concatenate", "name": "concatenate", "type": {".class": "CallableType", "arg_kinds": [2, 5, 5], "arg_names": ["a", "axis", "out"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "UnionType", "items": ["typing.SupportsIndex", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "NoneType"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "concatenate of AxisConcatenator", "ret_type": {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [2, 5, 3], "arg_names": ["a", "axis", "out"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "UnionType", "items": ["typing.SupportsIndex", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._index_tricks_impl._ArrayT", "id": -1, "name": "_ArrayT", "namespace": "numpy.lib._index_tricks_impl.AxisConcatenator.concatenate#0", "upper_bound": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._shape._Shape"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.dtype"}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}, "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "concatenate of AxisConcatenator", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._index_tricks_impl._ArrayT", "id": -1, "name": "_ArrayT", "namespace": "numpy.lib._index_tricks_impl.AxisConcatenator.concatenate#0", "upper_bound": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._shape._Shape"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.dtype"}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._index_tricks_impl._ArrayT", "id": -1, "name": "_ArrayT", "namespace": "numpy.lib._index_tricks_impl.AxisConcatenator.concatenate#0", "upper_bound": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._shape._Shape"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.dtype"}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}, "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [2, 5, 5], "arg_names": ["a", "axis", "out"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "UnionType", "items": ["typing.SupportsIndex", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "NoneType"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "concatenate of AxisConcatenator", "ret_type": {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "makemat": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready"], "fullname": "numpy.lib._index_tricks_impl.AxisConcatenator.makemat", "name": "makemat", "type": {".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.dtype"}], "extra_attrs": null, "type_ref": "numpy.matrix"}}}}, "matrix": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "numpy.lib._index_tricks_impl.AxisConcatenator.matrix", "name": "matrix", "type": {".class": "TypeVarType", "default": {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, "fullname": "numpy.lib._index_tricks_impl._MatrixT_co", "id": 2, "name": "_MatrixT_co", "namespace": "numpy.lib._index_tricks_impl.AxisConcatenator", "upper_bound": "builtins.bool", "values": [], "variance": 1}}}, "ndmin": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "numpy.lib._index_tricks_impl.AxisConcatenator.ndmin", "name": "ndmin", "type": {".class": "TypeVarType", "default": {".class": "LiteralType", "fallback": "builtins.int", "value": 1}, "fullname": "numpy.lib._index_tricks_impl._NDMinT_co", "id": 3, "name": "_NDMinT_co", "namespace": "numpy.lib._index_tricks_impl.AxisConcatenator", "upper_bound": "builtins.int", "values": [], "variance": 1}}}, "trans1d": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "numpy.lib._index_tricks_impl.AxisConcatenator.trans1d", "name": "trans1d", "type": {".class": "TypeVarType", "default": {".class": "LiteralType", "fallback": "builtins.int", "value": -1}, "fullname": "numpy.lib._index_tricks_impl._Trans1DT_co", "id": 4, "name": "_Trans1DT_co", "namespace": "numpy.lib._index_tricks_impl.AxisConcatenator", "upper_bound": "builtins.int", "values": [], "variance": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._index_tricks_impl.AxisConcatenator.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "LiteralType", "fallback": "builtins.int", "value": 0}, "fullname": "numpy.lib._index_tricks_impl._AxisT_co", "id": 1, "name": "_AxisT_co", "namespace": "numpy.lib._index_tricks_impl.AxisConcatenator", "upper_bound": "builtins.int", "values": [], "variance": 1}, {".class": "TypeVarType", "default": {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, "fullname": "numpy.lib._index_tricks_impl._MatrixT_co", "id": 2, "name": "_MatrixT_co", "namespace": "numpy.lib._index_tricks_impl.AxisConcatenator", "upper_bound": "builtins.bool", "values": [], "variance": 1}, {".class": "TypeVarType", "default": {".class": "LiteralType", "fallback": "builtins.int", "value": 1}, "fullname": "numpy.lib._index_tricks_impl._NDMinT_co", "id": 3, "name": "_NDMinT_co", "namespace": "numpy.lib._index_tricks_impl.AxisConcatenator", "upper_bound": "builtins.int", "values": [], "variance": 1}, {".class": "TypeVarType", "default": {".class": "LiteralType", "fallback": "builtins.int", "value": -1}, "fullname": "numpy.lib._index_tricks_impl._Trans1DT_co", "id": 4, "name": "_Trans1DT_co", "namespace": "numpy.lib._index_tricks_impl.AxisConcatenator", "upper_bound": "builtins.int", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.lib._index_tricks_impl.AxisConcatenator"}, "values": [], "variance": 0}, "slots": ["axis", "matrix", "ndmin", "trans1d"], "tuple_type": null, "type_vars": ["_AxisT_co", "_MatrixT_co", "_NDMinT_co", "_Trans1DT_co"], "typeddict_type": null}}, "CClass": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.int", "value": -1}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "LiteralType", "fallback": "builtins.int", "value": 2}, {".class": "LiteralType", "fallback": "builtins.int", "value": 0}], "extra_attrs": null, "type_ref": "numpy.lib._index_tricks_impl.AxisConcatenator"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy.lib._index_tricks_impl.CClass", "name": "CClass", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_final"], "fullname": "numpy.lib._index_tricks_impl.CClass", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "numpy.lib._index_tricks_impl", "mro": ["numpy.lib._index_tricks_impl.CClass", "numpy.lib._index_tricks_impl.AxisConcatenator", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.lib._index_tricks_impl.CClass.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["numpy.lib._index_tricks_impl.CClass"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of CClass", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._index_tricks_impl.CClass.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "numpy.lib._index_tricks_impl.CClass", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ClassVar": {".class": "SymbolTableNode", "cross_ref": "typing.ClassVar", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Final": {".class": "SymbolTableNode", "cross_ref": "typing.Final", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Generic": {".class": "SymbolTableNode", "cross_ref": "typing.Generic", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Incomplete": {".class": "SymbolTableNode", "cross_ref": "_typeshed.Incomplete", "kind": "Gdef", "module_hidden": true, "module_public": false}, "IndexExpression": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy.lib._index_tricks_impl.IndexExpression", "name": "IndexExpression", "type_vars": [{".class": "TypeVarType", "default": "builtins.bool", "fullname": "numpy.lib._index_tricks_impl._BoolT_co", "id": 1, "name": "_BoolT_co", "namespace": "numpy.lib._index_tricks_impl.IndexExpression", "upper_bound": "builtins.bool", "values": [], "variance": 1}]}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "numpy.lib._index_tricks_impl.IndexExpression", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "numpy.lib._index_tricks_impl", "mro": ["numpy.lib._index_tricks_impl.IndexExpression", "builtins.object"], "names": {".class": "SymbolTable", "__getitem__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "numpy.lib._index_tricks_impl.IndexExpression.__getitem__", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.lib._index_tricks_impl.IndexExpression.__getitem__", "name": "__getitem__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.bool", "fullname": "numpy.lib._index_tricks_impl._BoolT_co", "id": 1, "name": "_BoolT_co", "namespace": "numpy.lib._index_tricks_impl.IndexExpression", "upper_bound": "builtins.bool", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.lib._index_tricks_impl.IndexExpression"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._index_tricks_impl._TupleT", "id": -1, "name": "_TupleT", "namespace": "numpy.lib._index_tricks_impl.IndexExpression.__getitem__#0", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}, "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__getitem__ of IndexExpression", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._index_tricks_impl._TupleT", "id": -1, "name": "_TupleT", "namespace": "numpy.lib._index_tricks_impl.IndexExpression.__getitem__#0", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._index_tricks_impl._TupleT", "id": -1, "name": "_TupleT", "namespace": "numpy.lib._index_tricks_impl.IndexExpression.__getitem__#0", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}, "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.lib._index_tricks_impl.IndexExpression.__getitem__", "name": "__getitem__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.bool", "fullname": "numpy.lib._index_tricks_impl._BoolT_co", "id": 1, "name": "_BoolT_co", "namespace": "numpy.lib._index_tricks_impl.IndexExpression", "upper_bound": "builtins.bool", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.lib._index_tricks_impl.IndexExpression"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._index_tricks_impl._TupleT", "id": -1, "name": "_TupleT", "namespace": "numpy.lib._index_tricks_impl.IndexExpression.__getitem__#0", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}, "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__getitem__ of IndexExpression", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._index_tricks_impl._TupleT", "id": -1, "name": "_TupleT", "namespace": "numpy.lib._index_tricks_impl.IndexExpression.__getitem__#0", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._index_tricks_impl._TupleT", "id": -1, "name": "_TupleT", "namespace": "numpy.lib._index_tricks_impl.IndexExpression.__getitem__#0", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}, "values": [], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.lib._index_tricks_impl.IndexExpression.__getitem__", "name": "__getitem__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.bool", "value": true}], "extra_attrs": null, "type_ref": "numpy.lib._index_tricks_impl.IndexExpression"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._index_tricks_impl._T", "id": -1, "name": "_T", "namespace": "numpy.lib._index_tricks_impl.IndexExpression.__getitem__#1", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__getitem__ of IndexExpression", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._index_tricks_impl._T", "id": -1, "name": "_T", "namespace": "numpy.lib._index_tricks_impl.IndexExpression.__getitem__#1", "upper_bound": "builtins.object", "values": [], "variance": 0}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._index_tricks_impl._T", "id": -1, "name": "_T", "namespace": "numpy.lib._index_tricks_impl.IndexExpression.__getitem__#1", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.lib._index_tricks_impl.IndexExpression.__getitem__", "name": "__getitem__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.bool", "value": true}], "extra_attrs": null, "type_ref": "numpy.lib._index_tricks_impl.IndexExpression"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._index_tricks_impl._T", "id": -1, "name": "_T", "namespace": "numpy.lib._index_tricks_impl.IndexExpression.__getitem__#1", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__getitem__ of IndexExpression", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._index_tricks_impl._T", "id": -1, "name": "_T", "namespace": "numpy.lib._index_tricks_impl.IndexExpression.__getitem__#1", "upper_bound": "builtins.object", "values": [], "variance": 0}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._index_tricks_impl._T", "id": -1, "name": "_T", "namespace": "numpy.lib._index_tricks_impl.IndexExpression.__getitem__#1", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.lib._index_tricks_impl.IndexExpression.__getitem__", "name": "__getitem__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.bool", "value": false}], "extra_attrs": null, "type_ref": "numpy.lib._index_tricks_impl.IndexExpression"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._index_tricks_impl._T", "id": -1, "name": "_T", "namespace": "numpy.lib._index_tricks_impl.IndexExpression.__getitem__", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__getitem__ of IndexExpression", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._index_tricks_impl._T", "id": -1, "name": "_T", "namespace": "numpy.lib._index_tricks_impl.IndexExpression.__getitem__", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._index_tricks_impl._T", "id": -1, "name": "_T", "namespace": "numpy.lib._index_tricks_impl.IndexExpression.__getitem__", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.lib._index_tricks_impl.IndexExpression.__getitem__", "name": "__getitem__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.bool", "value": false}], "extra_attrs": null, "type_ref": "numpy.lib._index_tricks_impl.IndexExpression"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._index_tricks_impl._T", "id": -1, "name": "_T", "namespace": "numpy.lib._index_tricks_impl.IndexExpression.__getitem__", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__getitem__ of IndexExpression", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._index_tricks_impl._T", "id": -1, "name": "_T", "namespace": "numpy.lib._index_tricks_impl.IndexExpression.__getitem__", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._index_tricks_impl._T", "id": -1, "name": "_T", "namespace": "numpy.lib._index_tricks_impl.IndexExpression.__getitem__", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.bool", "fullname": "numpy.lib._index_tricks_impl._BoolT_co", "id": 1, "name": "_BoolT_co", "namespace": "numpy.lib._index_tricks_impl.IndexExpression", "upper_bound": "builtins.bool", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.lib._index_tricks_impl.IndexExpression"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._index_tricks_impl._TupleT", "id": -1, "name": "_TupleT", "namespace": "numpy.lib._index_tricks_impl.IndexExpression.__getitem__#0", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}, "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__getitem__ of IndexExpression", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._index_tricks_impl._TupleT", "id": -1, "name": "_TupleT", "namespace": "numpy.lib._index_tricks_impl.IndexExpression.__getitem__#0", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._index_tricks_impl._TupleT", "id": -1, "name": "_TupleT", "namespace": "numpy.lib._index_tricks_impl.IndexExpression.__getitem__#0", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}, "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.bool", "value": true}], "extra_attrs": null, "type_ref": "numpy.lib._index_tricks_impl.IndexExpression"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._index_tricks_impl._T", "id": -1, "name": "_T", "namespace": "numpy.lib._index_tricks_impl.IndexExpression.__getitem__#1", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__getitem__ of IndexExpression", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._index_tricks_impl._T", "id": -1, "name": "_T", "namespace": "numpy.lib._index_tricks_impl.IndexExpression.__getitem__#1", "upper_bound": "builtins.object", "values": [], "variance": 0}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._index_tricks_impl._T", "id": -1, "name": "_T", "namespace": "numpy.lib._index_tricks_impl.IndexExpression.__getitem__#1", "upper_bound": "builtins.object", "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.bool", "value": false}], "extra_attrs": null, "type_ref": "numpy.lib._index_tricks_impl.IndexExpression"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._index_tricks_impl._T", "id": -1, "name": "_T", "namespace": "numpy.lib._index_tricks_impl.IndexExpression.__getitem__", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__getitem__ of IndexExpression", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._index_tricks_impl._T", "id": -1, "name": "_T", "namespace": "numpy.lib._index_tricks_impl.IndexExpression.__getitem__", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._index_tricks_impl._T", "id": -1, "name": "_T", "namespace": "numpy.lib._index_tricks_impl.IndexExpression.__getitem__", "upper_bound": "builtins.object", "values": [], "variance": 0}]}]}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "maketuple"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.lib._index_tricks_impl.IndexExpression.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "maketuple"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.bool", "fullname": "numpy.lib._index_tricks_impl._BoolT_co", "id": 1, "name": "_BoolT_co", "namespace": "numpy.lib._index_tricks_impl.IndexExpression", "upper_bound": "builtins.bool", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.lib._index_tricks_impl.IndexExpression"}, {".class": "TypeVarType", "default": "builtins.bool", "fullname": "numpy.lib._index_tricks_impl._BoolT_co", "id": 1, "name": "_BoolT_co", "namespace": "numpy.lib._index_tricks_impl.IndexExpression", "upper_bound": "builtins.bool", "values": [], "variance": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of IndexExpression", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "maketuple": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "numpy.lib._index_tricks_impl.IndexExpression.maketuple", "name": "maketuple", "type": {".class": "TypeVarType", "default": "builtins.bool", "fullname": "numpy.lib._index_tricks_impl._BoolT_co", "id": 1, "name": "_BoolT_co", "namespace": "numpy.lib._index_tricks_impl.IndexExpression", "upper_bound": "builtins.bool", "values": [], "variance": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._index_tricks_impl.IndexExpression.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.bool", "fullname": "numpy.lib._index_tricks_impl._BoolT_co", "id": 1, "name": "_BoolT_co", "namespace": "numpy.lib._index_tricks_impl.IndexExpression", "upper_bound": "builtins.bool", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.lib._index_tricks_impl.IndexExpression"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["_BoolT_co"], "typeddict_type": null}}, "L": {".class": "SymbolTableNode", "cross_ref": "typing.Literal", "kind": "Gdef", "module_hidden": true, "module_public": false}, "MGridClass": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.bool", "value": false}], "extra_attrs": null, "type_ref": "numpy.lib._index_tricks_impl.nd_grid"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy.lib._index_tricks_impl.MGridClass", "name": "MGridClass", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_final"], "fullname": "numpy.lib._index_tricks_impl.MGridClass", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "numpy.lib._index_tricks_impl", "mro": ["numpy.lib._index_tricks_impl.MGridClass", "numpy.lib._index_tricks_impl.nd_grid", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.lib._index_tricks_impl.MGridClass.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["numpy.lib._index_tricks_impl.MGridClass"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of MGridClass", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._index_tricks_impl.MGridClass.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "numpy.lib._index_tricks_impl.MGridClass", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "NDArray": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._array_like.NDArray", "kind": "Gdef", "module_hidden": true, "module_public": false}, "OGridClass": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.bool", "value": true}], "extra_attrs": null, "type_ref": "numpy.lib._index_tricks_impl.nd_grid"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy.lib._index_tricks_impl.OGridClass", "name": "OGridClass", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_final"], "fullname": "numpy.lib._index_tricks_impl.OGridClass", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "numpy.lib._index_tricks_impl", "mro": ["numpy.lib._index_tricks_impl.OGridClass", "numpy.lib._index_tricks_impl.nd_grid", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.lib._index_tricks_impl.OGridClass.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["numpy.lib._index_tricks_impl.OGridClass"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of OGridClass", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._index_tricks_impl.OGridClass.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "numpy.lib._index_tricks_impl.OGridClass", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "RClass": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.int", "value": 0}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "LiteralType", "fallback": "builtins.int", "value": 1}, {".class": "LiteralType", "fallback": "builtins.int", "value": -1}], "extra_attrs": null, "type_ref": "numpy.lib._index_tricks_impl.AxisConcatenator"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy.lib._index_tricks_impl.RClass", "name": "RClass", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_final"], "fullname": "numpy.lib._index_tricks_impl.RClass", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "numpy.lib._index_tricks_impl", "mro": ["numpy.lib._index_tricks_impl.RClass", "numpy.lib._index_tricks_impl.AxisConcatenator", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.lib._index_tricks_impl.RClass.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["numpy.lib._index_tricks_impl.RClass"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of RClass", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._index_tricks_impl.RClass.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "numpy.lib._index_tricks_impl.RClass", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Self": {".class": "SymbolTableNode", "cross_ref": "typing.Self", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef", "module_hidden": true, "module_public": false}, "SupportsIndex": {".class": "SymbolTableNode", "cross_ref": "typing.SupportsIndex", "kind": "Gdef", "module_hidden": true, "module_public": false}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.TypeVar", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_ArrayT": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._index_tricks_impl._ArrayT", "name": "_ArrayT", "upper_bound": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._shape._Shape"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.dtype"}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}, "values": [], "variance": 0}}, "_AxisT_co": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeVarExpr", "default": {".class": "LiteralType", "fallback": "builtins.int", "value": 0}, "fullname": "numpy.lib._index_tricks_impl._AxisT_co", "name": "_AxisT_co", "upper_bound": "builtins.int", "values": [], "variance": 1}}, "_BoolT_co": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeVarExpr", "default": "builtins.bool", "fullname": "numpy.lib._index_tricks_impl._BoolT_co", "name": "_BoolT_co", "upper_bound": "builtins.bool", "values": [], "variance": 1}}, "_DTypeT": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._index_tricks_impl._DTypeT", "name": "_DTypeT", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.dtype"}, "values": [], "variance": 0}}, "_FiniteNestedSequence": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._array_like._FiniteNestedSequence", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_MatrixT_co": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeVarExpr", "default": {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, "fullname": "numpy.lib._index_tricks_impl._MatrixT_co", "name": "_MatrixT_co", "upper_bound": "builtins.bool", "values": [], "variance": 1}}, "_NDMinT_co": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeVarExpr", "default": {".class": "LiteralType", "fallback": "builtins.int", "value": 1}, "fullname": "numpy.lib._index_tricks_impl._NDMinT_co", "name": "_NDMinT_co", "upper_bound": "builtins.int", "values": [], "variance": 1}}, "_NestedSequence": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._nested_sequence._NestedSequence", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_ScalarT": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._index_tricks_impl._ScalarT", "name": "_ScalarT", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}}, "_ScalarT_co": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._index_tricks_impl._ScalarT_co", "name": "_ScalarT_co", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 1}}, "_Shape": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._shape._Shape", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_SupportsArray": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._array_like._SupportsArray", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_SupportsDType": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._dtype_like._SupportsDType", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_T": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._index_tricks_impl._T", "name": "_T", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "_Trans1DT_co": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeVarExpr", "default": {".class": "LiteralType", "fallback": "builtins.int", "value": -1}, "fullname": "numpy.lib._index_tricks_impl._Trans1DT_co", "name": "_Trans1DT_co", "upper_bound": "builtins.int", "values": [], "variance": 1}}, "_TupleT": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._index_tricks_impl._TupleT", "name": "_TupleT", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}, "values": [], "variance": 0}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "numpy.lib._index_tricks_impl.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.lib._index_tricks_impl.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.lib._index_tricks_impl.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.lib._index_tricks_impl.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.lib._index_tricks_impl.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.lib._index_tricks_impl.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.lib._index_tricks_impl.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "c_": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready", "has_explicit_value"], "fullname": "numpy.lib._index_tricks_impl.c_", "name": "c_", "type": "numpy.lib._index_tricks_impl.CClass"}}, "deprecated": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.deprecated", "kind": "Gdef", "module_hidden": true, "module_public": false}, "diag_indices": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["n", "ndim"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.lib._index_tricks_impl.diag_indices", "name": "diag_indices", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["n", "ndim"], "arg_types": ["builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "diag_indices", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}], "extra_attrs": null, "type_ref": "builtins.tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "diag_indices_from": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["arr"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.lib._index_tricks_impl.diag_indices_from", "name": "diag_indices_from", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["arr"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "diag_indices_from", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}], "extra_attrs": null, "type_ref": "builtins.tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "fill_diagonal": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["a", "val", "wrap"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.lib._index_tricks_impl.fill_diagonal", "name": "fill_diagonal", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["a", "val", "wrap"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, "builtins.object", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "fill_diagonal", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "final": {".class": "SymbolTableNode", "cross_ref": "typing.final", "kind": "Gdef", "module_hidden": true, "module_public": false}, "index_exp": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready", "has_explicit_value"], "fullname": "numpy.lib._index_tricks_impl.index_exp", "name": "index_exp", "type": {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.bool", "value": true}], "extra_attrs": null, "type_ref": "numpy.lib._index_tricks_impl.IndexExpression"}}}, "ix_": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "numpy.lib._index_tricks_impl.ix_", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [2], "arg_names": ["args"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.lib._index_tricks_impl.ix_", "name": "ix_", "type": {".class": "CallableType", "arg_kinds": [2], "arg_names": ["args"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._index_tricks_impl._DTypeT", "id": -1, "name": "_DTypeT", "namespace": "numpy.lib._index_tricks_impl.ix_#0", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.dtype"}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._dtype_like._SupportsDType"}], "type_ref": "numpy._typing._array_like._FiniteNestedSequence"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ix_", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._shape._Shape"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._index_tricks_impl._DTypeT", "id": -1, "name": "_DTypeT", "namespace": "numpy.lib._index_tricks_impl.ix_#0", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.dtype"}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}], "extra_attrs": null, "type_ref": "builtins.tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._index_tricks_impl._DTypeT", "id": -1, "name": "_DTypeT", "namespace": "numpy.lib._index_tricks_impl.ix_#0", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.dtype"}, "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.lib._index_tricks_impl.ix_", "name": "ix_", "type": {".class": "CallableType", "arg_kinds": [2], "arg_names": ["args"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._index_tricks_impl._DTypeT", "id": -1, "name": "_DTypeT", "namespace": "numpy.lib._index_tricks_impl.ix_#0", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.dtype"}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._dtype_like._SupportsDType"}], "type_ref": "numpy._typing._array_like._FiniteNestedSequence"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ix_", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._shape._Shape"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._index_tricks_impl._DTypeT", "id": -1, "name": "_DTypeT", "namespace": "numpy.lib._index_tricks_impl.ix_#0", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.dtype"}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}], "extra_attrs": null, "type_ref": "builtins.tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._index_tricks_impl._DTypeT", "id": -1, "name": "_DTypeT", "namespace": "numpy.lib._index_tricks_impl.ix_#0", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.dtype"}, "values": [], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [2], "arg_names": ["args"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.lib._index_tricks_impl.ix_", "name": "ix_", "type": {".class": "CallableType", "arg_kinds": [2], "arg_names": ["args"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "numpy._typing._nested_sequence._NestedSequence"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ix_", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": ["numpy.str_"], "type_ref": "numpy._typing._array_like.NDArray"}], "extra_attrs": null, "type_ref": "builtins.tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.lib._index_tricks_impl.ix_", "name": "ix_", "type": {".class": "CallableType", "arg_kinds": [2], "arg_names": ["args"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "numpy._typing._nested_sequence._NestedSequence"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ix_", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": ["numpy.str_"], "type_ref": "numpy._typing._array_like.NDArray"}], "extra_attrs": null, "type_ref": "builtins.tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [2], "arg_names": ["args"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.lib._index_tricks_impl.ix_", "name": "ix_", "type": {".class": "CallableType", "arg_kinds": [2], "arg_names": ["args"], "arg_types": [{".class": "UnionType", "items": ["builtins.bytes", {".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "numpy._typing._nested_sequence._NestedSequence"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ix_", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": ["numpy.bytes_"], "type_ref": "numpy._typing._array_like.NDArray"}], "extra_attrs": null, "type_ref": "builtins.tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.lib._index_tricks_impl.ix_", "name": "ix_", "type": {".class": "CallableType", "arg_kinds": [2], "arg_names": ["args"], "arg_types": [{".class": "UnionType", "items": ["builtins.bytes", {".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "numpy._typing._nested_sequence._NestedSequence"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ix_", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": ["numpy.bytes_"], "type_ref": "numpy._typing._array_like.NDArray"}], "extra_attrs": null, "type_ref": "builtins.tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [2], "arg_names": ["args"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.lib._index_tricks_impl.ix_", "name": "ix_", "type": {".class": "CallableType", "arg_kinds": [2], "arg_names": ["args"], "arg_types": [{".class": "UnionType", "items": ["builtins.bool", {".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "numpy._typing._nested_sequence._NestedSequence"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ix_", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [{".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "numpy.bool"}], "type_ref": "numpy._typing._array_like.NDArray"}], "extra_attrs": null, "type_ref": "builtins.tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.lib._index_tricks_impl.ix_", "name": "ix_", "type": {".class": "CallableType", "arg_kinds": [2], "arg_names": ["args"], "arg_types": [{".class": "UnionType", "items": ["builtins.bool", {".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "numpy._typing._nested_sequence._NestedSequence"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ix_", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [{".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "numpy.bool"}], "type_ref": "numpy._typing._array_like.NDArray"}], "extra_attrs": null, "type_ref": "builtins.tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [2], "arg_names": ["args"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.lib._index_tricks_impl.ix_", "name": "ix_", "type": {".class": "CallableType", "arg_kinds": [2], "arg_names": ["args"], "arg_types": [{".class": "UnionType", "items": ["builtins.int", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "numpy._typing._nested_sequence._NestedSequence"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ix_", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}], "extra_attrs": null, "type_ref": "builtins.tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.lib._index_tricks_impl.ix_", "name": "ix_", "type": {".class": "CallableType", "arg_kinds": [2], "arg_names": ["args"], "arg_types": [{".class": "UnionType", "items": ["builtins.int", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "numpy._typing._nested_sequence._NestedSequence"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ix_", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}], "extra_attrs": null, "type_ref": "builtins.tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [2], "arg_names": ["args"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.lib._index_tricks_impl.ix_", "name": "ix_", "type": {".class": "CallableType", "arg_kinds": [2], "arg_names": ["args"], "arg_types": [{".class": "UnionType", "items": ["builtins.float", {".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "numpy._typing._nested_sequence._NestedSequence"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ix_", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": ["numpy.float64"], "type_ref": "numpy._typing._array_like.NDArray"}], "extra_attrs": null, "type_ref": "builtins.tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.lib._index_tricks_impl.ix_", "name": "ix_", "type": {".class": "CallableType", "arg_kinds": [2], "arg_names": ["args"], "arg_types": [{".class": "UnionType", "items": ["builtins.float", {".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "numpy._typing._nested_sequence._NestedSequence"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ix_", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": ["numpy.float64"], "type_ref": "numpy._typing._array_like.NDArray"}], "extra_attrs": null, "type_ref": "builtins.tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [2], "arg_names": ["args"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.lib._index_tricks_impl.ix_", "name": "ix_", "type": {".class": "CallableType", "arg_kinds": [2], "arg_names": ["args"], "arg_types": [{".class": "UnionType", "items": ["builtins.complex", {".class": "Instance", "args": ["builtins.complex"], "extra_attrs": null, "type_ref": "numpy._typing._nested_sequence._NestedSequence"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ix_", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": ["numpy.complex128"], "type_ref": "numpy._typing._array_like.NDArray"}], "extra_attrs": null, "type_ref": "builtins.tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.lib._index_tricks_impl.ix_", "name": "ix_", "type": {".class": "CallableType", "arg_kinds": [2], "arg_names": ["args"], "arg_types": [{".class": "UnionType", "items": ["builtins.complex", {".class": "Instance", "args": ["builtins.complex"], "extra_attrs": null, "type_ref": "numpy._typing._nested_sequence._NestedSequence"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ix_", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": ["numpy.complex128"], "type_ref": "numpy._typing._array_like.NDArray"}], "extra_attrs": null, "type_ref": "builtins.tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [2], "arg_names": ["args"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._index_tricks_impl._DTypeT", "id": -1, "name": "_DTypeT", "namespace": "numpy.lib._index_tricks_impl.ix_#0", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.dtype"}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._dtype_like._SupportsDType"}], "type_ref": "numpy._typing._array_like._FiniteNestedSequence"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ix_", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._shape._Shape"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._index_tricks_impl._DTypeT", "id": -1, "name": "_DTypeT", "namespace": "numpy.lib._index_tricks_impl.ix_#0", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.dtype"}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}], "extra_attrs": null, "type_ref": "builtins.tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._index_tricks_impl._DTypeT", "id": -1, "name": "_DTypeT", "namespace": "numpy.lib._index_tricks_impl.ix_#0", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.dtype"}, "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [2], "arg_names": ["args"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "numpy._typing._nested_sequence._NestedSequence"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ix_", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": ["numpy.str_"], "type_ref": "numpy._typing._array_like.NDArray"}], "extra_attrs": null, "type_ref": "builtins.tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [2], "arg_names": ["args"], "arg_types": [{".class": "UnionType", "items": ["builtins.bytes", {".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "numpy._typing._nested_sequence._NestedSequence"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ix_", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": ["numpy.bytes_"], "type_ref": "numpy._typing._array_like.NDArray"}], "extra_attrs": null, "type_ref": "builtins.tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [2], "arg_names": ["args"], "arg_types": [{".class": "UnionType", "items": ["builtins.bool", {".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "numpy._typing._nested_sequence._NestedSequence"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ix_", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [{".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "numpy.bool"}], "type_ref": "numpy._typing._array_like.NDArray"}], "extra_attrs": null, "type_ref": "builtins.tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [2], "arg_names": ["args"], "arg_types": [{".class": "UnionType", "items": ["builtins.int", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "numpy._typing._nested_sequence._NestedSequence"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ix_", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}], "extra_attrs": null, "type_ref": "builtins.tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [2], "arg_names": ["args"], "arg_types": [{".class": "UnionType", "items": ["builtins.float", {".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "numpy._typing._nested_sequence._NestedSequence"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ix_", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": ["numpy.float64"], "type_ref": "numpy._typing._array_like.NDArray"}], "extra_attrs": null, "type_ref": "builtins.tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [2], "arg_names": ["args"], "arg_types": [{".class": "UnionType", "items": ["builtins.complex", {".class": "Instance", "args": ["builtins.complex"], "extra_attrs": null, "type_ref": "numpy._typing._nested_sequence._NestedSequence"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ix_", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": ["numpy.complex128"], "type_ref": "numpy._typing._array_like.NDArray"}], "extra_attrs": null, "type_ref": "builtins.tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "mgrid": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready", "has_explicit_value"], "fullname": "numpy.lib._index_tricks_impl.mgrid", "name": "mgrid", "type": "numpy.lib._index_tricks_impl.MGridClass"}}, "nd_grid": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy.lib._index_tricks_impl.nd_grid", "name": "nd_grid", "type_vars": [{".class": "TypeVarType", "default": "builtins.bool", "fullname": "numpy.lib._index_tricks_impl._BoolT_co", "id": 1, "name": "_BoolT_co", "namespace": "numpy.lib._index_tricks_impl.nd_grid", "upper_bound": "builtins.bool", "values": [], "variance": 1}]}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "numpy.lib._index_tricks_impl.nd_grid", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "numpy.lib._index_tricks_impl", "mro": ["numpy.lib._index_tricks_impl.nd_grid", "builtins.object"], "names": {".class": "SymbolTable", "__getitem__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "numpy.lib._index_tricks_impl.nd_grid.__getitem__", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.lib._index_tricks_impl.nd_grid.__getitem__", "name": "__getitem__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.bool", "value": false}], "extra_attrs": null, "type_ref": "numpy.lib._index_tricks_impl.nd_grid"}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.slice"}, {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.slice"}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__getitem__ of nd_grid", "ret_type": {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.lib._index_tricks_impl.nd_grid.__getitem__", "name": "__getitem__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.bool", "value": false}], "extra_attrs": null, "type_ref": "numpy.lib._index_tricks_impl.nd_grid"}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.slice"}, {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.slice"}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__getitem__ of nd_grid", "ret_type": {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.lib._index_tricks_impl.nd_grid.__getitem__", "name": "__getitem__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.bool", "value": true}], "extra_attrs": null, "type_ref": "numpy.lib._index_tricks_impl.nd_grid"}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.slice"}, {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.slice"}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__getitem__ of nd_grid", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}], "extra_attrs": null, "type_ref": "builtins.tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.lib._index_tricks_impl.nd_grid.__getitem__", "name": "__getitem__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.bool", "value": true}], "extra_attrs": null, "type_ref": "numpy.lib._index_tricks_impl.nd_grid"}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.slice"}, {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.slice"}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__getitem__ of nd_grid", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}], "extra_attrs": null, "type_ref": "builtins.tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.bool", "value": false}], "extra_attrs": null, "type_ref": "numpy.lib._index_tricks_impl.nd_grid"}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.slice"}, {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.slice"}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__getitem__ of nd_grid", "ret_type": {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.bool", "value": true}], "extra_attrs": null, "type_ref": "numpy.lib._index_tricks_impl.nd_grid"}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.slice"}, {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.slice"}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__getitem__ of nd_grid", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}], "extra_attrs": null, "type_ref": "builtins.tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "sparse"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.lib._index_tricks_impl.nd_grid.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "sparse"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.bool", "fullname": "numpy.lib._index_tricks_impl._BoolT_co", "id": 1, "name": "_BoolT_co", "namespace": "numpy.lib._index_tricks_impl.nd_grid", "upper_bound": "builtins.bool", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.lib._index_tricks_impl.nd_grid"}, {".class": "TypeVarType", "default": "builtins.bool", "fullname": "numpy.lib._index_tricks_impl._BoolT_co", "id": 1, "name": "_BoolT_co", "namespace": "numpy.lib._index_tricks_impl.nd_grid", "upper_bound": "builtins.bool", "values": [], "variance": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of nd_grid", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "sparse": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "numpy.lib._index_tricks_impl.nd_grid.sparse", "name": "sparse", "type": {".class": "TypeVarType", "default": "builtins.bool", "fullname": "numpy.lib._index_tricks_impl._BoolT_co", "id": 1, "name": "_BoolT_co", "namespace": "numpy.lib._index_tricks_impl.nd_grid", "upper_bound": "builtins.bool", "values": [], "variance": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._index_tricks_impl.nd_grid.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.bool", "fullname": "numpy.lib._index_tricks_impl._BoolT_co", "id": 1, "name": "_BoolT_co", "namespace": "numpy.lib._index_tricks_impl.nd_grid", "upper_bound": "builtins.bool", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.lib._index_tricks_impl.nd_grid"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["_BoolT_co"], "typeddict_type": null}}, "ndenumerate": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy.lib._index_tricks_impl.ndenumerate", "name": "ndenumerate", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._index_tricks_impl._ScalarT_co", "id": 1, "name": "_ScalarT_co", "namespace": "numpy.lib._index_tricks_impl.ndenumerate", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 1}]}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "numpy.lib._index_tricks_impl.ndenumerate", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "numpy.lib._index_tricks_impl", "mro": ["numpy.lib._index_tricks_impl.ndenumerate", "builtins.object"], "names": {".class": "SymbolTable", "__iter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.lib._index_tricks_impl.ndenumerate.__iter__", "name": "__iter__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._index_tricks_impl.ndenumerate.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._index_tricks_impl._ScalarT_co", "id": 1, "name": "_ScalarT_co", "namespace": "numpy.lib._index_tricks_impl.ndenumerate", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.lib._index_tricks_impl.ndenumerate"}, "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__iter__ of ndenumerate", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._index_tricks_impl.ndenumerate.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._index_tricks_impl._ScalarT_co", "id": 1, "name": "_ScalarT_co", "namespace": "numpy.lib._index_tricks_impl.ndenumerate", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.lib._index_tricks_impl.ndenumerate"}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._index_tricks_impl.ndenumerate.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._index_tricks_impl._ScalarT_co", "id": 1, "name": "_ScalarT_co", "namespace": "numpy.lib._index_tricks_impl.ndenumerate", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.lib._index_tricks_impl.ndenumerate"}, "values": [], "variance": 0}]}}}, "__new__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": ["is_static"], "fullname": "numpy.lib._index_tricks_impl.ndenumerate.__new__", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "arr"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_overload", "is_decorated"], "fullname": "numpy.lib._index_tricks_impl.ndenumerate.__new__", "name": "__new__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "arr"], "arg_types": [{".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._index_tricks_impl._ScalarT_co", "id": 1, "name": "_ScalarT_co", "namespace": "numpy.lib._index_tricks_impl.ndenumerate", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.lib._index_tricks_impl.ndenumerate"}}, {".class": "TypeAliasType", "args": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._index_tricks_impl._ScalarT", "id": -1, "name": "_ScalarT", "namespace": "numpy.lib._index_tricks_impl.ndenumerate.__new__#0", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy.dtype"}], "extra_attrs": null, "type_ref": "numpy._typing._array_like._SupportsArray"}], "type_ref": "numpy._typing._array_like._FiniteNestedSequence"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__new__ of ndenumerate", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._index_tricks_impl._ScalarT", "id": -1, "name": "_ScalarT", "namespace": "numpy.lib._index_tricks_impl.ndenumerate.__new__#0", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy.lib._index_tricks_impl.ndenumerate"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._index_tricks_impl._ScalarT", "id": -1, "name": "_ScalarT", "namespace": "numpy.lib._index_tricks_impl.ndenumerate.__new__#0", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.lib._index_tricks_impl.ndenumerate.__new__", "name": "__new__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "arr"], "arg_types": [{".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._index_tricks_impl._ScalarT_co", "id": 1, "name": "_ScalarT_co", "namespace": "numpy.lib._index_tricks_impl.ndenumerate", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.lib._index_tricks_impl.ndenumerate"}}, {".class": "TypeAliasType", "args": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._index_tricks_impl._ScalarT", "id": -1, "name": "_ScalarT", "namespace": "numpy.lib._index_tricks_impl.ndenumerate.__new__#0", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy.dtype"}], "extra_attrs": null, "type_ref": "numpy._typing._array_like._SupportsArray"}], "type_ref": "numpy._typing._array_like._FiniteNestedSequence"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__new__ of ndenumerate", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._index_tricks_impl._ScalarT", "id": -1, "name": "_ScalarT", "namespace": "numpy.lib._index_tricks_impl.ndenumerate.__new__#0", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy.lib._index_tricks_impl.ndenumerate"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._index_tricks_impl._ScalarT", "id": -1, "name": "_ScalarT", "namespace": "numpy.lib._index_tricks_impl.ndenumerate.__new__#0", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "arr"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_overload", "is_decorated"], "fullname": "numpy.lib._index_tricks_impl.ndenumerate.__new__", "name": "__new__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "arr"], "arg_types": [{".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._index_tricks_impl._ScalarT_co", "id": 1, "name": "_ScalarT_co", "namespace": "numpy.lib._index_tricks_impl.ndenumerate", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.lib._index_tricks_impl.ndenumerate"}}, {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "numpy._typing._nested_sequence._NestedSequence"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__new__ of ndenumerate", "ret_type": {".class": "Instance", "args": ["numpy.str_"], "extra_attrs": null, "type_ref": "numpy.lib._index_tricks_impl.ndenumerate"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.lib._index_tricks_impl.ndenumerate.__new__", "name": "__new__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "arr"], "arg_types": [{".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._index_tricks_impl._ScalarT_co", "id": 1, "name": "_ScalarT_co", "namespace": "numpy.lib._index_tricks_impl.ndenumerate", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.lib._index_tricks_impl.ndenumerate"}}, {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "numpy._typing._nested_sequence._NestedSequence"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__new__ of ndenumerate", "ret_type": {".class": "Instance", "args": ["numpy.str_"], "extra_attrs": null, "type_ref": "numpy.lib._index_tricks_impl.ndenumerate"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "arr"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_overload", "is_decorated"], "fullname": "numpy.lib._index_tricks_impl.ndenumerate.__new__", "name": "__new__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "arr"], "arg_types": [{".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._index_tricks_impl._ScalarT_co", "id": 1, "name": "_ScalarT_co", "namespace": "numpy.lib._index_tricks_impl.ndenumerate", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.lib._index_tricks_impl.ndenumerate"}}, {".class": "UnionType", "items": ["builtins.bytes", {".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "numpy._typing._nested_sequence._NestedSequence"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__new__ of ndenumerate", "ret_type": {".class": "Instance", "args": ["numpy.bytes_"], "extra_attrs": null, "type_ref": "numpy.lib._index_tricks_impl.ndenumerate"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.lib._index_tricks_impl.ndenumerate.__new__", "name": "__new__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "arr"], "arg_types": [{".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._index_tricks_impl._ScalarT_co", "id": 1, "name": "_ScalarT_co", "namespace": "numpy.lib._index_tricks_impl.ndenumerate", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.lib._index_tricks_impl.ndenumerate"}}, {".class": "UnionType", "items": ["builtins.bytes", {".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "numpy._typing._nested_sequence._NestedSequence"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__new__ of ndenumerate", "ret_type": {".class": "Instance", "args": ["numpy.bytes_"], "extra_attrs": null, "type_ref": "numpy.lib._index_tricks_impl.ndenumerate"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "arr"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_overload", "is_decorated"], "fullname": "numpy.lib._index_tricks_impl.ndenumerate.__new__", "name": "__new__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "arr"], "arg_types": [{".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._index_tricks_impl._ScalarT_co", "id": 1, "name": "_ScalarT_co", "namespace": "numpy.lib._index_tricks_impl.ndenumerate", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.lib._index_tricks_impl.ndenumerate"}}, {".class": "UnionType", "items": ["builtins.bool", {".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "numpy._typing._nested_sequence._NestedSequence"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__new__ of ndenumerate", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "numpy.bool"}], "extra_attrs": null, "type_ref": "numpy.lib._index_tricks_impl.ndenumerate"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.lib._index_tricks_impl.ndenumerate.__new__", "name": "__new__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "arr"], "arg_types": [{".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._index_tricks_impl._ScalarT_co", "id": 1, "name": "_ScalarT_co", "namespace": "numpy.lib._index_tricks_impl.ndenumerate", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.lib._index_tricks_impl.ndenumerate"}}, {".class": "UnionType", "items": ["builtins.bool", {".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "numpy._typing._nested_sequence._NestedSequence"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__new__ of ndenumerate", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "numpy.bool"}], "extra_attrs": null, "type_ref": "numpy.lib._index_tricks_impl.ndenumerate"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "arr"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_overload", "is_decorated"], "fullname": "numpy.lib._index_tricks_impl.ndenumerate.__new__", "name": "__new__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "arr"], "arg_types": [{".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._index_tricks_impl._ScalarT_co", "id": 1, "name": "_ScalarT_co", "namespace": "numpy.lib._index_tricks_impl.ndenumerate", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.lib._index_tricks_impl.ndenumerate"}}, {".class": "UnionType", "items": ["builtins.int", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "numpy._typing._nested_sequence._NestedSequence"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__new__ of ndenumerate", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "extra_attrs": null, "type_ref": "numpy.lib._index_tricks_impl.ndenumerate"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.lib._index_tricks_impl.ndenumerate.__new__", "name": "__new__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "arr"], "arg_types": [{".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._index_tricks_impl._ScalarT_co", "id": 1, "name": "_ScalarT_co", "namespace": "numpy.lib._index_tricks_impl.ndenumerate", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.lib._index_tricks_impl.ndenumerate"}}, {".class": "UnionType", "items": ["builtins.int", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "numpy._typing._nested_sequence._NestedSequence"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__new__ of ndenumerate", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "extra_attrs": null, "type_ref": "numpy.lib._index_tricks_impl.ndenumerate"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "arr"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_overload", "is_decorated"], "fullname": "numpy.lib._index_tricks_impl.ndenumerate.__new__", "name": "__new__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "arr"], "arg_types": [{".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._index_tricks_impl._ScalarT_co", "id": 1, "name": "_ScalarT_co", "namespace": "numpy.lib._index_tricks_impl.ndenumerate", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.lib._index_tricks_impl.ndenumerate"}}, {".class": "UnionType", "items": ["builtins.float", {".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "numpy._typing._nested_sequence._NestedSequence"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__new__ of ndenumerate", "ret_type": {".class": "Instance", "args": ["numpy.float64"], "extra_attrs": null, "type_ref": "numpy.lib._index_tricks_impl.ndenumerate"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.lib._index_tricks_impl.ndenumerate.__new__", "name": "__new__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "arr"], "arg_types": [{".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._index_tricks_impl._ScalarT_co", "id": 1, "name": "_ScalarT_co", "namespace": "numpy.lib._index_tricks_impl.ndenumerate", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.lib._index_tricks_impl.ndenumerate"}}, {".class": "UnionType", "items": ["builtins.float", {".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "numpy._typing._nested_sequence._NestedSequence"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__new__ of ndenumerate", "ret_type": {".class": "Instance", "args": ["numpy.float64"], "extra_attrs": null, "type_ref": "numpy.lib._index_tricks_impl.ndenumerate"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "arr"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_overload", "is_decorated"], "fullname": "numpy.lib._index_tricks_impl.ndenumerate.__new__", "name": "__new__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "arr"], "arg_types": [{".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._index_tricks_impl._ScalarT_co", "id": 1, "name": "_ScalarT_co", "namespace": "numpy.lib._index_tricks_impl.ndenumerate", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.lib._index_tricks_impl.ndenumerate"}}, {".class": "UnionType", "items": ["builtins.complex", {".class": "Instance", "args": ["builtins.complex"], "extra_attrs": null, "type_ref": "numpy._typing._nested_sequence._NestedSequence"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__new__ of ndenumerate", "ret_type": {".class": "Instance", "args": ["numpy.complex128"], "extra_attrs": null, "type_ref": "numpy.lib._index_tricks_impl.ndenumerate"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.lib._index_tricks_impl.ndenumerate.__new__", "name": "__new__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "arr"], "arg_types": [{".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._index_tricks_impl._ScalarT_co", "id": 1, "name": "_ScalarT_co", "namespace": "numpy.lib._index_tricks_impl.ndenumerate", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.lib._index_tricks_impl.ndenumerate"}}, {".class": "UnionType", "items": ["builtins.complex", {".class": "Instance", "args": ["builtins.complex"], "extra_attrs": null, "type_ref": "numpy._typing._nested_sequence._NestedSequence"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__new__ of ndenumerate", "ret_type": {".class": "Instance", "args": ["numpy.complex128"], "extra_attrs": null, "type_ref": "numpy.lib._index_tricks_impl.ndenumerate"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "arr"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_overload", "is_decorated"], "fullname": "numpy.lib._index_tricks_impl.ndenumerate.__new__", "name": "__new__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "arr"], "arg_types": [{".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._index_tricks_impl._ScalarT_co", "id": 1, "name": "_ScalarT_co", "namespace": "numpy.lib._index_tricks_impl.ndenumerate", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.lib._index_tricks_impl.ndenumerate"}}, "builtins.object"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__new__ of ndenumerate", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.lib._index_tricks_impl.ndenumerate"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.lib._index_tricks_impl.ndenumerate.__new__", "name": "__new__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "arr"], "arg_types": [{".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._index_tricks_impl._ScalarT_co", "id": 1, "name": "_ScalarT_co", "namespace": "numpy.lib._index_tricks_impl.ndenumerate", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.lib._index_tricks_impl.ndenumerate"}}, "builtins.object"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__new__ of ndenumerate", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.lib._index_tricks_impl.ndenumerate"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "arr"], "arg_types": [{".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._index_tricks_impl._ScalarT_co", "id": 1, "name": "_ScalarT_co", "namespace": "numpy.lib._index_tricks_impl.ndenumerate", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.lib._index_tricks_impl.ndenumerate"}}, {".class": "TypeAliasType", "args": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._index_tricks_impl._ScalarT", "id": -1, "name": "_ScalarT", "namespace": "numpy.lib._index_tricks_impl.ndenumerate.__new__#0", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy.dtype"}], "extra_attrs": null, "type_ref": "numpy._typing._array_like._SupportsArray"}], "type_ref": "numpy._typing._array_like._FiniteNestedSequence"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__new__ of ndenumerate", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._index_tricks_impl._ScalarT", "id": -1, "name": "_ScalarT", "namespace": "numpy.lib._index_tricks_impl.ndenumerate.__new__#0", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy.lib._index_tricks_impl.ndenumerate"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._index_tricks_impl._ScalarT", "id": -1, "name": "_ScalarT", "namespace": "numpy.lib._index_tricks_impl.ndenumerate.__new__#0", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "arr"], "arg_types": [{".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._index_tricks_impl._ScalarT_co", "id": 1, "name": "_ScalarT_co", "namespace": "numpy.lib._index_tricks_impl.ndenumerate", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.lib._index_tricks_impl.ndenumerate"}}, {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "numpy._typing._nested_sequence._NestedSequence"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__new__ of ndenumerate", "ret_type": {".class": "Instance", "args": ["numpy.str_"], "extra_attrs": null, "type_ref": "numpy.lib._index_tricks_impl.ndenumerate"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "arr"], "arg_types": [{".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._index_tricks_impl._ScalarT_co", "id": 1, "name": "_ScalarT_co", "namespace": "numpy.lib._index_tricks_impl.ndenumerate", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.lib._index_tricks_impl.ndenumerate"}}, {".class": "UnionType", "items": ["builtins.bytes", {".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "numpy._typing._nested_sequence._NestedSequence"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__new__ of ndenumerate", "ret_type": {".class": "Instance", "args": ["numpy.bytes_"], "extra_attrs": null, "type_ref": "numpy.lib._index_tricks_impl.ndenumerate"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "arr"], "arg_types": [{".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._index_tricks_impl._ScalarT_co", "id": 1, "name": "_ScalarT_co", "namespace": "numpy.lib._index_tricks_impl.ndenumerate", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.lib._index_tricks_impl.ndenumerate"}}, {".class": "UnionType", "items": ["builtins.bool", {".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "numpy._typing._nested_sequence._NestedSequence"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__new__ of ndenumerate", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "numpy.bool"}], "extra_attrs": null, "type_ref": "numpy.lib._index_tricks_impl.ndenumerate"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "arr"], "arg_types": [{".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._index_tricks_impl._ScalarT_co", "id": 1, "name": "_ScalarT_co", "namespace": "numpy.lib._index_tricks_impl.ndenumerate", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.lib._index_tricks_impl.ndenumerate"}}, {".class": "UnionType", "items": ["builtins.int", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "numpy._typing._nested_sequence._NestedSequence"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__new__ of ndenumerate", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "extra_attrs": null, "type_ref": "numpy.lib._index_tricks_impl.ndenumerate"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "arr"], "arg_types": [{".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._index_tricks_impl._ScalarT_co", "id": 1, "name": "_ScalarT_co", "namespace": "numpy.lib._index_tricks_impl.ndenumerate", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.lib._index_tricks_impl.ndenumerate"}}, {".class": "UnionType", "items": ["builtins.float", {".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "numpy._typing._nested_sequence._NestedSequence"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__new__ of ndenumerate", "ret_type": {".class": "Instance", "args": ["numpy.float64"], "extra_attrs": null, "type_ref": "numpy.lib._index_tricks_impl.ndenumerate"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "arr"], "arg_types": [{".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._index_tricks_impl._ScalarT_co", "id": 1, "name": "_ScalarT_co", "namespace": "numpy.lib._index_tricks_impl.ndenumerate", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.lib._index_tricks_impl.ndenumerate"}}, {".class": "UnionType", "items": ["builtins.complex", {".class": "Instance", "args": ["builtins.complex"], "extra_attrs": null, "type_ref": "numpy._typing._nested_sequence._NestedSequence"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__new__ of ndenumerate", "ret_type": {".class": "Instance", "args": ["numpy.complex128"], "extra_attrs": null, "type_ref": "numpy.lib._index_tricks_impl.ndenumerate"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "arr"], "arg_types": [{".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._index_tricks_impl._ScalarT_co", "id": 1, "name": "_ScalarT_co", "namespace": "numpy.lib._index_tricks_impl.ndenumerate", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.lib._index_tricks_impl.ndenumerate"}}, "builtins.object"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__new__ of ndenumerate", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.lib._index_tricks_impl.ndenumerate"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "__next__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "numpy.lib._index_tricks_impl.ndenumerate.__next__", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.lib._index_tricks_impl.ndenumerate.__next__", "name": "__next__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "numpy.bool"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": ["builtins.int", "builtins.float", "builtins.complex"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.number"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.bytes", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.flexible"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["datetime.date", "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.datetime64"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["datetime.<PERSON><PERSON><PERSON>", "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.timedelta64"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "numpy.lib._index_tricks_impl.ndenumerate"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__next__ of ndenumerate", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._index_tricks_impl._ScalarT_co", "id": 1, "name": "_ScalarT_co", "namespace": "numpy.lib._index_tricks_impl.ndenumerate", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 1}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.lib._index_tricks_impl.ndenumerate.__next__", "name": "__next__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "numpy.bool"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": ["builtins.int", "builtins.float", "builtins.complex"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.number"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.bytes", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.flexible"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["datetime.date", "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.datetime64"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["datetime.<PERSON><PERSON><PERSON>", "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.timedelta64"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "numpy.lib._index_tricks_impl.ndenumerate"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__next__ of ndenumerate", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._index_tricks_impl._ScalarT_co", "id": 1, "name": "_ScalarT_co", "namespace": "numpy.lib._index_tricks_impl.ndenumerate", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 1}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.lib._index_tricks_impl.ndenumerate.__next__", "name": "__next__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": ["numpy.object_"], "extra_attrs": null, "type_ref": "numpy.lib._index_tricks_impl.ndenumerate"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__next__ of ndenumerate", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.lib._index_tricks_impl.ndenumerate.__next__", "name": "__next__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": ["numpy.object_"], "extra_attrs": null, "type_ref": "numpy.lib._index_tricks_impl.ndenumerate"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__next__ of ndenumerate", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.lib._index_tricks_impl.ndenumerate.__next__", "name": "__next__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._index_tricks_impl._ScalarT_co", "id": 1, "name": "_ScalarT_co", "namespace": "numpy.lib._index_tricks_impl.ndenumerate", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.lib._index_tricks_impl.ndenumerate"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__next__ of ndenumerate", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._index_tricks_impl._ScalarT_co", "id": 1, "name": "_ScalarT_co", "namespace": "numpy.lib._index_tricks_impl.ndenumerate", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 1}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.lib._index_tricks_impl.ndenumerate.__next__", "name": "__next__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._index_tricks_impl._ScalarT_co", "id": 1, "name": "_ScalarT_co", "namespace": "numpy.lib._index_tricks_impl.ndenumerate", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.lib._index_tricks_impl.ndenumerate"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__next__ of ndenumerate", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._index_tricks_impl._ScalarT_co", "id": 1, "name": "_ScalarT_co", "namespace": "numpy.lib._index_tricks_impl.ndenumerate", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 1}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "numpy.bool"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": ["builtins.int", "builtins.float", "builtins.complex"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.number"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.bytes", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.flexible"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["datetime.date", "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.datetime64"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["datetime.<PERSON><PERSON><PERSON>", "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.timedelta64"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "numpy.lib._index_tricks_impl.ndenumerate"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__next__ of ndenumerate", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._index_tricks_impl._ScalarT_co", "id": 1, "name": "_ScalarT_co", "namespace": "numpy.lib._index_tricks_impl.ndenumerate", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 1}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": ["numpy.object_"], "extra_attrs": null, "type_ref": "numpy.lib._index_tricks_impl.ndenumerate"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__next__ of ndenumerate", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._index_tricks_impl._ScalarT_co", "id": 1, "name": "_ScalarT_co", "namespace": "numpy.lib._index_tricks_impl.ndenumerate", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.lib._index_tricks_impl.ndenumerate"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__next__ of ndenumerate", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._index_tricks_impl._ScalarT_co", "id": 1, "name": "_ScalarT_co", "namespace": "numpy.lib._index_tricks_impl.ndenumerate", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 1}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._index_tricks_impl.ndenumerate.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._index_tricks_impl._ScalarT_co", "id": 1, "name": "_ScalarT_co", "namespace": "numpy.lib._index_tricks_impl.ndenumerate", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.lib._index_tricks_impl.ndenumerate"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["_ScalarT_co"], "typeddict_type": null}}, "ndindex": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy.lib._index_tricks_impl.ndindex", "name": "ndindex", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "numpy.lib._index_tricks_impl.ndindex", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "numpy.lib._index_tricks_impl", "mro": ["numpy.lib._index_tricks_impl.ndindex", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "numpy.lib._index_tricks_impl.ndindex.__init__", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.lib._index_tricks_impl.ndindex.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["numpy.lib._index_tricks_impl.ndindex", {".class": "Instance", "args": ["typing.SupportsIndex"], "extra_attrs": null, "type_ref": "builtins.tuple"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ndindex", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.lib._index_tricks_impl.ndindex.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["numpy.lib._index_tricks_impl.ndindex", {".class": "Instance", "args": ["typing.SupportsIndex"], "extra_attrs": null, "type_ref": "builtins.tuple"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ndindex", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2], "arg_names": [null, "shape"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.lib._index_tricks_impl.ndindex.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 2], "arg_names": [null, "shape"], "arg_types": ["numpy.lib._index_tricks_impl.ndindex", "typing.SupportsIndex"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ndindex", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.lib._index_tricks_impl.ndindex.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 2], "arg_names": [null, "shape"], "arg_types": ["numpy.lib._index_tricks_impl.ndindex", "typing.SupportsIndex"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ndindex", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["numpy.lib._index_tricks_impl.ndindex", {".class": "Instance", "args": ["typing.SupportsIndex"], "extra_attrs": null, "type_ref": "builtins.tuple"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ndindex", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 2], "arg_names": [null, "shape"], "arg_types": ["numpy.lib._index_tricks_impl.ndindex", "typing.SupportsIndex"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ndindex", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "__iter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.lib._index_tricks_impl.ndindex.__iter__", "name": "__iter__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._index_tricks_impl.ndindex.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "numpy.lib._index_tricks_impl.ndindex", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__iter__ of ndindex", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._index_tricks_impl.ndindex.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "numpy.lib._index_tricks_impl.ndindex", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._index_tricks_impl.ndindex.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "numpy.lib._index_tricks_impl.ndindex", "values": [], "variance": 0}]}}}, "__next__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.lib._index_tricks_impl.ndindex.__next__", "name": "__next__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["numpy.lib._index_tricks_impl.ndindex"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__next__ of ndindex", "ret_type": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "ndincr": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": "function numpy.lib._index_tricks_impl.ndindex.ndincr is deprecated: Deprecated since 1.20.0.", "flags": ["is_decorated"], "fullname": "numpy.lib._index_tricks_impl.ndindex.ndincr", "name": "ndincr", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["numpy.lib._index_tricks_impl.ndindex"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ndincr of ndindex", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "numpy.lib._index_tricks_impl.ndindex.ndincr", "name": "ndincr", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["numpy.lib._index_tricks_impl.ndindex"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ndincr of ndindex", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._index_tricks_impl.ndindex.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "numpy.lib._index_tricks_impl.ndindex", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "np": {".class": "SymbolTableNode", "cross_ref": "numpy", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ogrid": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready", "has_explicit_value"], "fullname": "numpy.lib._index_tricks_impl.ogrid", "name": "<PERSON><PERSON>", "type": "numpy.lib._index_tricks_impl.OGridClass"}}, "overload": {".class": "SymbolTableNode", "cross_ref": "typing.overload", "kind": "Gdef", "module_hidden": true, "module_public": false}, "r_": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready", "has_explicit_value"], "fullname": "numpy.lib._index_tricks_impl.r_", "name": "r_", "type": "numpy.lib._index_tricks_impl.RClass"}}, "ravel_multi_index": {".class": "SymbolTableNode", "cross_ref": "numpy._core.multiarray.ravel_multi_index", "kind": "Gdef"}, "s_": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready", "has_explicit_value"], "fullname": "numpy.lib._index_tricks_impl.s_", "name": "s_", "type": {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.bool", "value": false}], "extra_attrs": null, "type_ref": "numpy.lib._index_tricks_impl.IndexExpression"}}}, "unravel_index": {".class": "SymbolTableNode", "cross_ref": "numpy._core.multiarray.unravel_index", "kind": "Gdef"}}, "path": "c:\\Python313\\Lib\\site-packages\\numpy\\lib\\_index_tricks_impl.pyi"}