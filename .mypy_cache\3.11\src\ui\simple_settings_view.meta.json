{"data_mtime": 1750101895, "dep_lines": [10, 9, 1, 1, 1, 1, 8], "dep_prios": [5, 5, 5, 30, 30, 30, 10], "dependencies": ["src.utils.logger", "typing", "builtins", "_frozen_importlib", "abc", "src.utils"], "hash": "7ebf24038f076a28a94e483b8d6f59b5777f4c00", "id": "src.ui.simple_settings_view", "ignore_all": true, "interface_hash": "b931817683cf906fafab4e7426a8c40612abfe08", "mtime": 1750101836, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "E:\\db\\0617\\src\\ui\\simple_settings_view.py", "plugin_data": null, "size": 11782, "suppressed": ["flet"], "version_id": "1.15.0"}