{".class": "MypyFile", "_fullname": "numpy.linalg", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "LinAlgError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.ValueError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy.linalg.LinAlgError", "name": "LinAlgError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "numpy.linalg.LinAlgError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "numpy.linalg", "mro": ["numpy.linalg.LinAlgError", "builtins.ValueError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.linalg.LinAlgError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "numpy.linalg.LinAlgError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "numpy.linalg.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.linalg.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.linalg.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.linalg.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.linalg.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.linalg.__package__", "name": "__package__", "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.linalg.__path__", "name": "__path__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.linalg.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "cholesky": {".class": "SymbolTableNode", "cross_ref": "numpy.linalg._linalg.cholesky", "kind": "Gdef"}, "cond": {".class": "SymbolTableNode", "cross_ref": "numpy.linalg._linalg.cond", "kind": "Gdef"}, "cross": {".class": "SymbolTableNode", "cross_ref": "numpy.linalg._linalg.cross", "kind": "Gdef"}, "det": {".class": "SymbolTableNode", "cross_ref": "numpy.linalg._linalg.det", "kind": "Gdef"}, "diagonal": {".class": "SymbolTableNode", "cross_ref": "numpy.linalg._linalg.diagonal", "kind": "Gdef"}, "eig": {".class": "SymbolTableNode", "cross_ref": "numpy.linalg._linalg.eig", "kind": "Gdef"}, "eigh": {".class": "SymbolTableNode", "cross_ref": "numpy.linalg._linalg.eigh", "kind": "Gdef"}, "eigvals": {".class": "SymbolTableNode", "cross_ref": "numpy.linalg._linalg.eigvals", "kind": "Gdef"}, "eigvalsh": {".class": "SymbolTableNode", "cross_ref": "numpy.linalg._linalg.eigvalsh", "kind": "Gdef"}, "inv": {".class": "SymbolTableNode", "cross_ref": "numpy.linalg._linalg.inv", "kind": "Gdef"}, "lstsq": {".class": "SymbolTableNode", "cross_ref": "numpy.linalg._linalg.lstsq", "kind": "Gdef"}, "matmul": {".class": "SymbolTableNode", "cross_ref": "numpy.linalg._linalg.matmul", "kind": "Gdef"}, "matrix_norm": {".class": "SymbolTableNode", "cross_ref": "numpy.linalg._linalg.matrix_norm", "kind": "Gdef"}, "matrix_power": {".class": "SymbolTableNode", "cross_ref": "numpy.linalg._linalg.matrix_power", "kind": "Gdef"}, "matrix_rank": {".class": "SymbolTableNode", "cross_ref": "numpy.linalg._linalg.matrix_rank", "kind": "Gdef"}, "matrix_transpose": {".class": "SymbolTableNode", "cross_ref": "numpy._core.fromnumeric.matrix_transpose", "kind": "Gdef"}, "multi_dot": {".class": "SymbolTableNode", "cross_ref": "numpy.linalg._linalg.multi_dot", "kind": "Gdef"}, "norm": {".class": "SymbolTableNode", "cross_ref": "numpy.linalg._linalg.norm", "kind": "Gdef"}, "outer": {".class": "SymbolTableNode", "cross_ref": "numpy.linalg._linalg.outer", "kind": "Gdef"}, "pinv": {".class": "SymbolTableNode", "cross_ref": "numpy.linalg._linalg.pinv", "kind": "Gdef"}, "qr": {".class": "SymbolTableNode", "cross_ref": "numpy.linalg._linalg.qr", "kind": "Gdef"}, "slogdet": {".class": "SymbolTableNode", "cross_ref": "numpy.linalg._linalg.slogdet", "kind": "Gdef"}, "solve": {".class": "SymbolTableNode", "cross_ref": "numpy.linalg._linalg.solve", "kind": "Gdef"}, "svd": {".class": "SymbolTableNode", "cross_ref": "numpy.linalg._linalg.svd", "kind": "Gdef"}, "svdvals": {".class": "SymbolTableNode", "cross_ref": "numpy.linalg._linalg.svdvals", "kind": "Gdef"}, "tensordot": {".class": "SymbolTableNode", "cross_ref": "numpy._core.numeric.tensordot", "kind": "Gdef"}, "tensorinv": {".class": "SymbolTableNode", "cross_ref": "numpy.linalg._linalg.tensorinv", "kind": "Gdef"}, "tensorsolve": {".class": "SymbolTableNode", "cross_ref": "numpy.linalg._linalg.tensorsolve", "kind": "Gdef"}, "trace": {".class": "SymbolTableNode", "cross_ref": "numpy.linalg._linalg.trace", "kind": "Gdef"}, "vecdot": {".class": "SymbolTableNode", "cross_ref": "numpy.vecdot", "kind": "Gdef"}, "vector_norm": {".class": "SymbolTableNode", "cross_ref": "numpy.linalg._linalg.vector_norm", "kind": "Gdef"}}, "path": "c:\\Python313\\Lib\\site-packages\\numpy\\linalg\\__init__.pyi"}