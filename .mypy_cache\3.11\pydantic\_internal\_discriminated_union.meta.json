{"data_mtime": 1750100862, "dep_lines": [9, 16, 3, 6, 8, 9, 15, 1, 4, 6, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 25, 5, 10, 5, 20, 25, 5, 5, 5, 5, 30, 30, 30, 30, 30], "dependencies": ["pydantic._internal._core_utils", "pydantic._internal._core_metadata", "collections.abc", "pydantic_core.core_schema", "pydantic.errors", "pydantic._internal", "pydantic.types", "__future__", "typing", "pydantic_core", "builtins", "_frozen_importlib", "_typeshed", "abc", "types", "typing_extensions"], "hash": "07b8446a052edf99ca3f69a26603cddec006e358", "id": "pydantic._internal._discriminated_union", "ignore_all": true, "interface_hash": "e1f7c74e8c43d96d4c7a2008a9129630ddca02e1", "mtime": 1748795478, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Python313\\Lib\\site-packages\\pydantic\\_internal\\_discriminated_union.py", "plugin_data": null, "size": 25478, "suppressed": [], "version_id": "1.15.0"}