{"data_mtime": 1750100847, "dep_lines": [5, 13, 1, 3, 4, 6, 10, 13, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 25, 5, 10, 10, 5, 25, 25, 5, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["collections.abc", "numpy.typing", "__future__", "os", "sys", "typing", "numbers", "numpy", "builtins", "_frozen_importlib", "_typeshed", "abc", "array", "mmap", "numpy._typing", "numpy._typing._dtype_like", "types"], "hash": "2923c108e52d1a74e723940e47055625907df33d", "id": "PIL._typing", "ignore_all": true, "interface_hash": "fb10e3b073db5c5028a5c7c0aa94b2f28a88085a", "mtime": 1745696559, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Python313\\Lib\\site-packages\\PIL\\_typing.py", "plugin_data": null, "size": 1305, "suppressed": [], "version_id": "1.15.0"}