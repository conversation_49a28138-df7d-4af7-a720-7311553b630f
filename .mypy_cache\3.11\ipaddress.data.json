{".class": "MypyFile", "_fullname": "ipaddress", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "AddressValueError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.ValueError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ipaddress.AddressValueError", "name": "AddressValueError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ipaddress.AddressValueError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "ipaddress", "mro": ["ipaddress.AddressValueError", "builtins.ValueError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ipaddress.AddressValueError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "ipaddress.AddressValueError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Final": {".class": "SymbolTableNode", "cross_ref": "typing.Final", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Generic": {".class": "SymbolTableNode", "cross_ref": "typing.Generic", "kind": "Gdef", "module_hidden": true, "module_public": false}, "IPV4LENGTH": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 32, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "ipaddress.IPV4LENGTH", "name": "IPV4LENGTH", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 32}, "type_ref": "builtins.int"}}}, "IPV6LENGTH": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 128, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "ipaddress.IPV6LENGTH", "name": "IPV6LENGTH", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 128}, "type_ref": "builtins.int"}}}, "IPv4Address": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["ipaddress._BaseV4", "ipaddress._BaseAddress"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ipaddress.IPv4Address", "name": "IPv4Address", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ipaddress.IPv4Address", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "ipaddress", "mro": ["ipaddress.IPv4Address", "ipaddress._BaseV4", "ipaddress._BaseAddress", "ipaddress._IPAddressBase", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "address"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ipaddress.IPv4Address.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "address"], "arg_types": ["ipaddress.IPv4Address", "builtins.object"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of IPv4Address", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_global": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "ipaddress.IPv4Address.is_global", "name": "is_global", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["ipaddress.IPv4Address"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_global of IPv4Address", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "ipaddress.IPv4Address.is_global", "name": "is_global", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["ipaddress.IPv4Address"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_global of IPv4Address", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "is_link_local": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "ipaddress.IPv4Address.is_link_local", "name": "is_link_local", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["ipaddress.IPv4Address"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_link_local of IPv4Address", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "ipaddress.IPv4Address.is_link_local", "name": "is_link_local", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["ipaddress.IPv4Address"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_link_local of IPv4Address", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "is_loopback": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "ipaddress.IPv4Address.is_loopback", "name": "is_loopback", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["ipaddress.IPv4Address"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_loopback of IPv4Address", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "ipaddress.IPv4Address.is_loopback", "name": "is_loopback", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["ipaddress.IPv4Address"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_loopback of IPv4Address", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "is_multicast": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "ipaddress.IPv4Address.is_multicast", "name": "is_multicast", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["ipaddress.IPv4Address"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_multicast of IPv4Address", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "ipaddress.IPv4Address.is_multicast", "name": "is_multicast", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["ipaddress.IPv4Address"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_multicast of IPv4Address", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "is_private": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "ipaddress.IPv4Address.is_private", "name": "is_private", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["ipaddress.IPv4Address"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_private of IPv4Address", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "ipaddress.IPv4Address.is_private", "name": "is_private", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["ipaddress.IPv4Address"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_private of IPv4Address", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "is_reserved": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "ipaddress.IPv4Address.is_reserved", "name": "is_reserved", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["ipaddress.IPv4Address"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_reserved of IPv4Address", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "ipaddress.IPv4Address.is_reserved", "name": "is_reserved", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["ipaddress.IPv4Address"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_reserved of IPv4Address", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "is_unspecified": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "ipaddress.IPv4Address.is_unspecified", "name": "is_unspecified", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["ipaddress.IPv4Address"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_unspecified of IPv4Address", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "ipaddress.IPv4Address.is_unspecified", "name": "is_unspecified", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["ipaddress.IPv4Address"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_unspecified of IPv4Address", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "packed": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "ipaddress.IPv4Address.packed", "name": "packed", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["ipaddress.IPv4Address"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "packed of IPv4Address", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "ipaddress.IPv4Address.packed", "name": "packed", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["ipaddress.IPv4Address"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "packed of IPv4Address", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ipaddress.IPv4Address.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "ipaddress.IPv4Address", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "IPv4Interface": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["ipaddress.IPv4Address"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ipaddress.IPv4Interface", "name": "IPv4Interface", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ipaddress.IPv4Interface", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "ipaddress", "mro": ["ipaddress.IPv4Interface", "ipaddress.IPv4Address", "ipaddress._BaseV4", "ipaddress._BaseAddress", "ipaddress._IPAddressBase", "builtins.object"], "names": {".class": "SymbolTable", "__eq__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ipaddress.IPv4Interface.__eq__", "name": "__eq__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["ipaddress.IPv4Interface", "builtins.object"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__eq__ of IPv4Interface", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__hash__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ipaddress.IPv4Interface.__hash__", "name": "__hash__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["ipaddress.IPv4Interface"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__hash__ of IPv4Interface", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "hostmask": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "ipaddress.IPv4Interface.hostmask", "name": "hostmask", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["ipaddress.IPv4Interface"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "hostmask of IPv4Interface", "ret_type": "ipaddress.IPv4Address", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "ipaddress.IPv4Interface.hostmask", "name": "hostmask", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["ipaddress.IPv4Interface"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "hostmask of IPv4Interface", "ret_type": "ipaddress.IPv4Address", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "ip": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "ipaddress.IPv4Interface.ip", "name": "ip", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["ipaddress.IPv4Interface"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ip of IPv4Interface", "ret_type": "ipaddress.IPv4Address", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "ipaddress.IPv4Interface.ip", "name": "ip", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["ipaddress.IPv4Interface"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ip of IPv4Interface", "ret_type": "ipaddress.IPv4Address", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "netmask": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ipaddress.IPv4Interface.netmask", "name": "netmask", "type": "ipaddress.IPv4Address"}}, "network": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ipaddress.IPv4Interface.network", "name": "network", "type": "ipaddress.IPv4Network"}}, "with_hostmask": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "ipaddress.IPv4Interface.with_hostmask", "name": "with_hostmask", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["ipaddress.IPv4Interface"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "with_hostmask of IPv4Interface", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "ipaddress.IPv4Interface.with_hostmask", "name": "with_hostmask", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["ipaddress.IPv4Interface"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "with_hostmask of IPv4Interface", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "with_netmask": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "ipaddress.IPv4Interface.with_netmask", "name": "with_netmask", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["ipaddress.IPv4Interface"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "with_netmask of IPv4Interface", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "ipaddress.IPv4Interface.with_netmask", "name": "with_netmask", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["ipaddress.IPv4Interface"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "with_netmask of IPv4Interface", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "with_prefixlen": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "ipaddress.IPv4Interface.with_prefixlen", "name": "with_prefixlen", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["ipaddress.IPv4Interface"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "with_prefixlen of IPv4Interface", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "ipaddress.IPv4Interface.with_prefixlen", "name": "with_prefixlen", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["ipaddress.IPv4Interface"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "with_prefixlen of IPv4Interface", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ipaddress.IPv4Interface.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "ipaddress.IPv4Interface", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "IPv4Network": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["ipaddress._BaseV4", {".class": "Instance", "args": ["ipaddress.IPv4Address"], "extra_attrs": null, "type_ref": "ipaddress._BaseNetwork"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ipaddress.IPv4Network", "name": "IPv4Network", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ipaddress.IPv4Network", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "ipaddress", "mro": ["ipaddress.IPv4Network", "ipaddress._BaseV4", "ipaddress._BaseNetwork", "ipaddress._IPAddressBase", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "address", "strict"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ipaddress.IPv4Network.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "address", "strict"], "arg_types": ["ipaddress.IPv4Network", "builtins.object", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of IPv4Network", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ipaddress.IPv4Network.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "ipaddress.IPv4Network", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "IPv6Address": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["ipaddress._BaseV6", "ipaddress._BaseAddress"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ipaddress.IPv6Address", "name": "IPv6Address", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ipaddress.IPv6Address", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "ipaddress", "mro": ["ipaddress.IPv6Address", "ipaddress._BaseV6", "ipaddress._BaseAddress", "ipaddress._IPAddressBase", "builtins.object"], "names": {".class": "SymbolTable", "__eq__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ipaddress.IPv6Address.__eq__", "name": "__eq__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["ipaddress.IPv6Address", "builtins.object"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__eq__ of IPv6Address", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__hash__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ipaddress.IPv6Address.__hash__", "name": "__hash__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["ipaddress.IPv6Address"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__hash__ of IPv6Address", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "address"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ipaddress.IPv6Address.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "address"], "arg_types": ["ipaddress.IPv6Address", "builtins.object"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of IPv6Address", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "ipv4_mapped": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "ipaddress.IPv6Address.ipv4_mapped", "name": "ipv4_mapped", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["ipaddress.IPv6Address"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ipv4_mapped of IPv6Address", "ret_type": {".class": "UnionType", "items": ["ipaddress.IPv4Address", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "ipaddress.IPv6Address.ipv4_mapped", "name": "ipv4_mapped", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["ipaddress.IPv6Address"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ipv4_mapped of IPv6Address", "ret_type": {".class": "UnionType", "items": ["ipaddress.IPv4Address", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "is_global": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "ipaddress.IPv6Address.is_global", "name": "is_global", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["ipaddress.IPv6Address"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_global of IPv6Address", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "ipaddress.IPv6Address.is_global", "name": "is_global", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["ipaddress.IPv6Address"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_global of IPv6Address", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "is_link_local": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "ipaddress.IPv6Address.is_link_local", "name": "is_link_local", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["ipaddress.IPv6Address"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_link_local of IPv6Address", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "ipaddress.IPv6Address.is_link_local", "name": "is_link_local", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["ipaddress.IPv6Address"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_link_local of IPv6Address", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "is_loopback": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "ipaddress.IPv6Address.is_loopback", "name": "is_loopback", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["ipaddress.IPv6Address"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_loopback of IPv6Address", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "ipaddress.IPv6Address.is_loopback", "name": "is_loopback", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["ipaddress.IPv6Address"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_loopback of IPv6Address", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "is_multicast": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "ipaddress.IPv6Address.is_multicast", "name": "is_multicast", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["ipaddress.IPv6Address"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_multicast of IPv6Address", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "ipaddress.IPv6Address.is_multicast", "name": "is_multicast", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["ipaddress.IPv6Address"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_multicast of IPv6Address", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "is_private": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "ipaddress.IPv6Address.is_private", "name": "is_private", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["ipaddress.IPv6Address"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_private of IPv6Address", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "ipaddress.IPv6Address.is_private", "name": "is_private", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["ipaddress.IPv6Address"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_private of IPv6Address", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "is_reserved": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "ipaddress.IPv6Address.is_reserved", "name": "is_reserved", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["ipaddress.IPv6Address"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_reserved of IPv6Address", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "ipaddress.IPv6Address.is_reserved", "name": "is_reserved", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["ipaddress.IPv6Address"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_reserved of IPv6Address", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "is_site_local": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "ipaddress.IPv6Address.is_site_local", "name": "is_site_local", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["ipaddress.IPv6Address"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_site_local of IPv6Address", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "ipaddress.IPv6Address.is_site_local", "name": "is_site_local", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["ipaddress.IPv6Address"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_site_local of IPv6Address", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "is_unspecified": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "ipaddress.IPv6Address.is_unspecified", "name": "is_unspecified", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["ipaddress.IPv6Address"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_unspecified of IPv6Address", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "ipaddress.IPv6Address.is_unspecified", "name": "is_unspecified", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["ipaddress.IPv6Address"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_unspecified of IPv6Address", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "packed": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "ipaddress.IPv6Address.packed", "name": "packed", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["ipaddress.IPv6Address"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "packed of IPv6Address", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "ipaddress.IPv6Address.packed", "name": "packed", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["ipaddress.IPv6Address"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "packed of IPv6Address", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "scope_id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "ipaddress.IPv6Address.scope_id", "name": "scope_id", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["ipaddress.IPv6Address"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "scope_id of IPv6Address", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "ipaddress.IPv6Address.scope_id", "name": "scope_id", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["ipaddress.IPv6Address"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "scope_id of IPv6Address", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "sixtofour": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "ipaddress.IPv6Address.sixtofour", "name": "sixtofour", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["ipaddress.IPv6Address"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "sixtofour of IPv6Address", "ret_type": {".class": "UnionType", "items": ["ipaddress.IPv4Address", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "ipaddress.IPv6Address.sixtofour", "name": "sixtofour", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["ipaddress.IPv6Address"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "sixtofour of IPv6Address", "ret_type": {".class": "UnionType", "items": ["ipaddress.IPv4Address", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "teredo": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "ipaddress.IPv6Address.teredo", "name": "teredo", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["ipaddress.IPv6Address"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "teredo of IPv6Address", "ret_type": {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": ["ipaddress.IPv4Address", "ipaddress.IPv4Address"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "ipaddress.IPv6Address.teredo", "name": "teredo", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["ipaddress.IPv6Address"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "teredo of IPv6Address", "ret_type": {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": ["ipaddress.IPv4Address", "ipaddress.IPv4Address"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ipaddress.IPv6Address.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "ipaddress.IPv6Address", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "IPv6Interface": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["ipaddress.IPv6Address"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ipaddress.IPv6Interface", "name": "IPv6Interface", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ipaddress.IPv6Interface", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "ipaddress", "mro": ["ipaddress.IPv6Interface", "ipaddress.IPv6Address", "ipaddress._BaseV6", "ipaddress._BaseAddress", "ipaddress._IPAddressBase", "builtins.object"], "names": {".class": "SymbolTable", "__eq__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ipaddress.IPv6Interface.__eq__", "name": "__eq__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["ipaddress.IPv6Interface", "builtins.object"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__eq__ of IPv6Interface", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__hash__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ipaddress.IPv6Interface.__hash__", "name": "__hash__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["ipaddress.IPv6Interface"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__hash__ of IPv6Interface", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "hostmask": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "ipaddress.IPv6Interface.hostmask", "name": "hostmask", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["ipaddress.IPv6Interface"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "hostmask of IPv6Interface", "ret_type": "ipaddress.IPv6Address", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "ipaddress.IPv6Interface.hostmask", "name": "hostmask", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["ipaddress.IPv6Interface"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "hostmask of IPv6Interface", "ret_type": "ipaddress.IPv6Address", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "ip": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "ipaddress.IPv6Interface.ip", "name": "ip", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["ipaddress.IPv6Interface"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ip of IPv6Interface", "ret_type": "ipaddress.IPv6Address", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "ipaddress.IPv6Interface.ip", "name": "ip", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["ipaddress.IPv6Interface"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ip of IPv6Interface", "ret_type": "ipaddress.IPv6Address", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "netmask": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ipaddress.IPv6Interface.netmask", "name": "netmask", "type": "ipaddress.IPv6Address"}}, "network": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ipaddress.IPv6Interface.network", "name": "network", "type": "ipaddress.IPv6Network"}}, "with_hostmask": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "ipaddress.IPv6Interface.with_hostmask", "name": "with_hostmask", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["ipaddress.IPv6Interface"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "with_hostmask of IPv6Interface", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "ipaddress.IPv6Interface.with_hostmask", "name": "with_hostmask", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["ipaddress.IPv6Interface"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "with_hostmask of IPv6Interface", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "with_netmask": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "ipaddress.IPv6Interface.with_netmask", "name": "with_netmask", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["ipaddress.IPv6Interface"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "with_netmask of IPv6Interface", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "ipaddress.IPv6Interface.with_netmask", "name": "with_netmask", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["ipaddress.IPv6Interface"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "with_netmask of IPv6Interface", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "with_prefixlen": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "ipaddress.IPv6Interface.with_prefixlen", "name": "with_prefixlen", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["ipaddress.IPv6Interface"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "with_prefixlen of IPv6Interface", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "ipaddress.IPv6Interface.with_prefixlen", "name": "with_prefixlen", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["ipaddress.IPv6Interface"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "with_prefixlen of IPv6Interface", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ipaddress.IPv6Interface.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "ipaddress.IPv6Interface", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "IPv6Network": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["ipaddress._BaseV6", {".class": "Instance", "args": ["ipaddress.IPv6Address"], "extra_attrs": null, "type_ref": "ipaddress._BaseNetwork"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ipaddress.IPv6Network", "name": "IPv6Network", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ipaddress.IPv6Network", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "ipaddress", "mro": ["ipaddress.IPv6Network", "ipaddress._BaseV6", "ipaddress._BaseNetwork", "ipaddress._IPAddressBase", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "address", "strict"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ipaddress.IPv6Network.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "address", "strict"], "arg_types": ["ipaddress.IPv6Network", "builtins.object", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of IPv6Network", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_site_local": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "ipaddress.IPv6Network.is_site_local", "name": "is_site_local", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["ipaddress.IPv6Network"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_site_local of IPv6Network", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "ipaddress.IPv6Network.is_site_local", "name": "is_site_local", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["ipaddress.IPv6Network"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_site_local of IPv6Network", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ipaddress.IPv6Network.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "ipaddress.IPv6Network", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Iterator": {".class": "SymbolTableNode", "cross_ref": "typing.Iterator", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing.Literal", "kind": "Gdef", "module_hidden": true, "module_public": false}, "NetmaskValueError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.ValueError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ipaddress.NetmaskValueError", "name": "NetmaskValueError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ipaddress.NetmaskValueError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "ipaddress", "mro": ["ipaddress.NetmaskValueError", "builtins.ValueError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ipaddress.NetmaskValueError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "ipaddress.NetmaskValueError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Self": {".class": "SymbolTableNode", "cross_ref": "typing.Self", "kind": "Gdef", "module_hidden": true, "module_public": false}, "TypeAlias": {".class": "SymbolTableNode", "cross_ref": "typing.TypeAlias", "kind": "Gdef", "module_hidden": true, "module_public": false}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_A": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ipaddress._A", "name": "_A", "upper_bound": "builtins.object", "values": ["ipaddress.IPv4Address", "ipaddress.IPv6Address"], "variance": 0}}, "_BaseAddress": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["ipaddress._IPAddressBase"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ipaddress._BaseAddress", "name": "_BaseAddress", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ipaddress._BaseAddress", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "ipaddress", "mro": ["ipaddress._BaseAddress", "ipaddress._IPAddressBase", "builtins.object"], "names": {".class": "SymbolTable", "__add__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ipaddress._BaseAddress.__add__", "name": "__add__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ipaddress._BaseAddress.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "ipaddress._BaseAddress", "values": [], "variance": 0}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__add__ of _BaseAddress", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ipaddress._BaseAddress.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "ipaddress._BaseAddress", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ipaddress._BaseAddress.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "ipaddress._BaseAddress", "values": [], "variance": 0}]}}}, "__eq__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ipaddress._BaseAddress.__eq__", "name": "__eq__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["ipaddress._BaseAddress", "builtins.object"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__eq__ of _BaseAddress", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__format__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "fmt"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ipaddress._BaseAddress.__format__", "name": "__format__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "fmt"], "arg_types": ["ipaddress._BaseAddress", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__format__ of _BaseAddress", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__ge__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ipaddress._BaseAddress.__ge__", "name": "__ge__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ipaddress._BaseAddress.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "ipaddress._BaseAddress", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ipaddress._BaseAddress.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "ipaddress._BaseAddress", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__ge__ of _BaseAddress", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ipaddress._BaseAddress.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "ipaddress._BaseAddress", "values": [], "variance": 0}]}}}, "__gt__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ipaddress._BaseAddress.__gt__", "name": "__gt__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ipaddress._BaseAddress.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "ipaddress._BaseAddress", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ipaddress._BaseAddress.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "ipaddress._BaseAddress", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__gt__ of _BaseAddress", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ipaddress._BaseAddress.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "ipaddress._BaseAddress", "values": [], "variance": 0}]}}}, "__hash__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ipaddress._BaseAddress.__hash__", "name": "__hash__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["ipaddress._BaseAddress"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__hash__ of _BaseAddress", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__int__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ipaddress._BaseAddress.__int__", "name": "__int__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["ipaddress._BaseAddress"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__int__ of _BaseAddress", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__le__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ipaddress._BaseAddress.__le__", "name": "__le__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ipaddress._BaseAddress.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "ipaddress._BaseAddress", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ipaddress._BaseAddress.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "ipaddress._BaseAddress", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__le__ of _BaseAddress", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ipaddress._BaseAddress.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "ipaddress._BaseAddress", "values": [], "variance": 0}]}}}, "__lt__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ipaddress._BaseAddress.__lt__", "name": "__lt__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ipaddress._BaseAddress.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "ipaddress._BaseAddress", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ipaddress._BaseAddress.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "ipaddress._BaseAddress", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__lt__ of _BaseAddress", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ipaddress._BaseAddress.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "ipaddress._BaseAddress", "values": [], "variance": 0}]}}}, "__sub__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ipaddress._BaseAddress.__sub__", "name": "__sub__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ipaddress._BaseAddress.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "ipaddress._BaseAddress", "values": [], "variance": 0}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__sub__ of _BaseAddress", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ipaddress._BaseAddress.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "ipaddress._BaseAddress", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ipaddress._BaseAddress.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "ipaddress._BaseAddress", "values": [], "variance": 0}]}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ipaddress._BaseAddress.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "ipaddress._BaseAddress", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_BaseNetwork": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["ipaddress._IPAddressBase"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ipaddress._BaseNetwork", "name": "_BaseNetwork", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ipaddress._A", "id": 1, "name": "_A", "namespace": "ipaddress._BaseNetwork", "upper_bound": "builtins.object", "values": ["ipaddress.IPv4Address", "ipaddress.IPv6Address"], "variance": 0}]}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ipaddress._BaseNetwork", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "ipaddress", "mro": ["ipaddress._BaseNetwork", "ipaddress._IPAddressBase", "builtins.object"], "names": {".class": "SymbolTable", "__contains__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ipaddress._BaseNetwork.__contains__", "name": "__contains__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ipaddress._A", "id": 1, "name": "_A", "namespace": "ipaddress._BaseNetwork", "upper_bound": "builtins.object", "values": ["ipaddress.IPv4Address", "ipaddress.IPv6Address"], "variance": 0}], "extra_attrs": null, "type_ref": "ipaddress._BaseNetwork"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__contains__ of _BaseNetwork", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__eq__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ipaddress._BaseNetwork.__eq__", "name": "__eq__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ipaddress._A", "id": 1, "name": "_A", "namespace": "ipaddress._BaseNetwork", "upper_bound": "builtins.object", "values": ["ipaddress.IPv4Address", "ipaddress.IPv6Address"], "variance": 0}], "extra_attrs": null, "type_ref": "ipaddress._BaseNetwork"}, "builtins.object"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__eq__ of _BaseNetwork", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__ge__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ipaddress._BaseNetwork.__ge__", "name": "__ge__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ipaddress._BaseNetwork.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ipaddress._A", "id": 1, "name": "_A", "namespace": "ipaddress._BaseNetwork", "upper_bound": "builtins.object", "values": ["ipaddress.IPv4Address", "ipaddress.IPv6Address"], "variance": 0}], "extra_attrs": null, "type_ref": "ipaddress._BaseNetwork"}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ipaddress._BaseNetwork.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ipaddress._A", "id": 1, "name": "_A", "namespace": "ipaddress._BaseNetwork", "upper_bound": "builtins.object", "values": ["ipaddress.IPv4Address", "ipaddress.IPv6Address"], "variance": 0}], "extra_attrs": null, "type_ref": "ipaddress._BaseNetwork"}, "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__ge__ of _BaseNetwork", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ipaddress._BaseNetwork.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ipaddress._A", "id": 1, "name": "_A", "namespace": "ipaddress._BaseNetwork", "upper_bound": "builtins.object", "values": ["ipaddress.IPv4Address", "ipaddress.IPv6Address"], "variance": 0}], "extra_attrs": null, "type_ref": "ipaddress._BaseNetwork"}, "values": [], "variance": 0}]}}}, "__getitem__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ipaddress._BaseNetwork.__getitem__", "name": "__getitem__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ipaddress._A", "id": 1, "name": "_A", "namespace": "ipaddress._BaseNetwork", "upper_bound": "builtins.object", "values": ["ipaddress.IPv4Address", "ipaddress.IPv6Address"], "variance": 0}], "extra_attrs": null, "type_ref": "ipaddress._BaseNetwork"}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__getitem__ of _BaseNetwork", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ipaddress._A", "id": 1, "name": "_A", "namespace": "ipaddress._BaseNetwork", "upper_bound": "builtins.object", "values": ["ipaddress.IPv4Address", "ipaddress.IPv6Address"], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__gt__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ipaddress._BaseNetwork.__gt__", "name": "__gt__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ipaddress._BaseNetwork.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ipaddress._A", "id": 1, "name": "_A", "namespace": "ipaddress._BaseNetwork", "upper_bound": "builtins.object", "values": ["ipaddress.IPv4Address", "ipaddress.IPv6Address"], "variance": 0}], "extra_attrs": null, "type_ref": "ipaddress._BaseNetwork"}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ipaddress._BaseNetwork.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ipaddress._A", "id": 1, "name": "_A", "namespace": "ipaddress._BaseNetwork", "upper_bound": "builtins.object", "values": ["ipaddress.IPv4Address", "ipaddress.IPv6Address"], "variance": 0}], "extra_attrs": null, "type_ref": "ipaddress._BaseNetwork"}, "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__gt__ of _BaseNetwork", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ipaddress._BaseNetwork.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ipaddress._A", "id": 1, "name": "_A", "namespace": "ipaddress._BaseNetwork", "upper_bound": "builtins.object", "values": ["ipaddress.IPv4Address", "ipaddress.IPv6Address"], "variance": 0}], "extra_attrs": null, "type_ref": "ipaddress._BaseNetwork"}, "values": [], "variance": 0}]}}}, "__hash__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ipaddress._BaseNetwork.__hash__", "name": "__hash__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ipaddress._A", "id": 1, "name": "_A", "namespace": "ipaddress._BaseNetwork", "upper_bound": "builtins.object", "values": ["ipaddress.IPv4Address", "ipaddress.IPv6Address"], "variance": 0}], "extra_attrs": null, "type_ref": "ipaddress._BaseNetwork"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__hash__ of _BaseNetwork", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__iter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ipaddress._BaseNetwork.__iter__", "name": "__iter__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ipaddress._A", "id": 1, "name": "_A", "namespace": "ipaddress._BaseNetwork", "upper_bound": "builtins.object", "values": ["ipaddress.IPv4Address", "ipaddress.IPv6Address"], "variance": 0}], "extra_attrs": null, "type_ref": "ipaddress._BaseNetwork"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__iter__ of _BaseNetwork", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ipaddress._A", "id": 1, "name": "_A", "namespace": "ipaddress._BaseNetwork", "upper_bound": "builtins.object", "values": ["ipaddress.IPv4Address", "ipaddress.IPv6Address"], "variance": 0}], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__le__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ipaddress._BaseNetwork.__le__", "name": "__le__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ipaddress._BaseNetwork.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ipaddress._A", "id": 1, "name": "_A", "namespace": "ipaddress._BaseNetwork", "upper_bound": "builtins.object", "values": ["ipaddress.IPv4Address", "ipaddress.IPv6Address"], "variance": 0}], "extra_attrs": null, "type_ref": "ipaddress._BaseNetwork"}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ipaddress._BaseNetwork.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ipaddress._A", "id": 1, "name": "_A", "namespace": "ipaddress._BaseNetwork", "upper_bound": "builtins.object", "values": ["ipaddress.IPv4Address", "ipaddress.IPv6Address"], "variance": 0}], "extra_attrs": null, "type_ref": "ipaddress._BaseNetwork"}, "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__le__ of _BaseNetwork", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ipaddress._BaseNetwork.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ipaddress._A", "id": 1, "name": "_A", "namespace": "ipaddress._BaseNetwork", "upper_bound": "builtins.object", "values": ["ipaddress.IPv4Address", "ipaddress.IPv6Address"], "variance": 0}], "extra_attrs": null, "type_ref": "ipaddress._BaseNetwork"}, "values": [], "variance": 0}]}}}, "__lt__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ipaddress._BaseNetwork.__lt__", "name": "__lt__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ipaddress._BaseNetwork.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ipaddress._A", "id": 1, "name": "_A", "namespace": "ipaddress._BaseNetwork", "upper_bound": "builtins.object", "values": ["ipaddress.IPv4Address", "ipaddress.IPv6Address"], "variance": 0}], "extra_attrs": null, "type_ref": "ipaddress._BaseNetwork"}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ipaddress._BaseNetwork.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ipaddress._A", "id": 1, "name": "_A", "namespace": "ipaddress._BaseNetwork", "upper_bound": "builtins.object", "values": ["ipaddress.IPv4Address", "ipaddress.IPv6Address"], "variance": 0}], "extra_attrs": null, "type_ref": "ipaddress._BaseNetwork"}, "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__lt__ of _BaseNetwork", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ipaddress._BaseNetwork.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ipaddress._A", "id": 1, "name": "_A", "namespace": "ipaddress._BaseNetwork", "upper_bound": "builtins.object", "values": ["ipaddress.IPv4Address", "ipaddress.IPv6Address"], "variance": 0}], "extra_attrs": null, "type_ref": "ipaddress._BaseNetwork"}, "values": [], "variance": 0}]}}}, "address_exclude": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ipaddress._BaseNetwork.address_exclude", "name": "address_exclude", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "other"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ipaddress._BaseNetwork.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ipaddress._A", "id": 1, "name": "_A", "namespace": "ipaddress._BaseNetwork", "upper_bound": "builtins.object", "values": ["ipaddress.IPv4Address", "ipaddress.IPv6Address"], "variance": 0}], "extra_attrs": null, "type_ref": "ipaddress._BaseNetwork"}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ipaddress._BaseNetwork.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ipaddress._A", "id": 1, "name": "_A", "namespace": "ipaddress._BaseNetwork", "upper_bound": "builtins.object", "values": ["ipaddress.IPv4Address", "ipaddress.IPv6Address"], "variance": 0}], "extra_attrs": null, "type_ref": "ipaddress._BaseNetwork"}, "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "address_exclude of _BaseNetwork", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ipaddress._BaseNetwork.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ipaddress._A", "id": 1, "name": "_A", "namespace": "ipaddress._BaseNetwork", "upper_bound": "builtins.object", "values": ["ipaddress.IPv4Address", "ipaddress.IPv6Address"], "variance": 0}], "extra_attrs": null, "type_ref": "ipaddress._BaseNetwork"}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ipaddress._BaseNetwork.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ipaddress._A", "id": 1, "name": "_A", "namespace": "ipaddress._BaseNetwork", "upper_bound": "builtins.object", "values": ["ipaddress.IPv4Address", "ipaddress.IPv6Address"], "variance": 0}], "extra_attrs": null, "type_ref": "ipaddress._BaseNetwork"}, "values": [], "variance": 0}]}}}, "broadcast_address": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "ipaddress._BaseNetwork.broadcast_address", "name": "broadcast_address", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ipaddress._A", "id": 1, "name": "_A", "namespace": "ipaddress._BaseNetwork", "upper_bound": "builtins.object", "values": ["ipaddress.IPv4Address", "ipaddress.IPv6Address"], "variance": 0}], "extra_attrs": null, "type_ref": "ipaddress._BaseNetwork"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "broadcast_address of _BaseNetwork", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ipaddress._A", "id": 1, "name": "_A", "namespace": "ipaddress._BaseNetwork", "upper_bound": "builtins.object", "values": ["ipaddress.IPv4Address", "ipaddress.IPv6Address"], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "ipaddress._BaseNetwork.broadcast_address", "name": "broadcast_address", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ipaddress._A", "id": 1, "name": "_A", "namespace": "ipaddress._BaseNetwork", "upper_bound": "builtins.object", "values": ["ipaddress.IPv4Address", "ipaddress.IPv6Address"], "variance": 0}], "extra_attrs": null, "type_ref": "ipaddress._BaseNetwork"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "broadcast_address of _BaseNetwork", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ipaddress._A", "id": 1, "name": "_A", "namespace": "ipaddress._BaseNetwork", "upper_bound": "builtins.object", "values": ["ipaddress.IPv4Address", "ipaddress.IPv6Address"], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "compare_networks": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ipaddress._BaseNetwork.compare_networks", "name": "compare_networks", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "other"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ipaddress._BaseNetwork.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ipaddress._A", "id": 1, "name": "_A", "namespace": "ipaddress._BaseNetwork", "upper_bound": "builtins.object", "values": ["ipaddress.IPv4Address", "ipaddress.IPv6Address"], "variance": 0}], "extra_attrs": null, "type_ref": "ipaddress._BaseNetwork"}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ipaddress._BaseNetwork.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ipaddress._A", "id": 1, "name": "_A", "namespace": "ipaddress._BaseNetwork", "upper_bound": "builtins.object", "values": ["ipaddress.IPv4Address", "ipaddress.IPv6Address"], "variance": 0}], "extra_attrs": null, "type_ref": "ipaddress._BaseNetwork"}, "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "compare_networks of _BaseNetwork", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ipaddress._BaseNetwork.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ipaddress._A", "id": 1, "name": "_A", "namespace": "ipaddress._BaseNetwork", "upper_bound": "builtins.object", "values": ["ipaddress.IPv4Address", "ipaddress.IPv6Address"], "variance": 0}], "extra_attrs": null, "type_ref": "ipaddress._BaseNetwork"}, "values": [], "variance": 0}]}}}, "hostmask": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "ipaddress._BaseNetwork.hostmask", "name": "hostmask", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ipaddress._A", "id": 1, "name": "_A", "namespace": "ipaddress._BaseNetwork", "upper_bound": "builtins.object", "values": ["ipaddress.IPv4Address", "ipaddress.IPv6Address"], "variance": 0}], "extra_attrs": null, "type_ref": "ipaddress._BaseNetwork"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "hostmask of _BaseNetwork", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ipaddress._A", "id": 1, "name": "_A", "namespace": "ipaddress._BaseNetwork", "upper_bound": "builtins.object", "values": ["ipaddress.IPv4Address", "ipaddress.IPv6Address"], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "ipaddress._BaseNetwork.hostmask", "name": "hostmask", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ipaddress._A", "id": 1, "name": "_A", "namespace": "ipaddress._BaseNetwork", "upper_bound": "builtins.object", "values": ["ipaddress.IPv4Address", "ipaddress.IPv6Address"], "variance": 0}], "extra_attrs": null, "type_ref": "ipaddress._BaseNetwork"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "hostmask of _BaseNetwork", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ipaddress._A", "id": 1, "name": "_A", "namespace": "ipaddress._BaseNetwork", "upper_bound": "builtins.object", "values": ["ipaddress.IPv4Address", "ipaddress.IPv6Address"], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "hosts": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ipaddress._BaseNetwork.hosts", "name": "hosts", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ipaddress._A", "id": 1, "name": "_A", "namespace": "ipaddress._BaseNetwork", "upper_bound": "builtins.object", "values": ["ipaddress.IPv4Address", "ipaddress.IPv6Address"], "variance": 0}], "extra_attrs": null, "type_ref": "ipaddress._BaseNetwork"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "hosts of _BaseNetwork", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ipaddress._A", "id": 1, "name": "_A", "namespace": "ipaddress._BaseNetwork", "upper_bound": "builtins.object", "values": ["ipaddress.IPv4Address", "ipaddress.IPv6Address"], "variance": 0}], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_global": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "ipaddress._BaseNetwork.is_global", "name": "is_global", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ipaddress._A", "id": 1, "name": "_A", "namespace": "ipaddress._BaseNetwork", "upper_bound": "builtins.object", "values": ["ipaddress.IPv4Address", "ipaddress.IPv6Address"], "variance": 0}], "extra_attrs": null, "type_ref": "ipaddress._BaseNetwork"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_global of _BaseNetwork", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "ipaddress._BaseNetwork.is_global", "name": "is_global", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ipaddress._A", "id": 1, "name": "_A", "namespace": "ipaddress._BaseNetwork", "upper_bound": "builtins.object", "values": ["ipaddress.IPv4Address", "ipaddress.IPv6Address"], "variance": 0}], "extra_attrs": null, "type_ref": "ipaddress._BaseNetwork"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_global of _BaseNetwork", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "is_link_local": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "ipaddress._BaseNetwork.is_link_local", "name": "is_link_local", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ipaddress._A", "id": 1, "name": "_A", "namespace": "ipaddress._BaseNetwork", "upper_bound": "builtins.object", "values": ["ipaddress.IPv4Address", "ipaddress.IPv6Address"], "variance": 0}], "extra_attrs": null, "type_ref": "ipaddress._BaseNetwork"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_link_local of _BaseNetwork", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "ipaddress._BaseNetwork.is_link_local", "name": "is_link_local", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ipaddress._A", "id": 1, "name": "_A", "namespace": "ipaddress._BaseNetwork", "upper_bound": "builtins.object", "values": ["ipaddress.IPv4Address", "ipaddress.IPv6Address"], "variance": 0}], "extra_attrs": null, "type_ref": "ipaddress._BaseNetwork"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_link_local of _BaseNetwork", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "is_loopback": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "ipaddress._BaseNetwork.is_loopback", "name": "is_loopback", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ipaddress._A", "id": 1, "name": "_A", "namespace": "ipaddress._BaseNetwork", "upper_bound": "builtins.object", "values": ["ipaddress.IPv4Address", "ipaddress.IPv6Address"], "variance": 0}], "extra_attrs": null, "type_ref": "ipaddress._BaseNetwork"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_loopback of _BaseNetwork", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "ipaddress._BaseNetwork.is_loopback", "name": "is_loopback", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ipaddress._A", "id": 1, "name": "_A", "namespace": "ipaddress._BaseNetwork", "upper_bound": "builtins.object", "values": ["ipaddress.IPv4Address", "ipaddress.IPv6Address"], "variance": 0}], "extra_attrs": null, "type_ref": "ipaddress._BaseNetwork"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_loopback of _BaseNetwork", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "is_multicast": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "ipaddress._BaseNetwork.is_multicast", "name": "is_multicast", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ipaddress._A", "id": 1, "name": "_A", "namespace": "ipaddress._BaseNetwork", "upper_bound": "builtins.object", "values": ["ipaddress.IPv4Address", "ipaddress.IPv6Address"], "variance": 0}], "extra_attrs": null, "type_ref": "ipaddress._BaseNetwork"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_multicast of _BaseNetwork", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "ipaddress._BaseNetwork.is_multicast", "name": "is_multicast", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ipaddress._A", "id": 1, "name": "_A", "namespace": "ipaddress._BaseNetwork", "upper_bound": "builtins.object", "values": ["ipaddress.IPv4Address", "ipaddress.IPv6Address"], "variance": 0}], "extra_attrs": null, "type_ref": "ipaddress._BaseNetwork"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_multicast of _BaseNetwork", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "is_private": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "ipaddress._BaseNetwork.is_private", "name": "is_private", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ipaddress._A", "id": 1, "name": "_A", "namespace": "ipaddress._BaseNetwork", "upper_bound": "builtins.object", "values": ["ipaddress.IPv4Address", "ipaddress.IPv6Address"], "variance": 0}], "extra_attrs": null, "type_ref": "ipaddress._BaseNetwork"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_private of _BaseNetwork", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "ipaddress._BaseNetwork.is_private", "name": "is_private", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ipaddress._A", "id": 1, "name": "_A", "namespace": "ipaddress._BaseNetwork", "upper_bound": "builtins.object", "values": ["ipaddress.IPv4Address", "ipaddress.IPv6Address"], "variance": 0}], "extra_attrs": null, "type_ref": "ipaddress._BaseNetwork"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_private of _BaseNetwork", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "is_reserved": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "ipaddress._BaseNetwork.is_reserved", "name": "is_reserved", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ipaddress._A", "id": 1, "name": "_A", "namespace": "ipaddress._BaseNetwork", "upper_bound": "builtins.object", "values": ["ipaddress.IPv4Address", "ipaddress.IPv6Address"], "variance": 0}], "extra_attrs": null, "type_ref": "ipaddress._BaseNetwork"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_reserved of _BaseNetwork", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "ipaddress._BaseNetwork.is_reserved", "name": "is_reserved", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ipaddress._A", "id": 1, "name": "_A", "namespace": "ipaddress._BaseNetwork", "upper_bound": "builtins.object", "values": ["ipaddress.IPv4Address", "ipaddress.IPv6Address"], "variance": 0}], "extra_attrs": null, "type_ref": "ipaddress._BaseNetwork"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_reserved of _BaseNetwork", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "is_unspecified": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "ipaddress._BaseNetwork.is_unspecified", "name": "is_unspecified", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ipaddress._A", "id": 1, "name": "_A", "namespace": "ipaddress._BaseNetwork", "upper_bound": "builtins.object", "values": ["ipaddress.IPv4Address", "ipaddress.IPv6Address"], "variance": 0}], "extra_attrs": null, "type_ref": "ipaddress._BaseNetwork"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_unspecified of _BaseNetwork", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "ipaddress._BaseNetwork.is_unspecified", "name": "is_unspecified", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ipaddress._A", "id": 1, "name": "_A", "namespace": "ipaddress._BaseNetwork", "upper_bound": "builtins.object", "values": ["ipaddress.IPv4Address", "ipaddress.IPv6Address"], "variance": 0}], "extra_attrs": null, "type_ref": "ipaddress._BaseNetwork"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_unspecified of _BaseNetwork", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "netmask": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ipaddress._BaseNetwork.netmask", "name": "netmask", "type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ipaddress._A", "id": 1, "name": "_A", "namespace": "ipaddress._BaseNetwork", "upper_bound": "builtins.object", "values": ["ipaddress.IPv4Address", "ipaddress.IPv6Address"], "variance": 0}}}, "network_address": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ipaddress._BaseNetwork.network_address", "name": "network_address", "type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ipaddress._A", "id": 1, "name": "_A", "namespace": "ipaddress._BaseNetwork", "upper_bound": "builtins.object", "values": ["ipaddress.IPv4Address", "ipaddress.IPv6Address"], "variance": 0}}}, "num_addresses": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "ipaddress._BaseNetwork.num_addresses", "name": "num_addresses", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ipaddress._A", "id": 1, "name": "_A", "namespace": "ipaddress._BaseNetwork", "upper_bound": "builtins.object", "values": ["ipaddress.IPv4Address", "ipaddress.IPv6Address"], "variance": 0}], "extra_attrs": null, "type_ref": "ipaddress._BaseNetwork"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "num_addresses of _BaseNetwork", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "ipaddress._BaseNetwork.num_addresses", "name": "num_addresses", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ipaddress._A", "id": 1, "name": "_A", "namespace": "ipaddress._BaseNetwork", "upper_bound": "builtins.object", "values": ["ipaddress.IPv4Address", "ipaddress.IPv6Address"], "variance": 0}], "extra_attrs": null, "type_ref": "ipaddress._BaseNetwork"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "num_addresses of _BaseNetwork", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "overlaps": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ipaddress._BaseNetwork.overlaps", "name": "overlaps", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "other"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ipaddress._A", "id": 1, "name": "_A", "namespace": "ipaddress._BaseNetwork", "upper_bound": "builtins.object", "values": ["ipaddress.IPv4Address", "ipaddress.IPv6Address"], "variance": 0}], "extra_attrs": null, "type_ref": "ipaddress._BaseNetwork"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["ipaddress.IPv4Address"], "extra_attrs": null, "type_ref": "ipaddress._BaseNetwork"}, {".class": "Instance", "args": ["ipaddress.IPv6Address"], "extra_attrs": null, "type_ref": "ipaddress._BaseNetwork"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "overlaps of _BaseNetwork", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "prefixlen": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "ipaddress._BaseNetwork.prefixlen", "name": "prefixlen", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ipaddress._A", "id": 1, "name": "_A", "namespace": "ipaddress._BaseNetwork", "upper_bound": "builtins.object", "values": ["ipaddress.IPv4Address", "ipaddress.IPv6Address"], "variance": 0}], "extra_attrs": null, "type_ref": "ipaddress._BaseNetwork"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "prefixlen of _BaseNetwork", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "ipaddress._BaseNetwork.prefixlen", "name": "prefixlen", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ipaddress._A", "id": 1, "name": "_A", "namespace": "ipaddress._BaseNetwork", "upper_bound": "builtins.object", "values": ["ipaddress.IPv4Address", "ipaddress.IPv6Address"], "variance": 0}], "extra_attrs": null, "type_ref": "ipaddress._BaseNetwork"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "prefixlen of _BaseNetwork", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "subnet_of": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ipaddress._BaseNetwork.subnet_of", "name": "subnet_of", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "other"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ipaddress._BaseNetwork.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ipaddress._A", "id": 1, "name": "_A", "namespace": "ipaddress._BaseNetwork", "upper_bound": "builtins.object", "values": ["ipaddress.IPv4Address", "ipaddress.IPv6Address"], "variance": 0}], "extra_attrs": null, "type_ref": "ipaddress._BaseNetwork"}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ipaddress._BaseNetwork.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ipaddress._A", "id": 1, "name": "_A", "namespace": "ipaddress._BaseNetwork", "upper_bound": "builtins.object", "values": ["ipaddress.IPv4Address", "ipaddress.IPv6Address"], "variance": 0}], "extra_attrs": null, "type_ref": "ipaddress._BaseNetwork"}, "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "subnet_of of _BaseNetwork", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ipaddress._BaseNetwork.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ipaddress._A", "id": 1, "name": "_A", "namespace": "ipaddress._BaseNetwork", "upper_bound": "builtins.object", "values": ["ipaddress.IPv4Address", "ipaddress.IPv6Address"], "variance": 0}], "extra_attrs": null, "type_ref": "ipaddress._BaseNetwork"}, "values": [], "variance": 0}]}}}, "subnets": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "prefixlen_diff", "new_prefix"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ipaddress._BaseNetwork.subnets", "name": "subnets", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "prefixlen_diff", "new_prefix"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ipaddress._BaseNetwork.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ipaddress._A", "id": 1, "name": "_A", "namespace": "ipaddress._BaseNetwork", "upper_bound": "builtins.object", "values": ["ipaddress.IPv4Address", "ipaddress.IPv6Address"], "variance": 0}], "extra_attrs": null, "type_ref": "ipaddress._BaseNetwork"}, "values": [], "variance": 0}, "builtins.int", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "subnets of _BaseNetwork", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ipaddress._BaseNetwork.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ipaddress._A", "id": 1, "name": "_A", "namespace": "ipaddress._BaseNetwork", "upper_bound": "builtins.object", "values": ["ipaddress.IPv4Address", "ipaddress.IPv6Address"], "variance": 0}], "extra_attrs": null, "type_ref": "ipaddress._BaseNetwork"}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ipaddress._BaseNetwork.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ipaddress._A", "id": 1, "name": "_A", "namespace": "ipaddress._BaseNetwork", "upper_bound": "builtins.object", "values": ["ipaddress.IPv4Address", "ipaddress.IPv6Address"], "variance": 0}], "extra_attrs": null, "type_ref": "ipaddress._BaseNetwork"}, "values": [], "variance": 0}]}}}, "supernet": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "prefixlen_diff", "new_prefix"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ipaddress._BaseNetwork.supernet", "name": "supernet", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "prefixlen_diff", "new_prefix"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ipaddress._BaseNetwork.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ipaddress._A", "id": 1, "name": "_A", "namespace": "ipaddress._BaseNetwork", "upper_bound": "builtins.object", "values": ["ipaddress.IPv4Address", "ipaddress.IPv6Address"], "variance": 0}], "extra_attrs": null, "type_ref": "ipaddress._BaseNetwork"}, "values": [], "variance": 0}, "builtins.int", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "supernet of _BaseNetwork", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ipaddress._BaseNetwork.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ipaddress._A", "id": 1, "name": "_A", "namespace": "ipaddress._BaseNetwork", "upper_bound": "builtins.object", "values": ["ipaddress.IPv4Address", "ipaddress.IPv6Address"], "variance": 0}], "extra_attrs": null, "type_ref": "ipaddress._BaseNetwork"}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ipaddress._BaseNetwork.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ipaddress._A", "id": 1, "name": "_A", "namespace": "ipaddress._BaseNetwork", "upper_bound": "builtins.object", "values": ["ipaddress.IPv4Address", "ipaddress.IPv6Address"], "variance": 0}], "extra_attrs": null, "type_ref": "ipaddress._BaseNetwork"}, "values": [], "variance": 0}]}}}, "supernet_of": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ipaddress._BaseNetwork.supernet_of", "name": "supernet_of", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "other"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ipaddress._BaseNetwork.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ipaddress._A", "id": 1, "name": "_A", "namespace": "ipaddress._BaseNetwork", "upper_bound": "builtins.object", "values": ["ipaddress.IPv4Address", "ipaddress.IPv6Address"], "variance": 0}], "extra_attrs": null, "type_ref": "ipaddress._BaseNetwork"}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ipaddress._BaseNetwork.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ipaddress._A", "id": 1, "name": "_A", "namespace": "ipaddress._BaseNetwork", "upper_bound": "builtins.object", "values": ["ipaddress.IPv4Address", "ipaddress.IPv6Address"], "variance": 0}], "extra_attrs": null, "type_ref": "ipaddress._BaseNetwork"}, "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "supernet_of of _BaseNetwork", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ipaddress._BaseNetwork.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ipaddress._A", "id": 1, "name": "_A", "namespace": "ipaddress._BaseNetwork", "upper_bound": "builtins.object", "values": ["ipaddress.IPv4Address", "ipaddress.IPv6Address"], "variance": 0}], "extra_attrs": null, "type_ref": "ipaddress._BaseNetwork"}, "values": [], "variance": 0}]}}}, "with_hostmask": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "ipaddress._BaseNetwork.with_hostmask", "name": "with_hostmask", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ipaddress._A", "id": 1, "name": "_A", "namespace": "ipaddress._BaseNetwork", "upper_bound": "builtins.object", "values": ["ipaddress.IPv4Address", "ipaddress.IPv6Address"], "variance": 0}], "extra_attrs": null, "type_ref": "ipaddress._BaseNetwork"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "with_hostmask of _BaseNetwork", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "ipaddress._BaseNetwork.with_hostmask", "name": "with_hostmask", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ipaddress._A", "id": 1, "name": "_A", "namespace": "ipaddress._BaseNetwork", "upper_bound": "builtins.object", "values": ["ipaddress.IPv4Address", "ipaddress.IPv6Address"], "variance": 0}], "extra_attrs": null, "type_ref": "ipaddress._BaseNetwork"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "with_hostmask of _BaseNetwork", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "with_netmask": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "ipaddress._BaseNetwork.with_netmask", "name": "with_netmask", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ipaddress._A", "id": 1, "name": "_A", "namespace": "ipaddress._BaseNetwork", "upper_bound": "builtins.object", "values": ["ipaddress.IPv4Address", "ipaddress.IPv6Address"], "variance": 0}], "extra_attrs": null, "type_ref": "ipaddress._BaseNetwork"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "with_netmask of _BaseNetwork", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "ipaddress._BaseNetwork.with_netmask", "name": "with_netmask", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ipaddress._A", "id": 1, "name": "_A", "namespace": "ipaddress._BaseNetwork", "upper_bound": "builtins.object", "values": ["ipaddress.IPv4Address", "ipaddress.IPv6Address"], "variance": 0}], "extra_attrs": null, "type_ref": "ipaddress._BaseNetwork"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "with_netmask of _BaseNetwork", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "with_prefixlen": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "ipaddress._BaseNetwork.with_prefixlen", "name": "with_prefixlen", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ipaddress._A", "id": 1, "name": "_A", "namespace": "ipaddress._BaseNetwork", "upper_bound": "builtins.object", "values": ["ipaddress.IPv4Address", "ipaddress.IPv6Address"], "variance": 0}], "extra_attrs": null, "type_ref": "ipaddress._BaseNetwork"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "with_prefixlen of _BaseNetwork", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "ipaddress._BaseNetwork.with_prefixlen", "name": "with_prefixlen", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ipaddress._A", "id": 1, "name": "_A", "namespace": "ipaddress._BaseNetwork", "upper_bound": "builtins.object", "values": ["ipaddress.IPv4Address", "ipaddress.IPv6Address"], "variance": 0}], "extra_attrs": null, "type_ref": "ipaddress._BaseNetwork"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "with_prefixlen of _BaseNetwork", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ipaddress._BaseNetwork.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ipaddress._A", "id": 1, "name": "_A", "namespace": "ipaddress._BaseNetwork", "upper_bound": "builtins.object", "values": ["ipaddress.IPv4Address", "ipaddress.IPv6Address"], "variance": 0}], "extra_attrs": null, "type_ref": "ipaddress._BaseNetwork"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["_A"], "typeddict_type": null}}, "_BaseV4": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ipaddress._BaseV4", "name": "_BaseV4", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ipaddress._BaseV4", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "ipaddress", "mro": ["ipaddress._BaseV4", "builtins.object"], "names": {".class": "SymbolTable", "max_prefixlen": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "ipaddress._BaseV4.max_prefixlen", "name": "max_prefixlen", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["ipaddress._BaseV4"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "max_prefixlen of _BaseV4", "ret_type": {".class": "LiteralType", "fallback": "builtins.int", "value": 32}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "ipaddress._BaseV4.max_prefixlen", "name": "max_prefixlen", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["ipaddress._BaseV4"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "max_prefixlen of _BaseV4", "ret_type": {".class": "LiteralType", "fallback": "builtins.int", "value": 32}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "version": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "ipaddress._BaseV4.version", "name": "version", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["ipaddress._BaseV4"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "version of _BaseV4", "ret_type": {".class": "LiteralType", "fallback": "builtins.int", "value": 4}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "ipaddress._BaseV4.version", "name": "version", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["ipaddress._BaseV4"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "version of _BaseV4", "ret_type": {".class": "LiteralType", "fallback": "builtins.int", "value": 4}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ipaddress._BaseV4.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "ipaddress._BaseV4", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_BaseV6": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ipaddress._BaseV6", "name": "_BaseV6", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ipaddress._BaseV6", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "ipaddress", "mro": ["ipaddress._BaseV6", "builtins.object"], "names": {".class": "SymbolTable", "max_prefixlen": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "ipaddress._BaseV6.max_prefixlen", "name": "max_prefixlen", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["ipaddress._BaseV6"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "max_prefixlen of _BaseV6", "ret_type": {".class": "LiteralType", "fallback": "builtins.int", "value": 128}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "ipaddress._BaseV6.max_prefixlen", "name": "max_prefixlen", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["ipaddress._BaseV6"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "max_prefixlen of _BaseV6", "ret_type": {".class": "LiteralType", "fallback": "builtins.int", "value": 128}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "version": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "ipaddress._BaseV6.version", "name": "version", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["ipaddress._BaseV6"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "version of _BaseV6", "ret_type": {".class": "LiteralType", "fallback": "builtins.int", "value": 6}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "ipaddress._BaseV6.version", "name": "version", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["ipaddress._BaseV6"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "version of _BaseV6", "ret_type": {".class": "LiteralType", "fallback": "builtins.int", "value": 6}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ipaddress._BaseV6.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "ipaddress._BaseV6", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_IPAddressBase": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ipaddress._IPAddressBase", "name": "_IPAddressBase", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ipaddress._IPAddressBase", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "ipaddress", "mro": ["ipaddress._IPAddressBase", "builtins.object"], "names": {".class": "SymbolTable", "compressed": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "ipaddress._IPAddressBase.compressed", "name": "compressed", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["ipaddress._IPAddressBase"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "compressed of _IPAddressBase", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "ipaddress._IPAddressBase.compressed", "name": "compressed", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["ipaddress._IPAddressBase"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "compressed of _IPAddressBase", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "exploded": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "ipaddress._IPAddressBase.exploded", "name": "exploded", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["ipaddress._IPAddressBase"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "exploded of _IPAddressBase", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "ipaddress._IPAddressBase.exploded", "name": "exploded", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["ipaddress._IPAddressBase"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "exploded of _IPAddressBase", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "reverse_pointer": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "ipaddress._IPAddressBase.reverse_pointer", "name": "reverse_pointer", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["ipaddress._IPAddressBase"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "reverse_pointer of _IPAddressBase", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "ipaddress._IPAddressBase.reverse_pointer", "name": "reverse_pointer", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["ipaddress._IPAddressBase"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "reverse_pointer of _IPAddressBase", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "version": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "ipaddress._IPAddressBase.version", "name": "version", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["ipaddress._IPAddressBase"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "version of _IPAddressBase", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "ipaddress._IPAddressBase.version", "name": "version", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["ipaddress._IPAddressBase"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "version of _IPAddressBase", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ipaddress._IPAddressBase.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "ipaddress._IPAddressBase", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_N": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ipaddress._N", "name": "_N", "upper_bound": "builtins.object", "values": ["ipaddress.IPv4Network", "ipaddress.IPv6Network"], "variance": 0}}, "_RawIPAddress": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "ipaddress._RawIPAddress", "line": 13, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": ["builtins.int", "builtins.str", "builtins.bytes", "ipaddress.IPv4Address", "ipaddress.IPv6Address"], "uses_pep604_syntax": true}}}, "_RawNetworkPart": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "ipaddress._RawNetworkPart", "line": 14, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": ["ipaddress.IPv4Network", "ipaddress.IPv6Network", "ipaddress.IPv4Interface", "ipaddress.IPv6Interface"], "uses_pep604_syntax": true}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "ipaddress.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "ipaddress.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "ipaddress.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "ipaddress.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "ipaddress.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "ipaddress.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "collapse_addresses": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["addresses"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ipaddress.collapse_addresses", "name": "collapse_addresses", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["addresses"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ipaddress._N", "id": -1, "name": "_N", "namespace": "ipaddress.collapse_addresses", "upper_bound": "builtins.object", "values": ["ipaddress.IPv4Network", "ipaddress.IPv6Network"], "variance": 0}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "collapse_addresses", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ipaddress._N", "id": -1, "name": "_N", "namespace": "ipaddress.collapse_addresses", "upper_bound": "builtins.object", "values": ["ipaddress.IPv4Network", "ipaddress.IPv6Network"], "variance": 0}], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ipaddress._N", "id": -1, "name": "_N", "namespace": "ipaddress.collapse_addresses", "upper_bound": "builtins.object", "values": ["ipaddress.IPv4Network", "ipaddress.IPv6Network"], "variance": 0}]}}}, "get_mixed_type_key": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "ipaddress.get_mixed_type_key", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["obj"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "ipaddress.get_mixed_type_key", "name": "get_mixed_type_key", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["obj"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ipaddress._A", "id": -1, "name": "_A", "namespace": "ipaddress.get_mixed_type_key#0", "upper_bound": "builtins.object", "values": ["ipaddress.IPv4Address", "ipaddress.IPv6Address"], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_mixed_type_key", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.int", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ipaddress._A", "id": -1, "name": "_A", "namespace": "ipaddress.get_mixed_type_key#0", "upper_bound": "builtins.object", "values": ["ipaddress.IPv4Address", "ipaddress.IPv6Address"], "variance": 0}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ipaddress._A", "id": -1, "name": "_A", "namespace": "ipaddress.get_mixed_type_key#0", "upper_bound": "builtins.object", "values": ["ipaddress.IPv4Address", "ipaddress.IPv6Address"], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "ipaddress.get_mixed_type_key", "name": "get_mixed_type_key", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["obj"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ipaddress._A", "id": -1, "name": "_A", "namespace": "ipaddress.get_mixed_type_key#0", "upper_bound": "builtins.object", "values": ["ipaddress.IPv4Address", "ipaddress.IPv6Address"], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_mixed_type_key", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.int", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ipaddress._A", "id": -1, "name": "_A", "namespace": "ipaddress.get_mixed_type_key#0", "upper_bound": "builtins.object", "values": ["ipaddress.IPv4Address", "ipaddress.IPv6Address"], "variance": 0}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ipaddress._A", "id": -1, "name": "_A", "namespace": "ipaddress.get_mixed_type_key#0", "upper_bound": "builtins.object", "values": ["ipaddress.IPv4Address", "ipaddress.IPv6Address"], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["obj"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "ipaddress.get_mixed_type_key", "name": "get_mixed_type_key", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["obj"], "arg_types": ["ipaddress.IPv4Network"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_mixed_type_key", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "ipaddress.IPv4Address", "ipaddress.IPv4Address"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "ipaddress.get_mixed_type_key", "name": "get_mixed_type_key", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["obj"], "arg_types": ["ipaddress.IPv4Network"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_mixed_type_key", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "ipaddress.IPv4Address", "ipaddress.IPv4Address"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["obj"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "ipaddress.get_mixed_type_key", "name": "get_mixed_type_key", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["obj"], "arg_types": ["ipaddress.IPv6Network"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_mixed_type_key", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "ipaddress.IPv6Address", "ipaddress.IPv6Address"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "ipaddress.get_mixed_type_key", "name": "get_mixed_type_key", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["obj"], "arg_types": ["ipaddress.IPv6Network"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_mixed_type_key", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "ipaddress.IPv6Address", "ipaddress.IPv6Address"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": ["obj"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ipaddress._A", "id": -1, "name": "_A", "namespace": "ipaddress.get_mixed_type_key#0", "upper_bound": "builtins.object", "values": ["ipaddress.IPv4Address", "ipaddress.IPv6Address"], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_mixed_type_key", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.int", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ipaddress._A", "id": -1, "name": "_A", "namespace": "ipaddress.get_mixed_type_key#0", "upper_bound": "builtins.object", "values": ["ipaddress.IPv4Address", "ipaddress.IPv6Address"], "variance": 0}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ipaddress._A", "id": -1, "name": "_A", "namespace": "ipaddress.get_mixed_type_key#0", "upper_bound": "builtins.object", "values": ["ipaddress.IPv4Address", "ipaddress.IPv6Address"], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0], "arg_names": ["obj"], "arg_types": ["ipaddress.IPv4Network"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_mixed_type_key", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "ipaddress.IPv4Address", "ipaddress.IPv4Address"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0], "arg_names": ["obj"], "arg_types": ["ipaddress.IPv6Network"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_mixed_type_key", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "ipaddress.IPv6Address", "ipaddress.IPv6Address"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "ip_address": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["address"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ipaddress.ip_address", "name": "ip_address", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["address"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "ipaddress._RawIPAddress"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ip_address", "ret_type": {".class": "UnionType", "items": ["ipaddress.IPv4Address", "ipaddress.IPv6Address"], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "ip_interface": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["address"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ipaddress.ip_interface", "name": "ip_interface", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["address"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "ipaddress._RawIPAddress"}, {".class": "TypeAliasType", "args": [], "type_ref": "ipaddress._RawNetworkPart"}, {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "ipaddress._RawIPAddress"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "ipaddress._RawIPAddress"}, "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ip_interface", "ret_type": {".class": "UnionType", "items": ["ipaddress.IPv4Interface", "ipaddress.IPv6Interface"], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "ip_network": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["address", "strict"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ipaddress.ip_network", "name": "ip_network", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["address", "strict"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "ipaddress._RawIPAddress"}, {".class": "TypeAliasType", "args": [], "type_ref": "ipaddress._RawNetworkPart"}, {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "ipaddress._RawIPAddress"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "ipaddress._RawIPAddress"}, "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ip_network", "ret_type": {".class": "UnionType", "items": ["ipaddress.IPv4Network", "ipaddress.IPv6Network"], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "overload": {".class": "SymbolTableNode", "cross_ref": "typing.overload", "kind": "Gdef", "module_hidden": true, "module_public": false}, "summarize_address_range": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "ipaddress.summarize_address_range", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["first", "last"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "ipaddress.summarize_address_range", "name": "summarize_address_range", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["first", "last"], "arg_types": ["ipaddress.IPv4Address", "ipaddress.IPv4Address"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "summarize_address_range", "ret_type": {".class": "Instance", "args": ["ipaddress.IPv4Network"], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "ipaddress.summarize_address_range", "name": "summarize_address_range", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["first", "last"], "arg_types": ["ipaddress.IPv4Address", "ipaddress.IPv4Address"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "summarize_address_range", "ret_type": {".class": "Instance", "args": ["ipaddress.IPv4Network"], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["first", "last"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "ipaddress.summarize_address_range", "name": "summarize_address_range", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["first", "last"], "arg_types": ["ipaddress.IPv6Address", "ipaddress.IPv6Address"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "summarize_address_range", "ret_type": {".class": "Instance", "args": ["ipaddress.IPv6Network"], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "ipaddress.summarize_address_range", "name": "summarize_address_range", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["first", "last"], "arg_types": ["ipaddress.IPv6Address", "ipaddress.IPv6Address"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "summarize_address_range", "ret_type": {".class": "Instance", "args": ["ipaddress.IPv6Network"], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["first", "last"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "ipaddress.summarize_address_range", "name": "summarize_address_range", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["first", "last"], "arg_types": [{".class": "UnionType", "items": ["ipaddress.IPv4Address", "ipaddress.IPv6Address"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["ipaddress.IPv4Address", "ipaddress.IPv6Address"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "summarize_address_range", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["ipaddress.IPv4Network"], "extra_attrs": null, "type_ref": "typing.Iterator"}, {".class": "Instance", "args": ["ipaddress.IPv6Network"], "extra_attrs": null, "type_ref": "typing.Iterator"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "ipaddress.summarize_address_range", "name": "summarize_address_range", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["first", "last"], "arg_types": [{".class": "UnionType", "items": ["ipaddress.IPv4Address", "ipaddress.IPv6Address"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["ipaddress.IPv4Address", "ipaddress.IPv6Address"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "summarize_address_range", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["ipaddress.IPv4Network"], "extra_attrs": null, "type_ref": "typing.Iterator"}, {".class": "Instance", "args": ["ipaddress.IPv6Network"], "extra_attrs": null, "type_ref": "typing.Iterator"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["first", "last"], "arg_types": ["ipaddress.IPv4Address", "ipaddress.IPv4Address"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "summarize_address_range", "ret_type": {".class": "Instance", "args": ["ipaddress.IPv4Network"], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["first", "last"], "arg_types": ["ipaddress.IPv6Address", "ipaddress.IPv6Address"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "summarize_address_range", "ret_type": {".class": "Instance", "args": ["ipaddress.IPv6Network"], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["first", "last"], "arg_types": [{".class": "UnionType", "items": ["ipaddress.IPv4Address", "ipaddress.IPv6Address"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["ipaddress.IPv4Address", "ipaddress.IPv6Address"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "summarize_address_range", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["ipaddress.IPv4Network"], "extra_attrs": null, "type_ref": "typing.Iterator"}, {".class": "Instance", "args": ["ipaddress.IPv6Network"], "extra_attrs": null, "type_ref": "typing.Iterator"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef", "module_hidden": true, "module_public": false}, "v4_int_to_packed": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["address"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ipaddress.v4_int_to_packed", "name": "v4_int_to_packed", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["address"], "arg_types": ["builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "v4_int_to_packed", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "v6_int_to_packed": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["address"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ipaddress.v6_int_to_packed", "name": "v6_int_to_packed", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["address"], "arg_types": ["builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "v6_int_to_packed", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "path": "c:\\Users\\<USER>\\.vscode\\extensions\\ms-python.mypy-type-checker-2025.2.0\\bundled\\libs\\mypy\\typeshed\\stdlib\\ipaddress.pyi"}