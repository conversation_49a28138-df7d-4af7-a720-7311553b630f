{"data_mtime": 1750100832, "dep_lines": [1, 1, 1, 1, 1, 1], "dep_prios": [5, 30, 30, 30, 30, 30], "dependencies": ["builtins", "_frozen_importlib", "_typeshed", "abc", "typing", "typing_extensions"], "hash": "ccbe31f3b8fe7a1461367fce0e70a2e8c13e3b82", "id": "cv2.gapi.video", "ignore_all": true, "interface_hash": "b7497c1c03a215a7d3cb2a69d8215c4c15b033a8", "mtime": 1750096905, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Python313\\Lib\\site-packages\\cv2\\gapi\\video\\__init__.pyi", "plugin_data": null, "size": 160, "suppressed": [], "version_id": "1.15.0"}