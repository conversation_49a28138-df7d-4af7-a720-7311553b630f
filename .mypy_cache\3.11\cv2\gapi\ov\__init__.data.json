{".class": "MypyFile", "_fullname": "cv2.gapi.ov", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "PyParams": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "cv2.gapi.ov.PyParams", "name": "PyParams", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "cv2.gapi.ov.PyParams", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "cv2.gapi.ov", "mro": ["cv2.gapi.ov.PyParams", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "cv2.gapi.ov.PyParams.__init__", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.gapi.ov.PyParams.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cv2.gapi.ov.PyParams"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of PyParams", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.gapi.ov.PyParams.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cv2.gapi.ov.PyParams"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of PyParams", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "tag", "model_path", "bin_path", "device"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.gapi.ov.PyParams.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "tag", "model_path", "bin_path", "device"], "arg_types": ["cv2.gapi.ov.PyParams", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of PyParams", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.gapi.ov.PyParams.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "tag", "model_path", "bin_path", "device"], "arg_types": ["cv2.gapi.ov.PyParams", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of PyParams", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "tag", "blob_path", "device"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.gapi.ov.PyParams.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "tag", "blob_path", "device"], "arg_types": ["cv2.gapi.ov.PyParams", "builtins.str", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of PyParams", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.gapi.ov.PyParams.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "tag", "blob_path", "device"], "arg_types": ["cv2.gapi.ov.PyParams", "builtins.str", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of PyParams", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cv2.gapi.ov.PyParams"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of PyParams", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "tag", "model_path", "bin_path", "device"], "arg_types": ["cv2.gapi.ov.PyParams", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of PyParams", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "tag", "blob_path", "device"], "arg_types": ["cv2.gapi.ov.PyParams", "builtins.str", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of PyParams", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "cfgInputModelLayout": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "cv2.gapi.ov.PyParams.cfgInputModelLayout", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "tensor_layout"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.gapi.ov.PyParams.cfgInputModelLayout", "name": "cfgInputModelLayout", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "tensor_layout"], "arg_types": ["cv2.gapi.ov.PyParams", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cfgInputModelLayout of PyParams", "ret_type": "cv2.gapi.ov.PyParams", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.gapi.ov.PyParams.cfgInputModelLayout", "name": "cfgInputModelLayout", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "tensor_layout"], "arg_types": ["cv2.gapi.ov.PyParams", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cfgInputModelLayout of PyParams", "ret_type": "cv2.gapi.ov.PyParams", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "layout_map"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.gapi.ov.PyParams.cfgInputModelLayout", "name": "cfgInputModelLayout", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "layout_map"], "arg_types": ["cv2.gapi.ov.PyParams", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.map_string_and_string"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cfgInputModelLayout of PyParams", "ret_type": "cv2.gapi.ov.PyParams", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.gapi.ov.PyParams.cfgInputModelLayout", "name": "cfgInputModelLayout", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "layout_map"], "arg_types": ["cv2.gapi.ov.PyParams", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.map_string_and_string"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cfgInputModelLayout of PyParams", "ret_type": "cv2.gapi.ov.PyParams", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "tensor_layout"], "arg_types": ["cv2.gapi.ov.PyParams", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cfgInputModelLayout of PyParams", "ret_type": "cv2.gapi.ov.PyParams", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "layout_map"], "arg_types": ["cv2.gapi.ov.PyParams", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.map_string_and_string"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cfgInputModelLayout of PyParams", "ret_type": "cv2.gapi.ov.PyParams", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "cfgInputTensorLayout": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "cv2.gapi.ov.PyParams.cfgInputTensorLayout", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "tensor_layout"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.gapi.ov.PyParams.cfgInputTensorLayout", "name": "cfgInputTensorLayout", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "tensor_layout"], "arg_types": ["cv2.gapi.ov.PyParams", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cfgInputTensorLayout of PyParams", "ret_type": "cv2.gapi.ov.PyParams", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.gapi.ov.PyParams.cfgInputTensorLayout", "name": "cfgInputTensorLayout", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "tensor_layout"], "arg_types": ["cv2.gapi.ov.PyParams", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cfgInputTensorLayout of PyParams", "ret_type": "cv2.gapi.ov.PyParams", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "layout_map"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.gapi.ov.PyParams.cfgInputTensorLayout", "name": "cfgInputTensorLayout", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "layout_map"], "arg_types": ["cv2.gapi.ov.PyParams", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.map_string_and_string"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cfgInputTensorLayout of PyParams", "ret_type": "cv2.gapi.ov.PyParams", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.gapi.ov.PyParams.cfgInputTensorLayout", "name": "cfgInputTensorLayout", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "layout_map"], "arg_types": ["cv2.gapi.ov.PyParams", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.map_string_and_string"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cfgInputTensorLayout of PyParams", "ret_type": "cv2.gapi.ov.PyParams", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "tensor_layout"], "arg_types": ["cv2.gapi.ov.PyParams", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cfgInputTensorLayout of PyParams", "ret_type": "cv2.gapi.ov.PyParams", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "layout_map"], "arg_types": ["cv2.gapi.ov.PyParams", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.map_string_and_string"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cfgInputTensorLayout of PyParams", "ret_type": "cv2.gapi.ov.PyParams", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "cfgMean": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "cv2.gapi.ov.PyParams.cfgMean", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "mean_values"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.gapi.ov.PyParams.cfgMean", "name": "cfg<PERSON><PERSON>", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "mean_values"], "arg_types": ["cv2.gapi.ov.PyParams", {".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cfgMean of PyParams", "ret_type": "cv2.gapi.ov.PyParams", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.gapi.ov.PyParams.cfgMean", "name": "cfg<PERSON><PERSON>", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "mean_values"], "arg_types": ["cv2.gapi.ov.PyParams", {".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cfgMean of PyParams", "ret_type": "cv2.gapi.ov.PyParams", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "mean_map"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.gapi.ov.PyParams.cfgMean", "name": "cfg<PERSON><PERSON>", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "mean_map"], "arg_types": ["cv2.gapi.ov.PyParams", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.map_string_and_vector_float"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cfgMean of PyParams", "ret_type": "cv2.gapi.ov.PyParams", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.gapi.ov.PyParams.cfgMean", "name": "cfg<PERSON><PERSON>", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "mean_map"], "arg_types": ["cv2.gapi.ov.PyParams", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.map_string_and_vector_float"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cfgMean of PyParams", "ret_type": "cv2.gapi.ov.PyParams", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "mean_values"], "arg_types": ["cv2.gapi.ov.PyParams", {".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cfgMean of PyParams", "ret_type": "cv2.gapi.ov.PyParams", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "mean_map"], "arg_types": ["cv2.gapi.ov.PyParams", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.map_string_and_vector_float"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cfgMean of PyParams", "ret_type": "cv2.gapi.ov.PyParams", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "cfgNumRequests": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "nireq"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.gapi.ov.PyParams.cfgNumRequests", "name": "cfgNumRequests", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "nireq"], "arg_types": ["cv2.gapi.ov.PyParams", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cfgNumRequests of PyParams", "ret_type": "cv2.gapi.ov.PyParams", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "cfgOutputModelLayout": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "cv2.gapi.ov.PyParams.cfgOutputModelLayout", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "tensor_layout"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.gapi.ov.PyParams.cfgOutputModelLayout", "name": "cfgOutputModelLayout", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "tensor_layout"], "arg_types": ["cv2.gapi.ov.PyParams", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cfgOutputModelLayout of PyParams", "ret_type": "cv2.gapi.ov.PyParams", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.gapi.ov.PyParams.cfgOutputModelLayout", "name": "cfgOutputModelLayout", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "tensor_layout"], "arg_types": ["cv2.gapi.ov.PyParams", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cfgOutputModelLayout of PyParams", "ret_type": "cv2.gapi.ov.PyParams", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "layout_map"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.gapi.ov.PyParams.cfgOutputModelLayout", "name": "cfgOutputModelLayout", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "layout_map"], "arg_types": ["cv2.gapi.ov.PyParams", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.map_string_and_string"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cfgOutputModelLayout of PyParams", "ret_type": "cv2.gapi.ov.PyParams", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.gapi.ov.PyParams.cfgOutputModelLayout", "name": "cfgOutputModelLayout", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "layout_map"], "arg_types": ["cv2.gapi.ov.PyParams", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.map_string_and_string"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cfgOutputModelLayout of PyParams", "ret_type": "cv2.gapi.ov.PyParams", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "tensor_layout"], "arg_types": ["cv2.gapi.ov.PyParams", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cfgOutputModelLayout of PyParams", "ret_type": "cv2.gapi.ov.PyParams", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "layout_map"], "arg_types": ["cv2.gapi.ov.PyParams", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.map_string_and_string"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cfgOutputModelLayout of PyParams", "ret_type": "cv2.gapi.ov.PyParams", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "cfgOutputTensorLayout": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "cv2.gapi.ov.PyParams.cfgOutputTensorLayout", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "tensor_layout"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.gapi.ov.PyParams.cfgOutputTensorLayout", "name": "cfgOutputTensorLayout", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "tensor_layout"], "arg_types": ["cv2.gapi.ov.PyParams", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cfgOutputTensorLayout of PyParams", "ret_type": "cv2.gapi.ov.PyParams", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.gapi.ov.PyParams.cfgOutputTensorLayout", "name": "cfgOutputTensorLayout", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "tensor_layout"], "arg_types": ["cv2.gapi.ov.PyParams", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cfgOutputTensorLayout of PyParams", "ret_type": "cv2.gapi.ov.PyParams", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "layout_map"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.gapi.ov.PyParams.cfgOutputTensorLayout", "name": "cfgOutputTensorLayout", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "layout_map"], "arg_types": ["cv2.gapi.ov.PyParams", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.map_string_and_string"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cfgOutputTensorLayout of PyParams", "ret_type": "cv2.gapi.ov.PyParams", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.gapi.ov.PyParams.cfgOutputTensorLayout", "name": "cfgOutputTensorLayout", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "layout_map"], "arg_types": ["cv2.gapi.ov.PyParams", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.map_string_and_string"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cfgOutputTensorLayout of PyParams", "ret_type": "cv2.gapi.ov.PyParams", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "tensor_layout"], "arg_types": ["cv2.gapi.ov.PyParams", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cfgOutputTensorLayout of PyParams", "ret_type": "cv2.gapi.ov.PyParams", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "layout_map"], "arg_types": ["cv2.gapi.ov.PyParams", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.map_string_and_string"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cfgOutputTensorLayout of PyParams", "ret_type": "cv2.gapi.ov.PyParams", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "cfgOutputTensorPrecision": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "cv2.gapi.ov.PyParams.cfgOutputTensorPrecision", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "precision"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.gapi.ov.PyParams.cfgOutputTensorPrecision", "name": "cfgOutputTensorPrecision", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "precision"], "arg_types": ["cv2.gapi.ov.PyParams", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cfgOutputTensorPrecision of PyParams", "ret_type": "cv2.gapi.ov.PyParams", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.gapi.ov.PyParams.cfgOutputTensorPrecision", "name": "cfgOutputTensorPrecision", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "precision"], "arg_types": ["cv2.gapi.ov.PyParams", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cfgOutputTensorPrecision of PyParams", "ret_type": "cv2.gapi.ov.PyParams", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "precision_map"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.gapi.ov.PyParams.cfgOutputTensorPrecision", "name": "cfgOutputTensorPrecision", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "precision_map"], "arg_types": ["cv2.gapi.ov.PyParams", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.map_string_and_int"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cfgOutputTensorPrecision of PyParams", "ret_type": "cv2.gapi.ov.PyParams", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.gapi.ov.PyParams.cfgOutputTensorPrecision", "name": "cfgOutputTensorPrecision", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "precision_map"], "arg_types": ["cv2.gapi.ov.PyParams", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.map_string_and_int"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cfgOutputTensorPrecision of PyParams", "ret_type": "cv2.gapi.ov.PyParams", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "precision"], "arg_types": ["cv2.gapi.ov.PyParams", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cfgOutputTensorPrecision of PyParams", "ret_type": "cv2.gapi.ov.PyParams", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "precision_map"], "arg_types": ["cv2.gapi.ov.PyParams", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.map_string_and_int"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cfgOutputTensorPrecision of PyParams", "ret_type": "cv2.gapi.ov.PyParams", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "cfgPluginConfig": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.gapi.ov.PyParams.cfgPluginConfig", "name": "cfgPluginConfig", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "config"], "arg_types": ["cv2.gapi.ov.PyParams", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.map_string_and_string"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cfgPluginConfig of PyParams", "ret_type": "cv2.gapi.ov.PyParams", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "cfgReshape": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "cv2.gapi.ov.PyParams.cfgReshape", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "new_shape"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.gapi.ov.PyParams.cfgReshape", "name": "cfgReshape", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "new_shape"], "arg_types": ["cv2.gapi.ov.PyParams", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cfgReshape of PyParams", "ret_type": "cv2.gapi.ov.PyParams", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.gapi.ov.PyParams.cfgReshape", "name": "cfgReshape", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "new_shape"], "arg_types": ["cv2.gapi.ov.PyParams", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cfgReshape of PyParams", "ret_type": "cv2.gapi.ov.PyParams", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "new_shape_map"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.gapi.ov.PyParams.cfgReshape", "name": "cfgReshape", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "new_shape_map"], "arg_types": ["cv2.gapi.ov.PyParams", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.map_string_and_vector_size_t"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cfgReshape of PyParams", "ret_type": "cv2.gapi.ov.PyParams", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.gapi.ov.PyParams.cfgReshape", "name": "cfgReshape", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "new_shape_map"], "arg_types": ["cv2.gapi.ov.PyParams", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.map_string_and_vector_size_t"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cfgReshape of PyParams", "ret_type": "cv2.gapi.ov.PyParams", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "new_shape"], "arg_types": ["cv2.gapi.ov.PyParams", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cfgReshape of PyParams", "ret_type": "cv2.gapi.ov.PyParams", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "new_shape_map"], "arg_types": ["cv2.gapi.ov.PyParams", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.map_string_and_vector_size_t"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cfgReshape of PyParams", "ret_type": "cv2.gapi.ov.PyParams", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "cfgResize": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "cv2.gapi.ov.PyParams.cfgResize", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "interpolation"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.gapi.ov.PyParams.cfgResize", "name": "cfgResize", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "interpolation"], "arg_types": ["cv2.gapi.ov.PyParams", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cfgResize of PyParams", "ret_type": "cv2.gapi.ov.PyParams", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.gapi.ov.PyParams.cfgResize", "name": "cfgResize", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "interpolation"], "arg_types": ["cv2.gapi.ov.PyParams", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cfgResize of PyParams", "ret_type": "cv2.gapi.ov.PyParams", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "interpolation"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.gapi.ov.PyParams.cfgResize", "name": "cfgResize", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "interpolation"], "arg_types": ["cv2.gapi.ov.PyParams", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.map_string_and_int"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cfgResize of PyParams", "ret_type": "cv2.gapi.ov.PyParams", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.gapi.ov.PyParams.cfgResize", "name": "cfgResize", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "interpolation"], "arg_types": ["cv2.gapi.ov.PyParams", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.map_string_and_int"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cfgResize of PyParams", "ret_type": "cv2.gapi.ov.PyParams", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "interpolation"], "arg_types": ["cv2.gapi.ov.PyParams", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cfgResize of PyParams", "ret_type": "cv2.gapi.ov.PyParams", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "interpolation"], "arg_types": ["cv2.gapi.ov.PyParams", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.map_string_and_int"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cfgResize of PyParams", "ret_type": "cv2.gapi.ov.PyParams", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "cfgScale": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "cv2.gapi.ov.PyParams.cfgScale", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "scale_values"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.gapi.ov.PyParams.cfgScale", "name": "cfgScale", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "scale_values"], "arg_types": ["cv2.gapi.ov.PyParams", {".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cfgScale of PyParams", "ret_type": "cv2.gapi.ov.PyParams", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.gapi.ov.PyParams.cfgScale", "name": "cfgScale", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "scale_values"], "arg_types": ["cv2.gapi.ov.PyParams", {".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cfgScale of PyParams", "ret_type": "cv2.gapi.ov.PyParams", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "scale_map"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.gapi.ov.PyParams.cfgScale", "name": "cfgScale", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "scale_map"], "arg_types": ["cv2.gapi.ov.PyParams", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.map_string_and_vector_float"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cfgScale of PyParams", "ret_type": "cv2.gapi.ov.PyParams", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.gapi.ov.PyParams.cfgScale", "name": "cfgScale", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "scale_map"], "arg_types": ["cv2.gapi.ov.PyParams", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.map_string_and_vector_float"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cfgScale of PyParams", "ret_type": "cv2.gapi.ov.PyParams", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "scale_values"], "arg_types": ["cv2.gapi.ov.PyParams", {".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cfgScale of PyParams", "ret_type": "cv2.gapi.ov.PyParams", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "scale_map"], "arg_types": ["cv2.gapi.ov.PyParams", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.map_string_and_vector_float"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cfgScale of PyParams", "ret_type": "cv2.gapi.ov.PyParams", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cv2.gapi.ov.PyParams.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cv2.gapi.ov.PyParams", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "cv2.gapi.ov.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.gapi.ov.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.gapi.ov.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.gapi.ov.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.gapi.ov.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.gapi.ov.__package__", "name": "__package__", "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.gapi.ov.__path__", "name": "__path__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.gapi.ov.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_typing": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef", "module_hidden": true, "module_public": false}, "cv2": {".class": "SymbolTableNode", "cross_ref": "cv2", "kind": "Gdef", "module_hidden": true, "module_public": false}, "params": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "cv2.gapi.ov.params", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["tag", "model_path", "weights", "device"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.gapi.ov.params", "name": "params", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["tag", "model_path", "weights", "device"], "arg_types": ["builtins.str", "builtins.str", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "params", "ret_type": "cv2.gapi.ov.PyParams", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.gapi.ov.params", "name": "params", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["tag", "model_path", "weights", "device"], "arg_types": ["builtins.str", "builtins.str", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "params", "ret_type": "cv2.gapi.ov.PyParams", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["tag", "bin_path", "device"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.gapi.ov.params", "name": "params", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["tag", "bin_path", "device"], "arg_types": ["builtins.str", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "params", "ret_type": "cv2.gapi.ov.PyParams", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.gapi.ov.params", "name": "params", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["tag", "bin_path", "device"], "arg_types": ["builtins.str", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "params", "ret_type": "cv2.gapi.ov.PyParams", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["tag", "model_path", "weights", "device"], "arg_types": ["builtins.str", "builtins.str", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "params", "ret_type": "cv2.gapi.ov.PyParams", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["tag", "bin_path", "device"], "arg_types": ["builtins.str", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "params", "ret_type": "cv2.gapi.ov.PyParams", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}}, "path": "c:\\Python313\\Lib\\site-packages\\cv2\\gapi\\ov\\__init__.pyi"}