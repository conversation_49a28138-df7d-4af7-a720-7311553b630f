{".class": "MypyFile", "_fullname": "numpy.polynomial.laguerre", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "ABCPolyBase": {".class": "SymbolTableNode", "cross_ref": "numpy.polynomial._polybase.ABCPolyBase", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Final": {".class": "SymbolTableNode", "cross_ref": "typing.Final", "kind": "Gdef", "module_hidden": true, "module_public": false}, "L": {".class": "SymbolTableNode", "cross_ref": "typing.Literal", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Laguerre": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "L"}], "extra_attrs": null, "type_ref": "numpy.polynomial._polybase.ABCPolyBase"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy.polynomial.laguerre.Laguerre", "name": "<PERSON><PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "numpy.polynomial.laguerre.Laguerre", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "numpy.polynomial.laguerre", "mro": ["numpy.polynomial.laguerre.Laguerre", "numpy.polynomial._polybase.ABCPolyBase", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial.laguerre.Laguerre.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "numpy.polynomial.laguerre.Laguerre", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_Array1": {".class": "SymbolTableNode", "cross_ref": "numpy.polynomial._polytypes._Array1", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_Array2": {".class": "SymbolTableNode", "cross_ref": "numpy.polynomial._polytypes._Array2", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_FuncBinOp": {".class": "SymbolTableNode", "cross_ref": "numpy.polynomial._polytypes._FuncBinOp", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_FuncCompanion": {".class": "SymbolTableNode", "cross_ref": "numpy.polynomial._polytypes._FuncCompanion", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_FuncDer": {".class": "SymbolTableNode", "cross_ref": "numpy.polynomial._polytypes._FuncDer", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_FuncFit": {".class": "SymbolTableNode", "cross_ref": "numpy.polynomial._polytypes._FuncFit", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_FuncFromRoots": {".class": "SymbolTableNode", "cross_ref": "numpy.polynomial._polytypes._FuncFromRoots", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_FuncGauss": {".class": "SymbolTableNode", "cross_ref": "numpy.polynomial._polytypes._FuncGauss", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_FuncInteg": {".class": "SymbolTableNode", "cross_ref": "numpy.polynomial._polytypes._FuncInteg", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_FuncLine": {".class": "SymbolTableNode", "cross_ref": "numpy.polynomial._polytypes._FuncLine", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_FuncPoly2Ortho": {".class": "SymbolTableNode", "cross_ref": "numpy.polynomial._polytypes._FuncPoly2Ortho", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_FuncPow": {".class": "SymbolTableNode", "cross_ref": "numpy.polynomial._polytypes._FuncPow", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_FuncRoots": {".class": "SymbolTableNode", "cross_ref": "numpy.polynomial._polytypes._FuncRoots", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_FuncUnOp": {".class": "SymbolTableNode", "cross_ref": "numpy.polynomial._polytypes._FuncUnOp", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_FuncVal": {".class": "SymbolTableNode", "cross_ref": "numpy.polynomial._polytypes._FuncVal", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_FuncVal2D": {".class": "SymbolTableNode", "cross_ref": "numpy.polynomial._polytypes._FuncVal2D", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_FuncVal3D": {".class": "SymbolTableNode", "cross_ref": "numpy.polynomial._polytypes._FuncVal3D", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_FuncValFromRoots": {".class": "SymbolTableNode", "cross_ref": "numpy.polynomial._polytypes._FuncValFromRoots", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_FuncVander": {".class": "SymbolTableNode", "cross_ref": "numpy.polynomial._polytypes._FuncVander", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_FuncVander2D": {".class": "SymbolTableNode", "cross_ref": "numpy.polynomial._polytypes._FuncVander2D", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_FuncVander3D": {".class": "SymbolTableNode", "cross_ref": "numpy.polynomial._polytypes._FuncVander3D", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_FuncWeight": {".class": "SymbolTableNode", "cross_ref": "numpy.polynomial._polytypes._FuncWeight", "kind": "Gdef", "module_hidden": true, "module_public": false}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "numpy.polynomial.laguerre.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.laguerre.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.laguerre.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.laguerre.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.laguerre.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.laguerre.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.laguerre.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "lag2poly": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.laguerre.lag2poly", "name": "lag2poly", "type": {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "lag2poly"}], "extra_attrs": null, "type_ref": "numpy.polynomial._polytypes._FuncUnOp"}}}, "lagadd": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.laguerre.lagadd", "name": "lagadd", "type": {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "lagadd"}], "extra_attrs": null, "type_ref": "numpy.polynomial._polytypes._FuncBinOp"}}}, "lagcompanion": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.laguerre.lagcompanion", "name": "lagcompanion", "type": {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "lagcompanion"}], "extra_attrs": null, "type_ref": "numpy.polynomial._polytypes._FuncCompanion"}}}, "lagder": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.laguerre.lagder", "name": "lagder", "type": {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "lagder"}], "extra_attrs": null, "type_ref": "numpy.polynomial._polytypes._FuncDer"}}}, "lagdiv": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.laguerre.lagdiv", "name": "lagdiv", "type": {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "lagdiv"}], "extra_attrs": null, "type_ref": "numpy.polynomial._polytypes._FuncBinOp"}}}, "lagdomain": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "numpy.polynomial.laguerre.lagdomain", "name": "lagdomain", "type": {".class": "TypeAliasType", "args": ["numpy.float64"], "type_ref": "numpy.polynomial._polytypes._Array2"}}}, "lagfit": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.laguerre.lagfit", "name": "lagfit", "type": {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "lagfit"}], "extra_attrs": null, "type_ref": "numpy.polynomial._polytypes._FuncFit"}}}, "lagfromroots": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.laguerre.lagfromroots", "name": "lagfromroots", "type": {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "lagfromroots"}], "extra_attrs": null, "type_ref": "numpy.polynomial._polytypes._FuncFromRoots"}}}, "laggauss": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.laguerre.laggauss", "name": "laggauss", "type": {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "laggauss"}], "extra_attrs": null, "type_ref": "numpy.polynomial._polytypes._FuncGauss"}}}, "laggrid2d": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.laguerre.laggrid2d", "name": "laggrid2d", "type": {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "laggrid2d"}], "extra_attrs": null, "type_ref": "numpy.polynomial._polytypes._FuncVal2D"}}}, "laggrid3d": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.laguerre.laggrid3d", "name": "laggrid3d", "type": {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "laggrid3d"}], "extra_attrs": null, "type_ref": "numpy.polynomial._polytypes._FuncVal3D"}}}, "lagint": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.laguerre.lagint", "name": "lagint", "type": {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "lagint"}], "extra_attrs": null, "type_ref": "numpy.polynomial._polytypes._FuncInteg"}}}, "lagline": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.laguerre.lagline", "name": "lagline", "type": {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "lagline"}], "extra_attrs": null, "type_ref": "numpy.polynomial._polytypes._FuncLine"}}}, "lagmul": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.laguerre.lagmul", "name": "lagmul", "type": {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "lagmul"}], "extra_attrs": null, "type_ref": "numpy.polynomial._polytypes._FuncBinOp"}}}, "lagmulx": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.laguerre.lagmulx", "name": "lagmulx", "type": {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "lagmulx"}], "extra_attrs": null, "type_ref": "numpy.polynomial._polytypes._FuncUnOp"}}}, "lagone": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "numpy.polynomial.laguerre.lagone", "name": "lagone", "type": {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.int_"}], "type_ref": "numpy.polynomial._polytypes._Array1"}}}, "lagpow": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.laguerre.lagpow", "name": "lagpow", "type": {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "lagpow"}], "extra_attrs": null, "type_ref": "numpy.polynomial._polytypes._FuncPow"}}}, "lagroots": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.laguerre.lagroots", "name": "lagroots", "type": {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "lagroots"}], "extra_attrs": null, "type_ref": "numpy.polynomial._polytypes._FuncRoots"}}}, "lagsub": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.laguerre.lagsub", "name": "lagsub", "type": {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "lagsub"}], "extra_attrs": null, "type_ref": "numpy.polynomial._polytypes._FuncBinOp"}}}, "lagtrim": {".class": "SymbolTableNode", "cross_ref": "numpy.polynomial.polyutils.trimcoef", "kind": "Gdef"}, "lagval": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.laguerre.lagval", "name": "lagval", "type": {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "lagval"}], "extra_attrs": null, "type_ref": "numpy.polynomial._polytypes._FuncVal"}}}, "lagval2d": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.laguerre.lagval2d", "name": "lagval2d", "type": {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "lagval2d"}], "extra_attrs": null, "type_ref": "numpy.polynomial._polytypes._FuncVal2D"}}}, "lagval3d": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.laguerre.lagval3d", "name": "lagval3d", "type": {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "lagval3d"}], "extra_attrs": null, "type_ref": "numpy.polynomial._polytypes._FuncVal3D"}}}, "lagvalfromroots": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.laguerre.lagvalfromroots", "name": "lagvalfromroots", "type": {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "lagvalfromroots"}], "extra_attrs": null, "type_ref": "numpy.polynomial._polytypes._FuncValFromRoots"}}}, "lagvander": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.laguerre.lagvander", "name": "lagvander", "type": {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "lagvander"}], "extra_attrs": null, "type_ref": "numpy.polynomial._polytypes._FuncVander"}}}, "lagvander2d": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.laguerre.lagvander2d", "name": "lagvander2d", "type": {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "lagvander2d"}], "extra_attrs": null, "type_ref": "numpy.polynomial._polytypes._FuncVander2D"}}}, "lagvander3d": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.laguerre.lagvander3d", "name": "lagvander3d", "type": {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "lagvander3d"}], "extra_attrs": null, "type_ref": "numpy.polynomial._polytypes._FuncVander3D"}}}, "lagweight": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.laguerre.lagweight", "name": "lagweight", "type": {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "lagweight"}], "extra_attrs": null, "type_ref": "numpy.polynomial._polytypes._FuncWeight"}}}, "lagx": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "numpy.polynomial.laguerre.lagx", "name": "lagx", "type": {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.int_"}], "type_ref": "numpy.polynomial._polytypes._Array2"}}}, "lagzero": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "numpy.polynomial.laguerre.lagzero", "name": "lagzero", "type": {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.int_"}], "type_ref": "numpy.polynomial._polytypes._Array1"}}}, "np": {".class": "SymbolTableNode", "cross_ref": "numpy", "kind": "Gdef", "module_hidden": true, "module_public": false}, "poly2lag": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.laguerre.poly2lag", "name": "poly2lag", "type": {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "poly2lag"}], "extra_attrs": null, "type_ref": "numpy.polynomial._polytypes._FuncPoly2Ortho"}}}}, "path": "c:\\Python313\\Lib\\site-packages\\numpy\\polynomial\\laguerre.pyi"}