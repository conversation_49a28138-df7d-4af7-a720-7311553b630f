# -*- coding: utf-8 -*-
"""
服务层测试用例

测试核心服务的功能和性能。
"""

import unittest
import tempfile
import os
from unittest.mock import Mock, patch, MagicMock
from pathlib import Path

import numpy as np
from PIL import Image

# 导入要测试的服务
from src.services.window_service import WindowService, WindowInfo
from src.services.vision_service import VisionService, DetectionResult
from src.services.ocr_service import OCRService, OCREngine, TextType, OCRResult
from src.services.strategy_service import StrategyService, StrategyDifficulty, RiskPreference
from src.models.game_state import GameState, Player, Card, CardSuit, CardRank
from src.utils.config import ConfigManager


class TestWindowService(unittest.TestCase):
    """窗口服务测试"""
    
    def setUp(self):
        """测试前准备"""
        self.config = ConfigManager()
        self.window_service = WindowService(self.config)
    
    def test_init(self):
        """测试初始化"""
        self.assertIsNotNone(self.window_service)
        self.assertEqual(self.window_service.config, self.config)
    
    @patch('src.services.window_service.win32gui')
    def test_get_windows(self, mock_win32gui):
        """测试获取窗口列表"""
        # 模拟窗口数据
        mock_win32gui.EnumWindows.return_value = None
        mock_win32gui.GetWindowText.side_effect = ["斗地主", "其他窗口"]
        mock_win32gui.IsWindowVisible.return_value = True
        mock_win32gui.GetWindowRect.return_value = (0, 0, 800, 600)
        
        # 由于EnumWindows的复杂性，这里主要测试方法存在
        windows = self.window_service.get_windows()
        self.assertIsInstance(windows, list)
    
    def test_window_info_creation(self):
        """测试窗口信息创建"""
        window_info = WindowInfo(
            hwnd=12345,
            title="测试窗口",
            class_name="TestClass",
            rect=(0, 0, 800, 600),
            is_visible=True
        )
        
        self.assertEqual(window_info.hwnd, 12345)
        self.assertEqual(window_info.title, "测试窗口")
        self.assertEqual(window_info.width, 800)
        self.assertEqual(window_info.height, 600)


class TestVisionService(unittest.TestCase):
    """视觉服务测试"""
    
    def setUp(self):
        """测试前准备"""
        self.config = ConfigManager()
        self.vision_service = VisionService(self.config)
    
    def test_init(self):
        """测试初始化"""
        self.assertIsNotNone(self.vision_service)
        self.assertEqual(self.vision_service.config, self.config)
    
    def test_create_test_image(self):
        """创建测试图像"""
        # 创建一个简单的测试图像
        image = Image.new('RGB', (100, 100), color='red')
        image_array = np.array(image)
        
        self.assertEqual(image_array.shape, (100, 100, 3))
        return image_array
    
    def test_preprocess_image(self):
        """测试图像预处理"""
        test_image = self.test_create_test_image()
        
        # 测试预处理
        processed = self.vision_service.preprocess_image(test_image)
        self.assertIsInstance(processed, np.ndarray)
        self.assertEqual(len(processed.shape), 3)  # 应该是3维数组
    
    @patch('src.services.vision_service.cv2')
    def test_detect_cards(self, mock_cv2):
        """测试卡牌检测"""
        test_image = self.test_create_test_image()
        
        # 模拟检测结果
        mock_cv2.findContours.return_value = ([], None)
        
        results = self.vision_service.detect_cards(test_image)
        self.assertIsInstance(results, list)
    
    def test_detection_result_creation(self):
        """测试检测结果创建"""
        result = DetectionResult(
            bbox=(10, 10, 50, 80),
            confidence=0.95,
            class_name="红桃A",
            features={"suit": "hearts", "rank": "A"}
        )
        
        self.assertEqual(result.bbox, (10, 10, 50, 80))
        self.assertEqual(result.confidence, 0.95)
        self.assertEqual(result.class_name, "红桃A")
        self.assertEqual(result.width, 40)
        self.assertEqual(result.height, 70)


class TestOCRService(unittest.TestCase):
    """OCR服务测试"""
    
    def setUp(self):
        """测试前准备"""
        self.config = ConfigManager()
        self.ocr_service = OCRService(self.config)
    
    def test_init(self):
        """测试初始化"""
        self.assertIsNotNone(self.ocr_service)
        self.assertEqual(self.ocr_service.config, self.config)
        self.assertEqual(self.ocr_service.current_engine, OCREngine.PADDLE_OCR)
    
    def test_create_test_image(self):
        """创建包含文字的测试图像"""
        # 创建一个包含文字的简单图像
        image = Image.new('RGB', (200, 50), color='white')
        # 这里应该添加文字，但为了简化测试，直接返回空白图像
        return np.array(image)
    
    def test_preprocess_image(self):
        """测试图像预处理"""
        test_image = self.test_create_test_image()
        
        processed = self.ocr_service.preprocess_image(test_image)
        self.assertIsInstance(processed, np.ndarray)
    
    @patch('src.services.ocr_service.PaddleOCR')
    def test_paddle_ocr_recognize(self, mock_paddle):
        """测试PaddleOCR识别"""
        # 模拟PaddleOCR返回结果
        mock_paddle_instance = Mock()
        mock_paddle_instance.ocr.return_value = [[
            [[[0, 0], [100, 0], [100, 30], [0, 30]], ("测试文字", 0.95)]
        ]]
        mock_paddle.return_value = mock_paddle_instance
        
        test_image = self.test_create_test_image()
        results = self.ocr_service._paddle_ocr_recognize(test_image)
        
        self.assertIsInstance(results, list)
        if results:  # 如果有结果
            self.assertIsInstance(results[0], OCRResult)
    
    def test_classify_text_type(self):
        """测试文本类型分类"""
        # 测试不同类型的文本
        test_cases = [
            ("叫地主", TextType.BUTTON),
            ("不叫", TextType.BUTTON),
            ("出牌", TextType.BUTTON),
            ("要不起", TextType.BUTTON),
            ("地主", TextType.ROLE),
            ("农民", TextType.ROLE),
            ("17", TextType.CARD_COUNT),
            ("分数: 1000", TextType.SCORE),
            ("其他文字", TextType.OTHER)
        ]
        
        for text, expected_type in test_cases:
            result_type = self.ocr_service.classify_text_type(text)
            self.assertEqual(result_type, expected_type, f"文本 '{text}' 分类错误")
    
    def test_ocr_result_creation(self):
        """测试OCR结果创建"""
        result = OCRResult(
            text="测试文字",
            confidence=0.95,
            bbox=(10, 10, 100, 40),
            text_type=TextType.BUTTON
        )
        
        self.assertEqual(result.text, "测试文字")
        self.assertEqual(result.confidence, 0.95)
        self.assertEqual(result.bbox, (10, 10, 100, 40))
        self.assertEqual(result.text_type, TextType.BUTTON)


class TestStrategyService(unittest.TestCase):
    """策略服务测试"""
    
    def setUp(self):
        """测试前准备"""
        self.config = ConfigManager()
        self.strategy_service = StrategyService(self.config)
    
    def test_init(self):
        """测试初始化"""
        self.assertIsNotNone(self.strategy_service)
        self.assertEqual(self.strategy_service.config, self.config)
        self.assertEqual(self.strategy_service.difficulty, StrategyDifficulty.MEDIUM)
        self.assertEqual(self.strategy_service.risk_preference, RiskPreference.BALANCED)
    
    def test_create_test_game_state(self):
        """创建测试游戏状态"""
        # 创建测试玩家
        players = [
            Player(id="player1", name="玩家1", position=0, role="地主"),
            Player(id="player2", name="玩家2", position=1, role="农民"),
            Player(id="player3", name="玩家3", position=2, role="农民")
        ]
        
        # 创建测试手牌
        hand_cards = [
            Card(suit=CardSuit.SPADES, rank=CardRank.ACE),
            Card(suit=CardSuit.HEARTS, rank=CardRank.KING),
            Card(suit=CardSuit.DIAMONDS, rank=CardRank.QUEEN)
        ]
        
        game_state = GameState(
            players=players,
            current_player=0,
            phase="playing",
            hand_cards=hand_cards
        )
        
        return game_state
    
    def test_analyze_game_state(self):
        """测试游戏状态分析"""
        game_state = self.test_create_test_game_state()
        
        analysis = self.strategy_service.analyze_game_state(game_state)
        
        self.assertIsNotNone(analysis)
        self.assertIn('win_probability', analysis.__dict__)
        self.assertIn('position_strength', analysis.__dict__)
        self.assertIn('threat_level', analysis.__dict__)
    
    def test_get_play_suggestion(self):
        """测试出牌建议"""
        game_state = self.test_create_test_game_state()
        
        suggestion = self.strategy_service.get_play_suggestion(game_state)
        
        self.assertIsNotNone(suggestion)
        self.assertIn('suggested_cards', suggestion.__dict__)
        self.assertIn('confidence', suggestion.__dict__)
        self.assertIn('reasoning', suggestion.__dict__)
    
    def test_evaluate_hand_quality(self):
        """测试手牌质量评估"""
        hand_cards = [
            Card(suit=CardSuit.SPADES, rank=CardRank.ACE),
            Card(suit=CardSuit.HEARTS, rank=CardRank.ACE),
            Card(suit=CardSuit.DIAMONDS, rank=CardRank.KING),
            Card(suit=CardSuit.CLUBS, rank=CardRank.KING)
        ]
        
        quality = self.strategy_service.evaluate_hand_quality(hand_cards)
        
        self.assertIsInstance(quality, (int, float))
        self.assertGreaterEqual(quality, 0)
        self.assertLessEqual(quality, 100)
    
    def test_calculate_win_probability(self):
        """测试胜率计算"""
        game_state = self.test_create_test_game_state()
        
        win_prob = self.strategy_service.calculate_win_probability(game_state)
        
        self.assertIsInstance(win_prob, (int, float))
        self.assertGreaterEqual(win_prob, 0)
        self.assertLessEqual(win_prob, 1)
    
    def test_set_difficulty(self):
        """测试设置难度"""
        self.strategy_service.set_difficulty(StrategyDifficulty.HARD)
        self.assertEqual(self.strategy_service.difficulty, StrategyDifficulty.HARD)
        
        self.strategy_service.set_difficulty(StrategyDifficulty.EASY)
        self.assertEqual(self.strategy_service.difficulty, StrategyDifficulty.EASY)
    
    def test_set_risk_preference(self):
        """测试设置风险偏好"""
        self.strategy_service.set_risk_preference(RiskPreference.AGGRESSIVE)
        self.assertEqual(self.strategy_service.risk_preference, RiskPreference.AGGRESSIVE)
        
        self.strategy_service.set_risk_preference(RiskPreference.CONSERVATIVE)
        self.assertEqual(self.strategy_service.risk_preference, RiskPreference.CONSERVATIVE)


class TestIntegration(unittest.TestCase):
    """集成测试"""
    
    def setUp(self):
        """测试前准备"""
        self.config = ConfigManager()
        
        # 初始化所有服务
        self.window_service = WindowService(self.config)
        self.vision_service = VisionService(self.config)
        self.ocr_service = OCRService(self.config)
        self.strategy_service = StrategyService(self.config)
    
    def test_services_integration(self):
        """测试服务集成"""
        # 测试所有服务都能正常初始化
        self.assertIsNotNone(self.window_service)
        self.assertIsNotNone(self.vision_service)
        self.assertIsNotNone(self.ocr_service)
        self.assertIsNotNone(self.strategy_service)
    
    def test_config_sharing(self):
        """测试配置共享"""
        # 测试所有服务共享同一个配置对象
        self.assertEqual(self.window_service.config, self.config)
        self.assertEqual(self.vision_service.config, self.config)
        self.assertEqual(self.ocr_service.config, self.config)
        self.assertEqual(self.strategy_service.config, self.config)
    
    @patch('src.services.window_service.win32gui')
    def test_full_pipeline_simulation(self, mock_win32gui):
        """测试完整流水线模拟"""
        # 这是一个简化的集成测试，模拟完整的处理流程
        
        # 1. 模拟获取窗口
        mock_win32gui.EnumWindows.return_value = None
        windows = self.window_service.get_windows()
        self.assertIsInstance(windows, list)
        
        # 2. 创建测试图像
        test_image = np.zeros((600, 800, 3), dtype=np.uint8)
        
        # 3. 测试视觉处理
        processed_image = self.vision_service.preprocess_image(test_image)
        self.assertIsNotNone(processed_image)
        
        # 4. 测试OCR处理
        ocr_processed = self.ocr_service.preprocess_image(test_image)
        self.assertIsNotNone(ocr_processed)
        
        # 5. 创建测试游戏状态
        players = [
            Player(id="p1", name="玩家1", position=0, role="地主"),
            Player(id="p2", name="玩家2", position=1, role="农民"),
            Player(id="p3", name="玩家3", position=2, role="农民")
        ]
        
        game_state = GameState(
            players=players,
            current_player=0,
            phase="playing",
            hand_cards=[]
        )
        
        # 6. 测试策略分析
        analysis = self.strategy_service.analyze_game_state(game_state)
        self.assertIsNotNone(analysis)
        
        suggestion = self.strategy_service.get_play_suggestion(game_state)
        self.assertIsNotNone(suggestion)


if __name__ == '__main__':
    # 运行测试
    unittest.main(verbosity=2)