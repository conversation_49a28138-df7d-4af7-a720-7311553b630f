{".class": "MypyFile", "_fullname": "cv2.gapi", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "ArgType": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "cv2.gapi.ArgType", "line": 59, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "builtins.int"}}, "BGR2Gray": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["src"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.gapi.BGR2Gray", "name": "BGR2Gray", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["src"], "arg_types": ["cv2.GMat"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "BGR2Gray", "ret_type": "cv2.GMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "BGR2I420": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["src"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.gapi.BGR2I420", "name": "BGR2I420", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["src"], "arg_types": ["cv2.GMat"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "BGR2I420", "ret_type": "cv2.GMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "BGR2LUV": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["src"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.gapi.BGR2LUV", "name": "BGR2LUV", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["src"], "arg_types": ["cv2.GMat"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "BGR2LUV", "ret_type": "cv2.GMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "BGR2RGB": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["src"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.gapi.BGR2RGB", "name": "BGR2RGB", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["src"], "arg_types": ["cv2.GMat"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "BGR2RGB", "ret_type": "cv2.GMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "BGR2YUV": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["src"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.gapi.BGR2YUV", "name": "BGR2YUV", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["src"], "arg_types": ["cv2.GMat"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "BGR2YUV", "ret_type": "cv2.GMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "BayerGR2RGB": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["src_gr"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.gapi.BayerGR2RGB", "name": "BayerGR2RGB", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["src_gr"], "arg_types": ["cv2.GMat"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "BayerGR2RGB", "ret_type": "cv2.GMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "CV_ANY": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.gapi.CV_ANY", "name": "CV_ANY", "type": "builtins.int"}}, "CV_BOOL": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.gapi.CV_BOOL", "name": "CV_BOOL", "type": "builtins.int"}}, "CV_DOUBLE": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.gapi.CV_DOUBLE", "name": "CV_DOUBLE", "type": "builtins.int"}}, "CV_DRAW_PRIM": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.gapi.CV_DRAW_PRIM", "name": "CV_DRAW_PRIM", "type": "builtins.int"}}, "CV_FLOAT": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.gapi.CV_FLOAT", "name": "CV_FLOAT", "type": "builtins.int"}}, "CV_GMAT": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.gapi.CV_GMAT", "name": "CV_GMAT", "type": "builtins.int"}}, "CV_INT": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.gapi.CV_INT", "name": "CV_INT", "type": "builtins.int"}}, "CV_INT64": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.gapi.CV_INT64", "name": "CV_INT64", "type": "builtins.int"}}, "CV_MAT": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.gapi.CV_MAT", "name": "CV_MAT", "type": "builtins.int"}}, "CV_POINT": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.gapi.CV_POINT", "name": "CV_POINT", "type": "builtins.int"}}, "CV_POINT2F": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.gapi.CV_POINT2F", "name": "CV_POINT2F", "type": "builtins.int"}}, "CV_POINT3F": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.gapi.CV_POINT3F", "name": "CV_POINT3F", "type": "builtins.int"}}, "CV_RECT": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.gapi.CV_RECT", "name": "CV_RECT", "type": "builtins.int"}}, "CV_SCALAR": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.gapi.CV_SCALAR", "name": "CV_SCALAR", "type": "builtins.int"}}, "CV_SIZE": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.gapi.CV_SIZE", "name": "CV_SIZE", "type": "builtins.int"}}, "CV_STRING": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.gapi.CV_STRING", "name": "CV_STRING", "type": "builtins.int"}}, "CV_UINT64": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.gapi.CV_UINT64", "name": "CV_UINT64", "type": "builtins.int"}}, "Canny": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["image", "threshold1", "threshold2", "apertureSize", "L2gradient"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.gapi.<PERSON>", "name": "<PERSON><PERSON>", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["image", "threshold1", "threshold2", "apertureSize", "L2gradient"], "arg_types": ["cv2.GMat", "builtins.float", "builtins.float", "builtins.int", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "<PERSON><PERSON>", "ret_type": "cv2.GMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "GNetPackage": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "cv2.gapi.GNetPackage", "name": "GNetPackage", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "cv2.gapi.GNetPackage", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "cv2.gapi", "mro": ["cv2.gapi.GNetPackage", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "cv2.gapi.GNetPackage.__init__", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.gapi.GNetPackage.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cv2.gapi.GNetPackage"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of GNetPackage", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.gapi.GNetPackage.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cv2.gapi.GNetPackage"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of GNetPackage", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "nets"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.gapi.GNetPackage.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "nets"], "arg_types": ["cv2.gapi.GNetPackage", {".class": "Instance", "args": ["cv2.gapi.GNetParam"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of GNetPackage", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.gapi.GNetPackage.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "nets"], "arg_types": ["cv2.gapi.GNetPackage", {".class": "Instance", "args": ["cv2.gapi.GNetParam"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of GNetPackage", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cv2.gapi.GNetPackage"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of GNetPackage", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "nets"], "arg_types": ["cv2.gapi.GNetPackage", {".class": "Instance", "args": ["cv2.gapi.GNetParam"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of GNetPackage", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cv2.gapi.GNetPackage.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cv2.gapi.GNetPackage", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "GNetParam": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "cv2.gapi.GNetParam", "name": "GNetParam", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "cv2.gapi.GNetParam", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "cv2.gapi", "mro": ["cv2.gapi.GNetParam", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cv2.gapi.GNetParam.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cv2.gapi.GNetParam", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "I4202BGR": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["src"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.gapi.I4202BGR", "name": "I4202BGR", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["src"], "arg_types": ["cv2.GMat"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "I4202BGR", "ret_type": "cv2.GMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "I4202RGB": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["src"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.gapi.I4202RGB", "name": "I4202RGB", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["src"], "arg_types": ["cv2.GMat"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "I4202RGB", "ret_type": "cv2.GMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "LUT": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["src", "lut"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.gapi.LUT", "name": "LUT", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["src", "lut"], "arg_types": ["cv2.GMat", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "LUT", "ret_type": "cv2.GMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "LUV2BGR": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["src"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.gapi.LUV2BGR", "name": "LUV2BGR", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["src"], "arg_types": ["cv2.GMat"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "LUV2BGR", "ret_type": "cv2.GMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "Laplacian": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1], "arg_names": ["src", "ddepth", "ksize", "scale", "delta", "borderType"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.gapi.<PERSON>", "name": "Lapla<PERSON>", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1], "arg_names": ["src", "ddepth", "ksize", "scale", "delta", "borderType"], "arg_types": ["cv2.GMat", "builtins.int", "builtins.int", "builtins.float", "builtins.float", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "Lapla<PERSON>", "ret_type": "cv2.GMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "NV12toBGR": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["src_y", "src_uv"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.gapi.NV12toBGR", "name": "NV12toBGR", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["src_y", "src_uv"], "arg_types": ["cv2.GMat", "cv2.GMat"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "NV12toBGR", "ret_type": "cv2.GMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "NV12toGray": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["src_y", "src_uv"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.gapi.NV12toGray", "name": "NV12toGray", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["src_y", "src_uv"], "arg_types": ["cv2.GMat", "cv2.GMat"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "NV12toGray", "ret_type": "cv2.GMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "NV12toRGB": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["src_y", "src_uv"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.gapi.NV12toRGB", "name": "NV12toRGB", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["src_y", "src_uv"], "arg_types": ["cv2.GMat", "cv2.GMat"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "NV12toRGB", "ret_type": "cv2.GMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "RGB2Gray": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "cv2.gapi.RGB2Gray", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["src"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.gapi.RGB2Gray", "name": "RGB2Gray", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["src"], "arg_types": ["cv2.GMat"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "RGB2Gray", "ret_type": "cv2.GMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.gapi.RGB2Gray", "name": "RGB2Gray", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["src"], "arg_types": ["cv2.GMat"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "RGB2Gray", "ret_type": "cv2.GMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["src", "rY", "gY", "bY"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.gapi.RGB2Gray", "name": "RGB2Gray", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["src", "rY", "gY", "bY"], "arg_types": ["cv2.GMat", "builtins.float", "builtins.float", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "RGB2Gray", "ret_type": "cv2.GMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.gapi.RGB2Gray", "name": "RGB2Gray", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["src", "rY", "gY", "bY"], "arg_types": ["cv2.GMat", "builtins.float", "builtins.float", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "RGB2Gray", "ret_type": "cv2.GMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": ["src"], "arg_types": ["cv2.GMat"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "RGB2Gray", "ret_type": "cv2.GMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["src", "rY", "gY", "bY"], "arg_types": ["cv2.GMat", "builtins.float", "builtins.float", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "RGB2Gray", "ret_type": "cv2.GMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "RGB2HSV": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["src"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.gapi.RGB2HSV", "name": "RGB2HSV", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["src"], "arg_types": ["cv2.GMat"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "RGB2HSV", "ret_type": "cv2.GMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "RGB2I420": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["src"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.gapi.RGB2I420", "name": "RGB2I420", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["src"], "arg_types": ["cv2.GMat"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "RGB2I420", "ret_type": "cv2.GMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "RGB2Lab": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["src"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.gapi.RGB2Lab", "name": "RGB2Lab", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["src"], "arg_types": ["cv2.GMat"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "RGB2Lab", "ret_type": "cv2.GMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "RGB2YUV": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["src"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.gapi.RGB2YUV", "name": "RGB2YUV", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["src"], "arg_types": ["cv2.GMat"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "RGB2YUV", "ret_type": "cv2.GMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "RGB2YUV422": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["src"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.gapi.RGB2YUV422", "name": "RGB2YUV422", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["src"], "arg_types": ["cv2.GMat"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "RGB2YUV422", "ret_type": "cv2.GMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "STEREO_OUTPUT_FORMAT_DEPTH_16F": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.gapi.STEREO_OUTPUT_FORMAT_DEPTH_16F", "name": "STEREO_OUTPUT_FORMAT_DEPTH_16F", "type": "builtins.int"}}, "STEREO_OUTPUT_FORMAT_DEPTH_32F": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.gapi.STEREO_OUTPUT_FORMAT_DEPTH_32F", "name": "STEREO_OUTPUT_FORMAT_DEPTH_32F", "type": "builtins.int"}}, "STEREO_OUTPUT_FORMAT_DEPTH_FLOAT16": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.gapi.STEREO_OUTPUT_FORMAT_DEPTH_FLOAT16", "name": "STEREO_OUTPUT_FORMAT_DEPTH_FLOAT16", "type": "builtins.int"}}, "STEREO_OUTPUT_FORMAT_DEPTH_FLOAT32": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.gapi.STEREO_OUTPUT_FORMAT_DEPTH_FLOAT32", "name": "STEREO_OUTPUT_FORMAT_DEPTH_FLOAT32", "type": "builtins.int"}}, "STEREO_OUTPUT_FORMAT_DISPARITY_16Q_10_5": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.gapi.STEREO_OUTPUT_FORMAT_DISPARITY_16Q_10_5", "name": "STEREO_OUTPUT_FORMAT_DISPARITY_16Q_10_5", "type": "builtins.int"}}, "STEREO_OUTPUT_FORMAT_DISPARITY_16Q_11_4": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.gapi.STEREO_OUTPUT_FORMAT_DISPARITY_16Q_11_4", "name": "STEREO_OUTPUT_FORMAT_DISPARITY_16Q_11_4", "type": "builtins.int"}}, "STEREO_OUTPUT_FORMAT_DISPARITY_FIXED16_11_5": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.gapi.STEREO_OUTPUT_FORMAT_DISPARITY_FIXED16_11_5", "name": "STEREO_OUTPUT_FORMAT_DISPARITY_FIXED16_11_5", "type": "builtins.int"}}, "STEREO_OUTPUT_FORMAT_DISPARITY_FIXED16_12_4": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.gapi.STEREO_OUTPUT_FORMAT_DISPARITY_FIXED16_12_4", "name": "STEREO_OUTPUT_FORMAT_DISPARITY_FIXED16_12_4", "type": "builtins.int"}}, "Sobel": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1, 1, 1, 1, 1], "arg_names": ["src", "ddepth", "dx", "dy", "ksize", "scale", "delta", "borderType", "borderValue"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.gapi.<PERSON><PERSON>", "name": "<PERSON><PERSON>", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1, 1, 1, 1, 1], "arg_names": ["src", "ddepth", "dx", "dy", "ksize", "scale", "delta", "borderType", "borderValue"], "arg_types": ["cv2.GMat", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.float", "builtins.float", "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.<PERSON>.<PERSON>"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "<PERSON><PERSON>", "ret_type": "cv2.GMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "SobelXY": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1, 1, 1], "arg_names": ["src", "ddepth", "order", "ksize", "scale", "delta", "borderType", "borderValue"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.gapi.SobelXY", "name": "SobelXY", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1, 1, 1], "arg_names": ["src", "ddepth", "order", "ksize", "scale", "delta", "borderType", "borderValue"], "arg_types": ["cv2.GMat", "builtins.int", "builtins.int", "builtins.int", "builtins.float", "builtins.float", "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.<PERSON>.<PERSON>"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "SobelXY", "ret_type": {".class": "TupleType", "implicit": false, "items": ["cv2.GMat", "cv2.GMat"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "StereoOutputFormat": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "cv2.gapi.StereoOutputFormat", "line": 39, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "builtins.int"}}, "StereoOutputFormat_DEPTH_16F": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.gapi.StereoOutputFormat_DEPTH_16F", "name": "StereoOutputFormat_DEPTH_16F", "type": "builtins.int"}}, "StereoOutputFormat_DEPTH_32F": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.gapi.StereoOutputFormat_DEPTH_32F", "name": "StereoOutputFormat_DEPTH_32F", "type": "builtins.int"}}, "StereoOutputFormat_DEPTH_FLOAT16": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.gapi.StereoOutputFormat_DEPTH_FLOAT16", "name": "StereoOutputFormat_DEPTH_FLOAT16", "type": "builtins.int"}}, "StereoOutputFormat_DEPTH_FLOAT32": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.gapi.StereoOutputFormat_DEPTH_FLOAT32", "name": "StereoOutputFormat_DEPTH_FLOAT32", "type": "builtins.int"}}, "StereoOutputFormat_DISPARITY_16Q_10_5": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.gapi.StereoOutputFormat_DISPARITY_16Q_10_5", "name": "StereoOutputFormat_DISPARITY_16Q_10_5", "type": "builtins.int"}}, "StereoOutputFormat_DISPARITY_16Q_11_4": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.gapi.StereoOutputFormat_DISPARITY_16Q_11_4", "name": "StereoOutputFormat_DISPARITY_16Q_11_4", "type": "builtins.int"}}, "StereoOutputFormat_DISPARITY_FIXED16_11_5": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.gapi.StereoOutputFormat_DISPARITY_FIXED16_11_5", "name": "StereoOutputFormat_DISPARITY_FIXED16_11_5", "type": "builtins.int"}}, "StereoOutputFormat_DISPARITY_FIXED16_12_4": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.gapi.StereoOutputFormat_DISPARITY_FIXED16_12_4", "name": "StereoOutputFormat_DISPARITY_FIXED16_12_4", "type": "builtins.int"}}, "YUV2BGR": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["src"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.gapi.YUV2BGR", "name": "YUV2BGR", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["src"], "arg_types": ["cv2.GMat"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "YUV2BGR", "ret_type": "cv2.GMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "YUV2RGB": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["src"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.gapi.YUV2RGB", "name": "YUV2RGB", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["src"], "arg_types": ["cv2.GMat"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "YUV2RGB", "ret_type": "cv2.GMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "cv2.gapi.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.gapi.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.gapi.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.gapi.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.gapi.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.gapi.__package__", "name": "__package__", "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.gapi.__path__", "name": "__path__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.gapi.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_typing": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef", "module_hidden": true, "module_public": false}, "absDiff": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["src1", "src2"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.gapi.absDiff", "name": "absDiff", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["src1", "src2"], "arg_types": ["cv2.GMat", "cv2.GMat"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "absDiff", "ret_type": "cv2.GMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "absDiffC": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["src", "c"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.gapi.absDiffC", "name": "absDiffC", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["src", "c"], "arg_types": ["cv2.GMat", "cv2.GScalar"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "absDiffC", "ret_type": "cv2.GMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "add": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["src1", "src2", "ddepth"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.gapi.add", "name": "add", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["src1", "src2", "ddepth"], "arg_types": ["cv2.GMat", "cv2.GMat", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add", "ret_type": "cv2.GMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "addC": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "cv2.gapi.addC", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["src1", "c", "ddepth"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.gapi.addC", "name": "addC", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["src1", "c", "ddepth"], "arg_types": ["cv2.GMat", "cv2.GScalar", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "addC", "ret_type": "cv2.GMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.gapi.addC", "name": "addC", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["src1", "c", "ddepth"], "arg_types": ["cv2.GMat", "cv2.GScalar", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "addC", "ret_type": "cv2.GMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["c", "src1", "ddepth"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.gapi.addC", "name": "addC", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["c", "src1", "ddepth"], "arg_types": ["cv2.GScalar", "cv2.GMat", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "addC", "ret_type": "cv2.GMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.gapi.addC", "name": "addC", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["c", "src1", "ddepth"], "arg_types": ["cv2.GScalar", "cv2.GMat", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "addC", "ret_type": "cv2.GMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["src1", "c", "ddepth"], "arg_types": ["cv2.GMat", "cv2.GScalar", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "addC", "ret_type": "cv2.GMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["c", "src1", "ddepth"], "arg_types": ["cv2.GScalar", "cv2.GMat", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "addC", "ret_type": "cv2.GMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "addWeighted": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 1], "arg_names": ["src1", "alpha", "src2", "beta", "gamma", "ddepth"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.gapi.addWeighted", "name": "addWeighted", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 1], "arg_names": ["src1", "alpha", "src2", "beta", "gamma", "ddepth"], "arg_types": ["cv2.GMat", "builtins.float", "cv2.GMat", "builtins.float", "builtins.float", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "addWeighted", "ret_type": "cv2.GMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "bilateralFilter": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["src", "d", "sigmaColor", "sigmaSpace", "borderType"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.gapi.bilateralFilter", "name": "bilateralFilter", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["src", "d", "sigmaColor", "sigmaSpace", "borderType"], "arg_types": ["cv2.GMat", "builtins.int", "builtins.float", "builtins.float", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "bilateralFilter", "ret_type": "cv2.GMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "bitwise_and": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "cv2.gapi.bitwise_and", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["src1", "src2"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.gapi.bitwise_and", "name": "bitwise_and", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["src1", "src2"], "arg_types": ["cv2.GMat", "cv2.GMat"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "bitwise_and", "ret_type": "cv2.GMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.gapi.bitwise_and", "name": "bitwise_and", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["src1", "src2"], "arg_types": ["cv2.GMat", "cv2.GMat"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "bitwise_and", "ret_type": "cv2.GMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["src1", "src2"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.gapi.bitwise_and", "name": "bitwise_and", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["src1", "src2"], "arg_types": ["cv2.GMat", "cv2.GScalar"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "bitwise_and", "ret_type": "cv2.GMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.gapi.bitwise_and", "name": "bitwise_and", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["src1", "src2"], "arg_types": ["cv2.GMat", "cv2.GScalar"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "bitwise_and", "ret_type": "cv2.GMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["src1", "src2"], "arg_types": ["cv2.GMat", "cv2.GMat"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "bitwise_and", "ret_type": "cv2.GMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["src1", "src2"], "arg_types": ["cv2.GMat", "cv2.GScalar"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "bitwise_and", "ret_type": "cv2.GMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "bitwise_not": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["src"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.gapi.bitwise_not", "name": "bitwise_not", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["src"], "arg_types": ["cv2.GMat"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "bitwise_not", "ret_type": "cv2.GMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "bitwise_or": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "cv2.gapi.bitwise_or", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["src1", "src2"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.gapi.bitwise_or", "name": "bitwise_or", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["src1", "src2"], "arg_types": ["cv2.GMat", "cv2.GMat"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "bitwise_or", "ret_type": "cv2.GMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.gapi.bitwise_or", "name": "bitwise_or", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["src1", "src2"], "arg_types": ["cv2.GMat", "cv2.GMat"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "bitwise_or", "ret_type": "cv2.GMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["src1", "src2"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.gapi.bitwise_or", "name": "bitwise_or", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["src1", "src2"], "arg_types": ["cv2.GMat", "cv2.GScalar"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "bitwise_or", "ret_type": "cv2.GMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.gapi.bitwise_or", "name": "bitwise_or", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["src1", "src2"], "arg_types": ["cv2.GMat", "cv2.GScalar"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "bitwise_or", "ret_type": "cv2.GMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["src1", "src2"], "arg_types": ["cv2.GMat", "cv2.GMat"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "bitwise_or", "ret_type": "cv2.GMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["src1", "src2"], "arg_types": ["cv2.GMat", "cv2.GScalar"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "bitwise_or", "ret_type": "cv2.GMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "bitwise_xor": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "cv2.gapi.bitwise_xor", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["src1", "src2"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.gapi.bitwise_xor", "name": "bitwise_xor", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["src1", "src2"], "arg_types": ["cv2.GMat", "cv2.GMat"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "bitwise_xor", "ret_type": "cv2.GMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.gapi.bitwise_xor", "name": "bitwise_xor", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["src1", "src2"], "arg_types": ["cv2.GMat", "cv2.GMat"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "bitwise_xor", "ret_type": "cv2.GMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["src1", "src2"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.gapi.bitwise_xor", "name": "bitwise_xor", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["src1", "src2"], "arg_types": ["cv2.GMat", "cv2.GScalar"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "bitwise_xor", "ret_type": "cv2.GMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.gapi.bitwise_xor", "name": "bitwise_xor", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["src1", "src2"], "arg_types": ["cv2.GMat", "cv2.GScalar"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "bitwise_xor", "ret_type": "cv2.GMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["src1", "src2"], "arg_types": ["cv2.GMat", "cv2.GMat"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "bitwise_xor", "ret_type": "cv2.GMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["src1", "src2"], "arg_types": ["cv2.GMat", "cv2.GScalar"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "bitwise_xor", "ret_type": "cv2.GMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "blur": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["src", "ksize", "anchor", "borderType", "borderValue"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.gapi.blur", "name": "blur", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["src", "ksize", "anchor", "borderType", "borderValue"], "arg_types": ["cv2.GMat", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.<PERSON>.Size"}, {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.Point"}, "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.<PERSON>.<PERSON>"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "blur", "ret_type": "cv2.GMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "boundingRect": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "cv2.gapi.boundingRect", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["src"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.gapi.boundingRect", "name": "boundingRect", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["src"], "arg_types": ["cv2.GMat"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "boundingRect", "ret_type": "cv2.GOpaqueT", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.gapi.boundingRect", "name": "boundingRect", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["src"], "arg_types": ["cv2.GMat"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "boundingRect", "ret_type": "cv2.GOpaqueT", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["src"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.gapi.boundingRect", "name": "boundingRect", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["src"], "arg_types": ["cv2.GArrayT"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "boundingRect", "ret_type": "cv2.GOpaqueT", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.gapi.boundingRect", "name": "boundingRect", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["src"], "arg_types": ["cv2.GArrayT"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "boundingRect", "ret_type": "cv2.GOpaqueT", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["src"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.gapi.boundingRect", "name": "boundingRect", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["src"], "arg_types": ["cv2.GArrayT"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "boundingRect", "ret_type": "cv2.GOpaqueT", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.gapi.boundingRect", "name": "boundingRect", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["src"], "arg_types": ["cv2.GArrayT"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "boundingRect", "ret_type": "cv2.GOpaqueT", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": ["src"], "arg_types": ["cv2.GMat"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "boundingRect", "ret_type": "cv2.GOpaqueT", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0], "arg_names": ["src"], "arg_types": ["cv2.GArrayT"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "boundingRect", "ret_type": "cv2.GOpaqueT", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0], "arg_names": ["src"], "arg_types": ["cv2.GArrayT"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "boundingRect", "ret_type": "cv2.GOpaqueT", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "boxFilter": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1, 1], "arg_names": ["src", "dtype", "ksize", "anchor", "normalize", "borderType", "borderValue"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.gapi.boxFilter", "name": "boxFilter", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1, 1], "arg_names": ["src", "dtype", "ksize", "anchor", "normalize", "borderType", "borderValue"], "arg_types": ["cv2.GMat", "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.<PERSON>.Size"}, {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.Point"}, "builtins.bool", "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.<PERSON>.<PERSON>"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "boxFilter", "ret_type": "cv2.GMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "cartToPolar": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["x", "y", "angleInDegrees"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.gapi.cartToPolar", "name": "cartToPolar", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["x", "y", "angleInDegrees"], "arg_types": ["cv2.GMat", "cv2.GMat", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cartToPolar", "ret_type": {".class": "TupleType", "implicit": false, "items": ["cv2.GMat", "cv2.GMat"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "cmpEQ": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "cv2.gapi.cmpEQ", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["src1", "src2"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.gapi.cmpEQ", "name": "cmpEQ", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["src1", "src2"], "arg_types": ["cv2.GMat", "cv2.GMat"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cmpEQ", "ret_type": "cv2.GMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.gapi.cmpEQ", "name": "cmpEQ", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["src1", "src2"], "arg_types": ["cv2.GMat", "cv2.GMat"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cmpEQ", "ret_type": "cv2.GMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["src1", "src2"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.gapi.cmpEQ", "name": "cmpEQ", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["src1", "src2"], "arg_types": ["cv2.GMat", "cv2.GScalar"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cmpEQ", "ret_type": "cv2.GMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.gapi.cmpEQ", "name": "cmpEQ", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["src1", "src2"], "arg_types": ["cv2.GMat", "cv2.GScalar"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cmpEQ", "ret_type": "cv2.GMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["src1", "src2"], "arg_types": ["cv2.GMat", "cv2.GMat"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cmpEQ", "ret_type": "cv2.GMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["src1", "src2"], "arg_types": ["cv2.GMat", "cv2.GScalar"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cmpEQ", "ret_type": "cv2.GMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "cmpGE": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "cv2.gapi.cmpGE", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["src1", "src2"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.gapi.cmpGE", "name": "cmpGE", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["src1", "src2"], "arg_types": ["cv2.GMat", "cv2.GMat"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cmpGE", "ret_type": "cv2.GMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.gapi.cmpGE", "name": "cmpGE", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["src1", "src2"], "arg_types": ["cv2.GMat", "cv2.GMat"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cmpGE", "ret_type": "cv2.GMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["src1", "src2"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.gapi.cmpGE", "name": "cmpGE", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["src1", "src2"], "arg_types": ["cv2.GMat", "cv2.GScalar"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cmpGE", "ret_type": "cv2.GMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.gapi.cmpGE", "name": "cmpGE", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["src1", "src2"], "arg_types": ["cv2.GMat", "cv2.GScalar"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cmpGE", "ret_type": "cv2.GMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["src1", "src2"], "arg_types": ["cv2.GMat", "cv2.GMat"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cmpGE", "ret_type": "cv2.GMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["src1", "src2"], "arg_types": ["cv2.GMat", "cv2.GScalar"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cmpGE", "ret_type": "cv2.GMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "cmpGT": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "cv2.gapi.cmpGT", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["src1", "src2"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.gapi.cmpGT", "name": "cmpGT", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["src1", "src2"], "arg_types": ["cv2.GMat", "cv2.GMat"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cmpGT", "ret_type": "cv2.GMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.gapi.cmpGT", "name": "cmpGT", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["src1", "src2"], "arg_types": ["cv2.GMat", "cv2.GMat"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cmpGT", "ret_type": "cv2.GMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["src1", "src2"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.gapi.cmpGT", "name": "cmpGT", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["src1", "src2"], "arg_types": ["cv2.GMat", "cv2.GScalar"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cmpGT", "ret_type": "cv2.GMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.gapi.cmpGT", "name": "cmpGT", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["src1", "src2"], "arg_types": ["cv2.GMat", "cv2.GScalar"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cmpGT", "ret_type": "cv2.GMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["src1", "src2"], "arg_types": ["cv2.GMat", "cv2.GMat"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cmpGT", "ret_type": "cv2.GMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["src1", "src2"], "arg_types": ["cv2.GMat", "cv2.GScalar"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cmpGT", "ret_type": "cv2.GMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "cmpLE": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "cv2.gapi.cmpLE", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["src1", "src2"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.gapi.cmpLE", "name": "cmpLE", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["src1", "src2"], "arg_types": ["cv2.GMat", "cv2.GMat"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cmpLE", "ret_type": "cv2.GMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.gapi.cmpLE", "name": "cmpLE", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["src1", "src2"], "arg_types": ["cv2.GMat", "cv2.GMat"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cmpLE", "ret_type": "cv2.GMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["src1", "src2"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.gapi.cmpLE", "name": "cmpLE", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["src1", "src2"], "arg_types": ["cv2.GMat", "cv2.GScalar"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cmpLE", "ret_type": "cv2.GMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.gapi.cmpLE", "name": "cmpLE", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["src1", "src2"], "arg_types": ["cv2.GMat", "cv2.GScalar"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cmpLE", "ret_type": "cv2.GMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["src1", "src2"], "arg_types": ["cv2.GMat", "cv2.GMat"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cmpLE", "ret_type": "cv2.GMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["src1", "src2"], "arg_types": ["cv2.GMat", "cv2.GScalar"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cmpLE", "ret_type": "cv2.GMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "cmpLT": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "cv2.gapi.cmpLT", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["src1", "src2"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.gapi.cmpLT", "name": "cmpLT", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["src1", "src2"], "arg_types": ["cv2.GMat", "cv2.GMat"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cmpLT", "ret_type": "cv2.GMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.gapi.cmpLT", "name": "cmpLT", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["src1", "src2"], "arg_types": ["cv2.GMat", "cv2.GMat"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cmpLT", "ret_type": "cv2.GMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["src1", "src2"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.gapi.cmpLT", "name": "cmpLT", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["src1", "src2"], "arg_types": ["cv2.GMat", "cv2.GScalar"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cmpLT", "ret_type": "cv2.GMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.gapi.cmpLT", "name": "cmpLT", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["src1", "src2"], "arg_types": ["cv2.GMat", "cv2.GScalar"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cmpLT", "ret_type": "cv2.GMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["src1", "src2"], "arg_types": ["cv2.GMat", "cv2.GMat"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cmpLT", "ret_type": "cv2.GMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["src1", "src2"], "arg_types": ["cv2.GMat", "cv2.GScalar"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cmpLT", "ret_type": "cv2.GMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "cmpNE": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "cv2.gapi.cmpNE", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["src1", "src2"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.gapi.cmpNE", "name": "cmpNE", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["src1", "src2"], "arg_types": ["cv2.GMat", "cv2.GMat"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cmpNE", "ret_type": "cv2.GMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.gapi.cmpNE", "name": "cmpNE", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["src1", "src2"], "arg_types": ["cv2.GMat", "cv2.GMat"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cmpNE", "ret_type": "cv2.GMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["src1", "src2"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.gapi.cmpNE", "name": "cmpNE", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["src1", "src2"], "arg_types": ["cv2.GMat", "cv2.GScalar"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cmpNE", "ret_type": "cv2.GMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.gapi.cmpNE", "name": "cmpNE", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["src1", "src2"], "arg_types": ["cv2.GMat", "cv2.GScalar"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cmpNE", "ret_type": "cv2.GMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["src1", "src2"], "arg_types": ["cv2.GMat", "cv2.GMat"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cmpNE", "ret_type": "cv2.GMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["src1", "src2"], "arg_types": ["cv2.GMat", "cv2.GScalar"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cmpNE", "ret_type": "cv2.GMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "combine": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["lhs", "rhs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.gapi.combine", "name": "combine", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["lhs", "rhs"], "arg_types": ["cv2.GKernelPackage", "cv2.GKernelPackage"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "combine", "ret_type": "cv2.GKernelPackage", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "concatHor": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "cv2.gapi.concatHor", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["src1", "src2"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.gapi.concatHor", "name": "concatHor", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["src1", "src2"], "arg_types": ["cv2.GMat", "cv2.GMat"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "concatHor", "ret_type": "cv2.GMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.gapi.concatHor", "name": "concatHor", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["src1", "src2"], "arg_types": ["cv2.GMat", "cv2.GMat"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "concatHor", "ret_type": "cv2.GMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["v"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.gapi.concatHor", "name": "concatHor", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["v"], "arg_types": [{".class": "Instance", "args": ["cv2.GMat"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "concatHor", "ret_type": "cv2.GMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.gapi.concatHor", "name": "concatHor", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["v"], "arg_types": [{".class": "Instance", "args": ["cv2.GMat"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "concatHor", "ret_type": "cv2.GMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["src1", "src2"], "arg_types": ["cv2.GMat", "cv2.GMat"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "concatHor", "ret_type": "cv2.GMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0], "arg_names": ["v"], "arg_types": [{".class": "Instance", "args": ["cv2.GMat"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "concatHor", "ret_type": "cv2.GMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "concatVert": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "cv2.gapi.concatVert", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["src1", "src2"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.gapi.concatVert", "name": "con<PERSON><PERSON><PERSON>", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["src1", "src2"], "arg_types": ["cv2.GMat", "cv2.GMat"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "con<PERSON><PERSON><PERSON>", "ret_type": "cv2.GMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.gapi.concatVert", "name": "con<PERSON><PERSON><PERSON>", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["src1", "src2"], "arg_types": ["cv2.GMat", "cv2.GMat"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "con<PERSON><PERSON><PERSON>", "ret_type": "cv2.GMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["v"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.gapi.concatVert", "name": "con<PERSON><PERSON><PERSON>", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["v"], "arg_types": [{".class": "Instance", "args": ["cv2.GMat"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "con<PERSON><PERSON><PERSON>", "ret_type": "cv2.GMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.gapi.concatVert", "name": "con<PERSON><PERSON><PERSON>", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["v"], "arg_types": [{".class": "Instance", "args": ["cv2.GMat"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "con<PERSON><PERSON><PERSON>", "ret_type": "cv2.GMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["src1", "src2"], "arg_types": ["cv2.GMat", "cv2.GMat"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "con<PERSON><PERSON><PERSON>", "ret_type": "cv2.GMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0], "arg_names": ["v"], "arg_types": [{".class": "Instance", "args": ["cv2.GMat"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "con<PERSON><PERSON><PERSON>", "ret_type": "cv2.GMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "convertTo": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["src", "rdepth", "alpha", "beta"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.gapi.convertTo", "name": "convertTo", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["src", "rdepth", "alpha", "beta"], "arg_types": ["cv2.GMat", "builtins.int", "builtins.float", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "convertTo", "ret_type": "cv2.GMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "copy": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["in_"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.gapi.copy", "name": "copy", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["in_"], "arg_types": ["cv2.GMat"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "copy", "ret_type": "cv2.GMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "core": {".class": "SymbolTableNode", "cross_ref": "cv2.gapi.core", "kind": "Gdef", "module_public": false}, "countNonZero": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["src"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.gapi.countNonZero", "name": "count<PERSON>on<PERSON><PERSON>", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["src"], "arg_types": ["cv2.GMat"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "count<PERSON>on<PERSON><PERSON>", "ret_type": "cv2.GOpaqueT", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "crop": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["src", "rect"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.gapi.crop", "name": "crop", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["src", "rect"], "arg_types": ["cv2.GMat", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.Rect"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "crop", "ret_type": "cv2.GMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "cv2": {".class": "SymbolTableNode", "cross_ref": "cv2", "kind": "Gdef", "module_hidden": true, "module_public": false}, "dilate": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1], "arg_names": ["src", "kernel", "anchor", "iterations", "borderType", "borderValue"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.gapi.dilate", "name": "dilate", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1], "arg_names": ["src", "kernel", "anchor", "iterations", "borderType", "borderValue"], "arg_types": ["cv2.GMat", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.Point"}, "builtins.int", "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.<PERSON>.<PERSON>"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "dilate", "ret_type": "cv2.GMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "dilate3x3": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["src", "iterations", "borderType", "borderValue"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.gapi.dilate3x3", "name": "dilate3x3", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["src", "iterations", "borderType", "borderValue"], "arg_types": ["cv2.GMat", "builtins.int", "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.<PERSON>.<PERSON>"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "dilate3x3", "ret_type": "cv2.GMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "div": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["src1", "src2", "scale", "ddepth"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.gapi.div", "name": "div", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["src1", "src2", "scale", "ddepth"], "arg_types": ["cv2.GMat", "cv2.GMat", "builtins.float", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "div", "ret_type": "cv2.GMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "divC": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["src", "divisor", "scale", "ddepth"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.gapi.divC", "name": "divC", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["src", "divisor", "scale", "ddepth"], "arg_types": ["cv2.GMat", "cv2.GScalar", "builtins.float", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "divC", "ret_type": "cv2.GMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "divRC": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["divident", "src", "scale", "ddepth"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.gapi.divRC", "name": "divRC", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["divident", "src", "scale", "ddepth"], "arg_types": ["cv2.GScalar", "cv2.GMat", "builtins.float", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "divRC", "ret_type": "cv2.GMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "equalizeHist": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["src"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.gapi.equalizeHist", "name": "equalizeHist", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["src"], "arg_types": ["cv2.GMat"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "equalizeHist", "ret_type": "cv2.GMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "erode": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1], "arg_names": ["src", "kernel", "anchor", "iterations", "borderType", "borderValue"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.gapi.erode", "name": "erode", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1], "arg_names": ["src", "kernel", "anchor", "iterations", "borderType", "borderValue"], "arg_types": ["cv2.GMat", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.Point"}, "builtins.int", "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.<PERSON>.<PERSON>"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "erode", "ret_type": "cv2.GMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "erode3x3": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["src", "iterations", "borderType", "borderValue"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.gapi.erode3x3", "name": "erode3x3", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["src", "iterations", "borderType", "borderValue"], "arg_types": ["cv2.GMat", "builtins.int", "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.<PERSON>.<PERSON>"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "erode3x3", "ret_type": "cv2.GMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "filter2D": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1, 1], "arg_names": ["src", "ddepth", "kernel", "anchor", "delta", "borderType", "borderValue"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.gapi.filter2D", "name": "filter2D", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1, 1], "arg_names": ["src", "ddepth", "kernel", "anchor", "delta", "borderType", "borderValue"], "arg_types": ["cv2.GMat", "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.Point"}, {".class": "TypeAliasType", "args": [], "type_ref": "cv2.<PERSON>.<PERSON>"}, "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.<PERSON>.<PERSON>"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "filter2D", "ret_type": "cv2.GMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "flip": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["src", "flipCode"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.gapi.flip", "name": "flip", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["src", "flipCode"], "arg_types": ["cv2.GMat", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "flip", "ret_type": "cv2.GMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "gaussianBlur": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1], "arg_names": ["src", "ksize", "sigmaX", "sigmaY", "borderType", "borderValue"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.gapi.g<PERSON><PERSON><PERSON>", "name": "g<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1], "arg_names": ["src", "ksize", "sigmaX", "sigmaY", "borderType", "borderValue"], "arg_types": ["cv2.GMat", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.<PERSON>.Size"}, "builtins.float", "builtins.float", "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.<PERSON>.<PERSON>"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "g<PERSON><PERSON><PERSON><PERSON><PERSON>", "ret_type": "cv2.GMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "goodFeaturesToTrack": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1, 1, 1, 1], "arg_names": ["image", "maxCorners", "qualityLevel", "minDistance", "mask", "blockSize", "useHarrisDetector", "k"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.gapi.goodFeaturesToTrack", "name": "goodFeaturesToTrack", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1, 1, 1, 1], "arg_names": ["image", "maxCorners", "qualityLevel", "minDistance", "mask", "blockSize", "useHarrisDetector", "k"], "arg_types": ["cv2.GMat", "builtins.int", "builtins.float", "builtins.float", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int", "builtins.bool", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "goodFeaturesToTrack", "ret_type": "cv2.GArrayT", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "ie": {".class": "SymbolTableNode", "cross_ref": "cv2.gapi.ie", "kind": "Gdef", "module_public": false}, "imgproc": {".class": "SymbolTableNode", "cross_ref": "cv2.gapi.imgproc", "kind": "Gdef", "module_public": false}, "inRange": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["src", "threshLow", "threshUp"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.gapi.inRange", "name": "inRange", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["src", "threshLow", "threshUp"], "arg_types": ["cv2.GMat", "cv2.GScalar", "cv2.GScalar"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "inRange", "ret_type": "cv2.GMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "infer": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "cv2.gapi.infer", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["name", "inputs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.gapi.infer", "name": "infer", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["name", "inputs"], "arg_types": ["builtins.str", "cv2.GInferInputs"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "infer", "ret_type": "cv2.GInferOutputs", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.gapi.infer", "name": "infer", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["name", "inputs"], "arg_types": ["builtins.str", "cv2.GInferInputs"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "infer", "ret_type": "cv2.GInferOutputs", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["name", "roi", "inputs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.gapi.infer", "name": "infer", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["name", "roi", "inputs"], "arg_types": ["builtins.str", "cv2.GOpaqueT", "cv2.GInferInputs"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "infer", "ret_type": "cv2.GInferOutputs", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.gapi.infer", "name": "infer", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["name", "roi", "inputs"], "arg_types": ["builtins.str", "cv2.GOpaqueT", "cv2.GInferInputs"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "infer", "ret_type": "cv2.GInferOutputs", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["name", "rois", "inputs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.gapi.infer", "name": "infer", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["name", "rois", "inputs"], "arg_types": ["builtins.str", "cv2.GArrayT", "cv2.GInferInputs"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "infer", "ret_type": "cv2.GInferListOutputs", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.gapi.infer", "name": "infer", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["name", "rois", "inputs"], "arg_types": ["builtins.str", "cv2.GArrayT", "cv2.GInferInputs"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "infer", "ret_type": "cv2.GInferListOutputs", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["name", "inputs"], "arg_types": ["builtins.str", "cv2.GInferInputs"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "infer", "ret_type": "cv2.GInferOutputs", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["name", "roi", "inputs"], "arg_types": ["builtins.str", "cv2.GOpaqueT", "cv2.GInferInputs"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "infer", "ret_type": "cv2.GInferOutputs", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["name", "rois", "inputs"], "arg_types": ["builtins.str", "cv2.GArrayT", "cv2.GInferInputs"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "infer", "ret_type": "cv2.GInferListOutputs", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "infer2": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["name", "in_", "inputs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.gapi.infer2", "name": "infer2", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["name", "in_", "inputs"], "arg_types": ["builtins.str", "cv2.GMat", "cv2.GInferListInputs"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "infer2", "ret_type": "cv2.GInferListOutputs", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "integral": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["src", "sdepth", "sqdepth"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.gapi.integral", "name": "integral", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["src", "sdepth", "sqdepth"], "arg_types": ["cv2.GMat", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "integral", "ret_type": {".class": "TupleType", "implicit": false, "items": ["cv2.GMat", "cv2.GMat"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "kmeans": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "cv2.gapi.kmeans", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["data", "K", "best<PERSON><PERSON><PERSON>", "criteria", "attempts", "flags"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.gapi.kmeans", "name": "kmeans", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["data", "K", "best<PERSON><PERSON><PERSON>", "criteria", "attempts", "flags"], "arg_types": ["cv2.GMat", "builtins.int", "cv2.GMat", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.TermCriteria"}, "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "kmeans", "ret_type": {".class": "TupleType", "implicit": false, "items": ["cv2.GOpaqueT", "cv2.GMat", "cv2.GMat"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.gapi.kmeans", "name": "kmeans", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["data", "K", "best<PERSON><PERSON><PERSON>", "criteria", "attempts", "flags"], "arg_types": ["cv2.GMat", "builtins.int", "cv2.GMat", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.TermCriteria"}, "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "kmeans", "ret_type": {".class": "TupleType", "implicit": false, "items": ["cv2.GOpaqueT", "cv2.GMat", "cv2.GMat"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["data", "K", "criteria", "attempts", "flags"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.gapi.kmeans", "name": "kmeans", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["data", "K", "criteria", "attempts", "flags"], "arg_types": ["cv2.GMat", "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.TermCriteria"}, "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "kmeans", "ret_type": {".class": "TupleType", "implicit": false, "items": ["cv2.GOpaqueT", "cv2.GMat", "cv2.GMat"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.gapi.kmeans", "name": "kmeans", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["data", "K", "criteria", "attempts", "flags"], "arg_types": ["cv2.GMat", "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.TermCriteria"}, "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "kmeans", "ret_type": {".class": "TupleType", "implicit": false, "items": ["cv2.GOpaqueT", "cv2.GMat", "cv2.GMat"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["data", "K", "best<PERSON><PERSON><PERSON>", "criteria", "attempts", "flags"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.gapi.kmeans", "name": "kmeans", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["data", "K", "best<PERSON><PERSON><PERSON>", "criteria", "attempts", "flags"], "arg_types": ["cv2.GArrayT", "builtins.int", "cv2.GArrayT", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.TermCriteria"}, "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "kmeans", "ret_type": {".class": "TupleType", "implicit": false, "items": ["cv2.GOpaqueT", "cv2.GArrayT", "cv2.GArrayT"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.gapi.kmeans", "name": "kmeans", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["data", "K", "best<PERSON><PERSON><PERSON>", "criteria", "attempts", "flags"], "arg_types": ["cv2.GArrayT", "builtins.int", "cv2.GArrayT", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.TermCriteria"}, "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "kmeans", "ret_type": {".class": "TupleType", "implicit": false, "items": ["cv2.GOpaqueT", "cv2.GArrayT", "cv2.GArrayT"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["data", "K", "best<PERSON><PERSON><PERSON>", "criteria", "attempts", "flags"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.gapi.kmeans", "name": "kmeans", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["data", "K", "best<PERSON><PERSON><PERSON>", "criteria", "attempts", "flags"], "arg_types": ["cv2.GArrayT", "builtins.int", "cv2.GArrayT", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.TermCriteria"}, "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "kmeans", "ret_type": {".class": "TupleType", "implicit": false, "items": ["cv2.GOpaqueT", "cv2.GArrayT", "cv2.GArrayT"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.gapi.kmeans", "name": "kmeans", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["data", "K", "best<PERSON><PERSON><PERSON>", "criteria", "attempts", "flags"], "arg_types": ["cv2.GArrayT", "builtins.int", "cv2.GArrayT", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.TermCriteria"}, "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "kmeans", "ret_type": {".class": "TupleType", "implicit": false, "items": ["cv2.GOpaqueT", "cv2.GArrayT", "cv2.GArrayT"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["data", "K", "best<PERSON><PERSON><PERSON>", "criteria", "attempts", "flags"], "arg_types": ["cv2.GMat", "builtins.int", "cv2.GMat", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.TermCriteria"}, "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "kmeans", "ret_type": {".class": "TupleType", "implicit": false, "items": ["cv2.GOpaqueT", "cv2.GMat", "cv2.GMat"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["data", "K", "criteria", "attempts", "flags"], "arg_types": ["cv2.GMat", "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.TermCriteria"}, "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "kmeans", "ret_type": {".class": "TupleType", "implicit": false, "items": ["cv2.GOpaqueT", "cv2.GMat", "cv2.GMat"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["data", "K", "best<PERSON><PERSON><PERSON>", "criteria", "attempts", "flags"], "arg_types": ["cv2.GArrayT", "builtins.int", "cv2.GArrayT", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.TermCriteria"}, "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "kmeans", "ret_type": {".class": "TupleType", "implicit": false, "items": ["cv2.GOpaqueT", "cv2.GArrayT", "cv2.GArrayT"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["data", "K", "best<PERSON><PERSON><PERSON>", "criteria", "attempts", "flags"], "arg_types": ["cv2.GArrayT", "builtins.int", "cv2.GArrayT", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.TermCriteria"}, "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "kmeans", "ret_type": {".class": "TupleType", "implicit": false, "items": ["cv2.GOpaqueT", "cv2.GArrayT", "cv2.GArrayT"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "mask": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["src", "mask"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.gapi.mask", "name": "mask", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["src", "mask"], "arg_types": ["cv2.GMat", "cv2.GMat"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "mask", "ret_type": "cv2.GMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "max": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["src1", "src2"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.gapi.max", "name": "max", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["src1", "src2"], "arg_types": ["cv2.GMat", "cv2.GMat"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "max", "ret_type": "cv2.GMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "mean": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["src"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.gapi.mean", "name": "mean", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["src"], "arg_types": ["cv2.GMat"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "mean", "ret_type": "cv2.GScalar", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "medianBlur": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["src", "ksize"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.gapi.median<PERSON><PERSON>r", "name": "medianBlur", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["src", "ksize"], "arg_types": ["cv2.GMat", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "medianBlur", "ret_type": "cv2.GMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "merge3": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["src1", "src2", "src3"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.gapi.merge3", "name": "merge3", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["src1", "src2", "src3"], "arg_types": ["cv2.GMat", "cv2.GMat", "cv2.GMat"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "merge3", "ret_type": "cv2.GMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "merge4": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["src1", "src2", "src3", "src4"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.gapi.merge4", "name": "merge4", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["src1", "src2", "src3", "src4"], "arg_types": ["cv2.GMat", "cv2.GMat", "cv2.GMat", "cv2.GMat"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "merge4", "ret_type": "cv2.GMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "min": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["src1", "src2"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.gapi.min", "name": "min", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["src1", "src2"], "arg_types": ["cv2.GMat", "cv2.GMat"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "min", "ret_type": "cv2.GMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "morphologyEx": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1, 1], "arg_names": ["src", "op", "kernel", "anchor", "iterations", "borderType", "borderValue"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.gapi.morphologyEx", "name": "morphologyEx", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1, 1], "arg_names": ["src", "op", "kernel", "anchor", "iterations", "borderType", "borderValue"], "arg_types": ["cv2.GMat", "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.Point"}, "builtins.int", "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.<PERSON>.<PERSON>"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "morphologyEx", "ret_type": "cv2.GMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "mul": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["src1", "src2", "scale", "ddepth"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.gapi.mul", "name": "mul", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["src1", "src2", "scale", "ddepth"], "arg_types": ["cv2.GMat", "cv2.GMat", "builtins.float", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "mul", "ret_type": "cv2.GMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "mulC": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "cv2.gapi.mulC", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["src", "multiplier", "ddepth"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.gapi.mulC", "name": "mulC", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["src", "multiplier", "ddepth"], "arg_types": ["cv2.GMat", "builtins.float", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "mulC", "ret_type": "cv2.GMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.gapi.mulC", "name": "mulC", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["src", "multiplier", "ddepth"], "arg_types": ["cv2.GMat", "builtins.float", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "mulC", "ret_type": "cv2.GMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["src", "multiplier", "ddepth"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.gapi.mulC", "name": "mulC", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["src", "multiplier", "ddepth"], "arg_types": ["cv2.GMat", "cv2.GScalar", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "mulC", "ret_type": "cv2.GMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.gapi.mulC", "name": "mulC", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["src", "multiplier", "ddepth"], "arg_types": ["cv2.GMat", "cv2.GScalar", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "mulC", "ret_type": "cv2.GMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["multiplier", "src", "ddepth"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.gapi.mulC", "name": "mulC", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["multiplier", "src", "ddepth"], "arg_types": ["cv2.GScalar", "cv2.GMat", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "mulC", "ret_type": "cv2.GMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.gapi.mulC", "name": "mulC", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["multiplier", "src", "ddepth"], "arg_types": ["cv2.GScalar", "cv2.GMat", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "mulC", "ret_type": "cv2.GMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["src", "multiplier", "ddepth"], "arg_types": ["cv2.GMat", "builtins.float", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "mulC", "ret_type": "cv2.GMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["src", "multiplier", "ddepth"], "arg_types": ["cv2.GMat", "cv2.GScalar", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "mulC", "ret_type": "cv2.GMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["multiplier", "src", "ddepth"], "arg_types": ["cv2.GScalar", "cv2.GMat", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "mulC", "ret_type": "cv2.GMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "normInf": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["src"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.gapi.normInf", "name": "normInf", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["src"], "arg_types": ["cv2.GMat"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "normInf", "ret_type": "cv2.GScalar", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "normL1": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["src"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.gapi.normL1", "name": "normL1", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["src"], "arg_types": ["cv2.GMat"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "normL1", "ret_type": "cv2.GScalar", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "normL2": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["src"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.gapi.normL2", "name": "normL2", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["src"], "arg_types": ["cv2.GMat"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "normL2", "ret_type": "cv2.GScalar", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "normalize": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["src", "alpha", "beta", "norm_type", "ddepth"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.gapi.normalize", "name": "normalize", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["src", "alpha", "beta", "norm_type", "ddepth"], "arg_types": ["cv2.GMat", "builtins.float", "builtins.float", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "normalize", "ret_type": "cv2.GMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "oak": {".class": "SymbolTableNode", "cross_ref": "cv2.gapi.oak", "kind": "Gdef", "module_public": false}, "onnx": {".class": "SymbolTableNode", "cross_ref": "cv2.gapi.onnx", "kind": "Gdef", "module_public": false}, "ot": {".class": "SymbolTableNode", "cross_ref": "cv2.gapi.ot", "kind": "Gdef", "module_public": false}, "ov": {".class": "SymbolTableNode", "cross_ref": "cv2.gapi.ov", "kind": "Gdef", "module_public": false}, "own": {".class": "SymbolTableNode", "cross_ref": "cv2.gapi.own", "kind": "Gdef", "module_public": false}, "parseSSD": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "cv2.gapi.parseSSD", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["in_", "inSz", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "filterLabel"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.gapi.parseSSD", "name": "parseSSD", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["in_", "inSz", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "filterLabel"], "arg_types": ["cv2.GMat", "cv2.GOpaqueT", "builtins.float", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parseSSD", "ret_type": {".class": "TupleType", "implicit": false, "items": ["cv2.GArrayT", "cv2.GArrayT"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.gapi.parseSSD", "name": "parseSSD", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["in_", "inSz", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "filterLabel"], "arg_types": ["cv2.GMat", "cv2.GOpaqueT", "builtins.float", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parseSSD", "ret_type": {".class": "TupleType", "implicit": false, "items": ["cv2.GArrayT", "cv2.GArrayT"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["in_", "inSz", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "alignmentToSquare", "filterOutOfBounds"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.gapi.parseSSD", "name": "parseSSD", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["in_", "inSz", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "alignmentToSquare", "filterOutOfBounds"], "arg_types": ["cv2.GMat", "cv2.GOpaqueT", "builtins.float", "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parseSSD", "ret_type": "cv2.GArrayT", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.gapi.parseSSD", "name": "parseSSD", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["in_", "inSz", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "alignmentToSquare", "filterOutOfBounds"], "arg_types": ["cv2.GMat", "cv2.GOpaqueT", "builtins.float", "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parseSSD", "ret_type": "cv2.GArrayT", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["in_", "inSz", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "filterLabel"], "arg_types": ["cv2.GMat", "cv2.GOpaqueT", "builtins.float", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parseSSD", "ret_type": {".class": "TupleType", "implicit": false, "items": ["cv2.GArrayT", "cv2.GArrayT"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["in_", "inSz", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "alignmentToSquare", "filterOutOfBounds"], "arg_types": ["cv2.GMat", "cv2.GOpaqueT", "builtins.float", "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parseSSD", "ret_type": "cv2.GArrayT", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "parseYolo": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["in_", "inSz", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nmsThreshold", "anchors"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.gapi.parse<PERSON><PERSON>", "name": "parseYolo", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["in_", "inSz", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nmsThreshold", "anchors"], "arg_types": ["cv2.GMat", "cv2.GOpaqueT", "builtins.float", "builtins.float", {".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parseYolo", "ret_type": {".class": "TupleType", "implicit": false, "items": ["cv2.GArrayT", "cv2.GArrayT"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "phase": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["x", "y", "angleInDegrees"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.gapi.phase", "name": "phase", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["x", "y", "angleInDegrees"], "arg_types": ["cv2.GMat", "cv2.GMat", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "phase", "ret_type": "cv2.GMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "polarToCart": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["magnitude", "angle", "angleInDegrees"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.gapi.polarToCart", "name": "polarToCart", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["magnitude", "angle", "angleInDegrees"], "arg_types": ["cv2.GMat", "cv2.GMat", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "polarToCart", "ret_type": {".class": "TupleType", "implicit": false, "items": ["cv2.GMat", "cv2.GMat"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "remap": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1, 1], "arg_names": ["src", "map1", "map2", "interpolation", "borderMode", "borderValue"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.gapi.remap", "name": "remap", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1, 1], "arg_names": ["src", "map1", "map2", "interpolation", "borderMode", "borderValue"], "arg_types": ["cv2.GMat", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, "builtins.int", "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.<PERSON>.<PERSON>"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "remap", "ret_type": "cv2.GMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "render": {".class": "SymbolTableNode", "cross_ref": "cv2.gapi.render", "kind": "Gdef", "module_public": false}, "resize": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["src", "dsize", "fx", "fy", "interpolation"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.gapi.resize", "name": "resize", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["src", "dsize", "fx", "fy", "interpolation"], "arg_types": ["cv2.GMat", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.<PERSON>.Size"}, "builtins.float", "builtins.float", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "resize", "ret_type": "cv2.GMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "select": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["src1", "src2", "mask"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.gapi.select", "name": "select", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["src1", "src2", "mask"], "arg_types": ["cv2.GMat", "cv2.GMat", "cv2.GMat"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "select", "ret_type": "cv2.GMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "sepFilter": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 1, 1], "arg_names": ["src", "ddepth", "kernelX", "kernelY", "anchor", "delta", "borderType", "borderValue"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.gapi.sepFilter", "name": "sepFilter", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 1, 1], "arg_names": ["src", "ddepth", "kernelX", "kernelY", "anchor", "delta", "borderType", "borderValue"], "arg_types": ["cv2.GMat", "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.Point"}, {".class": "TypeAliasType", "args": [], "type_ref": "cv2.<PERSON>.<PERSON>"}, "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.<PERSON>.<PERSON>"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "sepFilter", "ret_type": "cv2.GMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "split3": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["src"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.gapi.split3", "name": "split3", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["src"], "arg_types": ["cv2.GMat"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "split3", "ret_type": {".class": "TupleType", "implicit": false, "items": ["cv2.GMat", "cv2.GMat", "cv2.GMat"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "split4": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["src"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.gapi.split4", "name": "split4", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["src"], "arg_types": ["cv2.GMat"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "split4", "ret_type": {".class": "TupleType", "implicit": false, "items": ["cv2.GMat", "cv2.GMat", "cv2.GMat", "cv2.GMat"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "sqrt": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["src"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.gapi.sqrt", "name": "sqrt", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["src"], "arg_types": ["cv2.GMat"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "sqrt", "ret_type": "cv2.GMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "streaming": {".class": "SymbolTableNode", "cross_ref": "cv2.gapi.streaming", "kind": "Gdef", "module_public": false}, "sub": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["src1", "src2", "ddepth"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.gapi.sub", "name": "sub", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["src1", "src2", "ddepth"], "arg_types": ["cv2.GMat", "cv2.GMat", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "sub", "ret_type": "cv2.GMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "subC": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["src", "c", "ddepth"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.gapi.subC", "name": "subC", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["src", "c", "ddepth"], "arg_types": ["cv2.GMat", "cv2.GScalar", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "subC", "ret_type": "cv2.GMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "subRC": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["c", "src", "ddepth"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.gapi.subRC", "name": "subRC", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["c", "src", "ddepth"], "arg_types": ["cv2.GScalar", "cv2.GMat", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "subRC", "ret_type": "cv2.GMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "sum": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["src"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.gapi.sum", "name": "sum", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["src"], "arg_types": ["cv2.GMat"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "sum", "ret_type": "cv2.GScalar", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "threshold": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "cv2.gapi.threshold", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["src", "thresh", "maxval", "type"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.gapi.threshold", "name": "threshold", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["src", "thresh", "maxval", "type"], "arg_types": ["cv2.GMat", "cv2.GScalar", "cv2.GScalar", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "threshold", "ret_type": "cv2.GMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.gapi.threshold", "name": "threshold", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["src", "thresh", "maxval", "type"], "arg_types": ["cv2.GMat", "cv2.GScalar", "cv2.GScalar", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "threshold", "ret_type": "cv2.GMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["src", "maxval", "type"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.gapi.threshold", "name": "threshold", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["src", "maxval", "type"], "arg_types": ["cv2.GMat", "cv2.GScalar", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "threshold", "ret_type": {".class": "TupleType", "implicit": false, "items": ["cv2.GMat", "cv2.GScalar"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.gapi.threshold", "name": "threshold", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["src", "maxval", "type"], "arg_types": ["cv2.GMat", "cv2.GScalar", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "threshold", "ret_type": {".class": "TupleType", "implicit": false, "items": ["cv2.GMat", "cv2.GScalar"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["src", "thresh", "maxval", "type"], "arg_types": ["cv2.GMat", "cv2.GScalar", "cv2.GScalar", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "threshold", "ret_type": "cv2.GMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["src", "maxval", "type"], "arg_types": ["cv2.GMat", "cv2.GScalar", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "threshold", "ret_type": {".class": "TupleType", "implicit": false, "items": ["cv2.GMat", "cv2.GScalar"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "transpose": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["src"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.gapi.transpose", "name": "transpose", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["src"], "arg_types": ["cv2.GMat"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "transpose", "ret_type": "cv2.GMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "video": {".class": "SymbolTableNode", "cross_ref": "cv2.gapi.video", "kind": "Gdef", "module_public": false}, "warpAffine": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1], "arg_names": ["src", "M", "dsize", "flags", "borderMode", "borderValue"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.gapi.warpAffine", "name": "warpAffine", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1], "arg_names": ["src", "M", "dsize", "flags", "borderMode", "borderValue"], "arg_types": ["cv2.GMat", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "cv2.<PERSON>.Size"}, "builtins.int", "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.<PERSON>.<PERSON>"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "warpAffine", "ret_type": "cv2.GMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "warpPerspective": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1], "arg_names": ["src", "M", "dsize", "flags", "borderMode", "borderValue"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.gapi.warpPerspective", "name": "warpPerspective", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1], "arg_names": ["src", "M", "dsize", "flags", "borderMode", "borderValue"], "arg_types": ["cv2.GMat", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "cv2.<PERSON>.Size"}, "builtins.int", "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.<PERSON>.<PERSON>"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "warpPerspective", "ret_type": "cv2.GMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "wip": {".class": "SymbolTableNode", "cross_ref": "cv2.gapi.wip", "kind": "Gdef", "module_public": false}}, "path": "c:\\Python313\\Lib\\site-packages\\cv2\\gapi\\__init__.pyi"}