{"data_mtime": 1750101670, "dep_lines": [15, 16, 8, 9, 10, 13, 1, 1, 1, 1, 1, 1, 12], "dep_prios": [5, 5, 10, 5, 5, 5, 5, 30, 30, 30, 30, 30, 10], "dependencies": ["src.utils.logger", "src.utils.config", "asyncio", "typing", "datetime", "loguru", "builtins", "_asyncio", "_contextvars", "_frozen_importlib", "abc", "src.utils"], "hash": "9be298573fb0b7fea25b8b97b1f37ac01b159c66", "id": "src.ui.main_view", "ignore_all": true, "interface_hash": "e6df82fdf72159eb7b50d59dff3860729e6bc7ee", "mtime": 1750101647, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "E:\\db\\0617\\src\\ui\\main_view.py", "plugin_data": null, "size": 19501, "suppressed": ["flet"], "version_id": "1.15.0"}