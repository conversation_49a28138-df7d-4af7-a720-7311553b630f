{".class": "MypyFile", "_fullname": "numpy.polynomial", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "Chebyshev": {".class": "SymbolTableNode", "cross_ref": "numpy.polynomial.chebyshev.Chebyshev", "kind": "Gdef"}, "Final": {".class": "SymbolTableNode", "cross_ref": "typing.Final", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Hermite": {".class": "SymbolTableNode", "cross_ref": "numpy.polynomial.hermite.Hermite", "kind": "Gdef"}, "HermiteE": {".class": "SymbolTableNode", "cross_ref": "numpy.polynomial.hermite_e.<PERSON>", "kind": "Gdef"}, "Laguerre": {".class": "SymbolTableNode", "cross_ref": "numpy.polynomial.laguerre.Laguerre", "kind": "Gdef"}, "Legendre": {".class": "SymbolTableNode", "cross_ref": "numpy.polynomial.legendre.Legendre", "kind": "Gdef"}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing.Literal", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Polynomial": {".class": "SymbolTableNode", "cross_ref": "numpy.polynomial.polynomial.Polynomial", "kind": "Gdef"}, "_PytestTester": {".class": "SymbolTableNode", "cross_ref": "numpy._pytesttester.PytestTester", "kind": "Gdef", "module_hidden": true, "module_public": false}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "numpy.polynomial.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.__package__", "name": "__package__", "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.__path__", "name": "__path__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "chebyshev": {".class": "SymbolTableNode", "cross_ref": "numpy.polynomial.cheb<PERSON><PERSON>v", "kind": "Gdef"}, "hermite": {".class": "SymbolTableNode", "cross_ref": "numpy.polynomial.hermite", "kind": "Gdef"}, "hermite_e": {".class": "SymbolTableNode", "cross_ref": "numpy.polynomial.hermite_e", "kind": "Gdef"}, "laguerre": {".class": "SymbolTableNode", "cross_ref": "numpy.polynomial.laguerre", "kind": "Gdef"}, "legendre": {".class": "SymbolTableNode", "cross_ref": "numpy.polynomial.legendre", "kind": "Gdef"}, "polynomial": {".class": "SymbolTableNode", "cross_ref": "numpy.polynomial.polynomial", "kind": "Gdef"}, "set_default_printstyle": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["style"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.polynomial.set_default_printstyle", "name": "set_default_printstyle", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["style"], "arg_types": [{".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "ascii"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "unicode"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_default_printstyle", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "test": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "numpy.polynomial.test", "name": "test", "type": "numpy._pytesttester.PytestTester"}}}, "path": "c:\\Python313\\Lib\\site-packages\\numpy\\polynomial\\__init__.pyi"}