{".class": "MypyFile", "_fullname": "cv2.gapi.oak", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "COLOR_CAMERA_PARAMS_BOARD_SOCKET_BGR": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.gapi.oak.COLOR_CAMERA_PARAMS_BOARD_SOCKET_BGR", "name": "COLOR_CAMERA_PARAMS_BOARD_SOCKET_BGR", "type": "builtins.int"}}, "COLOR_CAMERA_PARAMS_BOARD_SOCKET_RGB": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.gapi.oak.COLOR_CAMERA_PARAMS_BOARD_SOCKET_RGB", "name": "COLOR_CAMERA_PARAMS_BOARD_SOCKET_RGB", "type": "builtins.int"}}, "COLOR_CAMERA_PARAMS_RESOLUTION_THE_1080_P": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.gapi.oak.COLOR_CAMERA_PARAMS_RESOLUTION_THE_1080_P", "name": "COLOR_CAMERA_PARAMS_RESOLUTION_THE_1080_P", "type": "builtins.int"}}, "ColorCameraParams_BoardSocket": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "cv2.gapi.oak.ColorCameraParams_BoardSocket", "line": 27, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "builtins.int"}}, "ColorCameraParams_BoardSocket_BGR": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.gapi.oak.ColorCameraParams_BoardSocket_BGR", "name": "ColorCameraParams_BoardSocket_BGR", "type": "builtins.int"}}, "ColorCameraParams_BoardSocket_RGB": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.gapi.oak.ColorCameraParams_BoardSocket_RGB", "name": "ColorCameraParams_BoardSocket_RGB", "type": "builtins.int"}}, "ColorCameraParams_Resolution": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "cv2.gapi.oak.ColorCameraParams_Resolution", "line": 32, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "builtins.int"}}, "ColorCameraParams_Resolution_THE_1080_P": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.gapi.oak.ColorCameraParams_Resolution_THE_1080_P", "name": "ColorCameraParams_Resolution_THE_1080_P", "type": "builtins.int"}}, "ENCODER_CONFIG_PROFILE_H264_BASELINE": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.gapi.oak.ENCODER_CONFIG_PROFILE_H264_BASELINE", "name": "ENCODER_CONFIG_PROFILE_H264_BASELINE", "type": "builtins.int"}}, "ENCODER_CONFIG_PROFILE_H264_HIGH": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.gapi.oak.ENCODER_CONFIG_PROFILE_H264_HIGH", "name": "ENCODER_CONFIG_PROFILE_H264_HIGH", "type": "builtins.int"}}, "ENCODER_CONFIG_PROFILE_H264_MAIN": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.gapi.oak.ENCODER_CONFIG_PROFILE_H264_MAIN", "name": "ENCODER_CONFIG_PROFILE_H264_MAIN", "type": "builtins.int"}}, "ENCODER_CONFIG_PROFILE_H265_MAIN": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.gapi.oak.ENCODER_CONFIG_PROFILE_H265_MAIN", "name": "ENCODER_CONFIG_PROFILE_H265_MAIN", "type": "builtins.int"}}, "ENCODER_CONFIG_PROFILE_MJPEG": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.gapi.oak.ENCODER_CONFIG_PROFILE_MJPEG", "name": "ENCODER_CONFIG_PROFILE_MJPEG", "type": "builtins.int"}}, "ENCODER_CONFIG_RATE_CONTROL_MODE_CBR": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.gapi.oak.ENCODER_CONFIG_RATE_CONTROL_MODE_CBR", "name": "ENCODER_CONFIG_RATE_CONTROL_MODE_CBR", "type": "builtins.int"}}, "ENCODER_CONFIG_RATE_CONTROL_MODE_VBR": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.gapi.oak.ENCODER_CONFIG_RATE_CONTROL_MODE_VBR", "name": "ENCODER_CONFIG_RATE_CONTROL_MODE_VBR", "type": "builtins.int"}}, "EncoderConfig_Profile": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "cv2.gapi.oak.EncoderConfig_Profile", "line": 20, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "builtins.int"}}, "EncoderConfig_Profile_H264_BASELINE": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.gapi.oak.EncoderConfig_Profile_H264_BASELINE", "name": "EncoderConfig_Profile_H264_BASELINE", "type": "builtins.int"}}, "EncoderConfig_Profile_H264_HIGH": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.gapi.oak.EncoderConfig_Profile_H264_HIGH", "name": "EncoderConfig_Profile_H264_HIGH", "type": "builtins.int"}}, "EncoderConfig_Profile_H264_MAIN": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.gapi.oak.EncoderConfig_Profile_H264_MAIN", "name": "EncoderConfig_Profile_H264_MAIN", "type": "builtins.int"}}, "EncoderConfig_Profile_H265_MAIN": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.gapi.oak.EncoderConfig_Profile_H265_MAIN", "name": "EncoderConfig_Profile_H265_MAIN", "type": "builtins.int"}}, "EncoderConfig_Profile_MJPEG": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.gapi.oak.EncoderConfig_Profile_MJPEG", "name": "EncoderConfig_Profile_MJPEG", "type": "builtins.int"}}, "EncoderConfig_RateControlMode": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "cv2.gapi.oak.EncoderConfig_RateControlMode", "line": 7, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "builtins.int"}}, "EncoderConfig_RateControlMode_CBR": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.gapi.oak.EncoderConfig_RateControlMode_CBR", "name": "EncoderConfig_RateControlMode_CBR", "type": "builtins.int"}}, "EncoderConfig_RateControlMode_VBR": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.gapi.oak.EncoderConfig_RateControlMode_VBR", "name": "EncoderConfig_RateControlMode_VBR", "type": "builtins.int"}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "cv2.gapi.oak.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.gapi.oak.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.gapi.oak.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.gapi.oak.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.gapi.oak.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.gapi.oak.__package__", "name": "__package__", "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.gapi.oak.__path__", "name": "__path__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.gapi.oak.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}}, "path": "c:\\Python313\\Lib\\site-packages\\cv2\\gapi\\oak\\__init__.pyi"}