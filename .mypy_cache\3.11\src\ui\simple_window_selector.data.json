{".class": "MypyFile", "_fullname": "src.ui.simple_window_selector", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "LoggerMixin": {".class": "SymbolTableNode", "cross_ref": "src.utils.logger.LoggerMixin", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "SimpleWindowSelector": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["src.utils.logger.LoggerMixin"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "src.ui.simple_window_selector.SimpleWindowSelector", "name": "SimpleWindowSelector", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "src.ui.simple_window_selector.SimpleWindowSelector", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "src.ui.simple_window_selector", "mro": ["src.ui.simple_window_selector.SimpleWindowSelector", "src.utils.logger.LoggerMixin", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1], "arg_names": ["self", "page", "window_service", "config_manager", "on_window_selected", "on_close"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "src.ui.simple_window_selector.SimpleWindowSelector.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1], "arg_names": ["self", "page", "window_service", "config_manager", "on_window_selected", "on_close"], "arg_types": ["src.ui.simple_window_selector.SimpleWindowSelector", {".class": "AnyType", "missing_import_name": "src.ui.simple_window_selector.ft", "source_any": null, "type_of_any": 3}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of SimpleWindowSelector", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_build_footer": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "src.ui.simple_window_selector.SimpleWindowSelector._build_footer", "name": "_build_footer", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["src.ui.simple_window_selector.SimpleWindowSelector"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_build_footer of SimpleWindowSelector", "ret_type": {".class": "AnyType", "missing_import_name": "src.ui.simple_window_selector.ft", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_build_header": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "src.ui.simple_window_selector.SimpleWindowSelector._build_header", "name": "_build_header", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["src.ui.simple_window_selector.SimpleWindowSelector"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_build_header of SimpleWindowSelector", "ret_type": {".class": "AnyType", "missing_import_name": "src.ui.simple_window_selector.ft", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_build_search_bar": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "src.ui.simple_window_selector.SimpleWindowSelector._build_search_bar", "name": "_build_search_bar", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["src.ui.simple_window_selector.SimpleWindowSelector"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_build_search_bar of SimpleWindowSelector", "ret_type": {".class": "AnyType", "missing_import_name": "src.ui.simple_window_selector.ft", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_build_window_list": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "src.ui.simple_window_selector.SimpleWindowSelector._build_window_list", "name": "_build_window_list", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["src.ui.simple_window_selector.SimpleWindowSelector"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_build_window_list of SimpleWindowSelector", "ret_type": {".class": "AnyType", "missing_import_name": "src.ui.simple_window_selector.ft", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_create_window_item": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "window_data", "title", "process", "handle"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "src.ui.simple_window_selector.SimpleWindowSelector._create_window_item", "name": "_create_window_item", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "window_data", "title", "process", "handle"], "arg_types": ["src.ui.simple_window_selector.SimpleWindowSelector", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.str", "builtins.str", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_create_window_item of SimpleWindowSelector", "ret_type": {".class": "AnyType", "missing_import_name": "src.ui.simple_window_selector.ft", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_load_windows": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "src.ui.simple_window_selector.SimpleWindowSelector._load_windows", "name": "_load_windows", "type": null}}, "_on_search_change": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "e"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "src.ui.simple_window_selector.SimpleWindowSelector._on_search_change", "name": "_on_search_change", "type": null}}, "_on_window_click": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "window_data"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "src.ui.simple_window_selector.SimpleWindowSelector._on_window_click", "name": "_on_window_click", "type": null}}, "_populate_window_list": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "src.ui.simple_window_selector.SimpleWindowSelector._populate_window_list", "name": "_populate_window_list", "type": null}}, "_refresh_windows": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "e"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "src.ui.simple_window_selector.SimpleWindowSelector._refresh_windows", "name": "_refresh_windows", "type": null}}, "_select_window": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "e"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "src.ui.simple_window_selector.SimpleWindowSelector._select_window", "name": "_select_window", "type": null}}, "_show_message": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "message"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "src.ui.simple_window_selector.SimpleWindowSelector._show_message", "name": "_show_message", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "message"], "arg_types": ["src.ui.simple_window_selector.SimpleWindowSelector", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_show_message of SimpleWindowSelector", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_update_window_list": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "src.ui.simple_window_selector.SimpleWindowSelector._update_window_list", "name": "_update_window_list", "type": null}}, "build": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "src.ui.simple_window_selector.SimpleWindowSelector.build", "name": "build", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["src.ui.simple_window_selector.SimpleWindowSelector"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "build of SimpleWindowSelector", "ret_type": {".class": "AnyType", "missing_import_name": "src.ui.simple_window_selector.ft", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "config": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "src.ui.simple_window_selector.SimpleWindowSelector.config", "name": "config", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "controls": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "src.ui.simple_window_selector.SimpleWindowSelector.controls", "name": "controls", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "src.ui.simple_window_selector.ft", "source_any": {".class": "AnyType", "missing_import_name": "src.ui.simple_window_selector.ft", "source_any": null, "type_of_any": 3}, "type_of_any": 7}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "on_close": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "src.ui.simple_window_selector.SimpleWindowSelector.on_close", "name": "on_close", "type": {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "on_window_selected": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "src.ui.simple_window_selector.SimpleWindowSelector.on_window_selected", "name": "on_window_selected", "type": {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "page": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "src.ui.simple_window_selector.SimpleWindowSelector.page", "name": "page", "type": {".class": "AnyType", "missing_import_name": "src.ui.simple_window_selector.ft", "source_any": null, "type_of_any": 3}}}, "refresh_button": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "src.ui.simple_window_selector.SimpleWindowSelector.refresh_button", "name": "refresh_button", "type": {".class": "NoneType"}}}, "search_field": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "src.ui.simple_window_selector.SimpleWindowSelector.search_field", "name": "search_field", "type": {".class": "NoneType"}}}, "selected_window": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "src.ui.simple_window_selector.SimpleWindowSelector.selected_window", "name": "selected_window", "type": {".class": "NoneType"}}}, "window_list_view": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "src.ui.simple_window_selector.SimpleWindowSelector.window_list_view", "name": "window_list_view", "type": {".class": "NoneType"}}}, "window_service": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "src.ui.simple_window_selector.SimpleWindowSelector.window_service", "name": "window_service", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "windows_list": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred", "invalid_partial_type"], "fullname": "src.ui.simple_window_selector.SimpleWindowSelector.windows_list", "name": "windows_list", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "extra_attrs": null, "type_ref": "builtins.list"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "src.ui.simple_window_selector.SimpleWindowSelector.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "src.ui.simple_window_selector.SimpleWindowSelector", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.ui.simple_window_selector.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.ui.simple_window_selector.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.ui.simple_window_selector.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.ui.simple_window_selector.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.ui.simple_window_selector.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.ui.simple_window_selector.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "ft": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "src.ui.simple_window_selector.ft", "name": "ft", "type": {".class": "AnyType", "missing_import_name": "src.ui.simple_window_selector.ft", "source_any": null, "type_of_any": 3}}}}, "path": "E:\\db\\0617\\src\\ui\\simple_window_selector.py"}