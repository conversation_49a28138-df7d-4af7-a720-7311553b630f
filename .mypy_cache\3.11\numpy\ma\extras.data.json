{".class": "MypyFile", "_fullname": "numpy.ma.extras", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "AxisConcatenator": {".class": "SymbolTableNode", "cross_ref": "numpy.lib._index_tricks_impl.AxisConcatenator", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Incomplete": {".class": "SymbolTableNode", "cross_ref": "_typeshed.Incomplete", "kind": "Gdef", "module_hidden": true, "module_public": false}, "MAxisConcatenator": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.int", "value": 0}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "LiteralType", "fallback": "builtins.int", "value": 1}, {".class": "LiteralType", "fallback": "builtins.int", "value": -1}], "extra_attrs": null, "type_ref": "numpy.lib._index_tricks_impl.AxisConcatenator"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy.ma.extras.MAxisConcatenator", "name": "MAxisConcatenator", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "numpy.ma.extras.MAxisConcatenator", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "numpy.ma.extras", "mro": ["numpy.ma.extras.MAxisConcatenator", "numpy.lib._index_tricks_impl.AxisConcatenator", "builtins.object"], "names": {".class": "SymbolTable", "concatenate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["arrays", "axis"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "numpy.ma.extras.MAxisConcatenator.concatenate", "name": "concatenate", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["arrays", "axis"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.Incomplete"}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "concatenate of MAxisConcatenator", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.Incomplete"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "numpy.ma.extras.MAxisConcatenator.concatenate", "name": "concatenate", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["arrays", "axis"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.Incomplete"}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "concatenate of MAxisConcatenator", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.Incomplete"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "makemat": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "arr"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "numpy.ma.extras.MAxisConcatenator.makemat", "name": "makemat", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "arr"], "arg_types": [{".class": "TypeType", "item": "numpy.ma.extras.MAxisConcatenator"}, {".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.Incomplete"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "makemat of MAxisConcatenator", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.Incomplete"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "numpy.ma.extras.MAxisConcatenator.makemat", "name": "makemat", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "arr"], "arg_types": [{".class": "TypeType", "item": "numpy.ma.extras.MAxisConcatenator"}, {".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.Incomplete"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "makemat of MAxisConcatenator", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.Incomplete"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.ma.extras.MAxisConcatenator.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "numpy.ma.extras.MAxisConcatenator", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "MaskedArray": {".class": "SymbolTableNode", "cross_ref": "numpy.ma.core.MaskedArray", "kind": "Gdef", "module_hidden": true, "module_public": false}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "numpy.ma.extras.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.ma.extras.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.ma.extras.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.ma.extras.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.ma.extras.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.ma.extras.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.ma.extras.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_fromnxfunction": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy.ma.extras._fromnxfunction", "name": "_fromnxfunction", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "numpy.ma.extras._fromnxfunction", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "numpy.ma.extras", "mro": ["numpy.ma.extras._fromnxfunction", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "params"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.extras._fromnxfunction.__call__", "name": "__call__", "type": null}}, "__doc__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "numpy.ma.extras._fromnxfunction.__doc__", "name": "__doc__", "type": {".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.Incomplete"}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "funcname"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.extras._fromnxfunction.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "funcname"], "arg_types": ["numpy.ma.extras._fromnxfunction", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of _fromnxfunction", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__name__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "numpy.ma.extras._fromnxfunction.__name__", "name": "__name__", "type": {".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.Incomplete"}}}, "getdoc": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.extras._fromnxfunction.getdoc", "name": "getdoc", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.ma.extras._fromnxfunction.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "numpy.ma.extras._fromnxfunction", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_fromnxfunction_allargs": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["numpy.ma.extras._fromnxfunction"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy.ma.extras._fromnxfunction_allargs", "name": "_fromnxfunction_allargs", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "numpy.ma.extras._fromnxfunction_allargs", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "numpy.ma.extras", "mro": ["numpy.ma.extras._fromnxfunction_allargs", "numpy.ma.extras._fromnxfunction", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "params"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.extras._fromnxfunction_allargs.__call__", "name": "__call__", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.ma.extras._fromnxfunction_allargs.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "numpy.ma.extras._fromnxfunction_allargs", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_fromnxfunction_seq": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["numpy.ma.extras._fromnxfunction"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy.ma.extras._fromnxfunction_seq", "name": "_fromnxfunction_seq", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "numpy.ma.extras._fromnxfunction_seq", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "numpy.ma.extras", "mro": ["numpy.ma.extras._fromnxfunction_seq", "numpy.ma.extras._fromnxfunction", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2, 4], "arg_names": ["self", "x", "args", "params"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.extras._fromnxfunction_seq.__call__", "name": "__call__", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.ma.extras._fromnxfunction_seq.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "numpy.ma.extras._fromnxfunction_seq", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_fromnxfunction_single": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["numpy.ma.extras._fromnxfunction"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy.ma.extras._fromnxfunction_single", "name": "_fromnxfunction_single", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "numpy.ma.extras._fromnxfunction_single", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "numpy.ma.extras", "mro": ["numpy.ma.extras._fromnxfunction_single", "numpy.ma.extras._fromnxfunction", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2, 4], "arg_names": ["self", "x", "args", "params"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.extras._fromnxfunction_single.__call__", "name": "__call__", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.ma.extras._fromnxfunction_single.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "numpy.ma.extras._fromnxfunction_single", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "apply_along_axis": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 2, 4], "arg_names": ["func1d", "axis", "arr", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.extras.apply_along_axis", "name": "apply_along_axis", "type": null}}, "apply_over_axes": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["func", "a", "axes"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.extras.apply_over_axes", "name": "apply_over_axes", "type": null}}, "atleast_1d": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.ma.extras.atleast_1d", "name": "atleast_1d", "type": "numpy.ma.extras._fromnxfunction_allargs"}}, "atleast_2d": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.ma.extras.atleast_2d", "name": "atleast_2d", "type": "numpy.ma.extras._fromnxfunction_allargs"}}, "atleast_3d": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.ma.extras.atleast_3d", "name": "atleast_3d", "type": "numpy.ma.extras._fromnxfunction_allargs"}}, "average": {".class": "SymbolTableNode", "cross_ref": "numpy.lib._function_base_impl.average", "kind": "Gdef"}, "clump_masked": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["a"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.extras.clump_masked", "name": "clump_masked", "type": null}}, "clump_unmasked": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["a"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.extras.clump_unmasked", "name": "clump_unmasked", "type": null}}, "column_stack": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.ma.extras.column_stack", "name": "column_stack", "type": "numpy.ma.extras._fromnxfunction_seq"}}, "compress_cols": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["a"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.extras.compress_cols", "name": "compress_cols", "type": null}}, "compress_nd": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["x", "axis"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.extras.compress_nd", "name": "compress_nd", "type": null}}, "compress_rowcols": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["x", "axis"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.extras.compress_rowcols", "name": "compress_rowcols", "type": null}}, "compress_rows": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["a"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.extras.compress_rows", "name": "compress_rows", "type": null}}, "corrcoef": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1], "arg_names": ["x", "y", "row<PERSON>", "bias", "allow_masked", "ddof"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.extras.corr<PERSON>f", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": null}}, "count_masked": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["arr", "axis"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.extras.count_masked", "name": "count_masked", "type": null}}, "cov": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1], "arg_names": ["x", "y", "row<PERSON>", "bias", "allow_masked", "ddof"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.extras.cov", "name": "cov", "type": null}}, "diagflat": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.ma.extras.diagflat", "name": "diagflat", "type": "numpy.ma.extras._fromnxfunction_single"}}, "dot": {".class": "SymbolTableNode", "cross_ref": "numpy.ma.core.dot", "kind": "Gdef"}, "dstack": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.ma.extras.dstack", "name": "dstack", "type": "numpy.ma.extras._fromnxfunction_seq"}}, "ediff1d": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["arr", "to_end", "to_begin"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.extras.ediff1d", "name": "ediff1d", "type": null}}, "flatnotmasked_contiguous": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["a"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.extras.flatnotmasked_contiguous", "name": "flatnotmasked_contiguous", "type": null}}, "flatnotmasked_edges": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["a"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.extras.flatnotmasked_edges", "name": "flatnotmasked_edges", "type": null}}, "hsplit": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.ma.extras.hsplit", "name": "hsplit", "type": "numpy.ma.extras._fromnxfunction_single"}}, "hstack": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.ma.extras.hstack", "name": "hstack", "type": "numpy.ma.extras._fromnxfunction_seq"}}, "in1d": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["ar1", "ar2", "assume_unique", "invert"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.extras.in1d", "name": "in1d", "type": null}}, "intersect1d": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["ar1", "ar2", "assume_unique"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.extras.intersect1d", "name": "intersect1d", "type": null}}, "isin": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["element", "test_elements", "assume_unique", "invert"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.extras.isin", "name": "isin", "type": null}}, "mask_cols": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["a", "axis"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.extras.mask_cols", "name": "mask_cols", "type": null}}, "mask_rowcols": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["a", "axis"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.extras.mask_rowcols", "name": "mask_rowcols", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["a", "axis"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.Incomplete"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.Incomplete"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "mask_rowcols", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.Incomplete"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.Incomplete"}], "extra_attrs": null, "type_ref": "numpy.dtype"}], "extra_attrs": null, "type_ref": "numpy.ma.core.MaskedArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "mask_rows": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["a", "axis"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.extras.mask_rows", "name": "mask_rows", "type": null}}, "masked_all": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["shape", "dtype"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.extras.masked_all", "name": "masked_all", "type": null}}, "masked_all_like": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["arr"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.extras.masked_all_like", "name": "masked_all_like", "type": null}}, "median": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["a", "axis", "out", "overwrite_input", "keepdims"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.extras.median", "name": "median", "type": null}}, "mr_": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.ma.extras.mr_", "name": "mr_", "type": "numpy.ma.extras.mr_class"}}, "mr_class": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["numpy.ma.extras.MAxisConcatenator"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy.ma.extras.mr_class", "name": "mr_class", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "numpy.ma.extras.mr_class", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "numpy.ma.extras", "mro": ["numpy.ma.extras.mr_class", "numpy.ma.extras.MAxisConcatenator", "numpy.lib._index_tricks_impl.AxisConcatenator", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.extras.mr_class.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["numpy.ma.extras.mr_class"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of mr_class", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.ma.extras.mr_class.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "numpy.ma.extras.mr_class", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ndenumerate": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["a", "compressed"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.extras.ndenumerate", "name": "ndenumerate", "type": null}}, "notmasked_contiguous": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["a", "axis"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.extras.notmasked_contiguous", "name": "notmasked_contiguous", "type": null}}, "notmasked_edges": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["a", "axis"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.extras.notmasked_edges", "name": "notmasked_edges", "type": null}}, "np": {".class": "SymbolTableNode", "cross_ref": "numpy", "kind": "Gdef", "module_hidden": true, "module_public": false}, "polyfit": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1, 1], "arg_names": ["x", "y", "deg", "rcond", "full", "w", "cov"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.extras.polyfit", "name": "polyfit", "type": null}}, "row_stack": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.ma.extras.row_stack", "name": "row_stack", "type": "numpy.ma.extras._fromnxfunction_seq"}}, "setdiff1d": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["ar1", "ar2", "assume_unique"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.extras.setdiff1d", "name": "setdiff1d", "type": null}}, "setxor1d": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["ar1", "ar2", "assume_unique"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.extras.setxor1d", "name": "setxor1d", "type": null}}, "stack": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.ma.extras.stack", "name": "stack", "type": "numpy.ma.extras._fromnxfunction_seq"}}, "union1d": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["ar1", "ar2"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.extras.union1d", "name": "union1d", "type": null}}, "unique": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["ar1", "return_index", "return_inverse"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.extras.unique", "name": "unique", "type": null}}, "vander": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["x", "n"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.extras.vander", "name": "vander", "type": null}}, "vstack": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.ma.extras.vstack", "name": "vstack", "type": "numpy.ma.extras._fromnxfunction_seq"}}}, "path": "c:\\Python313\\Lib\\site-packages\\numpy\\ma\\extras.pyi"}