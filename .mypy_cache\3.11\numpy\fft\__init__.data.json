{".class": "MypyFile", "_fullname": "numpy.fft", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "numpy.fft.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.fft.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.fft.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.fft.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.fft.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.fft.__package__", "name": "__package__", "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.fft.__path__", "name": "__path__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.fft.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "fft": {".class": "SymbolTableNode", "cross_ref": "numpy.fft._pocketfft.fft", "kind": "Gdef"}, "fft2": {".class": "SymbolTableNode", "cross_ref": "numpy.fft._pocketfft.fft2", "kind": "Gdef"}, "fftfreq": {".class": "SymbolTableNode", "cross_ref": "numpy.fft._helper.fftfreq", "kind": "Gdef"}, "fftn": {".class": "SymbolTableNode", "cross_ref": "numpy.fft._pocketfft.fftn", "kind": "Gdef"}, "fftshift": {".class": "SymbolTableNode", "cross_ref": "numpy.fft._helper.fftshift", "kind": "Gdef"}, "hfft": {".class": "SymbolTableNode", "cross_ref": "numpy.fft._pocketfft.hfft", "kind": "Gdef"}, "ifft": {".class": "SymbolTableNode", "cross_ref": "numpy.fft._pocketfft.ifft", "kind": "Gdef"}, "ifft2": {".class": "SymbolTableNode", "cross_ref": "numpy.fft._pocketfft.ifft2", "kind": "Gdef"}, "ifftn": {".class": "SymbolTableNode", "cross_ref": "numpy.fft._pocketfft.ifftn", "kind": "Gdef"}, "ifftshift": {".class": "SymbolTableNode", "cross_ref": "numpy.fft._helper.ifftshift", "kind": "Gdef"}, "ihfft": {".class": "SymbolTableNode", "cross_ref": "numpy.fft._pocketfft.ihfft", "kind": "Gdef"}, "irfft": {".class": "SymbolTableNode", "cross_ref": "numpy.fft._pocketfft.irfft", "kind": "Gdef"}, "irfft2": {".class": "SymbolTableNode", "cross_ref": "numpy.fft._pocketfft.irfft2", "kind": "Gdef"}, "irfftn": {".class": "SymbolTableNode", "cross_ref": "numpy.fft._pocketfft.irfftn", "kind": "Gdef"}, "rfft": {".class": "SymbolTableNode", "cross_ref": "numpy.fft._pocketfft.rfft", "kind": "Gdef"}, "rfft2": {".class": "SymbolTableNode", "cross_ref": "numpy.fft._pocketfft.rfft2", "kind": "Gdef"}, "rfftfreq": {".class": "SymbolTableNode", "cross_ref": "numpy.fft._helper.rfftfreq", "kind": "Gdef"}, "rfftn": {".class": "SymbolTableNode", "cross_ref": "numpy.fft._pocketfft.rfftn", "kind": "Gdef"}}, "path": "c:\\Python313\\Lib\\site-packages\\numpy\\fft\\__init__.pyi"}