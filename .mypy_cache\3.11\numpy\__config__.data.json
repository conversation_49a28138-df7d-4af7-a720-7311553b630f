{".class": "MypyFile", "_fullname": "numpy.__config__", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "CONFIG": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_final", "is_ready", "has_explicit_value"], "fullname": "numpy.__config__.CONFIG", "name": "CONFIG", "type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy.__config__._ConfigDict"}}}, "DisplayModes": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["enum.Enum"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy.__config__.DisplayModes", "name": "DisplayModes", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_enum"], "fullname": "numpy.__config__.DisplayModes", "has_param_spec_type": false, "metaclass_type": "enum.Enum<PERSON>", "metadata": {}, "module_name": "numpy.__config__", "mro": ["numpy.__config__.DisplayModes", "enum.Enum", "builtins.object"], "names": {".class": "SymbolTable", "dicts": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "numpy.__config__.DisplayModes.dicts", "name": "dicts", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "dicts"}, "type_ref": "builtins.str"}}}, "stdout": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "numpy.__config__.DisplayModes.stdout", "name": "stdout", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "stdout"}, "type_ref": "builtins.str"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.__config__.DisplayModes.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "numpy.__config__.DisplayModes", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Enum": {".class": "SymbolTableNode", "cross_ref": "enum.Enum", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Final": {".class": "SymbolTableNode", "cross_ref": "typing.Final", "kind": "Gdef", "module_hidden": true, "module_public": false}, "L": {".class": "SymbolTableNode", "cross_ref": "typing.Literal", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ModuleType": {".class": "SymbolTableNode", "cross_ref": "types.ModuleType", "kind": "Gdef", "module_hidden": true, "module_public": false}, "NotRequired": {".class": "SymbolTableNode", "cross_ref": "typing.NotRequired", "kind": "Gdef", "module_hidden": true, "module_public": false}, "TypedDict": {".class": "SymbolTableNode", "cross_ref": "typing.TypedDict", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_BuildDependenciesDict": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy.__config__._BuildDependenciesDict", "name": "_BuildDependenciesDict", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "numpy.__config__._BuildDependenciesDict", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "numpy.__config__", "mro": ["numpy.__config__._BuildDependenciesDict", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["blas", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.__config__._BuildDependenciesDictValue"}], ["lapack", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.__config__._BuildDependenciesDictValue"}]], "readonly_keys": [], "required_keys": ["blas", "lapack"]}}}, "_BuildDependenciesDictValue": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy.__config__._BuildDependenciesDictValue", "name": "_BuildDependenciesDictValue", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "numpy.__config__._BuildDependenciesDictValue", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "numpy.__config__", "mro": ["numpy.__config__._BuildDependenciesDictValue", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["name", "builtins.str"], ["found", {".class": "LiteralType", "fallback": "builtins.bool", "value": true}], ["version", "builtins.str"], ["include directory", "builtins.str"], ["lib directory", "builtins.str"], ["openblas configuration", "builtins.str"], ["pc file directory", "builtins.str"]], "readonly_keys": [], "required_keys": ["include directory", "lib directory", "name", "openblas configuration", "pc file directory", "version"]}}}, "_CompilerConfigDict": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy.__config__._CompilerConfigDict", "name": "_CompilerConfigDict", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "numpy.__config__._CompilerConfigDict", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "numpy.__config__", "mro": ["numpy.__config__._CompilerConfigDict", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["c", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.__config__._CompilerConfigDictValue"}], ["cython", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.__config__._CompilerConfigDictValue"}], ["c++", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.__config__._CompilerConfigDictValue"}]], "readonly_keys": [], "required_keys": ["c", "c++", "cython"]}}}, "_CompilerConfigDictValue": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy.__config__._CompilerConfigDictValue", "name": "_CompilerConfigDictValue", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "numpy.__config__._CompilerConfigDictValue", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "numpy.__config__", "mro": ["numpy.__config__._CompilerConfigDictValue", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["name", "builtins.str"], ["linker", "builtins.str"], ["version", "builtins.str"], ["commands", "builtins.str"], ["args", "builtins.str"], ["linker args", "builtins.str"]], "readonly_keys": [], "required_keys": ["args", "commands", "linker", "linker args", "name", "version"]}}}, "_ConfigDict": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy.__config__._ConfigDict", "name": "_ConfigDict", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "numpy.__config__._ConfigDict", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "numpy.__config__", "mro": ["numpy.__config__._ConfigDict", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["Compilers", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.__config__._CompilerConfigDict"}], ["Machine Information", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.__config__._MachineInformationDict"}], ["Build Dependencies", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.__config__._BuildDependenciesDict"}], ["Python Information", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.__config__._PythonInformationDict"}], ["SIMD Extensions", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.__config__._SIMDExtensionsDict"}]], "readonly_keys": [], "required_keys": ["Build Dependencies", "Compilers", "Machine Information", "Python Information", "SIMD Extensions"]}}}, "_MachineInformationDict": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy.__config__._MachineInformationDict", "name": "_MachineInformationDict", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "numpy.__config__._MachineInformationDict", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "numpy.__config__", "mro": ["numpy.__config__._MachineInformationDict", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["host", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.__config__._MachineInformationDictValue"}], ["build", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.__config__._MachineInformationDictValue"}], ["cross-compiled", {".class": "LiteralType", "fallback": "builtins.bool", "value": true}]], "readonly_keys": [], "required_keys": ["build", "host"]}}}, "_MachineInformationDictValue": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy.__config__._MachineInformationDictValue", "name": "_MachineInformationDictValue", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "numpy.__config__._MachineInformationDictValue", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "numpy.__config__", "mro": ["numpy.__config__._MachineInformationDictValue", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["cpu", "builtins.str"], ["family", "builtins.str"], ["endian", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "little"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "big"}], "uses_pep604_syntax": false}], ["system", "builtins.str"]], "readonly_keys": [], "required_keys": ["cpu", "endian", "family", "system"]}}}, "_PythonInformationDict": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy.__config__._PythonInformationDict", "name": "_PythonInformationDict", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "numpy.__config__._PythonInformationDict", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "numpy.__config__", "mro": ["numpy.__config__._PythonInformationDict", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["path", "builtins.str"], ["version", "builtins.str"]], "readonly_keys": [], "required_keys": ["path", "version"]}}}, "_SIMDExtensionsDict": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy.__config__._SIMDExtensionsDict", "name": "_SIMDExtensionsDict", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "numpy.__config__._SIMDExtensionsDict", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "numpy.__config__", "mro": ["numpy.__config__._SIMDExtensionsDict", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["baseline", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], ["found", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], ["not found", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}]], "readonly_keys": [], "required_keys": ["baseline", "found", "not found"]}}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "numpy.__config__.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.__config__.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.__config__.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.__config__.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.__config__.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.__config__.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.__config__.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_check_pyyaml": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.__config__._check_pyyaml", "name": "_check_pyyaml", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_check_pyyaml", "ret_type": "types.ModuleType", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "overload": {".class": "SymbolTableNode", "cross_ref": "typing.overload", "kind": "Gdef", "module_hidden": true, "module_public": false}, "show": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "numpy.__config__.show", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1], "arg_names": ["mode"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.__config__.show", "name": "show", "type": {".class": "CallableType", "arg_kinds": [1], "arg_names": ["mode"], "arg_types": [{".class": "LiteralType", "fallback": "builtins.str", "value": "stdout"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "show", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.__config__.show", "name": "show", "type": {".class": "CallableType", "arg_kinds": [1], "arg_names": ["mode"], "arg_types": [{".class": "LiteralType", "fallback": "builtins.str", "value": "stdout"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "show", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["mode"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.__config__.show", "name": "show", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["mode"], "arg_types": [{".class": "LiteralType", "fallback": "builtins.str", "value": "dicts"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "show", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy.__config__._ConfigDict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.__config__.show", "name": "show", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["mode"], "arg_types": [{".class": "LiteralType", "fallback": "builtins.str", "value": "dicts"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "show", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy.__config__._ConfigDict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [1], "arg_names": ["mode"], "arg_types": [{".class": "LiteralType", "fallback": "builtins.str", "value": "stdout"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "show", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0], "arg_names": ["mode"], "arg_types": [{".class": "LiteralType", "fallback": "builtins.str", "value": "dicts"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "show", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy.__config__._ConfigDict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "show_config": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "numpy.__config__.show_config", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1], "arg_names": ["mode"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.__config__.show_config", "name": "show_config", "type": {".class": "CallableType", "arg_kinds": [1], "arg_names": ["mode"], "arg_types": [{".class": "LiteralType", "fallback": "builtins.str", "value": "stdout"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "show_config", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.__config__.show_config", "name": "show_config", "type": {".class": "CallableType", "arg_kinds": [1], "arg_names": ["mode"], "arg_types": [{".class": "LiteralType", "fallback": "builtins.str", "value": "stdout"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "show_config", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["mode"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.__config__.show_config", "name": "show_config", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["mode"], "arg_types": [{".class": "LiteralType", "fallback": "builtins.str", "value": "dicts"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "show_config", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy.__config__._ConfigDict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.__config__.show_config", "name": "show_config", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["mode"], "arg_types": [{".class": "LiteralType", "fallback": "builtins.str", "value": "dicts"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "show_config", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy.__config__._ConfigDict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [1], "arg_names": ["mode"], "arg_types": [{".class": "LiteralType", "fallback": "builtins.str", "value": "stdout"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "show_config", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0], "arg_names": ["mode"], "arg_types": [{".class": "LiteralType", "fallback": "builtins.str", "value": "dicts"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "show_config", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy.__config__._ConfigDict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "type_check_only": {".class": "SymbolTableNode", "cross_ref": "typing.type_check_only", "kind": "Gdef", "module_hidden": true, "module_public": false}}, "path": "c:\\Python313\\Lib\\site-packages\\numpy\\__config__.pyi"}