{".class": "MypyFile", "_fullname": "numpy.ctypeslib", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ClassVar": {".class": "SymbolTableNode", "cross_ref": "typing.ClassVar", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DTypeLike": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._dtype_like.DTypeLike", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Generic": {".class": "SymbolTableNode", "cross_ref": "typing.Generic", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef", "module_hidden": true, "module_public": false}, "L": {".class": "SymbolTableNode", "cross_ref": "typing.Literal", "kind": "Gdef", "module_hidden": true, "module_public": false}, "NDArray": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._array_like.NDArray", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef", "module_hidden": true, "module_public": false}, "StrOrBytesPath": {".class": "SymbolTableNode", "cross_ref": "_typeshed.StrOrBytesPath", "kind": "Gdef", "module_hidden": true, "module_public": false}, "TypeAlias": {".class": "SymbolTableNode", "cross_ref": "typing.TypeAlias", "kind": "Gdef", "module_hidden": true, "module_public": false}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_ArrayLike": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._array_like._ArrayLike", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_BoolCodes": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._char_codes._BoolCodes", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_ByteCodes": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._char_codes._ByteCodes", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_DType": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.ctypeslib._DType", "name": "_DType", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.dtype"}, "values": [], "variance": 0}}, "_DTypeLike": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._dtype_like._DTypeLike", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_DTypeOptional": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.ctypeslib._DTypeOptional", "name": "_DTypeOptional", "upper_bound": {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.dtype"}], "uses_pep604_syntax": true}, "values": [], "variance": 0}}, "_DoubleCodes": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._char_codes._DoubleCodes", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_FlagsKind": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "numpy.ctypeslib._FlagsKind", "line": 76, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "C_CONTIGUOUS"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "CONTIGUOUS"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "C"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "F_CONTIGUOUS"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "FORTRAN"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "F"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "ALIGNED"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "A"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "WRITEABLE"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "W"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "OWNDATA"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "O"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "WRITEBACKIFCOPY"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "X"}], "uses_pep604_syntax": false}}}, "_IntCCodes": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._char_codes._IntCCodes", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_LongCodes": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._char_codes._LongCodes", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_LongDoubleCodes": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._char_codes._LongDoubleCodes", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_LongLongCodes": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._char_codes._LongLongCodes", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_SCT": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.ctypeslib._SCT", "name": "_SCT", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}}, "_Shape": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._shape._Shape", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_ShapeLike": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._shape._ShapeLike", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_ShortCodes": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._char_codes._ShortCodes", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_SingleCodes": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._char_codes._SingleCodes", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_UByteCodes": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._char_codes._UByteCodes", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_UIntCCodes": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._char_codes._UIntCCodes", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_ULongCodes": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._char_codes._ULongCodes", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_ULongLongCodes": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._char_codes._ULongLongCodes", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_UShortCodes": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._char_codes._UShortCodes", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_VoidDTypeLike": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._dtype_like._VoidDTypeLike", "kind": "Gdef", "module_hidden": true, "module_public": false}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "numpy.ctypeslib.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.ctypeslib.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.ctypeslib.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.ctypeslib.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.ctypeslib.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.ctypeslib.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.ctypeslib.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_c_intp": {".class": "SymbolTableNode", "cross_ref": "ctypes.c_int64", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_concrete_ndptr": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.ctypeslib._DType", "id": 1, "name": "_DType", "namespace": "numpy.ctypeslib._concrete_ndptr", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.dtype"}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy.ctypeslib._ndptr"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy.ctypeslib._concrete_ndptr", "name": "_concrete_ndptr", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.ctypeslib._DType", "id": 1, "name": "_DType", "namespace": "numpy.ctypeslib._concrete_ndptr", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.dtype"}, "values": [], "variance": 0}]}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "numpy.ctypeslib._concrete_ndptr", "has_param_spec_type": false, "metaclass_type": "_ctypes._PyCSimpleType", "metadata": {}, "module_name": "numpy.ctypeslib", "mro": ["numpy.ctypeslib._concrete_ndptr", "numpy.ctypeslib._ndptr", "ctypes.c_void_p", "_ctypes._PointerLike", "_ctypes._CanCastTo", "_ctypes._SimpleCData", "_ctypes._CData", "builtins.object"], "names": {".class": "SymbolTable", "_dtype_": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready"], "fullname": "numpy.ctypeslib._concrete_ndptr._dtype_", "name": "_dtype_", "type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.ctypeslib._DType", "id": 1, "name": "_DType", "namespace": "numpy.ctypeslib._concrete_ndptr", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.dtype"}, "values": [], "variance": 0}}}, "_shape_": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready"], "fullname": "numpy.ctypeslib._concrete_ndptr._shape_", "name": "_shape_", "type": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "contents": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "numpy.ctypeslib._concrete_ndptr.contents", "name": "contents", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.ctypeslib._DType", "id": 1, "name": "_DType", "namespace": "numpy.ctypeslib._concrete_ndptr", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.dtype"}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy.ctypeslib._concrete_ndptr"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "contents of _concrete_ndptr", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._shape._Shape"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.ctypeslib._DType", "id": 1, "name": "_DType", "namespace": "numpy.ctypeslib._concrete_ndptr", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.dtype"}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "numpy.ctypeslib._concrete_ndptr.contents", "name": "contents", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.ctypeslib._DType", "id": 1, "name": "_DType", "namespace": "numpy.ctypeslib._concrete_ndptr", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.dtype"}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy.ctypeslib._concrete_ndptr"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "contents of _concrete_ndptr", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._shape._Shape"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.ctypeslib._DType", "id": 1, "name": "_DType", "namespace": "numpy.ctypeslib._concrete_ndptr", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.dtype"}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.ctypeslib._concrete_ndptr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.ctypeslib._DType", "id": 1, "name": "_DType", "namespace": "numpy.ctypeslib._concrete_ndptr", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.dtype"}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy.ctypeslib._concrete_ndptr"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["_DType"], "typeddict_type": null}}, "_ctypes": {".class": "SymbolTableNode", "cross_ref": "numpy._core._internal._ctypes", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_ndptr": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["ctypes.c_void_p"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy.ctypeslib._ndptr", "name": "_ndptr", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.ctypeslib._DTypeOptional", "id": 1, "name": "_DTypeOptional", "namespace": "numpy.ctypeslib._ndptr", "upper_bound": {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.dtype"}], "uses_pep604_syntax": true}, "values": [], "variance": 0}]}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "numpy.ctypeslib._ndptr", "has_param_spec_type": false, "metaclass_type": "_ctypes._PyCSimpleType", "metadata": {}, "module_name": "numpy.ctypeslib", "mro": ["numpy.ctypeslib._ndptr", "ctypes.c_void_p", "_ctypes._PointerLike", "_ctypes._CanCastTo", "_ctypes._SimpleCData", "_ctypes._CData", "builtins.object"], "names": {".class": "SymbolTable", "_dtype_": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready"], "fullname": "numpy.ctypeslib._ndptr._dtype_", "name": "_dtype_", "type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.ctypeslib._DTypeOptional", "id": 1, "name": "_DTypeOptional", "namespace": "numpy.ctypeslib._ndptr", "upper_bound": {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.dtype"}], "uses_pep604_syntax": true}, "values": [], "variance": 0}}}, "_flags_": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready"], "fullname": "numpy.ctypeslib._ndptr._flags_", "name": "_flags_", "type": {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.ctypeslib._FlagsKind"}], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": true}}}, "_ndim_": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready"], "fullname": "numpy.ctypeslib._ndptr._ndim_", "name": "_ndim_", "type": {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.int"], "uses_pep604_syntax": true}}}, "_shape_": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready"], "fullname": "numpy.ctypeslib._ndptr._shape_", "name": "_shape_", "type": {".class": "NoneType"}}}, "from_param": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": ["is_class"], "fullname": "numpy.ctypeslib._ndptr.from_param", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "obj"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_overload", "is_decorated"], "fullname": "numpy.ctypeslib._ndptr.from_param", "name": "from_param", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "obj"], "arg_types": [{".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "NoneType"}], "extra_attrs": null, "type_ref": "numpy.ctypeslib._ndptr"}}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_param of _ndptr", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy._core._internal._ctypes"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_classmethod", "is_ready", "is_inferred"], "fullname": "numpy.ctypeslib._ndptr.from_param", "name": "from_param", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "obj"], "arg_types": [{".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "NoneType"}], "extra_attrs": null, "type_ref": "numpy.ctypeslib._ndptr"}}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_param of _ndptr", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy._core._internal._ctypes"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "obj"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_overload", "is_decorated"], "fullname": "numpy.ctypeslib._ndptr.from_param", "name": "from_param", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "obj"], "arg_types": [{".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.ctypeslib._DType", "id": -1, "name": "_DType", "namespace": "numpy.ctypeslib._ndptr.from_param", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.dtype"}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy.ctypeslib._ndptr"}}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.ctypeslib._DType", "id": -1, "name": "_DType", "namespace": "numpy.ctypeslib._ndptr.from_param", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.dtype"}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_param of _ndptr", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy._core._internal._ctypes"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.ctypeslib._DType", "id": -1, "name": "_DType", "namespace": "numpy.ctypeslib._ndptr.from_param", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.dtype"}, "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_classmethod", "is_ready", "is_inferred"], "fullname": "numpy.ctypeslib._ndptr.from_param", "name": "from_param", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "obj"], "arg_types": [{".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.ctypeslib._DType", "id": -1, "name": "_DType", "namespace": "numpy.ctypeslib._ndptr.from_param", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.dtype"}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy.ctypeslib._ndptr"}}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.ctypeslib._DType", "id": -1, "name": "_DType", "namespace": "numpy.ctypeslib._ndptr.from_param", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.dtype"}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_param of _ndptr", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy._core._internal._ctypes"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.ctypeslib._DType", "id": -1, "name": "_DType", "namespace": "numpy.ctypeslib._ndptr.from_param", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.dtype"}, "values": [], "variance": 0}]}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "obj"], "arg_types": [{".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "NoneType"}], "extra_attrs": null, "type_ref": "numpy.ctypeslib._ndptr"}}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_param of _ndptr", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy._core._internal._ctypes"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "obj"], "arg_types": [{".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.ctypeslib._DType", "id": -1, "name": "_DType", "namespace": "numpy.ctypeslib._ndptr.from_param", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.dtype"}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy.ctypeslib._ndptr"}}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.ctypeslib._DType", "id": -1, "name": "_DType", "namespace": "numpy.ctypeslib._ndptr.from_param", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.dtype"}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_param of _ndptr", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy._core._internal._ctypes"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.ctypeslib._DType", "id": -1, "name": "_DType", "namespace": "numpy.ctypeslib._ndptr.from_param", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.dtype"}, "values": [], "variance": 0}]}]}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.ctypeslib._ndptr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.ctypeslib._DTypeOptional", "id": 1, "name": "_DTypeOptional", "namespace": "numpy.ctypeslib._ndptr", "upper_bound": {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.dtype"}], "uses_pep604_syntax": true}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy.ctypeslib._ndptr"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["_DTypeOptional"], "typeddict_type": null}}, "as_array": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "numpy.ctypeslib.as_array", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["obj", "shape"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.ctypeslib.as_array", "name": "as_array", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["obj", "shape"], "arg_types": ["_ctypes._PointerLike", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_array", "ret_type": {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.ctypeslib.as_array", "name": "as_array", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["obj", "shape"], "arg_types": ["_ctypes._PointerLike", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_array", "ret_type": {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["obj", "shape"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.ctypeslib.as_array", "name": "as_array", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["obj", "shape"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.ctypeslib._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.ctypeslib.as_array#1", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like._ArrayLike"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._shape._ShapeLike"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_array", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.ctypeslib._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.ctypeslib.as_array#1", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.ctypeslib._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.ctypeslib.as_array#1", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.ctypeslib.as_array", "name": "as_array", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["obj", "shape"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.ctypeslib._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.ctypeslib.as_array#1", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like._ArrayLike"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._shape._ShapeLike"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_array", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.ctypeslib._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.ctypeslib.as_array#1", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.ctypeslib._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.ctypeslib.as_array#1", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["obj", "shape"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.ctypeslib.as_array", "name": "as_array", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["obj", "shape"], "arg_types": ["builtins.object", {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._shape._ShapeLike"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_array", "ret_type": {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.ctypeslib.as_array", "name": "as_array", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["obj", "shape"], "arg_types": ["builtins.object", {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._shape._ShapeLike"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_array", "ret_type": {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["obj", "shape"], "arg_types": ["_ctypes._PointerLike", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_array", "ret_type": {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["obj", "shape"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.ctypeslib._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.ctypeslib.as_array#1", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like._ArrayLike"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._shape._ShapeLike"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_array", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.ctypeslib._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.ctypeslib.as_array#1", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.ctypeslib._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.ctypeslib.as_array#1", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["obj", "shape"], "arg_types": ["builtins.object", {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._shape._ShapeLike"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_array", "ret_type": {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "as_ctypes": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "numpy.ctypeslib.as_ctypes", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["obj"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.ctypeslib.as_ctypes", "name": "as_ctypes", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["obj"], "arg_types": [{".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "numpy.bool"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_ctypes", "ret_type": "ctypes.c_bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.ctypeslib.as_ctypes", "name": "as_ctypes", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["obj"], "arg_types": [{".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "numpy.bool"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_ctypes", "ret_type": "ctypes.c_bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["obj"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.ctypeslib.as_ctypes", "name": "as_ctypes", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["obj"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.byte"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_ctypes", "ret_type": "ctypes.c_byte", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.ctypeslib.as_ctypes", "name": "as_ctypes", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["obj"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.byte"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_ctypes", "ret_type": "ctypes.c_byte", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["obj"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.ctypeslib.as_ctypes", "name": "as_ctypes", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["obj"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.short"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_ctypes", "ret_type": "ctypes.c_short", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.ctypeslib.as_ctypes", "name": "as_ctypes", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["obj"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.short"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_ctypes", "ret_type": "ctypes.c_short", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["obj"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.ctypeslib.as_ctypes", "name": "as_ctypes", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["obj"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intc"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_ctypes", "ret_type": "ctypes.c_int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.ctypeslib.as_ctypes", "name": "as_ctypes", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["obj"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intc"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_ctypes", "ret_type": "ctypes.c_int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["obj"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.ctypeslib.as_ctypes", "name": "as_ctypes", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["obj"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.long"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_ctypes", "ret_type": "ctypes.c_long", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.ctypeslib.as_ctypes", "name": "as_ctypes", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["obj"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.long"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_ctypes", "ret_type": "ctypes.c_long", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["obj"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.ctypeslib.as_ctypes", "name": "as_ctypes", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["obj"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.longlong"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_ctypes", "ret_type": "ctypes.c_longlong", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.ctypeslib.as_ctypes", "name": "as_ctypes", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["obj"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.longlong"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_ctypes", "ret_type": "ctypes.c_longlong", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["obj"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.ctypeslib.as_ctypes", "name": "as_ctypes", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["obj"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.ubyte"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_ctypes", "ret_type": "ctypes.c_ubyte", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.ctypeslib.as_ctypes", "name": "as_ctypes", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["obj"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.ubyte"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_ctypes", "ret_type": "ctypes.c_ubyte", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["obj"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.ctypeslib.as_ctypes", "name": "as_ctypes", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["obj"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.ushort"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_ctypes", "ret_type": "ctypes.c_ushort", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.ctypeslib.as_ctypes", "name": "as_ctypes", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["obj"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.ushort"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_ctypes", "ret_type": "ctypes.c_ushort", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["obj"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.ctypeslib.as_ctypes", "name": "as_ctypes", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["obj"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.uintc"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_ctypes", "ret_type": "ctypes.c_uint", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.ctypeslib.as_ctypes", "name": "as_ctypes", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["obj"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.uintc"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_ctypes", "ret_type": "ctypes.c_uint", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["obj"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.ctypeslib.as_ctypes", "name": "as_ctypes", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["obj"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.ulong"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_ctypes", "ret_type": "ctypes.c_ulong", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.ctypeslib.as_ctypes", "name": "as_ctypes", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["obj"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.ulong"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_ctypes", "ret_type": "ctypes.c_ulong", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["obj"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.ctypeslib.as_ctypes", "name": "as_ctypes", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["obj"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.ul<PERSON>"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_ctypes", "ret_type": "ctypes.c_ulonglong", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.ctypeslib.as_ctypes", "name": "as_ctypes", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["obj"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.ul<PERSON>"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_ctypes", "ret_type": "ctypes.c_ulonglong", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["obj"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.ctypeslib.as_ctypes", "name": "as_ctypes", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["obj"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.single"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_ctypes", "ret_type": "ctypes.c_float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.ctypeslib.as_ctypes", "name": "as_ctypes", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["obj"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.single"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_ctypes", "ret_type": "ctypes.c_float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["obj"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.ctypeslib.as_ctypes", "name": "as_ctypes", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["obj"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.double"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_ctypes", "ret_type": "ctypes.c_double", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.ctypeslib.as_ctypes", "name": "as_ctypes", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["obj"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.double"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_ctypes", "ret_type": "ctypes.c_double", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["obj"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.ctypeslib.as_ctypes", "name": "as_ctypes", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["obj"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.longdouble"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_ctypes", "ret_type": "ctypes.c_longdouble", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.ctypeslib.as_ctypes", "name": "as_ctypes", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["obj"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.longdouble"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_ctypes", "ret_type": "ctypes.c_longdouble", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["obj"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.ctypeslib.as_ctypes", "name": "as_ctypes", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["obj"], "arg_types": ["numpy.void"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_ctypes", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.ctypeslib.as_ctypes", "name": "as_ctypes", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["obj"], "arg_types": ["numpy.void"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_ctypes", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["obj"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.ctypeslib.as_ctypes", "name": "as_ctypes", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["obj"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "numpy.bool"}], "type_ref": "numpy._typing._array_like.NDArray"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_ctypes", "ret_type": {".class": "Instance", "args": ["ctypes.c_bool"], "extra_attrs": null, "type_ref": "_ctypes.Array"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.ctypeslib.as_ctypes", "name": "as_ctypes", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["obj"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "numpy.bool"}], "type_ref": "numpy._typing._array_like.NDArray"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_ctypes", "ret_type": {".class": "Instance", "args": ["ctypes.c_bool"], "extra_attrs": null, "type_ref": "_ctypes.Array"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["obj"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.ctypeslib.as_ctypes", "name": "as_ctypes", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["obj"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.byte"}], "type_ref": "numpy._typing._array_like.NDArray"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_ctypes", "ret_type": {".class": "Instance", "args": ["ctypes.c_byte"], "extra_attrs": null, "type_ref": "_ctypes.Array"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.ctypeslib.as_ctypes", "name": "as_ctypes", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["obj"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.byte"}], "type_ref": "numpy._typing._array_like.NDArray"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_ctypes", "ret_type": {".class": "Instance", "args": ["ctypes.c_byte"], "extra_attrs": null, "type_ref": "_ctypes.Array"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["obj"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.ctypeslib.as_ctypes", "name": "as_ctypes", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["obj"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.short"}], "type_ref": "numpy._typing._array_like.NDArray"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_ctypes", "ret_type": {".class": "Instance", "args": ["ctypes.c_short"], "extra_attrs": null, "type_ref": "_ctypes.Array"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.ctypeslib.as_ctypes", "name": "as_ctypes", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["obj"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.short"}], "type_ref": "numpy._typing._array_like.NDArray"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_ctypes", "ret_type": {".class": "Instance", "args": ["ctypes.c_short"], "extra_attrs": null, "type_ref": "_ctypes.Array"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["obj"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.ctypeslib.as_ctypes", "name": "as_ctypes", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["obj"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intc"}], "type_ref": "numpy._typing._array_like.NDArray"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_ctypes", "ret_type": {".class": "Instance", "args": ["ctypes.c_int"], "extra_attrs": null, "type_ref": "_ctypes.Array"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.ctypeslib.as_ctypes", "name": "as_ctypes", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["obj"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intc"}], "type_ref": "numpy._typing._array_like.NDArray"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_ctypes", "ret_type": {".class": "Instance", "args": ["ctypes.c_int"], "extra_attrs": null, "type_ref": "_ctypes.Array"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["obj"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.ctypeslib.as_ctypes", "name": "as_ctypes", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["obj"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.long"}], "type_ref": "numpy._typing._array_like.NDArray"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_ctypes", "ret_type": {".class": "Instance", "args": ["ctypes.c_long"], "extra_attrs": null, "type_ref": "_ctypes.Array"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.ctypeslib.as_ctypes", "name": "as_ctypes", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["obj"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.long"}], "type_ref": "numpy._typing._array_like.NDArray"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_ctypes", "ret_type": {".class": "Instance", "args": ["ctypes.c_long"], "extra_attrs": null, "type_ref": "_ctypes.Array"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["obj"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.ctypeslib.as_ctypes", "name": "as_ctypes", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["obj"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.longlong"}], "type_ref": "numpy._typing._array_like.NDArray"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_ctypes", "ret_type": {".class": "Instance", "args": ["ctypes.c_longlong"], "extra_attrs": null, "type_ref": "_ctypes.Array"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.ctypeslib.as_ctypes", "name": "as_ctypes", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["obj"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.longlong"}], "type_ref": "numpy._typing._array_like.NDArray"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_ctypes", "ret_type": {".class": "Instance", "args": ["ctypes.c_longlong"], "extra_attrs": null, "type_ref": "_ctypes.Array"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["obj"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.ctypeslib.as_ctypes", "name": "as_ctypes", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["obj"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.ubyte"}], "type_ref": "numpy._typing._array_like.NDArray"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_ctypes", "ret_type": {".class": "Instance", "args": ["ctypes.c_ubyte"], "extra_attrs": null, "type_ref": "_ctypes.Array"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.ctypeslib.as_ctypes", "name": "as_ctypes", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["obj"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.ubyte"}], "type_ref": "numpy._typing._array_like.NDArray"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_ctypes", "ret_type": {".class": "Instance", "args": ["ctypes.c_ubyte"], "extra_attrs": null, "type_ref": "_ctypes.Array"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["obj"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.ctypeslib.as_ctypes", "name": "as_ctypes", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["obj"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.ushort"}], "type_ref": "numpy._typing._array_like.NDArray"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_ctypes", "ret_type": {".class": "Instance", "args": ["ctypes.c_ushort"], "extra_attrs": null, "type_ref": "_ctypes.Array"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.ctypeslib.as_ctypes", "name": "as_ctypes", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["obj"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.ushort"}], "type_ref": "numpy._typing._array_like.NDArray"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_ctypes", "ret_type": {".class": "Instance", "args": ["ctypes.c_ushort"], "extra_attrs": null, "type_ref": "_ctypes.Array"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["obj"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.ctypeslib.as_ctypes", "name": "as_ctypes", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["obj"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.uintc"}], "type_ref": "numpy._typing._array_like.NDArray"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_ctypes", "ret_type": {".class": "Instance", "args": ["ctypes.c_uint"], "extra_attrs": null, "type_ref": "_ctypes.Array"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.ctypeslib.as_ctypes", "name": "as_ctypes", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["obj"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.uintc"}], "type_ref": "numpy._typing._array_like.NDArray"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_ctypes", "ret_type": {".class": "Instance", "args": ["ctypes.c_uint"], "extra_attrs": null, "type_ref": "_ctypes.Array"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["obj"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.ctypeslib.as_ctypes", "name": "as_ctypes", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["obj"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.ulong"}], "type_ref": "numpy._typing._array_like.NDArray"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_ctypes", "ret_type": {".class": "Instance", "args": ["ctypes.c_ulong"], "extra_attrs": null, "type_ref": "_ctypes.Array"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.ctypeslib.as_ctypes", "name": "as_ctypes", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["obj"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.ulong"}], "type_ref": "numpy._typing._array_like.NDArray"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_ctypes", "ret_type": {".class": "Instance", "args": ["ctypes.c_ulong"], "extra_attrs": null, "type_ref": "_ctypes.Array"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["obj"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.ctypeslib.as_ctypes", "name": "as_ctypes", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["obj"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.ul<PERSON>"}], "type_ref": "numpy._typing._array_like.NDArray"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_ctypes", "ret_type": {".class": "Instance", "args": ["ctypes.c_ulonglong"], "extra_attrs": null, "type_ref": "_ctypes.Array"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.ctypeslib.as_ctypes", "name": "as_ctypes", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["obj"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.ul<PERSON>"}], "type_ref": "numpy._typing._array_like.NDArray"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_ctypes", "ret_type": {".class": "Instance", "args": ["ctypes.c_ulonglong"], "extra_attrs": null, "type_ref": "_ctypes.Array"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["obj"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.ctypeslib.as_ctypes", "name": "as_ctypes", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["obj"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.single"}], "type_ref": "numpy._typing._array_like.NDArray"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_ctypes", "ret_type": {".class": "Instance", "args": ["ctypes.c_float"], "extra_attrs": null, "type_ref": "_ctypes.Array"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.ctypeslib.as_ctypes", "name": "as_ctypes", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["obj"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.single"}], "type_ref": "numpy._typing._array_like.NDArray"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_ctypes", "ret_type": {".class": "Instance", "args": ["ctypes.c_float"], "extra_attrs": null, "type_ref": "_ctypes.Array"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["obj"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.ctypeslib.as_ctypes", "name": "as_ctypes", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["obj"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.double"}], "type_ref": "numpy._typing._array_like.NDArray"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_ctypes", "ret_type": {".class": "Instance", "args": ["ctypes.c_double"], "extra_attrs": null, "type_ref": "_ctypes.Array"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.ctypeslib.as_ctypes", "name": "as_ctypes", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["obj"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.double"}], "type_ref": "numpy._typing._array_like.NDArray"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_ctypes", "ret_type": {".class": "Instance", "args": ["ctypes.c_double"], "extra_attrs": null, "type_ref": "_ctypes.Array"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["obj"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.ctypeslib.as_ctypes", "name": "as_ctypes", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["obj"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.longdouble"}], "type_ref": "numpy._typing._array_like.NDArray"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_ctypes", "ret_type": {".class": "Instance", "args": ["ctypes.c_longdouble"], "extra_attrs": null, "type_ref": "_ctypes.Array"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.ctypeslib.as_ctypes", "name": "as_ctypes", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["obj"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.longdouble"}], "type_ref": "numpy._typing._array_like.NDArray"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_ctypes", "ret_type": {".class": "Instance", "args": ["ctypes.c_longdouble"], "extra_attrs": null, "type_ref": "_ctypes.Array"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["obj"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.ctypeslib.as_ctypes", "name": "as_ctypes", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["obj"], "arg_types": [{".class": "TypeAliasType", "args": ["numpy.void"], "type_ref": "numpy._typing._array_like.NDArray"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_ctypes", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "_ctypes.Array"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.ctypeslib.as_ctypes", "name": "as_ctypes", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["obj"], "arg_types": [{".class": "TypeAliasType", "args": ["numpy.void"], "type_ref": "numpy._typing._array_like.NDArray"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_ctypes", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "_ctypes.Array"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": ["obj"], "arg_types": [{".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "numpy.bool"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_ctypes", "ret_type": "ctypes.c_bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0], "arg_names": ["obj"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.byte"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_ctypes", "ret_type": "ctypes.c_byte", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0], "arg_names": ["obj"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.short"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_ctypes", "ret_type": "ctypes.c_short", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0], "arg_names": ["obj"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intc"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_ctypes", "ret_type": "ctypes.c_int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0], "arg_names": ["obj"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.long"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_ctypes", "ret_type": "ctypes.c_long", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0], "arg_names": ["obj"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.longlong"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_ctypes", "ret_type": "ctypes.c_longlong", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0], "arg_names": ["obj"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.ubyte"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_ctypes", "ret_type": "ctypes.c_ubyte", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0], "arg_names": ["obj"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.ushort"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_ctypes", "ret_type": "ctypes.c_ushort", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0], "arg_names": ["obj"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.uintc"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_ctypes", "ret_type": "ctypes.c_uint", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0], "arg_names": ["obj"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.ulong"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_ctypes", "ret_type": "ctypes.c_ulong", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0], "arg_names": ["obj"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.ul<PERSON>"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_ctypes", "ret_type": "ctypes.c_ulonglong", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0], "arg_names": ["obj"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.single"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_ctypes", "ret_type": "ctypes.c_float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0], "arg_names": ["obj"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.double"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_ctypes", "ret_type": "ctypes.c_double", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0], "arg_names": ["obj"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.longdouble"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_ctypes", "ret_type": "ctypes.c_longdouble", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0], "arg_names": ["obj"], "arg_types": ["numpy.void"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_ctypes", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0], "arg_names": ["obj"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "numpy.bool"}], "type_ref": "numpy._typing._array_like.NDArray"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_ctypes", "ret_type": {".class": "Instance", "args": ["ctypes.c_bool"], "extra_attrs": null, "type_ref": "_ctypes.Array"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0], "arg_names": ["obj"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.byte"}], "type_ref": "numpy._typing._array_like.NDArray"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_ctypes", "ret_type": {".class": "Instance", "args": ["ctypes.c_byte"], "extra_attrs": null, "type_ref": "_ctypes.Array"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0], "arg_names": ["obj"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.short"}], "type_ref": "numpy._typing._array_like.NDArray"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_ctypes", "ret_type": {".class": "Instance", "args": ["ctypes.c_short"], "extra_attrs": null, "type_ref": "_ctypes.Array"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0], "arg_names": ["obj"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intc"}], "type_ref": "numpy._typing._array_like.NDArray"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_ctypes", "ret_type": {".class": "Instance", "args": ["ctypes.c_int"], "extra_attrs": null, "type_ref": "_ctypes.Array"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0], "arg_names": ["obj"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.long"}], "type_ref": "numpy._typing._array_like.NDArray"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_ctypes", "ret_type": {".class": "Instance", "args": ["ctypes.c_long"], "extra_attrs": null, "type_ref": "_ctypes.Array"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0], "arg_names": ["obj"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.longlong"}], "type_ref": "numpy._typing._array_like.NDArray"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_ctypes", "ret_type": {".class": "Instance", "args": ["ctypes.c_longlong"], "extra_attrs": null, "type_ref": "_ctypes.Array"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0], "arg_names": ["obj"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.ubyte"}], "type_ref": "numpy._typing._array_like.NDArray"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_ctypes", "ret_type": {".class": "Instance", "args": ["ctypes.c_ubyte"], "extra_attrs": null, "type_ref": "_ctypes.Array"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0], "arg_names": ["obj"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.ushort"}], "type_ref": "numpy._typing._array_like.NDArray"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_ctypes", "ret_type": {".class": "Instance", "args": ["ctypes.c_ushort"], "extra_attrs": null, "type_ref": "_ctypes.Array"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0], "arg_names": ["obj"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.uintc"}], "type_ref": "numpy._typing._array_like.NDArray"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_ctypes", "ret_type": {".class": "Instance", "args": ["ctypes.c_uint"], "extra_attrs": null, "type_ref": "_ctypes.Array"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0], "arg_names": ["obj"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.ulong"}], "type_ref": "numpy._typing._array_like.NDArray"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_ctypes", "ret_type": {".class": "Instance", "args": ["ctypes.c_ulong"], "extra_attrs": null, "type_ref": "_ctypes.Array"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0], "arg_names": ["obj"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.ul<PERSON>"}], "type_ref": "numpy._typing._array_like.NDArray"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_ctypes", "ret_type": {".class": "Instance", "args": ["ctypes.c_ulonglong"], "extra_attrs": null, "type_ref": "_ctypes.Array"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0], "arg_names": ["obj"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.single"}], "type_ref": "numpy._typing._array_like.NDArray"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_ctypes", "ret_type": {".class": "Instance", "args": ["ctypes.c_float"], "extra_attrs": null, "type_ref": "_ctypes.Array"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0], "arg_names": ["obj"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.double"}], "type_ref": "numpy._typing._array_like.NDArray"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_ctypes", "ret_type": {".class": "Instance", "args": ["ctypes.c_double"], "extra_attrs": null, "type_ref": "_ctypes.Array"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0], "arg_names": ["obj"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.longdouble"}], "type_ref": "numpy._typing._array_like.NDArray"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_ctypes", "ret_type": {".class": "Instance", "args": ["ctypes.c_longdouble"], "extra_attrs": null, "type_ref": "_ctypes.Array"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0], "arg_names": ["obj"], "arg_types": [{".class": "TypeAliasType", "args": ["numpy.void"], "type_ref": "numpy._typing._array_like.NDArray"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_ctypes", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "_ctypes.Array"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "as_ctypes_type": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "numpy.ctypeslib.as_ctypes_type", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["dtype"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.ctypeslib.as_ctypes_type", "name": "as_ctypes_type", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["dtype"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._char_codes._BoolCodes"}, {".class": "TypeAliasType", "args": [{".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "numpy.bool"}], "type_ref": "numpy._typing._dtype_like._DTypeLike"}, {".class": "TypeType", "item": "ctypes.c_bool"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_ctypes_type", "ret_type": {".class": "TypeType", "item": "ctypes.c_bool"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.ctypeslib.as_ctypes_type", "name": "as_ctypes_type", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["dtype"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._char_codes._BoolCodes"}, {".class": "TypeAliasType", "args": [{".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "numpy.bool"}], "type_ref": "numpy._typing._dtype_like._DTypeLike"}, {".class": "TypeType", "item": "ctypes.c_bool"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_ctypes_type", "ret_type": {".class": "TypeType", "item": "ctypes.c_bool"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["dtype"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.ctypeslib.as_ctypes_type", "name": "as_ctypes_type", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["dtype"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._char_codes._ByteCodes"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.byte"}], "type_ref": "numpy._typing._dtype_like._DTypeLike"}, {".class": "TypeType", "item": "ctypes.c_byte"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_ctypes_type", "ret_type": {".class": "TypeType", "item": "ctypes.c_byte"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.ctypeslib.as_ctypes_type", "name": "as_ctypes_type", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["dtype"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._char_codes._ByteCodes"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.byte"}], "type_ref": "numpy._typing._dtype_like._DTypeLike"}, {".class": "TypeType", "item": "ctypes.c_byte"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_ctypes_type", "ret_type": {".class": "TypeType", "item": "ctypes.c_byte"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["dtype"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.ctypeslib.as_ctypes_type", "name": "as_ctypes_type", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["dtype"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._char_codes._ShortCodes"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.short"}], "type_ref": "numpy._typing._dtype_like._DTypeLike"}, {".class": "TypeType", "item": "ctypes.c_short"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_ctypes_type", "ret_type": {".class": "TypeType", "item": "ctypes.c_short"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.ctypeslib.as_ctypes_type", "name": "as_ctypes_type", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["dtype"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._char_codes._ShortCodes"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.short"}], "type_ref": "numpy._typing._dtype_like._DTypeLike"}, {".class": "TypeType", "item": "ctypes.c_short"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_ctypes_type", "ret_type": {".class": "TypeType", "item": "ctypes.c_short"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["dtype"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.ctypeslib.as_ctypes_type", "name": "as_ctypes_type", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["dtype"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._char_codes._IntCCodes"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intc"}], "type_ref": "numpy._typing._dtype_like._DTypeLike"}, {".class": "TypeType", "item": "ctypes.c_int"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_ctypes_type", "ret_type": {".class": "TypeType", "item": "ctypes.c_int"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.ctypeslib.as_ctypes_type", "name": "as_ctypes_type", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["dtype"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._char_codes._IntCCodes"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intc"}], "type_ref": "numpy._typing._dtype_like._DTypeLike"}, {".class": "TypeType", "item": "ctypes.c_int"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_ctypes_type", "ret_type": {".class": "TypeType", "item": "ctypes.c_int"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["dtype"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.ctypeslib.as_ctypes_type", "name": "as_ctypes_type", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["dtype"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._char_codes._LongCodes"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.long"}], "type_ref": "numpy._typing._dtype_like._DTypeLike"}, {".class": "TypeType", "item": "ctypes.c_long"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_ctypes_type", "ret_type": {".class": "TypeType", "item": "ctypes.c_long"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.ctypeslib.as_ctypes_type", "name": "as_ctypes_type", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["dtype"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._char_codes._LongCodes"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.long"}], "type_ref": "numpy._typing._dtype_like._DTypeLike"}, {".class": "TypeType", "item": "ctypes.c_long"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_ctypes_type", "ret_type": {".class": "TypeType", "item": "ctypes.c_long"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["dtype"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.ctypeslib.as_ctypes_type", "name": "as_ctypes_type", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["dtype"], "arg_types": [{".class": "TypeType", "item": "builtins.int"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_ctypes_type", "ret_type": {".class": "TypeType", "item": "ctypes.c_int64"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.ctypeslib.as_ctypes_type", "name": "as_ctypes_type", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["dtype"], "arg_types": [{".class": "TypeType", "item": "builtins.int"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_ctypes_type", "ret_type": {".class": "TypeType", "item": "ctypes.c_int64"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["dtype"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.ctypeslib.as_ctypes_type", "name": "as_ctypes_type", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["dtype"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._char_codes._LongLongCodes"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.longlong"}], "type_ref": "numpy._typing._dtype_like._DTypeLike"}, {".class": "TypeType", "item": "ctypes.c_longlong"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_ctypes_type", "ret_type": {".class": "TypeType", "item": "ctypes.c_longlong"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.ctypeslib.as_ctypes_type", "name": "as_ctypes_type", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["dtype"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._char_codes._LongLongCodes"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.longlong"}], "type_ref": "numpy._typing._dtype_like._DTypeLike"}, {".class": "TypeType", "item": "ctypes.c_longlong"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_ctypes_type", "ret_type": {".class": "TypeType", "item": "ctypes.c_longlong"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["dtype"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.ctypeslib.as_ctypes_type", "name": "as_ctypes_type", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["dtype"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._char_codes._UByteCodes"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.ubyte"}], "type_ref": "numpy._typing._dtype_like._DTypeLike"}, {".class": "TypeType", "item": "ctypes.c_ubyte"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_ctypes_type", "ret_type": {".class": "TypeType", "item": "ctypes.c_ubyte"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.ctypeslib.as_ctypes_type", "name": "as_ctypes_type", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["dtype"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._char_codes._UByteCodes"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.ubyte"}], "type_ref": "numpy._typing._dtype_like._DTypeLike"}, {".class": "TypeType", "item": "ctypes.c_ubyte"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_ctypes_type", "ret_type": {".class": "TypeType", "item": "ctypes.c_ubyte"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["dtype"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.ctypeslib.as_ctypes_type", "name": "as_ctypes_type", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["dtype"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._char_codes._UShortCodes"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.ushort"}], "type_ref": "numpy._typing._dtype_like._DTypeLike"}, {".class": "TypeType", "item": "ctypes.c_ushort"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_ctypes_type", "ret_type": {".class": "TypeType", "item": "ctypes.c_ushort"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.ctypeslib.as_ctypes_type", "name": "as_ctypes_type", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["dtype"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._char_codes._UShortCodes"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.ushort"}], "type_ref": "numpy._typing._dtype_like._DTypeLike"}, {".class": "TypeType", "item": "ctypes.c_ushort"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_ctypes_type", "ret_type": {".class": "TypeType", "item": "ctypes.c_ushort"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["dtype"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.ctypeslib.as_ctypes_type", "name": "as_ctypes_type", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["dtype"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._char_codes._UIntCCodes"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.uintc"}], "type_ref": "numpy._typing._dtype_like._DTypeLike"}, {".class": "TypeType", "item": "ctypes.c_uint"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_ctypes_type", "ret_type": {".class": "TypeType", "item": "ctypes.c_uint"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.ctypeslib.as_ctypes_type", "name": "as_ctypes_type", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["dtype"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._char_codes._UIntCCodes"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.uintc"}], "type_ref": "numpy._typing._dtype_like._DTypeLike"}, {".class": "TypeType", "item": "ctypes.c_uint"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_ctypes_type", "ret_type": {".class": "TypeType", "item": "ctypes.c_uint"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["dtype"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.ctypeslib.as_ctypes_type", "name": "as_ctypes_type", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["dtype"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._char_codes._ULongCodes"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.ulong"}], "type_ref": "numpy._typing._dtype_like._DTypeLike"}, {".class": "TypeType", "item": "ctypes.c_ulong"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_ctypes_type", "ret_type": {".class": "TypeType", "item": "ctypes.c_ulong"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.ctypeslib.as_ctypes_type", "name": "as_ctypes_type", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["dtype"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._char_codes._ULongCodes"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.ulong"}], "type_ref": "numpy._typing._dtype_like._DTypeLike"}, {".class": "TypeType", "item": "ctypes.c_ulong"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_ctypes_type", "ret_type": {".class": "TypeType", "item": "ctypes.c_ulong"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["dtype"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.ctypeslib.as_ctypes_type", "name": "as_ctypes_type", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["dtype"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._char_codes._ULongLongCodes"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.ul<PERSON>"}], "type_ref": "numpy._typing._dtype_like._DTypeLike"}, {".class": "TypeType", "item": "ctypes.c_ulonglong"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_ctypes_type", "ret_type": {".class": "TypeType", "item": "ctypes.c_ulonglong"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.ctypeslib.as_ctypes_type", "name": "as_ctypes_type", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["dtype"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._char_codes._ULongLongCodes"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.ul<PERSON>"}], "type_ref": "numpy._typing._dtype_like._DTypeLike"}, {".class": "TypeType", "item": "ctypes.c_ulonglong"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_ctypes_type", "ret_type": {".class": "TypeType", "item": "ctypes.c_ulonglong"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["dtype"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.ctypeslib.as_ctypes_type", "name": "as_ctypes_type", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["dtype"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._char_codes._SingleCodes"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.single"}], "type_ref": "numpy._typing._dtype_like._DTypeLike"}, {".class": "TypeType", "item": "ctypes.c_float"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_ctypes_type", "ret_type": {".class": "TypeType", "item": "ctypes.c_float"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.ctypeslib.as_ctypes_type", "name": "as_ctypes_type", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["dtype"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._char_codes._SingleCodes"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.single"}], "type_ref": "numpy._typing._dtype_like._DTypeLike"}, {".class": "TypeType", "item": "ctypes.c_float"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_ctypes_type", "ret_type": {".class": "TypeType", "item": "ctypes.c_float"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["dtype"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.ctypeslib.as_ctypes_type", "name": "as_ctypes_type", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["dtype"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._char_codes._DoubleCodes"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.double"}], "type_ref": "numpy._typing._dtype_like._DTypeLike"}, {".class": "TypeType", "item": "builtins.float"}, {".class": "TypeType", "item": "ctypes.c_double"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_ctypes_type", "ret_type": {".class": "TypeType", "item": "ctypes.c_double"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.ctypeslib.as_ctypes_type", "name": "as_ctypes_type", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["dtype"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._char_codes._DoubleCodes"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.double"}], "type_ref": "numpy._typing._dtype_like._DTypeLike"}, {".class": "TypeType", "item": "builtins.float"}, {".class": "TypeType", "item": "ctypes.c_double"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_ctypes_type", "ret_type": {".class": "TypeType", "item": "ctypes.c_double"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["dtype"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.ctypeslib.as_ctypes_type", "name": "as_ctypes_type", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["dtype"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._char_codes._LongDoubleCodes"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.longdouble"}], "type_ref": "numpy._typing._dtype_like._DTypeLike"}, {".class": "TypeType", "item": "ctypes.c_longdouble"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_ctypes_type", "ret_type": {".class": "TypeType", "item": "ctypes.c_longdouble"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.ctypeslib.as_ctypes_type", "name": "as_ctypes_type", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["dtype"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._char_codes._LongDoubleCodes"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.longdouble"}], "type_ref": "numpy._typing._dtype_like._DTypeLike"}, {".class": "TypeType", "item": "ctypes.c_longdouble"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_ctypes_type", "ret_type": {".class": "TypeType", "item": "ctypes.c_longdouble"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["dtype"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.ctypeslib.as_ctypes_type", "name": "as_ctypes_type", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["dtype"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._dtype_like._VoidDTypeLike"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_ctypes_type", "ret_type": {".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.ctypeslib.as_ctypes_type", "name": "as_ctypes_type", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["dtype"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._dtype_like._VoidDTypeLike"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_ctypes_type", "ret_type": {".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["dtype"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.ctypeslib.as_ctypes_type", "name": "as_ctypes_type", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["dtype"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_ctypes_type", "ret_type": {".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.ctypeslib.as_ctypes_type", "name": "as_ctypes_type", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["dtype"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_ctypes_type", "ret_type": {".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": ["dtype"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._char_codes._BoolCodes"}, {".class": "TypeAliasType", "args": [{".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "numpy.bool"}], "type_ref": "numpy._typing._dtype_like._DTypeLike"}, {".class": "TypeType", "item": "ctypes.c_bool"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_ctypes_type", "ret_type": {".class": "TypeType", "item": "ctypes.c_bool"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0], "arg_names": ["dtype"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._char_codes._ByteCodes"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.byte"}], "type_ref": "numpy._typing._dtype_like._DTypeLike"}, {".class": "TypeType", "item": "ctypes.c_byte"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_ctypes_type", "ret_type": {".class": "TypeType", "item": "ctypes.c_byte"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0], "arg_names": ["dtype"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._char_codes._ShortCodes"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.short"}], "type_ref": "numpy._typing._dtype_like._DTypeLike"}, {".class": "TypeType", "item": "ctypes.c_short"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_ctypes_type", "ret_type": {".class": "TypeType", "item": "ctypes.c_short"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0], "arg_names": ["dtype"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._char_codes._IntCCodes"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intc"}], "type_ref": "numpy._typing._dtype_like._DTypeLike"}, {".class": "TypeType", "item": "ctypes.c_int"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_ctypes_type", "ret_type": {".class": "TypeType", "item": "ctypes.c_int"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0], "arg_names": ["dtype"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._char_codes._LongCodes"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.long"}], "type_ref": "numpy._typing._dtype_like._DTypeLike"}, {".class": "TypeType", "item": "ctypes.c_long"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_ctypes_type", "ret_type": {".class": "TypeType", "item": "ctypes.c_long"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0], "arg_names": ["dtype"], "arg_types": [{".class": "TypeType", "item": "builtins.int"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_ctypes_type", "ret_type": {".class": "TypeType", "item": "ctypes.c_int64"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0], "arg_names": ["dtype"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._char_codes._LongLongCodes"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.longlong"}], "type_ref": "numpy._typing._dtype_like._DTypeLike"}, {".class": "TypeType", "item": "ctypes.c_longlong"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_ctypes_type", "ret_type": {".class": "TypeType", "item": "ctypes.c_longlong"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0], "arg_names": ["dtype"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._char_codes._UByteCodes"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.ubyte"}], "type_ref": "numpy._typing._dtype_like._DTypeLike"}, {".class": "TypeType", "item": "ctypes.c_ubyte"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_ctypes_type", "ret_type": {".class": "TypeType", "item": "ctypes.c_ubyte"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0], "arg_names": ["dtype"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._char_codes._UShortCodes"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.ushort"}], "type_ref": "numpy._typing._dtype_like._DTypeLike"}, {".class": "TypeType", "item": "ctypes.c_ushort"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_ctypes_type", "ret_type": {".class": "TypeType", "item": "ctypes.c_ushort"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0], "arg_names": ["dtype"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._char_codes._UIntCCodes"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.uintc"}], "type_ref": "numpy._typing._dtype_like._DTypeLike"}, {".class": "TypeType", "item": "ctypes.c_uint"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_ctypes_type", "ret_type": {".class": "TypeType", "item": "ctypes.c_uint"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0], "arg_names": ["dtype"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._char_codes._ULongCodes"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.ulong"}], "type_ref": "numpy._typing._dtype_like._DTypeLike"}, {".class": "TypeType", "item": "ctypes.c_ulong"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_ctypes_type", "ret_type": {".class": "TypeType", "item": "ctypes.c_ulong"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0], "arg_names": ["dtype"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._char_codes._ULongLongCodes"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.ul<PERSON>"}], "type_ref": "numpy._typing._dtype_like._DTypeLike"}, {".class": "TypeType", "item": "ctypes.c_ulonglong"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_ctypes_type", "ret_type": {".class": "TypeType", "item": "ctypes.c_ulonglong"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0], "arg_names": ["dtype"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._char_codes._SingleCodes"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.single"}], "type_ref": "numpy._typing._dtype_like._DTypeLike"}, {".class": "TypeType", "item": "ctypes.c_float"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_ctypes_type", "ret_type": {".class": "TypeType", "item": "ctypes.c_float"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0], "arg_names": ["dtype"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._char_codes._DoubleCodes"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.double"}], "type_ref": "numpy._typing._dtype_like._DTypeLike"}, {".class": "TypeType", "item": "builtins.float"}, {".class": "TypeType", "item": "ctypes.c_double"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_ctypes_type", "ret_type": {".class": "TypeType", "item": "ctypes.c_double"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0], "arg_names": ["dtype"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._char_codes._LongDoubleCodes"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.longdouble"}], "type_ref": "numpy._typing._dtype_like._DTypeLike"}, {".class": "TypeType", "item": "ctypes.c_longdouble"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_ctypes_type", "ret_type": {".class": "TypeType", "item": "ctypes.c_longdouble"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0], "arg_names": ["dtype"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._dtype_like._VoidDTypeLike"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_ctypes_type", "ret_type": {".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0], "arg_names": ["dtype"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_ctypes_type", "ret_type": {".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "byte": {".class": "SymbolTableNode", "cross_ref": "numpy.byte", "kind": "Gdef", "module_hidden": true, "module_public": false}, "c_intp": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "numpy.ctypeslib.c_intp", "line": 109, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "ctypes.c_int64"}}, "ctypes": {".class": "SymbolTableNode", "cross_ref": "ctypes", "kind": "Gdef", "module_hidden": true, "module_public": false}, "double": {".class": "SymbolTableNode", "cross_ref": "numpy.double", "kind": "Gdef", "module_hidden": true, "module_public": false}, "dtype": {".class": "SymbolTableNode", "cross_ref": "numpy.dtype", "kind": "Gdef", "module_hidden": true, "module_public": false}, "flagsobj": {".class": "SymbolTableNode", "cross_ref": "numpy._core.multiarray.flagsobj", "kind": "Gdef", "module_hidden": true, "module_public": false}, "generic": {".class": "SymbolTableNode", "cross_ref": "numpy.generic", "kind": "Gdef", "module_hidden": true, "module_public": false}, "intc": {".class": "SymbolTableNode", "cross_ref": "numpy.intc", "kind": "Gdef", "module_hidden": true, "module_public": false}, "load_library": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["libname", "loader_path"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ctypeslib.load_library", "name": "load_library", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["libname", "loader_path"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.StrOrBytesPath"}, {".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.StrOrBytesPath"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "load_library", "ret_type": "ctypes.CDLL", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "long": {".class": "SymbolTableNode", "cross_ref": "numpy.long", "kind": "Gdef", "module_hidden": true, "module_public": false}, "longdouble": {".class": "SymbolTableNode", "cross_ref": "numpy.longdouble", "kind": "Gdef", "module_hidden": true, "module_public": false}, "longlong": {".class": "SymbolTableNode", "cross_ref": "numpy.longlong", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ndarray": {".class": "SymbolTableNode", "cross_ref": "numpy.n<PERSON><PERSON>", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ndpointer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "numpy.ctypeslib.ndpointer", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1, 1, 1, 1], "arg_names": ["dtype", "ndim", "shape", "flags"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.ctypeslib.ndpointer", "name": "ndpointer", "type": {".class": "CallableType", "arg_kinds": [1, 1, 1, 1], "arg_names": ["dtype", "ndim", "shape", "flags"], "arg_types": [{".class": "NoneType"}, "builtins.int", {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._shape._ShapeLike"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ctypeslib._FlagsKind"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.ctypeslib._FlagsKind"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, "builtins.int", "numpy._core.multiarray.flagsobj"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ndpointer", "ret_type": {".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "NoneType"}], "extra_attrs": null, "type_ref": "numpy.ctypeslib._ndptr"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.ctypeslib.ndpointer", "name": "ndpointer", "type": {".class": "CallableType", "arg_kinds": [1, 1, 1, 1], "arg_names": ["dtype", "ndim", "shape", "flags"], "arg_types": [{".class": "NoneType"}, "builtins.int", {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._shape._ShapeLike"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ctypeslib._FlagsKind"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.ctypeslib._FlagsKind"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, "builtins.int", "numpy._core.multiarray.flagsobj"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ndpointer", "ret_type": {".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "NoneType"}], "extra_attrs": null, "type_ref": "numpy.ctypeslib._ndptr"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 3, 5], "arg_names": ["dtype", "ndim", "shape", "flags"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.ctypeslib.ndpointer", "name": "ndpointer", "type": {".class": "CallableType", "arg_kinds": [0, 1, 3, 5], "arg_names": ["dtype", "ndim", "shape", "flags"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.ctypeslib._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.ctypeslib.ndpointer#1", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy._typing._dtype_like._DTypeLike"}, "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._shape._ShapeLike"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ctypeslib._FlagsKind"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.ctypeslib._FlagsKind"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, "builtins.int", "numpy._core.multiarray.flagsobj"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ndpointer", "ret_type": {".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.ctypeslib._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.ctypeslib.ndpointer#1", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy.dtype"}], "extra_attrs": null, "type_ref": "numpy.ctypeslib._concrete_ndptr"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.ctypeslib._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.ctypeslib.ndpointer#1", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.ctypeslib.ndpointer", "name": "ndpointer", "type": {".class": "CallableType", "arg_kinds": [0, 1, 3, 5], "arg_names": ["dtype", "ndim", "shape", "flags"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.ctypeslib._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.ctypeslib.ndpointer#1", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy._typing._dtype_like._DTypeLike"}, "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._shape._ShapeLike"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ctypeslib._FlagsKind"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.ctypeslib._FlagsKind"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, "builtins.int", "numpy._core.multiarray.flagsobj"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ndpointer", "ret_type": {".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.ctypeslib._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.ctypeslib.ndpointer#1", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy.dtype"}], "extra_attrs": null, "type_ref": "numpy.ctypeslib._concrete_ndptr"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.ctypeslib._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.ctypeslib.ndpointer#1", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 3, 5], "arg_names": ["dtype", "ndim", "shape", "flags"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.ctypeslib.ndpointer", "name": "ndpointer", "type": {".class": "CallableType", "arg_kinds": [0, 1, 3, 5], "arg_names": ["dtype", "ndim", "shape", "flags"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._dtype_like.DTypeLike"}, "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._shape._ShapeLike"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ctypeslib._FlagsKind"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.ctypeslib._FlagsKind"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, "builtins.int", "numpy._core.multiarray.flagsobj"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ndpointer", "ret_type": {".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.dtype"}], "extra_attrs": null, "type_ref": "numpy.ctypeslib._concrete_ndptr"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.ctypeslib.ndpointer", "name": "ndpointer", "type": {".class": "CallableType", "arg_kinds": [0, 1, 3, 5], "arg_names": ["dtype", "ndim", "shape", "flags"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._dtype_like.DTypeLike"}, "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._shape._ShapeLike"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ctypeslib._FlagsKind"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.ctypeslib._FlagsKind"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, "builtins.int", "numpy._core.multiarray.flagsobj"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ndpointer", "ret_type": {".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.dtype"}], "extra_attrs": null, "type_ref": "numpy.ctypeslib._concrete_ndptr"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["dtype", "ndim", "shape", "flags"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.ctypeslib.ndpointer", "name": "ndpointer", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["dtype", "ndim", "shape", "flags"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.ctypeslib._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.ctypeslib.ndpointer#3", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy._typing._dtype_like._DTypeLike"}, "builtins.int", {".class": "NoneType"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ctypeslib._FlagsKind"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.ctypeslib._FlagsKind"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, "builtins.int", "numpy._core.multiarray.flagsobj"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ndpointer", "ret_type": {".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.ctypeslib._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.ctypeslib.ndpointer#3", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy.dtype"}], "extra_attrs": null, "type_ref": "numpy.ctypeslib._ndptr"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.ctypeslib._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.ctypeslib.ndpointer#3", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.ctypeslib.ndpointer", "name": "ndpointer", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["dtype", "ndim", "shape", "flags"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.ctypeslib._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.ctypeslib.ndpointer#3", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy._typing._dtype_like._DTypeLike"}, "builtins.int", {".class": "NoneType"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ctypeslib._FlagsKind"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.ctypeslib._FlagsKind"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, "builtins.int", "numpy._core.multiarray.flagsobj"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ndpointer", "ret_type": {".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.ctypeslib._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.ctypeslib.ndpointer#3", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy.dtype"}], "extra_attrs": null, "type_ref": "numpy.ctypeslib._ndptr"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.ctypeslib._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.ctypeslib.ndpointer#3", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["dtype", "ndim", "shape", "flags"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.ctypeslib.ndpointer", "name": "ndpointer", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["dtype", "ndim", "shape", "flags"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._dtype_like.DTypeLike"}, "builtins.int", {".class": "NoneType"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ctypeslib._FlagsKind"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.ctypeslib._FlagsKind"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, "builtins.int", "numpy._core.multiarray.flagsobj"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ndpointer", "ret_type": {".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.dtype"}], "extra_attrs": null, "type_ref": "numpy.ctypeslib._ndptr"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.ctypeslib.ndpointer", "name": "ndpointer", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["dtype", "ndim", "shape", "flags"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._dtype_like.DTypeLike"}, "builtins.int", {".class": "NoneType"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ctypeslib._FlagsKind"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.ctypeslib._FlagsKind"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, "builtins.int", "numpy._core.multiarray.flagsobj"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ndpointer", "ret_type": {".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.dtype"}], "extra_attrs": null, "type_ref": "numpy.ctypeslib._ndptr"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [1, 1, 1, 1], "arg_names": ["dtype", "ndim", "shape", "flags"], "arg_types": [{".class": "NoneType"}, "builtins.int", {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._shape._ShapeLike"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ctypeslib._FlagsKind"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.ctypeslib._FlagsKind"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, "builtins.int", "numpy._core.multiarray.flagsobj"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ndpointer", "ret_type": {".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "NoneType"}], "extra_attrs": null, "type_ref": "numpy.ctypeslib._ndptr"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 1, 3, 5], "arg_names": ["dtype", "ndim", "shape", "flags"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.ctypeslib._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.ctypeslib.ndpointer#1", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy._typing._dtype_like._DTypeLike"}, "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._shape._ShapeLike"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ctypeslib._FlagsKind"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.ctypeslib._FlagsKind"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, "builtins.int", "numpy._core.multiarray.flagsobj"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ndpointer", "ret_type": {".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.ctypeslib._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.ctypeslib.ndpointer#1", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy.dtype"}], "extra_attrs": null, "type_ref": "numpy.ctypeslib._concrete_ndptr"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.ctypeslib._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.ctypeslib.ndpointer#1", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0, 1, 3, 5], "arg_names": ["dtype", "ndim", "shape", "flags"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._dtype_like.DTypeLike"}, "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._shape._ShapeLike"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ctypeslib._FlagsKind"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.ctypeslib._FlagsKind"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, "builtins.int", "numpy._core.multiarray.flagsobj"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ndpointer", "ret_type": {".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.dtype"}], "extra_attrs": null, "type_ref": "numpy.ctypeslib._concrete_ndptr"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["dtype", "ndim", "shape", "flags"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.ctypeslib._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.ctypeslib.ndpointer#3", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy._typing._dtype_like._DTypeLike"}, "builtins.int", {".class": "NoneType"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ctypeslib._FlagsKind"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.ctypeslib._FlagsKind"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, "builtins.int", "numpy._core.multiarray.flagsobj"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ndpointer", "ret_type": {".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.ctypeslib._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.ctypeslib.ndpointer#3", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy.dtype"}], "extra_attrs": null, "type_ref": "numpy.ctypeslib._ndptr"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.ctypeslib._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.ctypeslib.ndpointer#3", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["dtype", "ndim", "shape", "flags"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._dtype_like.DTypeLike"}, "builtins.int", {".class": "NoneType"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ctypeslib._FlagsKind"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.ctypeslib._FlagsKind"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, "builtins.int", "numpy._core.multiarray.flagsobj"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ndpointer", "ret_type": {".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.dtype"}], "extra_attrs": null, "type_ref": "numpy.ctypeslib._ndptr"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "np": {".class": "SymbolTableNode", "cross_ref": "numpy", "kind": "Gdef", "module_hidden": true, "module_public": false}, "overload": {".class": "SymbolTableNode", "cross_ref": "typing.overload", "kind": "Gdef", "module_hidden": true, "module_public": false}, "short": {".class": "SymbolTableNode", "cross_ref": "numpy.short", "kind": "Gdef", "module_hidden": true, "module_public": false}, "single": {".class": "SymbolTableNode", "cross_ref": "numpy.single", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ubyte": {".class": "SymbolTableNode", "cross_ref": "numpy.ubyte", "kind": "Gdef", "module_hidden": true, "module_public": false}, "uintc": {".class": "SymbolTableNode", "cross_ref": "numpy.uintc", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ulong": {".class": "SymbolTableNode", "cross_ref": "numpy.ulong", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ulonglong": {".class": "SymbolTableNode", "cross_ref": "numpy.ul<PERSON>", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ushort": {".class": "SymbolTableNode", "cross_ref": "numpy.ushort", "kind": "Gdef", "module_hidden": true, "module_public": false}, "void": {".class": "SymbolTableNode", "cross_ref": "numpy.void", "kind": "Gdef", "module_hidden": true, "module_public": false}}, "path": "c:\\Python313\\Lib\\site-packages\\numpy\\ctypeslib.pyi"}