{".class": "MypyFile", "_fullname": "numpy._typing._nbit_base", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "NBitBase": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy._typing._nbit_base.NBitBase", "name": "NBitBase", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_final"], "fullname": "numpy._typing._nbit_base.NBitBase", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "numpy._typing._nbit_base", "mro": ["numpy._typing._nbit_base.NBitBase", "builtins.object"], "names": {".class": "SymbolTable", "__init_subclass__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["cls"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class"], "fullname": "numpy._typing._nbit_base.NBitBase.__init_subclass__", "name": "__init_subclass__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "numpy._typing._nbit_base.NBitBase"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init_subclass__ of NBitBase", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._nbit_base.NBitBase.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "numpy._typing._nbit_base.NBitBase", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_128Bit": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["numpy._typing._nbit_base._256Bit"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy._typing._nbit_base._128Bit", "name": "_128Bit", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_final"], "fullname": "numpy._typing._nbit_base._128Bit", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "numpy._typing._nbit_base", "mro": ["numpy._typing._nbit_base._128Bit", "numpy._typing._nbit_base._256Bit", "numpy._typing._nbit_base.NBitBase", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._nbit_base._128Bit.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "numpy._typing._nbit_base._128Bit", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_16Bit": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["numpy._typing._nbit_base._32Bit"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy._typing._nbit_base._16Bit", "name": "_16Bit", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_final"], "fullname": "numpy._typing._nbit_base._16Bit", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "numpy._typing._nbit_base", "mro": ["numpy._typing._nbit_base._16Bit", "numpy._typing._nbit_base._32Bit", "numpy._typing._nbit_base._64Bit", "numpy._typing._nbit_base._80Bit", "numpy._typing._nbit_base._96Bit", "numpy._typing._nbit_base._128Bit", "numpy._typing._nbit_base._256Bit", "numpy._typing._nbit_base.NBitBase", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._nbit_base._16Bit.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "numpy._typing._nbit_base._16Bit", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_256Bit": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["numpy._typing._nbit_base.NBitBase"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy._typing._nbit_base._256Bit", "name": "_256Bit", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_final"], "fullname": "numpy._typing._nbit_base._256Bit", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "numpy._typing._nbit_base", "mro": ["numpy._typing._nbit_base._256Bit", "numpy._typing._nbit_base.NBitBase", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._nbit_base._256Bit.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "numpy._typing._nbit_base._256Bit", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_32Bit": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["numpy._typing._nbit_base._64Bit"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy._typing._nbit_base._32Bit", "name": "_32Bit", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_final"], "fullname": "numpy._typing._nbit_base._32Bit", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "numpy._typing._nbit_base", "mro": ["numpy._typing._nbit_base._32Bit", "numpy._typing._nbit_base._64Bit", "numpy._typing._nbit_base._80Bit", "numpy._typing._nbit_base._96Bit", "numpy._typing._nbit_base._128Bit", "numpy._typing._nbit_base._256Bit", "numpy._typing._nbit_base.NBitBase", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._nbit_base._32Bit.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "numpy._typing._nbit_base._32Bit", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_64Bit": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["numpy._typing._nbit_base._80Bit"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy._typing._nbit_base._64Bit", "name": "_64Bit", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_final"], "fullname": "numpy._typing._nbit_base._64Bit", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "numpy._typing._nbit_base", "mro": ["numpy._typing._nbit_base._64Bit", "numpy._typing._nbit_base._80Bit", "numpy._typing._nbit_base._96Bit", "numpy._typing._nbit_base._128Bit", "numpy._typing._nbit_base._256Bit", "numpy._typing._nbit_base.NBitBase", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._nbit_base._64Bit.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "numpy._typing._nbit_base._64Bit", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_80Bit": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["numpy._typing._nbit_base._96Bit"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy._typing._nbit_base._80Bit", "name": "_80Bit", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_final"], "fullname": "numpy._typing._nbit_base._80Bit", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "numpy._typing._nbit_base", "mro": ["numpy._typing._nbit_base._80Bit", "numpy._typing._nbit_base._96Bit", "numpy._typing._nbit_base._128Bit", "numpy._typing._nbit_base._256Bit", "numpy._typing._nbit_base.NBitBase", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._nbit_base._80Bit.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "numpy._typing._nbit_base._80Bit", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_8Bit": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["numpy._typing._nbit_base._16Bit"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy._typing._nbit_base._8Bit", "name": "_8Bit", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_final"], "fullname": "numpy._typing._nbit_base._8Bit", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "numpy._typing._nbit_base", "mro": ["numpy._typing._nbit_base._8Bit", "numpy._typing._nbit_base._16Bit", "numpy._typing._nbit_base._32Bit", "numpy._typing._nbit_base._64Bit", "numpy._typing._nbit_base._80Bit", "numpy._typing._nbit_base._96Bit", "numpy._typing._nbit_base._128Bit", "numpy._typing._nbit_base._256Bit", "numpy._typing._nbit_base.NBitBase", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._nbit_base._8Bit.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "numpy._typing._nbit_base._8Bit", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_96Bit": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["numpy._typing._nbit_base._128Bit"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy._typing._nbit_base._96Bit", "name": "_96Bit", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_final"], "fullname": "numpy._typing._nbit_base._96Bit", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "numpy._typing._nbit_base", "mro": ["numpy._typing._nbit_base._96Bit", "numpy._typing._nbit_base._128Bit", "numpy._typing._nbit_base._256Bit", "numpy._typing._nbit_base.NBitBase", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._nbit_base._96Bit.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "numpy._typing._nbit_base._96Bit", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy._typing._nbit_base.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy._typing._nbit_base.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy._typing._nbit_base.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy._typing._nbit_base.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy._typing._nbit_base.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy._typing._nbit_base.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "final": {".class": "SymbolTableNode", "cross_ref": "typing.final", "kind": "Gdef"}, "set_module": {".class": "SymbolTableNode", "cross_ref": "numpy._utils.set_module", "kind": "Gdef"}}, "path": "c:\\Python313\\Lib\\site-packages\\numpy\\_typing\\_nbit_base.py"}