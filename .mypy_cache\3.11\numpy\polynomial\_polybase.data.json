{".class": "MypyFile", "_fullname": "numpy.polynomial._polybase", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "ABCPolyBase": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": "abc.ABCMeta", "defn": {".class": "ClassDef", "fullname": "numpy.polynomial._polybase.ABCPolyBase", "name": "ABCPolyBase", "type_vars": [{".class": "TypeVarType", "default": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "fullname": "numpy.polynomial._polybase._NameCo", "id": 1, "name": "_NameCo", "namespace": "numpy.polynomial._polybase.ABCPolyBase", "upper_bound": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "values": [], "variance": 1}]}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "numpy.polynomial._polybase.ABCPolyBase", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "numpy.polynomial._polybase", "mro": ["numpy.polynomial._polybase.ABCPolyBase", "builtins.object"], "names": {".class": "SymbolTable", "__add__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.polynomial._polybase.ABCPolyBase.__add__", "name": "__add__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial._polybase._Self", "id": -1, "name": "_Self", "namespace": "numpy.polynomial._polybase.ABCPolyBase.__add__", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polybase._AnyOther"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__add__ of ABCPolyBase", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial._polybase._Self", "id": -1, "name": "_Self", "namespace": "numpy.polynomial._polybase.ABCPolyBase.__add__", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial._polybase._Self", "id": -1, "name": "_Self", "namespace": "numpy.polynomial._polybase.ABCPolyBase.__add__", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "__array_ufunc__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready"], "fullname": "numpy.polynomial._polybase.ABCPolyBase.__array_ufunc__", "name": "__array_ufunc__", "type": {".class": "NoneType"}}}, "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "numpy.polynomial._polybase.ABCPolyBase.__call__", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, "arg"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.polynomial._polybase.ABCPolyBase.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, "arg"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "fullname": "numpy.polynomial._polybase._NameCo", "id": 1, "name": "_NameCo", "namespace": "numpy.polynomial._polybase.ABCPolyBase", "upper_bound": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.polynomial._polybase.ABCPolyBase"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial._polybase._Other", "id": -1, "name": "_Other", "namespace": "numpy.polynomial._polybase.ABCPolyBase.__call__#0", "upper_bound": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.polynomial._polybase.ABCPolyBase"}, "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of ABCPolyBase", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial._polybase._Other", "id": -1, "name": "_Other", "namespace": "numpy.polynomial._polybase.ABCPolyBase.__call__#0", "upper_bound": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.polynomial._polybase.ABCPolyBase"}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial._polybase._Other", "id": -1, "name": "_Other", "namespace": "numpy.polynomial._polybase.ABCPolyBase.__call__#0", "upper_bound": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.polynomial._polybase.ABCPolyBase"}, "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.polynomial._polybase.ABCPolyBase.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, "arg"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "fullname": "numpy.polynomial._polybase._NameCo", "id": 1, "name": "_NameCo", "namespace": "numpy.polynomial._polybase.ABCPolyBase", "upper_bound": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.polynomial._polybase.ABCPolyBase"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial._polybase._Other", "id": -1, "name": "_Other", "namespace": "numpy.polynomial._polybase.ABCPolyBase.__call__#0", "upper_bound": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.polynomial._polybase.ABCPolyBase"}, "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of ABCPolyBase", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial._polybase._Other", "id": -1, "name": "_Other", "namespace": "numpy.polynomial._polybase.ABCPolyBase.__call__#0", "upper_bound": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.polynomial._polybase.ABCPolyBase"}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial._polybase._Other", "id": -1, "name": "_Other", "namespace": "numpy.polynomial._polybase.ABCPolyBase.__call__#0", "upper_bound": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.polynomial._polybase.ABCPolyBase"}, "values": [], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, "arg"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.polynomial._polybase.ABCPolyBase.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, "arg"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "fullname": "numpy.polynomial._polybase._NameCo", "id": 1, "name": "_NameCo", "namespace": "numpy.polynomial._polybase.ABCPolyBase", "upper_bound": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.polynomial._polybase.ABCPolyBase"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._scalars._FloatLike_co"}, "decimal.Decimal", "numbers.Real", "numpy.object_"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of ABCPolyBase", "ret_type": {".class": "UnionType", "items": ["numpy.float64", "numpy.complex128"], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.polynomial._polybase.ABCPolyBase.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, "arg"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "fullname": "numpy.polynomial._polybase._NameCo", "id": 1, "name": "_NameCo", "namespace": "numpy.polynomial._polybase.ABCPolyBase", "upper_bound": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.polynomial._polybase.ABCPolyBase"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._scalars._FloatLike_co"}, "decimal.Decimal", "numbers.Real", "numpy.object_"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of ABCPolyBase", "ret_type": {".class": "UnionType", "items": ["numpy.float64", "numpy.complex128"], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, "arg"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.polynomial._polybase.ABCPolyBase.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, "arg"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "fullname": "numpy.polynomial._polybase._NameCo", "id": 1, "name": "_NameCo", "namespace": "numpy.polynomial._polybase.ABCPolyBase", "upper_bound": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.polynomial._polybase.ABCPolyBase"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._scalars._NumberLike_co"}, "numbers.Complex"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of ABCPolyBase", "ret_type": "numpy.complex128", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.polynomial._polybase.ABCPolyBase.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, "arg"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "fullname": "numpy.polynomial._polybase._NameCo", "id": 1, "name": "_NameCo", "namespace": "numpy.polynomial._polybase.ABCPolyBase", "upper_bound": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.polynomial._polybase.ABCPolyBase"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._scalars._NumberLike_co"}, "numbers.Complex"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of ABCPolyBase", "ret_type": "numpy.complex128", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, "arg"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.polynomial._polybase.ABCPolyBase.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, "arg"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "fullname": "numpy.polynomial._polybase._NameCo", "id": 1, "name": "_NameCo", "namespace": "numpy.polynomial._polybase.ABCPolyBase", "upper_bound": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.polynomial._polybase.ABCPolyBase"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeFloat_co"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of ABCPolyBase", "ret_type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": ["numpy.float64"], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": ["numpy.complex128"], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": ["numpy.object_"], "type_ref": "numpy._typing._array_like.NDArray"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.polynomial._polybase.ABCPolyBase.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, "arg"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "fullname": "numpy.polynomial._polybase._NameCo", "id": 1, "name": "_NameCo", "namespace": "numpy.polynomial._polybase.ABCPolyBase", "upper_bound": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.polynomial._polybase.ABCPolyBase"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeFloat_co"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of ABCPolyBase", "ret_type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": ["numpy.float64"], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": ["numpy.complex128"], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": ["numpy.object_"], "type_ref": "numpy._typing._array_like.NDArray"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, "arg"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.polynomial._polybase.ABCPolyBase.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, "arg"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "fullname": "numpy.polynomial._polybase._NameCo", "id": 1, "name": "_NameCo", "namespace": "numpy.polynomial._polybase.ABCPolyBase", "upper_bound": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.polynomial._polybase.ABCPolyBase"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeComplex_co"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of ABCPolyBase", "ret_type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": ["numpy.complex128"], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": ["numpy.object_"], "type_ref": "numpy._typing._array_like.NDArray"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.polynomial._polybase.ABCPolyBase.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, "arg"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "fullname": "numpy.polynomial._polybase._NameCo", "id": 1, "name": "_NameCo", "namespace": "numpy.polynomial._polybase.ABCPolyBase", "upper_bound": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.polynomial._polybase.ABCPolyBase"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeComplex_co"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of ABCPolyBase", "ret_type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": ["numpy.complex128"], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": ["numpy.object_"], "type_ref": "numpy._typing._array_like.NDArray"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, "arg"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.polynomial._polybase.ABCPolyBase.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, "arg"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "fullname": "numpy.polynomial._polybase._NameCo", "id": 1, "name": "_NameCo", "namespace": "numpy.polynomial._polybase.ABCPolyBase", "upper_bound": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.polynomial._polybase.ABCPolyBase"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._ArrayLikeCoefObject_co"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of ABCPolyBase", "ret_type": {".class": "TypeAliasType", "args": ["numpy.object_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.polynomial._polybase.ABCPolyBase.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, "arg"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "fullname": "numpy.polynomial._polybase._NameCo", "id": 1, "name": "_NameCo", "namespace": "numpy.polynomial._polybase.ABCPolyBase", "upper_bound": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.polynomial._polybase.ABCPolyBase"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._ArrayLikeCoefObject_co"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of ABCPolyBase", "ret_type": {".class": "TypeAliasType", "args": ["numpy.object_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, "arg"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "fullname": "numpy.polynomial._polybase._NameCo", "id": 1, "name": "_NameCo", "namespace": "numpy.polynomial._polybase.ABCPolyBase", "upper_bound": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.polynomial._polybase.ABCPolyBase"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial._polybase._Other", "id": -1, "name": "_Other", "namespace": "numpy.polynomial._polybase.ABCPolyBase.__call__#0", "upper_bound": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.polynomial._polybase.ABCPolyBase"}, "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of ABCPolyBase", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial._polybase._Other", "id": -1, "name": "_Other", "namespace": "numpy.polynomial._polybase.ABCPolyBase.__call__#0", "upper_bound": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.polynomial._polybase.ABCPolyBase"}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial._polybase._Other", "id": -1, "name": "_Other", "namespace": "numpy.polynomial._polybase.ABCPolyBase.__call__#0", "upper_bound": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.polynomial._polybase.ABCPolyBase"}, "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, "arg"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "fullname": "numpy.polynomial._polybase._NameCo", "id": 1, "name": "_NameCo", "namespace": "numpy.polynomial._polybase.ABCPolyBase", "upper_bound": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.polynomial._polybase.ABCPolyBase"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._scalars._FloatLike_co"}, "decimal.Decimal", "numbers.Real", "numpy.object_"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of ABCPolyBase", "ret_type": {".class": "UnionType", "items": ["numpy.float64", "numpy.complex128"], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, "arg"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "fullname": "numpy.polynomial._polybase._NameCo", "id": 1, "name": "_NameCo", "namespace": "numpy.polynomial._polybase.ABCPolyBase", "upper_bound": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.polynomial._polybase.ABCPolyBase"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._scalars._NumberLike_co"}, "numbers.Complex"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of ABCPolyBase", "ret_type": "numpy.complex128", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, "arg"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "fullname": "numpy.polynomial._polybase._NameCo", "id": 1, "name": "_NameCo", "namespace": "numpy.polynomial._polybase.ABCPolyBase", "upper_bound": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.polynomial._polybase.ABCPolyBase"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeFloat_co"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of ABCPolyBase", "ret_type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": ["numpy.float64"], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": ["numpy.complex128"], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": ["numpy.object_"], "type_ref": "numpy._typing._array_like.NDArray"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, "arg"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "fullname": "numpy.polynomial._polybase._NameCo", "id": 1, "name": "_NameCo", "namespace": "numpy.polynomial._polybase.ABCPolyBase", "upper_bound": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.polynomial._polybase.ABCPolyBase"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeComplex_co"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of ABCPolyBase", "ret_type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": ["numpy.complex128"], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": ["numpy.object_"], "type_ref": "numpy._typing._array_like.NDArray"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, "arg"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "fullname": "numpy.polynomial._polybase._NameCo", "id": 1, "name": "_NameCo", "namespace": "numpy.polynomial._polybase.ABCPolyBase", "upper_bound": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.polynomial._polybase.ABCPolyBase"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._ArrayLikeCoefObject_co"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of ABCPolyBase", "ret_type": {".class": "TypeAliasType", "args": ["numpy.object_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "__divmod__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.polynomial._polybase.ABCPolyBase.__divmod__", "name": "__divmod__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial._polybase._Self", "id": -1, "name": "_Self", "namespace": "numpy.polynomial._polybase.ABCPolyBase.__divmod__", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polybase._AnyOther"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__divmod__ of ABCPolyBase", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial._polybase._Self", "id": -1, "name": "_Self", "namespace": "numpy.polynomial._polybase.ABCPolyBase.__divmod__", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "numpy.polynomial._polytypes._Tuple2"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial._polybase._Self", "id": -1, "name": "_Self", "namespace": "numpy.polynomial._polybase.ABCPolyBase.__divmod__", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "__eq__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.polynomial._polybase.ABCPolyBase.__eq__", "name": "__eq__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "fullname": "numpy.polynomial._polybase._NameCo", "id": 1, "name": "_NameCo", "namespace": "numpy.polynomial._polybase.ABCPolyBase", "upper_bound": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.polynomial._polybase.ABCPolyBase"}, "builtins.object"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__eq__ of ABCPolyBase", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__floordiv__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.polynomial._polybase.ABCPolyBase.__floordiv__", "name": "__floordiv__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial._polybase._Self", "id": -1, "name": "_Self", "namespace": "numpy.polynomial._polybase.ABCPolyBase.__floordiv__", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polybase._AnyOther"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__floordiv__ of ABCPolyBase", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial._polybase._Self", "id": -1, "name": "_Self", "namespace": "numpy.polynomial._polybase.ABCPolyBase.__floordiv__", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial._polybase._Self", "id": -1, "name": "_Self", "namespace": "numpy.polynomial._polybase.ABCPolyBase.__floordiv__", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "__format__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.polynomial._polybase.ABCPolyBase.__format__", "name": "__format__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "fullname": "numpy.polynomial._polybase._NameCo", "id": 1, "name": "_NameCo", "namespace": "numpy.polynomial._polybase.ABCPolyBase", "upper_bound": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.polynomial._polybase.ABCPolyBase"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__format__ of ABCPolyBase", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__getstate__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.polynomial._polybase.ABCPolyBase.__getstate__", "name": "__getstate__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "fullname": "numpy.polynomial._polybase._NameCo", "id": 1, "name": "_NameCo", "namespace": "numpy.polynomial._polybase.ABCPolyBase", "upper_bound": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.polynomial._polybase.ABCPolyBase"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__getstate__ of ABCPolyBase", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__hash__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready"], "fullname": "numpy.polynomial._polybase.ABCPolyBase.__hash__", "name": "__hash__", "type": {".class": "NoneType"}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1], "arg_names": [null, "coef", "domain", "window", "symbol"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.polynomial._polybase.ABCPolyBase.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1], "arg_names": [null, "coef", "domain", "window", "symbol"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "fullname": "numpy.polynomial._polybase._NameCo", "id": 1, "name": "_NameCo", "namespace": "numpy.polynomial._polybase.ABCPolyBase", "upper_bound": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.polynomial._polybase.ABCPolyBase"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co"}], "uses_pep604_syntax": true}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ABCPolyBase", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__iter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.polynomial._polybase.ABCPolyBase.__iter__", "name": "__iter__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "fullname": "numpy.polynomial._polybase._NameCo", "id": 1, "name": "_NameCo", "namespace": "numpy.polynomial._polybase.ABCPolyBase", "upper_bound": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.polynomial._polybase.ABCPolyBase"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__iter__ of ABCPolyBase", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": ["builtins.float", "builtins.complex"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.inexact"}, "builtins.object"], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__len__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.polynomial._polybase.ABCPolyBase.__len__", "name": "__len__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "fullname": "numpy.polynomial._polybase._NameCo", "id": 1, "name": "_NameCo", "namespace": "numpy.polynomial._polybase.ABCPolyBase", "upper_bound": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.polynomial._polybase.ABCPolyBase"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__len__ of ABCPolyBase", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__mod__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.polynomial._polybase.ABCPolyBase.__mod__", "name": "__mod__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial._polybase._Self", "id": -1, "name": "_Self", "namespace": "numpy.polynomial._polybase.ABCPolyBase.__mod__", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polybase._AnyOther"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mod__ of ABCPolyBase", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial._polybase._Self", "id": -1, "name": "_Self", "namespace": "numpy.polynomial._polybase.ABCPolyBase.__mod__", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial._polybase._Self", "id": -1, "name": "_Self", "namespace": "numpy.polynomial._polybase.ABCPolyBase.__mod__", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "__mul__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.polynomial._polybase.ABCPolyBase.__mul__", "name": "__mul__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial._polybase._Self", "id": -1, "name": "_Self", "namespace": "numpy.polynomial._polybase.ABCPolyBase.__mul__", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polybase._AnyOther"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mul__ of ABCPolyBase", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial._polybase._Self", "id": -1, "name": "_Self", "namespace": "numpy.polynomial._polybase.ABCPolyBase.__mul__", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial._polybase._Self", "id": -1, "name": "_Self", "namespace": "numpy.polynomial._polybase.ABCPolyBase.__mul__", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "__ne__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.polynomial._polybase.ABCPolyBase.__ne__", "name": "__ne__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "fullname": "numpy.polynomial._polybase._NameCo", "id": 1, "name": "_NameCo", "namespace": "numpy.polynomial._polybase.ABCPolyBase", "upper_bound": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.polynomial._polybase.ABCPolyBase"}, "builtins.object"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__ne__ of ABCPolyBase", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__neg__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.polynomial._polybase.ABCPolyBase.__neg__", "name": "__neg__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial._polybase._Self", "id": -1, "name": "_Self", "namespace": "numpy.polynomial._polybase.ABCPolyBase.__neg__", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__neg__ of ABCPolyBase", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial._polybase._Self", "id": -1, "name": "_Self", "namespace": "numpy.polynomial._polybase.ABCPolyBase.__neg__", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial._polybase._Self", "id": -1, "name": "_Self", "namespace": "numpy.polynomial._polybase.ABCPolyBase.__neg__", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "__pos__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.polynomial._polybase.ABCPolyBase.__pos__", "name": "__pos__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial._polybase._Self", "id": -1, "name": "_Self", "namespace": "numpy.polynomial._polybase.ABCPolyBase.__pos__", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__pos__ of ABCPolyBase", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial._polybase._Self", "id": -1, "name": "_Self", "namespace": "numpy.polynomial._polybase.ABCPolyBase.__pos__", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial._polybase._Self", "id": -1, "name": "_Self", "namespace": "numpy.polynomial._polybase.ABCPolyBase.__pos__", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "__pow__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.polynomial._polybase.ABCPolyBase.__pow__", "name": "__pow__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial._polybase._Self", "id": -1, "name": "_Self", "namespace": "numpy.polynomial._polybase.ABCPolyBase.__pow__", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polybase._AnyOther"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__pow__ of ABCPolyBase", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial._polybase._Self", "id": -1, "name": "_Self", "namespace": "numpy.polynomial._polybase.ABCPolyBase.__pow__", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial._polybase._Self", "id": -1, "name": "_Self", "namespace": "numpy.polynomial._polybase.ABCPolyBase.__pow__", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "__radd__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.polynomial._polybase.ABCPolyBase.__radd__", "name": "__radd__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial._polybase._Self", "id": -1, "name": "_Self", "namespace": "numpy.polynomial._polybase.ABCPolyBase.__radd__", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polybase._AnyOther"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__radd__ of ABCPolyBase", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial._polybase._Self", "id": -1, "name": "_Self", "namespace": "numpy.polynomial._polybase.ABCPolyBase.__radd__", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial._polybase._Self", "id": -1, "name": "_Self", "namespace": "numpy.polynomial._polybase.ABCPolyBase.__radd__", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "__rdivmod__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.polynomial._polybase.ABCPolyBase.__rdivmod__", "name": "__rdivmod__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial._polybase._Self", "id": -1, "name": "_Self", "namespace": "numpy.polynomial._polybase.ABCPolyBase.__rdivmod__", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polybase._AnyOther"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__rdivmod__ of ABCPolyBase", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial._polybase._Self", "id": -1, "name": "_Self", "namespace": "numpy.polynomial._polybase.ABCPolyBase.__rdivmod__", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "numpy.polynomial._polytypes._Tuple2"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial._polybase._Self", "id": -1, "name": "_Self", "namespace": "numpy.polynomial._polybase.ABCPolyBase.__rdivmod__", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "__rfloordiv__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.polynomial._polybase.ABCPolyBase.__rfloordiv__", "name": "__rfloordiv__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial._polybase._Self", "id": -1, "name": "_Self", "namespace": "numpy.polynomial._polybase.ABCPolyBase.__rfloordiv__", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polybase._AnyOther"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__rfloordiv__ of ABCPolyBase", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial._polybase._Self", "id": -1, "name": "_Self", "namespace": "numpy.polynomial._polybase.ABCPolyBase.__rfloordiv__", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial._polybase._Self", "id": -1, "name": "_Self", "namespace": "numpy.polynomial._polybase.ABCPolyBase.__rfloordiv__", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "__rmod__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.polynomial._polybase.ABCPolyBase.__rmod__", "name": "__rmod__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial._polybase._Self", "id": -1, "name": "_Self", "namespace": "numpy.polynomial._polybase.ABCPolyBase.__rmod__", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polybase._AnyOther"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__rmod__ of ABCPolyBase", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial._polybase._Self", "id": -1, "name": "_Self", "namespace": "numpy.polynomial._polybase.ABCPolyBase.__rmod__", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial._polybase._Self", "id": -1, "name": "_Self", "namespace": "numpy.polynomial._polybase.ABCPolyBase.__rmod__", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "__rmul__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.polynomial._polybase.ABCPolyBase.__rmul__", "name": "__rmul__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial._polybase._Self", "id": -1, "name": "_Self", "namespace": "numpy.polynomial._polybase.ABCPolyBase.__rmul__", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polybase._AnyOther"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__rmul__ of ABCPolyBase", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial._polybase._Self", "id": -1, "name": "_Self", "namespace": "numpy.polynomial._polybase.ABCPolyBase.__rmul__", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial._polybase._Self", "id": -1, "name": "_Self", "namespace": "numpy.polynomial._polybase.ABCPolyBase.__rmul__", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "__rsub__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.polynomial._polybase.ABCPolyBase.__rsub__", "name": "__rsub__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial._polybase._Self", "id": -1, "name": "_Self", "namespace": "numpy.polynomial._polybase.ABCPolyBase.__rsub__", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polybase._AnyOther"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__rsub__ of ABCPolyBase", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial._polybase._Self", "id": -1, "name": "_Self", "namespace": "numpy.polynomial._polybase.ABCPolyBase.__rsub__", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial._polybase._Self", "id": -1, "name": "_Self", "namespace": "numpy.polynomial._polybase.ABCPolyBase.__rsub__", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "__rtruediv__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.polynomial._polybase.ABCPolyBase.__rtruediv__", "name": "__rtruediv__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial._polybase._Self", "id": -1, "name": "_Self", "namespace": "numpy.polynomial._polybase.ABCPolyBase.__rtruediv__", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polybase._AnyOther"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__rtruediv__ of ABCPolyBase", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial._polybase._Self", "id": -1, "name": "_Self", "namespace": "numpy.polynomial._polybase.ABCPolyBase.__rtruediv__", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial._polybase._Self", "id": -1, "name": "_Self", "namespace": "numpy.polynomial._polybase.ABCPolyBase.__rtruediv__", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "__setstate__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.polynomial._polybase.ABCPolyBase.__setstate__", "name": "__setstate__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "fullname": "numpy.polynomial._polybase._NameCo", "id": 1, "name": "_NameCo", "namespace": "numpy.polynomial._polybase.ABCPolyBase", "upper_bound": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.polynomial._polybase.ABCPolyBase"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__setstate__ of ABCPolyBase", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__sub__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.polynomial._polybase.ABCPolyBase.__sub__", "name": "__sub__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial._polybase._Self", "id": -1, "name": "_Self", "namespace": "numpy.polynomial._polybase.ABCPolyBase.__sub__", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polybase._AnyOther"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__sub__ of ABCPolyBase", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial._polybase._Self", "id": -1, "name": "_Self", "namespace": "numpy.polynomial._polybase.ABCPolyBase.__sub__", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial._polybase._Self", "id": -1, "name": "_Self", "namespace": "numpy.polynomial._polybase.ABCPolyBase.__sub__", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "__truediv__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.polynomial._polybase.ABCPolyBase.__truediv__", "name": "__truediv__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial._polybase._Self", "id": -1, "name": "_Self", "namespace": "numpy.polynomial._polybase.ABCPolyBase.__truediv__", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polybase._AnyOther"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__truediv__ of ABCPolyBase", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial._polybase._Self", "id": -1, "name": "_Self", "namespace": "numpy.polynomial._polybase.ABCPolyBase.__truediv__", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial._polybase._Self", "id": -1, "name": "_Self", "namespace": "numpy.polynomial._polybase.ABCPolyBase.__truediv__", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "_repr_latex_term": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["i", "arg_str", "needs_parens"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "numpy.polynomial._polybase.ABCPolyBase._repr_latex_term", "name": "_repr_latex_term", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["i", "arg_str", "needs_parens"], "arg_types": ["builtins.str", "builtins.str", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_repr_latex_term of ABCPolyBase", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "numpy.polynomial._polybase.ABCPolyBase._repr_latex_term", "name": "_repr_latex_term", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["i", "arg_str", "needs_parens"], "arg_types": ["builtins.str", "builtins.str", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_repr_latex_term of ABCPolyBase", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_str_term_ascii": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["i", "arg_str"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "numpy.polynomial._polybase.ABCPolyBase._str_term_ascii", "name": "_str_term_ascii", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["i", "arg_str"], "arg_types": ["builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_str_term_ascii of ABCPolyBase", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "numpy.polynomial._polybase.ABCPolyBase._str_term_ascii", "name": "_str_term_ascii", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["i", "arg_str"], "arg_types": ["builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_str_term_ascii of ABCPolyBase", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_str_term_unicode": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": [null, "i", "arg_str"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "numpy.polynomial._polybase.ABCPolyBase._str_term_unicode", "name": "_str_term_unicode", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": [null, "i", "arg_str"], "arg_types": [{".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "fullname": "numpy.polynomial._polybase._NameCo", "id": 1, "name": "_NameCo", "namespace": "numpy.polynomial._polybase.ABCPolyBase", "upper_bound": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.polynomial._polybase.ABCPolyBase"}}, "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_str_term_unicode of ABCPolyBase", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "numpy.polynomial._polybase.ABCPolyBase._str_term_unicode", "name": "_str_term_unicode", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": [null, "i", "arg_str"], "arg_types": [{".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "fullname": "numpy.polynomial._polybase._NameCo", "id": 1, "name": "_NameCo", "namespace": "numpy.polynomial._polybase.ABCPolyBase", "upper_bound": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.polynomial._polybase.ABCPolyBase"}}, "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_str_term_unicode of ABCPolyBase", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_subscript_mapping": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready"], "fullname": "numpy.polynomial._polybase.ABCPolyBase._subscript_mapping", "name": "_subscript_mapping", "type": {".class": "Instance", "args": ["builtins.int", "builtins.str"], "extra_attrs": null, "type_ref": "typing.Mapping"}}}, "_superscript_mapping": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready"], "fullname": "numpy.polynomial._polybase.ABCPolyBase._superscript_mapping", "name": "_superscript_mapping", "type": {".class": "Instance", "args": ["builtins.int", "builtins.str"], "extra_attrs": null, "type_ref": "typing.Mapping"}}}, "_symbol": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "numpy.polynomial._polybase.ABCPolyBase._symbol", "name": "_symbol", "type": "builtins.str"}}, "_use_unicode": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready"], "fullname": "numpy.polynomial._polybase.ABCPolyBase._use_unicode", "name": "_use_unicode", "type": "builtins.bool"}}, "basis": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1], "arg_names": [null, "deg", "domain", "window", "symbol"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "numpy.polynomial._polybase.ABCPolyBase.basis", "name": "basis", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1], "arg_names": [null, "deg", "domain", "window", "symbol"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial._polybase._Self", "id": -1, "name": "_Self", "namespace": "numpy.polynomial._polybase.ABCPolyBase.basis", "upper_bound": "builtins.object", "values": [], "variance": 0}}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._AnyInt"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co"}], "uses_pep604_syntax": true}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "basis of ABCPolyBase", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial._polybase._Self", "id": -1, "name": "_Self", "namespace": "numpy.polynomial._polybase.ABCPolyBase.basis", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial._polybase._Self", "id": -1, "name": "_Self", "namespace": "numpy.polynomial._polybase.ABCPolyBase.basis", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "numpy.polynomial._polybase.ABCPolyBase.basis", "name": "basis", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1], "arg_names": [null, "deg", "domain", "window", "symbol"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial._polybase._Self", "id": -1, "name": "_Self", "namespace": "numpy.polynomial._polybase.ABCPolyBase.basis", "upper_bound": "builtins.object", "values": [], "variance": 0}}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._AnyInt"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co"}], "uses_pep604_syntax": true}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "basis of ABCPolyBase", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial._polybase._Self", "id": -1, "name": "_Self", "namespace": "numpy.polynomial._polybase.ABCPolyBase.basis", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial._polybase._Self", "id": -1, "name": "_Self", "namespace": "numpy.polynomial._polybase.ABCPolyBase.basis", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}}, "basis_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "numpy.polynomial._polybase.ABCPolyBase.basis_name", "name": "basis_name", "type": {".class": "TypeVarType", "default": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "fullname": "numpy.polynomial._polybase._NameCo", "id": 1, "name": "_NameCo", "namespace": "numpy.polynomial._polybase.ABCPolyBase", "upper_bound": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "values": [], "variance": 1}}}, "cast": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": [null, "series", "domain", "window"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "numpy.polynomial._polybase.ABCPolyBase.cast", "name": "cast", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": [null, "series", "domain", "window"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial._polybase._Self", "id": -1, "name": "_Self", "namespace": "numpy.polynomial._polybase.ABCPolyBase.cast", "upper_bound": "builtins.object", "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.polynomial._polybase.ABCPolyBase"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cast of ABCPolyBase", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial._polybase._Self", "id": -1, "name": "_Self", "namespace": "numpy.polynomial._polybase.ABCPolyBase.cast", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial._polybase._Self", "id": -1, "name": "_Self", "namespace": "numpy.polynomial._polybase.ABCPolyBase.cast", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "numpy.polynomial._polybase.ABCPolyBase.cast", "name": "cast", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": [null, "series", "domain", "window"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial._polybase._Self", "id": -1, "name": "_Self", "namespace": "numpy.polynomial._polybase.ABCPolyBase.cast", "upper_bound": "builtins.object", "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.polynomial._polybase.ABCPolyBase"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cast of ABCPolyBase", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial._polybase._Self", "id": -1, "name": "_Self", "namespace": "numpy.polynomial._polybase.ABCPolyBase.cast", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial._polybase._Self", "id": -1, "name": "_Self", "namespace": "numpy.polynomial._polybase.ABCPolyBase.cast", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}}, "coef": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "numpy.polynomial._polybase.ABCPolyBase.coef", "name": "coef", "type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._CoefSeries"}}}, "convert": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "numpy.polynomial._polybase.ABCPolyBase.convert", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": [null, null, null, "window"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.polynomial._polybase.ABCPolyBase.convert", "name": "convert", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": [null, null, null, "window"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "fullname": "numpy.polynomial._polybase._NameCo", "id": 1, "name": "_NameCo", "namespace": "numpy.polynomial._polybase.ABCPolyBase", "upper_bound": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.polynomial._polybase.ABCPolyBase"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co"}], "uses_pep604_syntax": true}, {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial._polybase._Other", "id": -1, "name": "_Other", "namespace": "numpy.polynomial._polybase.ABCPolyBase.convert#0", "upper_bound": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.polynomial._polybase.ABCPolyBase"}, "values": [], "variance": 0}}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "convert of ABCPolyBase", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial._polybase._Other", "id": -1, "name": "_Other", "namespace": "numpy.polynomial._polybase.ABCPolyBase.convert#0", "upper_bound": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.polynomial._polybase.ABCPolyBase"}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial._polybase._Other", "id": -1, "name": "_Other", "namespace": "numpy.polynomial._polybase.ABCPolyBase.convert#0", "upper_bound": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.polynomial._polybase.ABCPolyBase"}, "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.polynomial._polybase.ABCPolyBase.convert", "name": "convert", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": [null, null, null, "window"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "fullname": "numpy.polynomial._polybase._NameCo", "id": 1, "name": "_NameCo", "namespace": "numpy.polynomial._polybase.ABCPolyBase", "upper_bound": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.polynomial._polybase.ABCPolyBase"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co"}], "uses_pep604_syntax": true}, {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial._polybase._Other", "id": -1, "name": "_Other", "namespace": "numpy.polynomial._polybase.ABCPolyBase.convert#0", "upper_bound": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.polynomial._polybase.ABCPolyBase"}, "values": [], "variance": 0}}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "convert of ABCPolyBase", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial._polybase._Other", "id": -1, "name": "_Other", "namespace": "numpy.polynomial._polybase.ABCPolyBase.convert#0", "upper_bound": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.polynomial._polybase.ABCPolyBase"}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial._polybase._Other", "id": -1, "name": "_Other", "namespace": "numpy.polynomial._polybase.ABCPolyBase.convert#0", "upper_bound": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.polynomial._polybase.ABCPolyBase"}, "values": [], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 3, 5], "arg_names": [null, "domain", "kind", "window"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.polynomial._polybase.ABCPolyBase.convert", "name": "convert", "type": {".class": "CallableType", "arg_kinds": [0, 1, 3, 5], "arg_names": [null, "domain", "kind", "window"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "fullname": "numpy.polynomial._polybase._NameCo", "id": 1, "name": "_NameCo", "namespace": "numpy.polynomial._polybase.ABCPolyBase", "upper_bound": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.polynomial._polybase.ABCPolyBase"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co"}], "uses_pep604_syntax": true}, {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial._polybase._Other", "id": -1, "name": "_Other", "namespace": "numpy.polynomial._polybase.ABCPolyBase.convert#1", "upper_bound": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.polynomial._polybase.ABCPolyBase"}, "values": [], "variance": 0}}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "convert of ABCPolyBase", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial._polybase._Other", "id": -1, "name": "_Other", "namespace": "numpy.polynomial._polybase.ABCPolyBase.convert#1", "upper_bound": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.polynomial._polybase.ABCPolyBase"}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial._polybase._Other", "id": -1, "name": "_Other", "namespace": "numpy.polynomial._polybase.ABCPolyBase.convert#1", "upper_bound": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.polynomial._polybase.ABCPolyBase"}, "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.polynomial._polybase.ABCPolyBase.convert", "name": "convert", "type": {".class": "CallableType", "arg_kinds": [0, 1, 3, 5], "arg_names": [null, "domain", "kind", "window"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "fullname": "numpy.polynomial._polybase._NameCo", "id": 1, "name": "_NameCo", "namespace": "numpy.polynomial._polybase.ABCPolyBase", "upper_bound": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.polynomial._polybase.ABCPolyBase"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co"}], "uses_pep604_syntax": true}, {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial._polybase._Other", "id": -1, "name": "_Other", "namespace": "numpy.polynomial._polybase.ABCPolyBase.convert#1", "upper_bound": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.polynomial._polybase.ABCPolyBase"}, "values": [], "variance": 0}}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "convert of ABCPolyBase", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial._polybase._Other", "id": -1, "name": "_Other", "namespace": "numpy.polynomial._polybase.ABCPolyBase.convert#1", "upper_bound": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.polynomial._polybase.ABCPolyBase"}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial._polybase._Other", "id": -1, "name": "_Other", "namespace": "numpy.polynomial._polybase.ABCPolyBase.convert#1", "upper_bound": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.polynomial._polybase.ABCPolyBase"}, "values": [], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": [null, "domain", "kind", "window"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.polynomial._polybase.ABCPolyBase.convert", "name": "convert", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": [null, "domain", "kind", "window"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial._polybase._Self", "id": -1, "name": "_Self", "namespace": "numpy.polynomial._polybase.ABCPolyBase.convert", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial._polybase._Self", "id": -1, "name": "_Self", "namespace": "numpy.polynomial._polybase.ABCPolyBase.convert", "upper_bound": "builtins.object", "values": [], "variance": 0}}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "convert of ABCPolyBase", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial._polybase._Self", "id": -1, "name": "_Self", "namespace": "numpy.polynomial._polybase.ABCPolyBase.convert", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial._polybase._Self", "id": -1, "name": "_Self", "namespace": "numpy.polynomial._polybase.ABCPolyBase.convert", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.polynomial._polybase.ABCPolyBase.convert", "name": "convert", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": [null, "domain", "kind", "window"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial._polybase._Self", "id": -1, "name": "_Self", "namespace": "numpy.polynomial._polybase.ABCPolyBase.convert", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial._polybase._Self", "id": -1, "name": "_Self", "namespace": "numpy.polynomial._polybase.ABCPolyBase.convert", "upper_bound": "builtins.object", "values": [], "variance": 0}}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "convert of ABCPolyBase", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial._polybase._Self", "id": -1, "name": "_Self", "namespace": "numpy.polynomial._polybase.ABCPolyBase.convert", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial._polybase._Self", "id": -1, "name": "_Self", "namespace": "numpy.polynomial._polybase.ABCPolyBase.convert", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": [null, null, null, "window"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "fullname": "numpy.polynomial._polybase._NameCo", "id": 1, "name": "_NameCo", "namespace": "numpy.polynomial._polybase.ABCPolyBase", "upper_bound": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.polynomial._polybase.ABCPolyBase"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co"}], "uses_pep604_syntax": true}, {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial._polybase._Other", "id": -1, "name": "_Other", "namespace": "numpy.polynomial._polybase.ABCPolyBase.convert#0", "upper_bound": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.polynomial._polybase.ABCPolyBase"}, "values": [], "variance": 0}}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "convert of ABCPolyBase", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial._polybase._Other", "id": -1, "name": "_Other", "namespace": "numpy.polynomial._polybase.ABCPolyBase.convert#0", "upper_bound": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.polynomial._polybase.ABCPolyBase"}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial._polybase._Other", "id": -1, "name": "_Other", "namespace": "numpy.polynomial._polybase.ABCPolyBase.convert#0", "upper_bound": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.polynomial._polybase.ABCPolyBase"}, "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0, 1, 3, 5], "arg_names": [null, "domain", "kind", "window"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "fullname": "numpy.polynomial._polybase._NameCo", "id": 1, "name": "_NameCo", "namespace": "numpy.polynomial._polybase.ABCPolyBase", "upper_bound": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.polynomial._polybase.ABCPolyBase"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co"}], "uses_pep604_syntax": true}, {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial._polybase._Other", "id": -1, "name": "_Other", "namespace": "numpy.polynomial._polybase.ABCPolyBase.convert#1", "upper_bound": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.polynomial._polybase.ABCPolyBase"}, "values": [], "variance": 0}}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "convert of ABCPolyBase", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial._polybase._Other", "id": -1, "name": "_Other", "namespace": "numpy.polynomial._polybase.ABCPolyBase.convert#1", "upper_bound": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.polynomial._polybase.ABCPolyBase"}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial._polybase._Other", "id": -1, "name": "_Other", "namespace": "numpy.polynomial._polybase.ABCPolyBase.convert#1", "upper_bound": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.polynomial._polybase.ABCPolyBase"}, "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": [null, "domain", "kind", "window"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial._polybase._Self", "id": -1, "name": "_Self", "namespace": "numpy.polynomial._polybase.ABCPolyBase.convert", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial._polybase._Self", "id": -1, "name": "_Self", "namespace": "numpy.polynomial._polybase.ABCPolyBase.convert", "upper_bound": "builtins.object", "values": [], "variance": 0}}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "convert of ABCPolyBase", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial._polybase._Self", "id": -1, "name": "_Self", "namespace": "numpy.polynomial._polybase.ABCPolyBase.convert", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial._polybase._Self", "id": -1, "name": "_Self", "namespace": "numpy.polynomial._polybase.ABCPolyBase.convert", "upper_bound": "builtins.object", "values": [], "variance": 0}]}]}}}, "copy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.polynomial._polybase.ABCPolyBase.copy", "name": "copy", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial._polybase._Self", "id": -1, "name": "_Self", "namespace": "numpy.polynomial._polybase.ABCPolyBase.copy", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "copy of ABCPolyBase", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial._polybase._Self", "id": -1, "name": "_Self", "namespace": "numpy.polynomial._polybase.ABCPolyBase.copy", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial._polybase._Self", "id": -1, "name": "_Self", "namespace": "numpy.polynomial._polybase.ABCPolyBase.copy", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "cutdeg": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.polynomial._polybase.ABCPolyBase.cutdeg", "name": "cutdeg", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial._polybase._Self", "id": -1, "name": "_Self", "namespace": "numpy.polynomial._polybase.ABCPolyBase.cutdeg", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cutdeg of ABCPolyBase", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial._polybase._Self", "id": -1, "name": "_Self", "namespace": "numpy.polynomial._polybase.ABCPolyBase.cutdeg", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial._polybase._Self", "id": -1, "name": "_Self", "namespace": "numpy.polynomial._polybase.ABCPolyBase.cutdeg", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "degree": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.polynomial._polybase.ABCPolyBase.degree", "name": "degree", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "fullname": "numpy.polynomial._polybase._NameCo", "id": 1, "name": "_NameCo", "namespace": "numpy.polynomial._polybase.ABCPolyBase", "upper_bound": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.polynomial._polybase.ABCPolyBase"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "degree of ABCPolyBase", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "deriv": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": [null, "m"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.polynomial._polybase.ABCPolyBase.deriv", "name": "deriv", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": [null, "m"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial._polybase._Self", "id": -1, "name": "_Self", "namespace": "numpy.polynomial._polybase.ABCPolyBase.deriv", "upper_bound": "builtins.object", "values": [], "variance": 0}, "typing.SupportsIndex"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "deriv of ABCPolyBase", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial._polybase._Self", "id": -1, "name": "_Self", "namespace": "numpy.polynomial._polybase.ABCPolyBase.deriv", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial._polybase._Self", "id": -1, "name": "_Self", "namespace": "numpy.polynomial._polybase.ABCPolyBase.deriv", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "domain": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "numpy.polynomial._polybase.ABCPolyBase.domain", "name": "domain", "type": {".class": "TypeAliasType", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": ["builtins.float", "builtins.complex"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.inexact"}, "numpy.object_"], "uses_pep604_syntax": true}], "type_ref": "numpy.polynomial._polytypes._Array2"}}}, "fit": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": ["is_class"], "fullname": "numpy.polynomial._polybase.ABCPolyBase.fit", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1, 1, 1, 1, 1, 1], "arg_names": [null, "x", "y", "deg", "domain", "rcond", "full", "w", "window", "symbol"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_overload", "is_decorated"], "fullname": "numpy.polynomial._polybase.ABCPolyBase.fit", "name": "fit", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1, 1, 1, 1, 1, 1], "arg_names": [null, "x", "y", "deg", "domain", "rcond", "full", "w", "window", "symbol"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial._polybase._Self", "id": -1, "name": "_Self", "namespace": "numpy.polynomial._polybase.ABCPolyBase.fit#0", "upper_bound": "builtins.object", "values": [], "variance": 0}}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co"}, {".class": "UnionType", "items": ["builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeInt_co"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co"}], "uses_pep604_syntax": true}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._scalars._FloatLike_co"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co"}], "uses_pep604_syntax": true}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "fit of ABCPolyBase", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial._polybase._Self", "id": -1, "name": "_Self", "namespace": "numpy.polynomial._polybase.ABCPolyBase.fit#0", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial._polybase._Self", "id": -1, "name": "_Self", "namespace": "numpy.polynomial._polybase.ABCPolyBase.fit#0", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_classmethod", "is_ready", "is_inferred"], "fullname": "numpy.polynomial._polybase.ABCPolyBase.fit", "name": "fit", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1, 1, 1, 1, 1, 1], "arg_names": [null, "x", "y", "deg", "domain", "rcond", "full", "w", "window", "symbol"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial._polybase._Self", "id": -1, "name": "_Self", "namespace": "numpy.polynomial._polybase.ABCPolyBase.fit#0", "upper_bound": "builtins.object", "values": [], "variance": 0}}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co"}, {".class": "UnionType", "items": ["builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeInt_co"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co"}], "uses_pep604_syntax": true}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._scalars._FloatLike_co"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co"}], "uses_pep604_syntax": true}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "fit of ABCPolyBase", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial._polybase._Self", "id": -1, "name": "_Self", "namespace": "numpy.polynomial._polybase.ABCPolyBase.fit#0", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial._polybase._Self", "id": -1, "name": "_Self", "namespace": "numpy.polynomial._polybase.ABCPolyBase.fit#0", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1, 1, 3, 5, 5, 5], "arg_names": [null, "x", "y", "deg", "domain", "rcond", "full", "w", "window", "symbol"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_overload", "is_decorated"], "fullname": "numpy.polynomial._polybase.ABCPolyBase.fit", "name": "fit", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1, 1, 3, 5, 5, 5], "arg_names": [null, "x", "y", "deg", "domain", "rcond", "full", "w", "window", "symbol"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial._polybase._Self", "id": -1, "name": "_Self", "namespace": "numpy.polynomial._polybase.ABCPolyBase.fit#1", "upper_bound": "builtins.object", "values": [], "variance": 0}}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co"}, {".class": "UnionType", "items": ["builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeInt_co"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co"}], "uses_pep604_syntax": true}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._scalars._FloatLike_co"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co"}], "uses_pep604_syntax": true}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "fit of ABCPolyBase", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial._polybase._Self", "id": -1, "name": "_Self", "namespace": "numpy.polynomial._polybase.ABCPolyBase.fit#1", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": ["builtins.float", "builtins.complex"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.inexact"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int32"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial._polybase._Self", "id": -1, "name": "_Self", "namespace": "numpy.polynomial._polybase.ABCPolyBase.fit#1", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_classmethod", "is_ready", "is_inferred"], "fullname": "numpy.polynomial._polybase.ABCPolyBase.fit", "name": "fit", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1, 1, 3, 5, 5, 5], "arg_names": [null, "x", "y", "deg", "domain", "rcond", "full", "w", "window", "symbol"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial._polybase._Self", "id": -1, "name": "_Self", "namespace": "numpy.polynomial._polybase.ABCPolyBase.fit#1", "upper_bound": "builtins.object", "values": [], "variance": 0}}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co"}, {".class": "UnionType", "items": ["builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeInt_co"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co"}], "uses_pep604_syntax": true}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._scalars._FloatLike_co"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co"}], "uses_pep604_syntax": true}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "fit of ABCPolyBase", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial._polybase._Self", "id": -1, "name": "_Self", "namespace": "numpy.polynomial._polybase.ABCPolyBase.fit#1", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": ["builtins.float", "builtins.complex"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.inexact"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int32"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial._polybase._Self", "id": -1, "name": "_Self", "namespace": "numpy.polynomial._polybase.ABCPolyBase.fit#1", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 1, 1, 1], "arg_names": [null, null, null, null, null, null, null, "w", "window", "symbol"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_overload", "is_decorated"], "fullname": "numpy.polynomial._polybase.ABCPolyBase.fit", "name": "fit", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 1, 1, 1], "arg_names": [null, null, null, null, null, null, null, "w", "window", "symbol"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial._polybase._Self", "id": -1, "name": "_Self", "namespace": "numpy.polynomial._polybase.ABCPolyBase.fit", "upper_bound": "builtins.object", "values": [], "variance": 0}}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co"}, {".class": "UnionType", "items": ["builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeInt_co"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co"}], "uses_pep604_syntax": true}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._scalars._FloatLike_co"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co"}], "uses_pep604_syntax": true}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "fit of ABCPolyBase", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial._polybase._Self", "id": -1, "name": "_Self", "namespace": "numpy.polynomial._polybase.ABCPolyBase.fit", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": ["builtins.float", "builtins.complex"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.inexact"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int32"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial._polybase._Self", "id": -1, "name": "_Self", "namespace": "numpy.polynomial._polybase.ABCPolyBase.fit", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_classmethod", "is_ready", "is_inferred"], "fullname": "numpy.polynomial._polybase.ABCPolyBase.fit", "name": "fit", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 1, 1, 1], "arg_names": [null, null, null, null, null, null, null, "w", "window", "symbol"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial._polybase._Self", "id": -1, "name": "_Self", "namespace": "numpy.polynomial._polybase.ABCPolyBase.fit", "upper_bound": "builtins.object", "values": [], "variance": 0}}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co"}, {".class": "UnionType", "items": ["builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeInt_co"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co"}], "uses_pep604_syntax": true}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._scalars._FloatLike_co"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co"}], "uses_pep604_syntax": true}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "fit of ABCPolyBase", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial._polybase._Self", "id": -1, "name": "_Self", "namespace": "numpy.polynomial._polybase.ABCPolyBase.fit", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": ["builtins.float", "builtins.complex"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.inexact"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int32"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial._polybase._Self", "id": -1, "name": "_Self", "namespace": "numpy.polynomial._polybase.ABCPolyBase.fit", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1, 1, 1, 1, 1, 1], "arg_names": [null, "x", "y", "deg", "domain", "rcond", "full", "w", "window", "symbol"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial._polybase._Self", "id": -1, "name": "_Self", "namespace": "numpy.polynomial._polybase.ABCPolyBase.fit#0", "upper_bound": "builtins.object", "values": [], "variance": 0}}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co"}, {".class": "UnionType", "items": ["builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeInt_co"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co"}], "uses_pep604_syntax": true}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._scalars._FloatLike_co"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co"}], "uses_pep604_syntax": true}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "fit of ABCPolyBase", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial._polybase._Self", "id": -1, "name": "_Self", "namespace": "numpy.polynomial._polybase.ABCPolyBase.fit#0", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial._polybase._Self", "id": -1, "name": "_Self", "namespace": "numpy.polynomial._polybase.ABCPolyBase.fit#0", "upper_bound": "builtins.object", "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1, 1, 3, 5, 5, 5], "arg_names": [null, "x", "y", "deg", "domain", "rcond", "full", "w", "window", "symbol"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial._polybase._Self", "id": -1, "name": "_Self", "namespace": "numpy.polynomial._polybase.ABCPolyBase.fit#1", "upper_bound": "builtins.object", "values": [], "variance": 0}}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co"}, {".class": "UnionType", "items": ["builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeInt_co"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co"}], "uses_pep604_syntax": true}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._scalars._FloatLike_co"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co"}], "uses_pep604_syntax": true}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "fit of ABCPolyBase", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial._polybase._Self", "id": -1, "name": "_Self", "namespace": "numpy.polynomial._polybase.ABCPolyBase.fit#1", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": ["builtins.float", "builtins.complex"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.inexact"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int32"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial._polybase._Self", "id": -1, "name": "_Self", "namespace": "numpy.polynomial._polybase.ABCPolyBase.fit#1", "upper_bound": "builtins.object", "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 1, 1, 1], "arg_names": [null, null, null, null, null, null, null, "w", "window", "symbol"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial._polybase._Self", "id": -1, "name": "_Self", "namespace": "numpy.polynomial._polybase.ABCPolyBase.fit", "upper_bound": "builtins.object", "values": [], "variance": 0}}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co"}, {".class": "UnionType", "items": ["builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeInt_co"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co"}], "uses_pep604_syntax": true}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._scalars._FloatLike_co"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co"}], "uses_pep604_syntax": true}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "fit of ABCPolyBase", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial._polybase._Self", "id": -1, "name": "_Self", "namespace": "numpy.polynomial._polybase.ABCPolyBase.fit", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": ["builtins.float", "builtins.complex"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.inexact"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int32"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial._polybase._Self", "id": -1, "name": "_Self", "namespace": "numpy.polynomial._polybase.ABCPolyBase.fit", "upper_bound": "builtins.object", "values": [], "variance": 0}]}]}}}, "fromroots": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1], "arg_names": [null, "roots", "domain", "window", "symbol"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "numpy.polynomial._polybase.ABCPolyBase.fromroots", "name": "fromroots", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1], "arg_names": [null, "roots", "domain", "window", "symbol"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial._polybase._Self", "id": -1, "name": "_Self", "namespace": "numpy.polynomial._polybase.ABCPolyBase.fromroots", "upper_bound": "builtins.object", "values": [], "variance": 0}}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._ArrayLikeCoef_co"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co"}], "uses_pep604_syntax": true}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "fromroots of ABCPolyBase", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial._polybase._Self", "id": -1, "name": "_Self", "namespace": "numpy.polynomial._polybase.ABCPolyBase.fromroots", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial._polybase._Self", "id": -1, "name": "_Self", "namespace": "numpy.polynomial._polybase.ABCPolyBase.fromroots", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "numpy.polynomial._polybase.ABCPolyBase.fromroots", "name": "fromroots", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1], "arg_names": [null, "roots", "domain", "window", "symbol"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial._polybase._Self", "id": -1, "name": "_Self", "namespace": "numpy.polynomial._polybase.ABCPolyBase.fromroots", "upper_bound": "builtins.object", "values": [], "variance": 0}}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._ArrayLikeCoef_co"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co"}], "uses_pep604_syntax": true}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "fromroots of ABCPolyBase", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial._polybase._Self", "id": -1, "name": "_Self", "namespace": "numpy.polynomial._polybase.ABCPolyBase.fromroots", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial._polybase._Self", "id": -1, "name": "_Self", "namespace": "numpy.polynomial._polybase.ABCPolyBase.fromroots", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}}, "has_samecoef": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.polynomial._polybase.ABCPolyBase.has_samecoef", "name": "has_samecoef", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, "other"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "fullname": "numpy.polynomial._polybase._NameCo", "id": 1, "name": "_NameCo", "namespace": "numpy.polynomial._polybase.ABCPolyBase", "upper_bound": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.polynomial._polybase.ABCPolyBase"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.polynomial._polybase.ABCPolyBase"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "has_samecoef of ABCPolyBase", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "has_samedomain": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.polynomial._polybase.ABCPolyBase.has_samedomain", "name": "has_samedomain", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, "other"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "fullname": "numpy.polynomial._polybase._NameCo", "id": 1, "name": "_NameCo", "namespace": "numpy.polynomial._polybase.ABCPolyBase", "upper_bound": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.polynomial._polybase.ABCPolyBase"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.polynomial._polybase.ABCPolyBase"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "has_samedomain of ABCPolyBase", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "has_sametype": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "numpy.polynomial._polybase.ABCPolyBase.has_sametype", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.polynomial._polybase.ABCPolyBase.has_sametype", "name": "has_sametype", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, "other"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial._polybase._Self", "id": -1, "name": "_Self", "namespace": "numpy.polynomial._polybase.ABCPolyBase.has_sametype#0", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.polynomial._polybase.ABCPolyBase"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "has_sametype of ABCPolyBase", "ret_type": "builtins.bool", "type_guard": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial._polybase._Self", "id": -1, "name": "_Self", "namespace": "numpy.polynomial._polybase.ABCPolyBase.has_sametype#0", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial._polybase._Self", "id": -1, "name": "_Self", "namespace": "numpy.polynomial._polybase.ABCPolyBase.has_sametype#0", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.polynomial._polybase.ABCPolyBase.has_sametype", "name": "has_sametype", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, "other"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial._polybase._Self", "id": -1, "name": "_Self", "namespace": "numpy.polynomial._polybase.ABCPolyBase.has_sametype#0", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.polynomial._polybase.ABCPolyBase"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "has_sametype of ABCPolyBase", "ret_type": "builtins.bool", "type_guard": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial._polybase._Self", "id": -1, "name": "_Self", "namespace": "numpy.polynomial._polybase.ABCPolyBase.has_sametype#0", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial._polybase._Self", "id": -1, "name": "_Self", "namespace": "numpy.polynomial._polybase.ABCPolyBase.has_sametype#0", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.polynomial._polybase.ABCPolyBase.has_sametype", "name": "has_sametype", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, "other"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "fullname": "numpy.polynomial._polybase._NameCo", "id": 1, "name": "_NameCo", "namespace": "numpy.polynomial._polybase.ABCPolyBase", "upper_bound": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.polynomial._polybase.ABCPolyBase"}, "builtins.object"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "has_sametype of ABCPolyBase", "ret_type": {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.polynomial._polybase.ABCPolyBase.has_sametype", "name": "has_sametype", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, "other"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "fullname": "numpy.polynomial._polybase._NameCo", "id": 1, "name": "_NameCo", "namespace": "numpy.polynomial._polybase.ABCPolyBase", "upper_bound": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.polynomial._polybase.ABCPolyBase"}, "builtins.object"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "has_sametype of ABCPolyBase", "ret_type": {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, "other"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial._polybase._Self", "id": -1, "name": "_Self", "namespace": "numpy.polynomial._polybase.ABCPolyBase.has_sametype#0", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.polynomial._polybase.ABCPolyBase"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "has_sametype of ABCPolyBase", "ret_type": "builtins.bool", "type_guard": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial._polybase._Self", "id": -1, "name": "_Self", "namespace": "numpy.polynomial._polybase.ABCPolyBase.has_sametype#0", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial._polybase._Self", "id": -1, "name": "_Self", "namespace": "numpy.polynomial._polybase.ABCPolyBase.has_sametype#0", "upper_bound": "builtins.object", "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, "other"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "fullname": "numpy.polynomial._polybase._NameCo", "id": 1, "name": "_NameCo", "namespace": "numpy.polynomial._polybase.ABCPolyBase", "upper_bound": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.polynomial._polybase.ABCPolyBase"}, "builtins.object"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "has_sametype of ABCPolyBase", "ret_type": {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "has_samewindow": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.polynomial._polybase.ABCPolyBase.has_samewindow", "name": "has_samewindow", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, "other"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "fullname": "numpy.polynomial._polybase._NameCo", "id": 1, "name": "_NameCo", "namespace": "numpy.polynomial._polybase.ABCPolyBase", "upper_bound": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.polynomial._polybase.ABCPolyBase"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.polynomial._polybase.ABCPolyBase"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "has_samewindow of ABCPolyBase", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "identity": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": [null, "domain", "window", "symbol"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "numpy.polynomial._polybase.ABCPolyBase.identity", "name": "identity", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": [null, "domain", "window", "symbol"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial._polybase._Self", "id": -1, "name": "_Self", "namespace": "numpy.polynomial._polybase.ABCPolyBase.identity", "upper_bound": "builtins.object", "values": [], "variance": 0}}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co"}], "uses_pep604_syntax": true}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "identity of ABCPolyBase", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial._polybase._Self", "id": -1, "name": "_Self", "namespace": "numpy.polynomial._polybase.ABCPolyBase.identity", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial._polybase._Self", "id": -1, "name": "_Self", "namespace": "numpy.polynomial._polybase.ABCPolyBase.identity", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "numpy.polynomial._polybase.ABCPolyBase.identity", "name": "identity", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": [null, "domain", "window", "symbol"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial._polybase._Self", "id": -1, "name": "_Self", "namespace": "numpy.polynomial._polybase.ABCPolyBase.identity", "upper_bound": "builtins.object", "values": [], "variance": 0}}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co"}], "uses_pep604_syntax": true}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "identity of ABCPolyBase", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial._polybase._Self", "id": -1, "name": "_Self", "namespace": "numpy.polynomial._polybase.ABCPolyBase.identity", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial._polybase._Self", "id": -1, "name": "_Self", "namespace": "numpy.polynomial._polybase.ABCPolyBase.identity", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}}, "integ": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": [null, "m", "k", "lbnd"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.polynomial._polybase.ABCPolyBase.integ", "name": "integ", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": [null, "m", "k", "lbnd"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial._polybase._Self", "id": -1, "name": "_Self", "namespace": "numpy.polynomial._polybase.ABCPolyBase.integ", "upper_bound": "builtins.object", "values": [], "variance": 0}, "typing.SupportsIndex", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._CoefLike_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._CoefLike_co"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "integ of ABCPolyBase", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial._polybase._Self", "id": -1, "name": "_Self", "namespace": "numpy.polynomial._polybase.ABCPolyBase.integ", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial._polybase._Self", "id": -1, "name": "_Self", "namespace": "numpy.polynomial._polybase.ABCPolyBase.integ", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "linspace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": [null, "n", "domain"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.polynomial._polybase.ABCPolyBase.linspace", "name": "linspace", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": [null, "n", "domain"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "fullname": "numpy.polynomial._polybase._NameCo", "id": 1, "name": "_NameCo", "namespace": "numpy.polynomial._polybase.ABCPolyBase", "upper_bound": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.polynomial._polybase.ABCPolyBase"}, "typing.SupportsIndex", {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "linspace of ABCPolyBase", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [{".class": "UnionType", "items": ["numpy.float64", "numpy.complex128"], "uses_pep604_syntax": true}], "type_ref": "numpy.polynomial._polytypes._Series"}], "type_ref": "numpy.polynomial._polytypes._Tuple2"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "mapparms": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.polynomial._polybase.ABCPolyBase.mapparms", "name": "mapparms", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "fullname": "numpy.polynomial._polybase._NameCo", "id": 1, "name": "_NameCo", "namespace": "numpy.polynomial._polybase.ABCPolyBase", "upper_bound": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.polynomial._polybase.ABCPolyBase"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "mapparms of ABCPolyBase", "ret_type": {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy.polynomial._polytypes._Tuple2"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "maxpower": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready"], "fullname": "numpy.polynomial._polybase.ABCPolyBase.maxpower", "name": "maxpower", "type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polybase._Hundred"}}}, "roots": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.polynomial._polybase.ABCPolyBase.roots", "name": "roots", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "fullname": "numpy.polynomial._polybase._NameCo", "id": 1, "name": "_NameCo", "namespace": "numpy.polynomial._polybase.ABCPolyBase", "upper_bound": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.polynomial._polybase.ABCPolyBase"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "roots of ABCPolyBase", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._CoefSeries"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "symbol": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "numpy.polynomial._polybase.ABCPolyBase.symbol", "name": "symbol", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "fullname": "numpy.polynomial._polybase._NameCo", "id": 1, "name": "_NameCo", "namespace": "numpy.polynomial._polybase.ABCPolyBase", "upper_bound": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.polynomial._polybase.ABCPolyBase"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "symbol of ABCPolyBase", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "numpy.polynomial._polybase.ABCPolyBase.symbol", "name": "symbol", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "fullname": "numpy.polynomial._polybase._NameCo", "id": 1, "name": "_NameCo", "namespace": "numpy.polynomial._polybase.ABCPolyBase", "upper_bound": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.polynomial._polybase.ABCPolyBase"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "symbol of ABCPolyBase", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "trim": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": [null, "tol"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.polynomial._polybase.ABCPolyBase.trim", "name": "trim", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": [null, "tol"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial._polybase._Self", "id": -1, "name": "_Self", "namespace": "numpy.polynomial._polybase.ABCPolyBase.trim", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._scalars._FloatLike_co"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "trim of ABCPolyBase", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial._polybase._Self", "id": -1, "name": "_Self", "namespace": "numpy.polynomial._polybase.ABCPolyBase.trim", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial._polybase._Self", "id": -1, "name": "_Self", "namespace": "numpy.polynomial._polybase.ABCPolyBase.trim", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "truncate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, "size"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.polynomial._polybase.ABCPolyBase.truncate", "name": "truncate", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, "size"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial._polybase._Self", "id": -1, "name": "_Self", "namespace": "numpy.polynomial._polybase.ABCPolyBase.truncate", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._AnyInt"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "truncate of ABCPolyBase", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial._polybase._Self", "id": -1, "name": "_Self", "namespace": "numpy.polynomial._polybase.ABCPolyBase.truncate", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial._polybase._Self", "id": -1, "name": "_Self", "namespace": "numpy.polynomial._polybase.ABCPolyBase.truncate", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "window": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "numpy.polynomial._polybase.ABCPolyBase.window", "name": "window", "type": {".class": "TypeAliasType", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": ["builtins.float", "builtins.complex"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.inexact"}, "numpy.object_"], "uses_pep604_syntax": true}], "type_ref": "numpy.polynomial._polytypes._Array2"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial._polybase.ABCPolyBase.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "fullname": "numpy.polynomial._polybase._NameCo", "id": 1, "name": "_NameCo", "namespace": "numpy.polynomial._polybase.ABCPolyBase", "upper_bound": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.polynomial._polybase.ABCPolyBase"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["_NameCo"], "typeddict_type": null}}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ClassVar": {".class": "SymbolTableNode", "cross_ref": "typing.ClassVar", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Final": {".class": "SymbolTableNode", "cross_ref": "typing.Final", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Generic": {".class": "SymbolTableNode", "cross_ref": "typing.Generic", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Iterator": {".class": "SymbolTableNode", "cross_ref": "typing.Iterator", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing.Literal", "kind": "Gdef", "module_hidden": true, "module_public": false}, "LiteralString": {".class": "SymbolTableNode", "cross_ref": "typing.LiteralString", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Mapping": {".class": "SymbolTableNode", "cross_ref": "typing.Mapping", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef", "module_hidden": true, "module_public": false}, "SupportsIndex": {".class": "SymbolTableNode", "cross_ref": "typing.SupportsIndex", "kind": "Gdef", "module_hidden": true, "module_public": false}, "TypeAlias": {".class": "SymbolTableNode", "cross_ref": "typing.TypeAlias", "kind": "Gdef", "module_hidden": true, "module_public": false}, "TypeGuard": {".class": "SymbolTableNode", "cross_ref": "typing.TypeGuard", "kind": "Gdef", "module_hidden": true, "module_public": false}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.TypeVar", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_AnyInt": {".class": "SymbolTableNode", "cross_ref": "numpy.polynomial._polytypes._AnyInt", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_AnyOther": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "numpy.polynomial._polybase._AnyOther", "line": 54, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.polynomial._polybase.ABCPolyBase"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._CoefLike_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co"}], "uses_pep604_syntax": true}}}, "_Array2": {".class": "SymbolTableNode", "cross_ref": "numpy.polynomial._polytypes._Array2", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_ArrayLikeCoefObject_co": {".class": "SymbolTableNode", "cross_ref": "numpy.polynomial._polytypes._ArrayLikeCoefObject_co", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_ArrayLikeCoef_co": {".class": "SymbolTableNode", "cross_ref": "numpy.polynomial._polytypes._ArrayLikeCoef_co", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_ArrayLikeComplex_co": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._array_like._ArrayLikeComplex_co", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_ArrayLikeFloat_co": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._array_like._ArrayLikeFloat_co", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_CoefLike_co": {".class": "SymbolTableNode", "cross_ref": "numpy.polynomial._polytypes._CoefLike_co", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_CoefSeries": {".class": "SymbolTableNode", "cross_ref": "numpy.polynomial._polytypes._CoefSeries", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_FloatLike_co": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._scalars._FloatLike_co", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_Hundred": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "numpy.polynomial._polybase._Hundred", "line": 55, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "LiteralType", "fallback": "builtins.int", "value": 100}}}, "_NameCo": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeVarExpr", "default": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "fullname": "numpy.polynomial._polybase._NameCo", "name": "_NameCo", "upper_bound": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "values": [], "variance": 1}}, "_NumberLike_co": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._scalars._NumberLike_co", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_Other": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial._polybase._Other", "name": "_Other", "upper_bound": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.polynomial._polybase.ABCPolyBase"}, "values": [], "variance": 0}}, "_Self": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial._polybase._Self", "name": "_Self", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "_Series": {".class": "SymbolTableNode", "cross_ref": "numpy.polynomial._polytypes._Series", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_SeriesLikeCoef_co": {".class": "SymbolTableNode", "cross_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_SeriesLikeInt_co": {".class": "SymbolTableNode", "cross_ref": "numpy.polynomial._polytypes._SeriesLikeInt_co", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_Tuple2": {".class": "SymbolTableNode", "cross_ref": "numpy.polynomial._polytypes._Tuple2", "kind": "Gdef", "module_hidden": true, "module_public": false}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_final", "is_ready", "has_explicit_value"], "fullname": "numpy.polynomial._polybase.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial._polybase.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial._polybase.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial._polybase.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial._polybase.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial._polybase.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial._polybase.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "abc": {".class": "SymbolTableNode", "cross_ref": "abc", "kind": "Gdef", "module_hidden": true, "module_public": false}, "decimal": {".class": "SymbolTableNode", "cross_ref": "decimal", "kind": "Gdef", "module_hidden": true, "module_public": false}, "np": {".class": "SymbolTableNode", "cross_ref": "numpy", "kind": "Gdef", "module_hidden": true, "module_public": false}, "npt": {".class": "SymbolTableNode", "cross_ref": "numpy.typing", "kind": "Gdef", "module_hidden": true, "module_public": false}, "numbers": {".class": "SymbolTableNode", "cross_ref": "numbers", "kind": "Gdef", "module_hidden": true, "module_public": false}, "overload": {".class": "SymbolTableNode", "cross_ref": "typing.overload", "kind": "Gdef", "module_hidden": true, "module_public": false}}, "path": "c:\\Python313\\Lib\\site-packages\\numpy\\polynomial\\_polybase.pyi"}