# 🎮 斗地主AI助手

一个基于计算机视觉和人工智能的斗地主游戏辅助工具，能够实时识别游戏画面并提供智能策略建议。

## ✨ 主要功能

- 🖼️ **实时画面识别**: 自动捕获和分析游戏窗口
- 🃏 **智能卡牌识别**: 支持多种OCR引擎，准确识别手牌和桌面卡牌
- 🧠 **AI策略建议**: 基于游戏状态提供最优出牌建议
- 📊 **游戏分析**: 实时分析游戏局势和胜率
- 🎯 **多引擎支持**: 集成PaddleOCR、EasyOCR等多种识别引擎
- 🎨 **现代化界面**: 基于Flet的美观用户界面
- ⚙️ **灵活配置**: 丰富的设置选项和参数调整

## 🚀 技术栈 (2025最新)

### 核心框架
- **GUI**: Flet (基于Flutter的现代Python GUI)
- **OCR**: Mistral OCR + H2OVL-Mississippi + PaddleOCR
- **计算机视觉**: OpenCV + YOLOv9
- **AI模型**: Transformers (Hugging Face)

### 主要依赖
- Python 3.11+
- Flet 0.24+
- OpenCV 4.9+
- Transformers 4.40+
- PaddleOCR 2.8+
- Ultralytics 8.2+

## 📦 安装

### 1. 克隆项目
```bash
git clone <repository-url>
cd landlord-assistant-2025
```

### 2. 创建虚拟环境
```bash
python -m venv venv
venv\Scripts\activate  # Windows
```

### 3. 安装依赖 (使用国内镜像源)
```bash
# 使用清华大学镜像源
pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/

# 或使用阿里云镜像源
pip install -r requirements.txt -i https://mirrors.aliyun.com/pypi/simple/

# 或使用豆瓣镜像源
pip install -r requirements.txt -i https://pypi.douban.com/simple/
```

### 4. 运行程序
```bash
python src/main.py
```

## 🎮 使用方法

1. **启动程序** - 运行主程序
2. **选择游戏窗口** - 点击"选择窗口"按钮，选择斗地主游戏窗口
3. **配置识别区域** - 调整手牌、已出牌等区域的识别范围
4. **开始监控** - 程序将实时分析游戏状态并提供建议

## 📁 项目结构

```
landlord_assistant_2025/
├── src/                     # 源代码
│   ├── app.py              # Flet主应用
│   ├── main.py             # 程序入口
│   ├── models/             # 数据模型
│   ├── services/           # 业务逻辑服务
│   ├── ui/                 # 用户界面组件
│   └── utils/              # 工具函数
├── assets/                 # 资源文件
│   ├── models/             # AI模型文件
│   ├── templates/          # 卡牌模板
│   └── icons/              # 图标资源
├── tests/                  # 测试文件
├── pyproject.toml          # 项目配置
├── requirements.txt        # 依赖清单
└── README.md              # 项目说明
```

## ⚙️ 配置

### 镜像源配置
如果下载速度慢，可以配置pip使用国内镜像源：

```bash
# 临时使用
pip install <package> -i https://pypi.tuna.tsinghua.edu.cn/simple/

# 永久配置
pip config set global.index-url https://pypi.tuna.tsinghua.edu.cn/simple/
```

### GPU加速 (可选)
如果有NVIDIA GPU，可以安装CUDA版本的PyTorch：
```bash
pip install torch torchvision --index-url https://download.pytorch.org/whl/cu118
```

## 🔧 开发

### 安装开发依赖
```bash
pip install -e ".[dev]"
```

### 代码格式化
```bash
black src/
isort src/
```

### 运行测试
```bash
pytest
```

## 📝 许可证

MIT License

## ⚠️ 免责声明

本软件仅供学习和研究使用，请遵守相关游戏平台的使用条款。

## 🤝 贡献

欢迎提交Issue和Pull Request！

---

**注意**: 请确保在合法合规的前提下使用本软件。