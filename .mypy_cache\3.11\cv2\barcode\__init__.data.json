{".class": "MypyFile", "_fullname": "cv2.barcode", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "BarcodeDetector": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["cv2.GraphicalCodeDetector"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "cv2.barcode.BarcodeDetector", "name": "BarcodeDetector", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "cv2.barcode.BarcodeDetector", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "cv2.barcode", "mro": ["cv2.barcode.BarcodeDetector", "cv2.GraphicalCodeDetector", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "cv2.barcode.BarcodeDetector.__init__", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.barcode.BarcodeDetector.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cv2.barcode.BarcodeDetector"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of BarcodeDetector", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.barcode.BarcodeDetector.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cv2.barcode.BarcodeDetector"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of BarcodeDetector", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "prototxt_path", "model_path"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.barcode.BarcodeDetector.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "prototxt_path", "model_path"], "arg_types": ["cv2.barcode.BarcodeDetector", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of BarcodeDetector", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.barcode.BarcodeDetector.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "prototxt_path", "model_path"], "arg_types": ["cv2.barcode.BarcodeDetector", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of BarcodeDetector", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cv2.barcode.BarcodeDetector"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of BarcodeDetector", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "prototxt_path", "model_path"], "arg_types": ["cv2.barcode.BarcodeDetector", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of BarcodeDetector", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "decodeWithType": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "cv2.barcode.BarcodeDetector.decodeWithType", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "img", "points"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.barcode.BarcodeDetector.decodeWithType", "name": "decodeWithType", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "img", "points"], "arg_types": ["cv2.barcode.BarcodeDetector", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "decodeWithType of BarcodeDetector", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.bool", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.barcode.BarcodeDetector.decodeWithType", "name": "decodeWithType", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "img", "points"], "arg_types": ["cv2.barcode.BarcodeDetector", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "decodeWithType of BarcodeDetector", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.bool", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "img", "points"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.barcode.BarcodeDetector.decodeWithType", "name": "decodeWithType", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "img", "points"], "arg_types": ["cv2.barcode.BarcodeDetector", "cv2.UMat", "cv2.UMat"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "decodeWithType of BarcodeDetector", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.bool", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.barcode.BarcodeDetector.decodeWithType", "name": "decodeWithType", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "img", "points"], "arg_types": ["cv2.barcode.BarcodeDetector", "cv2.UMat", "cv2.UMat"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "decodeWithType of BarcodeDetector", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.bool", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "img", "points"], "arg_types": ["cv2.barcode.BarcodeDetector", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "decodeWithType of BarcodeDetector", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.bool", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "img", "points"], "arg_types": ["cv2.barcode.BarcodeDetector", "cv2.UMat", "cv2.UMat"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "decodeWithType of BarcodeDetector", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.bool", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "detectAndDecodeWithType": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "cv2.barcode.BarcodeDetector.detectAndDecodeWithType", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "img", "points"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.barcode.BarcodeDetector.detectAndDecodeWithType", "name": "detectAndDecodeWithType", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "img", "points"], "arg_types": ["cv2.barcode.BarcodeDetector", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "detectAndDecodeWithType of BarcodeDetector", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.bool", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.barcode.BarcodeDetector.detectAndDecodeWithType", "name": "detectAndDecodeWithType", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "img", "points"], "arg_types": ["cv2.barcode.BarcodeDetector", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "detectAndDecodeWithType of BarcodeDetector", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.bool", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "img", "points"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.barcode.BarcodeDetector.detectAndDecodeWithType", "name": "detectAndDecodeWithType", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "img", "points"], "arg_types": ["cv2.barcode.BarcodeDetector", "cv2.UMat", {".class": "UnionType", "items": ["cv2.UMat", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "detectAndDecodeWithType of BarcodeDetector", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.bool", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, "cv2.UMat"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.barcode.BarcodeDetector.detectAndDecodeWithType", "name": "detectAndDecodeWithType", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "img", "points"], "arg_types": ["cv2.barcode.BarcodeDetector", "cv2.UMat", {".class": "UnionType", "items": ["cv2.UMat", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "detectAndDecodeWithType of BarcodeDetector", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.bool", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, "cv2.UMat"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "img", "points"], "arg_types": ["cv2.barcode.BarcodeDetector", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "detectAndDecodeWithType of BarcodeDetector", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.bool", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "img", "points"], "arg_types": ["cv2.barcode.BarcodeDetector", "cv2.UMat", {".class": "UnionType", "items": ["cv2.UMat", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "detectAndDecodeWithType of BarcodeDetector", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.bool", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, "cv2.UMat"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "getDetectorScales": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.barcode.BarcodeDetector.getDetectorScales", "name": "getDetectorScales", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cv2.barcode.BarcodeDetector"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getDetectorScales of BarcodeDetector", "ret_type": {".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "typing.Sequence"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "getDownsamplingThreshold": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.barcode.BarcodeDetector.getDownsamplingThreshold", "name": "getDownsamplingThreshold", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cv2.barcode.BarcodeDetector"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getDownsamplingThreshold of BarcodeDetector", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "getGradientThreshold": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.barcode.BarcodeDetector.getGradientThreshold", "name": "getGradientThreshold", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cv2.barcode.BarcodeDetector"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getGradientThreshold of BarcodeDetector", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "setDetectorScales": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "sizes"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.barcode.BarcodeDetector.setDetectorScales", "name": "setDetectorScales", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "sizes"], "arg_types": ["cv2.barcode.BarcodeDetector", {".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "setDetectorScales of BarcodeDetector", "ret_type": "cv2.barcode.BarcodeDetector", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "setDownsamplingThreshold": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "thresh"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.barcode.BarcodeDetector.setDownsamplingThreshold", "name": "setDownsamplingThreshold", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "thresh"], "arg_types": ["cv2.barcode.BarcodeDetector", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "setDownsamplingThreshold of BarcodeDetector", "ret_type": "cv2.barcode.BarcodeDetector", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "setGradientThreshold": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "thresh"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.barcode.BarcodeDetector.setGradientThreshold", "name": "setGradientThreshold", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "thresh"], "arg_types": ["cv2.barcode.BarcodeDetector", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "setGradientThreshold of BarcodeDetector", "ret_type": "cv2.barcode.BarcodeDetector", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cv2.barcode.BarcodeDetector.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cv2.barcode.BarcodeDetector", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "cv2.barcode.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.barcode.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.barcode.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.barcode.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.barcode.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.barcode.__package__", "name": "__package__", "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.barcode.__path__", "name": "__path__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.barcode.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_typing": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef", "module_hidden": true, "module_public": false}, "cv2": {".class": "SymbolTableNode", "cross_ref": "cv2", "kind": "Gdef", "module_hidden": true, "module_public": false}}, "path": "c:\\Python313\\Lib\\site-packages\\cv2\\barcode\\__init__.pyi"}