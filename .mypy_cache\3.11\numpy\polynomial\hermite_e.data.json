{".class": "MypyFile", "_fullname": "numpy.polynomial.hermite_e", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "ABCPolyBase": {".class": "SymbolTableNode", "cross_ref": "numpy.polynomial._polybase.ABCPolyBase", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Final": {".class": "SymbolTableNode", "cross_ref": "typing.Final", "kind": "Gdef", "module_hidden": true, "module_public": false}, "HermiteE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "He"}], "extra_attrs": null, "type_ref": "numpy.polynomial._polybase.ABCPolyBase"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy.polynomial.hermite_e.<PERSON>", "name": "HermiteE", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "numpy.polynomial.hermite_e.<PERSON>", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "numpy.polynomial.hermite_e", "mro": ["numpy.polynomial.hermite_e.<PERSON>", "numpy.polynomial._polybase.ABCPolyBase", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial.hermite_e.Her<PERSON>.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "numpy.polynomial.hermite_e.<PERSON>", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "L": {".class": "SymbolTableNode", "cross_ref": "typing.Literal", "kind": "Gdef", "module_hidden": true, "module_public": false}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_Array1": {".class": "SymbolTableNode", "cross_ref": "numpy.polynomial._polytypes._Array1", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_Array2": {".class": "SymbolTableNode", "cross_ref": "numpy.polynomial._polytypes._Array2", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_FuncBinOp": {".class": "SymbolTableNode", "cross_ref": "numpy.polynomial._polytypes._FuncBinOp", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_FuncCompanion": {".class": "SymbolTableNode", "cross_ref": "numpy.polynomial._polytypes._FuncCompanion", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_FuncDer": {".class": "SymbolTableNode", "cross_ref": "numpy.polynomial._polytypes._FuncDer", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_FuncFit": {".class": "SymbolTableNode", "cross_ref": "numpy.polynomial._polytypes._FuncFit", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_FuncFromRoots": {".class": "SymbolTableNode", "cross_ref": "numpy.polynomial._polytypes._FuncFromRoots", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_FuncGauss": {".class": "SymbolTableNode", "cross_ref": "numpy.polynomial._polytypes._FuncGauss", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_FuncInteg": {".class": "SymbolTableNode", "cross_ref": "numpy.polynomial._polytypes._FuncInteg", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_FuncLine": {".class": "SymbolTableNode", "cross_ref": "numpy.polynomial._polytypes._FuncLine", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_FuncPoly2Ortho": {".class": "SymbolTableNode", "cross_ref": "numpy.polynomial._polytypes._FuncPoly2Ortho", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_FuncPow": {".class": "SymbolTableNode", "cross_ref": "numpy.polynomial._polytypes._FuncPow", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_FuncRoots": {".class": "SymbolTableNode", "cross_ref": "numpy.polynomial._polytypes._FuncRoots", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_FuncUnOp": {".class": "SymbolTableNode", "cross_ref": "numpy.polynomial._polytypes._FuncUnOp", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_FuncVal": {".class": "SymbolTableNode", "cross_ref": "numpy.polynomial._polytypes._FuncVal", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_FuncVal2D": {".class": "SymbolTableNode", "cross_ref": "numpy.polynomial._polytypes._FuncVal2D", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_FuncVal3D": {".class": "SymbolTableNode", "cross_ref": "numpy.polynomial._polytypes._FuncVal3D", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_FuncValFromRoots": {".class": "SymbolTableNode", "cross_ref": "numpy.polynomial._polytypes._FuncValFromRoots", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_FuncVander": {".class": "SymbolTableNode", "cross_ref": "numpy.polynomial._polytypes._FuncVander", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_FuncVander2D": {".class": "SymbolTableNode", "cross_ref": "numpy.polynomial._polytypes._FuncVander2D", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_FuncVander3D": {".class": "SymbolTableNode", "cross_ref": "numpy.polynomial._polytypes._FuncVander3D", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_FuncWeight": {".class": "SymbolTableNode", "cross_ref": "numpy.polynomial._polytypes._FuncWeight", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_ND": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial.hermite_e._ND", "name": "_ND", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "numpy.polynomial.hermite_e.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.hermite_e.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.hermite_e.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.hermite_e.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.hermite_e.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.hermite_e.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.hermite_e.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_normed_hermite_e_n": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["x", "n"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.polynomial.hermite_e._normed_hermite_e_n", "name": "_normed_hermite_e_n", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["x", "n"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial.hermite_e._ND", "id": -1, "name": "_ND", "namespace": "numpy.polynomial.hermite_e._normed_hermite_e_n", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "Instance", "args": ["numpy.float64"], "extra_attrs": null, "type_ref": "numpy.dtype"}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}, {".class": "UnionType", "items": ["builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_normed_hermite_e_n", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial.hermite_e._ND", "id": -1, "name": "_ND", "namespace": "numpy.polynomial.hermite_e._normed_hermite_e_n", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "Instance", "args": ["numpy.float64"], "extra_attrs": null, "type_ref": "numpy.dtype"}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial.hermite_e._ND", "id": -1, "name": "_ND", "namespace": "numpy.polynomial.hermite_e._normed_hermite_e_n", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}]}}}, "herme2poly": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.hermite_e.herme2poly", "name": "herme2poly", "type": {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "herme2poly"}], "extra_attrs": null, "type_ref": "numpy.polynomial._polytypes._FuncUnOp"}}}, "hermeadd": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.hermite_e.hermeadd", "name": "hermeadd", "type": {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "hermeadd"}], "extra_attrs": null, "type_ref": "numpy.polynomial._polytypes._FuncBinOp"}}}, "hermecompanion": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.hermite_e.hermecompanion", "name": "hermecompanion", "type": {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "hermecompanion"}], "extra_attrs": null, "type_ref": "numpy.polynomial._polytypes._FuncCompanion"}}}, "hermeder": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.her<PERSON>_<PERSON>.her<PERSON>er", "name": "her<PERSON><PERSON>", "type": {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "her<PERSON><PERSON>"}], "extra_attrs": null, "type_ref": "numpy.polynomial._polytypes._FuncDer"}}}, "hermediv": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.hermite_e.hermediv", "name": "her<PERSON><PERSON>", "type": {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "her<PERSON><PERSON>"}], "extra_attrs": null, "type_ref": "numpy.polynomial._polytypes._FuncBinOp"}}}, "hermedomain": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "numpy.polynomial.hermite_e.hermedomain", "name": "hermedomain", "type": {".class": "TypeAliasType", "args": ["numpy.float64"], "type_ref": "numpy.polynomial._polytypes._Array2"}}}, "hermefit": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.hermite_e.hermefit", "name": "hermefit", "type": {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "hermefit"}], "extra_attrs": null, "type_ref": "numpy.polynomial._polytypes._FuncFit"}}}, "hermefromroots": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.hermite_e.hermefrom<PERSON>", "name": "hermefromroots", "type": {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "hermefromroots"}], "extra_attrs": null, "type_ref": "numpy.polynomial._polytypes._FuncFromRoots"}}}, "hermegauss": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.hermite_e.hermegauss", "name": "hermegauss", "type": {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "hermegauss"}], "extra_attrs": null, "type_ref": "numpy.polynomial._polytypes._FuncGauss"}}}, "hermegrid2d": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.hermite_e.hermegrid2d", "name": "hermegrid2d", "type": {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "hermegrid2d"}], "extra_attrs": null, "type_ref": "numpy.polynomial._polytypes._FuncVal2D"}}}, "hermegrid3d": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.hermite_e.hermegrid3d", "name": "hermegrid3d", "type": {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "hermegrid3d"}], "extra_attrs": null, "type_ref": "numpy.polynomial._polytypes._FuncVal3D"}}}, "hermeint": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.hermite_e.hermeint", "name": "hermeint", "type": {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "hermeint"}], "extra_attrs": null, "type_ref": "numpy.polynomial._polytypes._FuncInteg"}}}, "hermeline": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.hermite_e.hermeline", "name": "hermeline", "type": {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "hermeline"}], "extra_attrs": null, "type_ref": "numpy.polynomial._polytypes._FuncLine"}}}, "hermemul": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.hermite_e.hermemul", "name": "hermemul", "type": {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "hermemul"}], "extra_attrs": null, "type_ref": "numpy.polynomial._polytypes._FuncBinOp"}}}, "hermemulx": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.hermite_e.hermemulx", "name": "hermemulx", "type": {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "hermemulx"}], "extra_attrs": null, "type_ref": "numpy.polynomial._polytypes._FuncUnOp"}}}, "hermeone": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "numpy.polynomial.hermite_e.hermeone", "name": "hermeone", "type": {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.int_"}], "type_ref": "numpy.polynomial._polytypes._Array1"}}}, "hermepow": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.hermite_e.her<PERSON>w", "name": "her<PERSON><PERSON>w", "type": {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "her<PERSON><PERSON>w"}], "extra_attrs": null, "type_ref": "numpy.polynomial._polytypes._FuncPow"}}}, "hermeroots": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.hermite_e.hermer<PERSON>s", "name": "<PERSON><PERSON><PERSON>s", "type": {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "<PERSON><PERSON><PERSON>s"}], "extra_attrs": null, "type_ref": "numpy.polynomial._polytypes._FuncRoots"}}}, "hermesub": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.hermite_e.hermesub", "name": "hermes<PERSON>", "type": {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "hermes<PERSON>"}], "extra_attrs": null, "type_ref": "numpy.polynomial._polytypes._FuncBinOp"}}}, "hermetrim": {".class": "SymbolTableNode", "cross_ref": "numpy.polynomial.polyutils.trimcoef", "kind": "Gdef"}, "hermeval": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.hermite_e.hermeval", "name": "hermeval", "type": {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "hermeval"}], "extra_attrs": null, "type_ref": "numpy.polynomial._polytypes._FuncVal"}}}, "hermeval2d": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.hermite_e.hermeval2d", "name": "hermeval2d", "type": {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "hermeval2d"}], "extra_attrs": null, "type_ref": "numpy.polynomial._polytypes._FuncVal2D"}}}, "hermeval3d": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.hermite_e.hermeval3d", "name": "hermeval3d", "type": {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "hermeval3d"}], "extra_attrs": null, "type_ref": "numpy.polynomial._polytypes._FuncVal3D"}}}, "hermevalfromroots": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.hermite_e.herme<PERSON>from<PERSON>", "name": "her<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "her<PERSON><PERSON><PERSON><PERSON><PERSON>"}], "extra_attrs": null, "type_ref": "numpy.polynomial._polytypes._FuncValFromRoots"}}}, "hermevander": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.hermite_e.her<PERSON>", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "<PERSON><PERSON><PERSON><PERSON>"}], "extra_attrs": null, "type_ref": "numpy.polynomial._polytypes._FuncVander"}}}, "hermevander2d": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.hermite_e.hermevander2d", "name": "hermevander2d", "type": {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "hermevander2d"}], "extra_attrs": null, "type_ref": "numpy.polynomial._polytypes._FuncVander2D"}}}, "hermevander3d": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.hermite_e.herme<PERSON>der3d", "name": "hermevander3d", "type": {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "hermevander3d"}], "extra_attrs": null, "type_ref": "numpy.polynomial._polytypes._FuncVander3D"}}}, "hermeweight": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.hermite_e.hermeweight", "name": "hermeweight", "type": {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "hermeweight"}], "extra_attrs": null, "type_ref": "numpy.polynomial._polytypes._FuncWeight"}}}, "hermex": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "numpy.polynomial.hermite_e.hermex", "name": "hermex", "type": {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.int_"}], "type_ref": "numpy.polynomial._polytypes._Array2"}}}, "hermezero": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "numpy.polynomial.hermite_e.hermezero", "name": "hermezero", "type": {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.int_"}], "type_ref": "numpy.polynomial._polytypes._Array1"}}}, "np": {".class": "SymbolTableNode", "cross_ref": "numpy", "kind": "Gdef", "module_hidden": true, "module_public": false}, "poly2herme": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.hermite_e.poly2herme", "name": "poly2herme", "type": {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "poly2herme"}], "extra_attrs": null, "type_ref": "numpy.polynomial._polytypes._FuncPoly2Ortho"}}}}, "path": "c:\\Python313\\Lib\\site-packages\\numpy\\polynomial\\hermite_e.pyi"}