{"data_mtime": 1750100861, "dep_lines": [34, 49, 50, 51, 72, 83, 86, 87, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 30, 32, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 25, 25, 10, 10, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 30, 30, 30, 30], "dependencies": ["pydantic.v1.fields", "pydantic.v1.json", "pydantic.v1.networks", "pydantic.v1.types", "pydantic.v1.typing", "pydantic.v1.utils", "pydantic.v1.dataclasses", "pydantic.v1.main", "re", "warnings", "collections", "dataclasses", "datetime", "decimal", "enum", "ipaddress", "pathlib", "typing", "uuid", "typing_extensions", "builtins", "_frozen_importlib", "_typeshed", "abc", "os"], "hash": "0799a57e5d15899fc450de7a1290379d94cdc9db", "id": "pydantic.v1.schema", "ignore_all": true, "interface_hash": "0bef00c3adbd8056bdebc3b81b388026ad5a352e", "mtime": 1748795478, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Python313\\Lib\\site-packages\\pydantic\\v1\\schema.py", "plugin_data": null, "size": 47801, "suppressed": [], "version_id": "1.15.0"}