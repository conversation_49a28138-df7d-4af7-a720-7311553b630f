{".class": "MypyFile", "_fullname": "numpy.polynomial.polyutils", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Final": {".class": "SymbolTableNode", "cross_ref": "typing.Final", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing.Literal", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef", "module_hidden": true, "module_public": false}, "SupportsIndex": {".class": "SymbolTableNode", "cross_ref": "typing.SupportsIndex", "kind": "Gdef", "module_hidden": true, "module_public": false}, "TypeAlias": {".class": "SymbolTableNode", "cross_ref": "typing.TypeAlias", "kind": "Gdef", "module_hidden": true, "module_public": false}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_AnyInt": {".class": "SymbolTableNode", "cross_ref": "numpy.polynomial._polytypes._AnyInt", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_AnyLineF": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "numpy.polynomial.polyutils._AnyLineF", "line": 61, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._CoefLike_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._CoefLike_co"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._CoefArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_AnyMulF": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "numpy.polynomial.polyutils._AnyMulF", "line": 65, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._CoefArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_AnyVanderF": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "numpy.polynomial.polyutils._AnyVanderF", "line": 69, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, "typing.SupportsIndex"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._CoefArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_Array2": {".class": "SymbolTableNode", "cross_ref": "numpy.polynomial._polytypes._Array2", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_ArrayLikeCoef_co": {".class": "SymbolTableNode", "cross_ref": "numpy.polynomial._polytypes._ArrayLikeCoef_co", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_ArrayLikeComplex_co": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._array_like._ArrayLikeComplex_co", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_ArrayLikeFloat_co": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._array_like._ArrayLikeFloat_co", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_CoefArray": {".class": "SymbolTableNode", "cross_ref": "numpy.polynomial._polytypes._CoefArray", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_CoefLike_co": {".class": "SymbolTableNode", "cross_ref": "numpy.polynomial._polytypes._CoefLike_co", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_CoefSeries": {".class": "SymbolTableNode", "cross_ref": "numpy.polynomial._polytypes._CoefSeries", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_ComplexArray": {".class": "SymbolTableNode", "cross_ref": "numpy.polynomial._polytypes._ComplexArray", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_ComplexSeries": {".class": "SymbolTableNode", "cross_ref": "numpy.polynomial._polytypes._ComplexSeries", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_FloatArray": {".class": "SymbolTableNode", "cross_ref": "numpy.polynomial._polytypes._FloatArray", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_FloatLike_co": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._scalars._FloatLike_co", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_FloatSeries": {".class": "SymbolTableNode", "cross_ref": "numpy.polynomial._polytypes._FloatSeries", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_FuncBinOp": {".class": "SymbolTableNode", "cross_ref": "numpy.polynomial._polytypes._FuncBinOp", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_FuncValND": {".class": "SymbolTableNode", "cross_ref": "numpy.polynomial._polytypes._FuncValND", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_FuncVanderND": {".class": "SymbolTableNode", "cross_ref": "numpy.polynomial._polytypes._FuncVanderND", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_NumberLike_co": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._scalars._NumberLike_co", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_ObjectArray": {".class": "SymbolTableNode", "cross_ref": "numpy.polynomial._polytypes._ObjectArray", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_ObjectSeries": {".class": "SymbolTableNode", "cross_ref": "numpy.polynomial._polytypes._ObjectSeries", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_SeriesLikeCoef_co": {".class": "SymbolTableNode", "cross_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_SeriesLikeComplex_co": {".class": "SymbolTableNode", "cross_ref": "numpy.polynomial._polytypes._SeriesLikeComplex_co", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_SeriesLikeFloat_co": {".class": "SymbolTableNode", "cross_ref": "numpy.polynomial._polytypes._SeriesLikeFloat_co", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_SeriesLikeInt_co": {".class": "SymbolTableNode", "cross_ref": "numpy.polynomial._polytypes._SeriesLikeInt_co", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_T_seq": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial.polyutils._T_seq", "name": "_T_seq", "upper_bound": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._CoefArray"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._CoefLike_co"}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "uses_pep604_syntax": true}, "values": [], "variance": 0}}, "_Tuple2": {".class": "SymbolTableNode", "cross_ref": "numpy.polynomial._polytypes._Tuple2", "kind": "Gdef", "module_hidden": true, "module_public": false}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_final", "is_ready", "has_explicit_value"], "fullname": "numpy.polynomial.polyutils.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.polyutils.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.polyutils.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.polyutils.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.polyutils.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.polyutils.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.polyutils.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_add": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "numpy.polynomial.polyutils._add", "name": "_add", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "numpy.polynomial._polytypes._FuncBinOp"}}}, "_as_int": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["x", "desc"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.polynomial.polyutils._as_int", "name": "_as_int", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["x", "desc"], "arg_types": ["typing.SupportsIndex", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_as_int", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_div": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "numpy.polynomial.polyutils._div", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["mul_f", "c1", "c2"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.polynomial.polyutils._div", "name": "_div", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["mul_f", "c1", "c2"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial.polyutils._AnyMulF"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeFloat_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeFloat_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_div", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._FloatSeries"}], "type_ref": "numpy.polynomial._polytypes._Tuple2"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.polynomial.polyutils._div", "name": "_div", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["mul_f", "c1", "c2"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial.polyutils._AnyMulF"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeFloat_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeFloat_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_div", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._FloatSeries"}], "type_ref": "numpy.polynomial._polytypes._Tuple2"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["mul_f", "c1", "c2"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.polynomial.polyutils._div", "name": "_div", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["mul_f", "c1", "c2"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial.polyutils._AnyMulF"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeComplex_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeComplex_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_div", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._ComplexSeries"}], "type_ref": "numpy.polynomial._polytypes._Tuple2"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.polynomial.polyutils._div", "name": "_div", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["mul_f", "c1", "c2"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial.polyutils._AnyMulF"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeComplex_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeComplex_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_div", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._ComplexSeries"}], "type_ref": "numpy.polynomial._polytypes._Tuple2"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["mul_f", "c1", "c2"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.polynomial.polyutils._div", "name": "_div", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["mul_f", "c1", "c2"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial.polyutils._AnyMulF"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_div", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._ObjectSeries"}], "type_ref": "numpy.polynomial._polytypes._Tuple2"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.polynomial.polyutils._div", "name": "_div", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["mul_f", "c1", "c2"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial.polyutils._AnyMulF"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_div", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._ObjectSeries"}], "type_ref": "numpy.polynomial._polytypes._Tuple2"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["mul_f", "c1", "c2"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.polynomial.polyutils._div", "name": "_div", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["mul_f", "c1", "c2"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial.polyutils._AnyMulF"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_div", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._CoefSeries"}], "type_ref": "numpy.polynomial._polytypes._Tuple2"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.polynomial.polyutils._div", "name": "_div", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["mul_f", "c1", "c2"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial.polyutils._AnyMulF"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_div", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._CoefSeries"}], "type_ref": "numpy.polynomial._polytypes._Tuple2"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["mul_f", "c1", "c2"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial.polyutils._AnyMulF"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeFloat_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeFloat_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_div", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._FloatSeries"}], "type_ref": "numpy.polynomial._polytypes._Tuple2"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["mul_f", "c1", "c2"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial.polyutils._AnyMulF"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeComplex_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeComplex_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_div", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._ComplexSeries"}], "type_ref": "numpy.polynomial._polytypes._Tuple2"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["mul_f", "c1", "c2"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial.polyutils._AnyMulF"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_div", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._ObjectSeries"}], "type_ref": "numpy.polynomial._polytypes._Tuple2"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["mul_f", "c1", "c2"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial.polyutils._AnyMulF"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_div", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._CoefSeries"}], "type_ref": "numpy.polynomial._polytypes._Tuple2"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "_fit": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "numpy.polynomial.polyutils._fit", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1, 1, 1, 1], "arg_names": ["vander_f", "x", "y", "deg", "domain", "rcond", "full", "w"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.polynomial.polyutils._fit", "name": "_fit", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1, 1, 1, 1], "arg_names": ["vander_f", "x", "y", "deg", "domain", "rcond", "full", "w"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial.polyutils._AnyVanderF"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeFloat_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeFloat_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeInt_co"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeFloat_co"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._scalars._FloatLike_co"}], "uses_pep604_syntax": true}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeFloat_co"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_fit", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._FloatArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.polynomial.polyutils._fit", "name": "_fit", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1, 1, 1, 1], "arg_names": ["vander_f", "x", "y", "deg", "domain", "rcond", "full", "w"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial.polyutils._AnyVanderF"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeFloat_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeFloat_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeInt_co"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeFloat_co"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._scalars._FloatLike_co"}], "uses_pep604_syntax": true}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeFloat_co"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_fit", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._FloatArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1, 1, 1, 1], "arg_names": ["vander_f", "x", "y", "deg", "domain", "rcond", "full", "w"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.polynomial.polyutils._fit", "name": "_fit", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1, 1, 1, 1], "arg_names": ["vander_f", "x", "y", "deg", "domain", "rcond", "full", "w"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial.polyutils._AnyVanderF"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeComplex_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeComplex_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeInt_co"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeComplex_co"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._scalars._FloatLike_co"}], "uses_pep604_syntax": true}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeComplex_co"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_fit", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._ComplexArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.polynomial.polyutils._fit", "name": "_fit", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1, 1, 1, 1], "arg_names": ["vander_f", "x", "y", "deg", "domain", "rcond", "full", "w"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial.polyutils._AnyVanderF"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeComplex_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeComplex_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeInt_co"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeComplex_co"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._scalars._FloatLike_co"}], "uses_pep604_syntax": true}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeComplex_co"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_fit", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._ComplexArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1, 1, 1, 1], "arg_names": ["vander_f", "x", "y", "deg", "domain", "rcond", "full", "w"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.polynomial.polyutils._fit", "name": "_fit", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1, 1, 1, 1], "arg_names": ["vander_f", "x", "y", "deg", "domain", "rcond", "full", "w"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial.polyutils._AnyVanderF"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._ArrayLikeCoef_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeInt_co"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._scalars._FloatLike_co"}], "uses_pep604_syntax": true}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_fit", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._CoefArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.polynomial.polyutils._fit", "name": "_fit", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1, 1, 1, 1], "arg_names": ["vander_f", "x", "y", "deg", "domain", "rcond", "full", "w"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial.polyutils._AnyVanderF"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._ArrayLikeCoef_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeInt_co"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._scalars._FloatLike_co"}], "uses_pep604_syntax": true}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_fit", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._CoefArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 1], "arg_names": [null, null, null, null, null, null, null, "w"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.polynomial.polyutils._fit", "name": "_fit", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 1], "arg_names": [null, null, null, null, null, null, null, "w"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial.polyutils._AnyVanderF"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeInt_co"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._scalars._FloatLike_co"}], "uses_pep604_syntax": true}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_fit", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._CoefSeries"}, {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": ["builtins.float", "builtins.complex"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.inexact"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int32"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.polynomial.polyutils._fit", "name": "_fit", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 1], "arg_names": [null, null, null, null, null, null, null, "w"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial.polyutils._AnyVanderF"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeInt_co"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._scalars._FloatLike_co"}], "uses_pep604_syntax": true}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_fit", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._CoefSeries"}, {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": ["builtins.float", "builtins.complex"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.inexact"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int32"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1, 1, 3, 5], "arg_names": ["vander_f", "x", "y", "deg", "domain", "rcond", "full", "w"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.polynomial.polyutils._fit", "name": "_fit", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1, 1, 3, 5], "arg_names": ["vander_f", "x", "y", "deg", "domain", "rcond", "full", "w"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial.polyutils._AnyVanderF"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeInt_co"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._scalars._FloatLike_co"}], "uses_pep604_syntax": true}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_fit", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._CoefSeries"}, {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": ["builtins.float", "builtins.complex"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.inexact"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int32"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.polynomial.polyutils._fit", "name": "_fit", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1, 1, 3, 5], "arg_names": ["vander_f", "x", "y", "deg", "domain", "rcond", "full", "w"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial.polyutils._AnyVanderF"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeInt_co"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._scalars._FloatLike_co"}], "uses_pep604_syntax": true}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_fit", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._CoefSeries"}, {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": ["builtins.float", "builtins.complex"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.inexact"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int32"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1, 1, 1, 1], "arg_names": ["vander_f", "x", "y", "deg", "domain", "rcond", "full", "w"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial.polyutils._AnyVanderF"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeFloat_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeFloat_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeInt_co"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeFloat_co"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._scalars._FloatLike_co"}], "uses_pep604_syntax": true}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeFloat_co"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_fit", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._FloatArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1, 1, 1, 1], "arg_names": ["vander_f", "x", "y", "deg", "domain", "rcond", "full", "w"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial.polyutils._AnyVanderF"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeComplex_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeComplex_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeInt_co"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeComplex_co"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._scalars._FloatLike_co"}], "uses_pep604_syntax": true}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeComplex_co"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_fit", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._ComplexArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1, 1, 1, 1], "arg_names": ["vander_f", "x", "y", "deg", "domain", "rcond", "full", "w"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial.polyutils._AnyVanderF"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._ArrayLikeCoef_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeInt_co"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._scalars._FloatLike_co"}], "uses_pep604_syntax": true}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_fit", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._CoefArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 1], "arg_names": [null, null, null, null, null, null, null, "w"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial.polyutils._AnyVanderF"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeInt_co"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._scalars._FloatLike_co"}], "uses_pep604_syntax": true}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_fit", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._CoefSeries"}, {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": ["builtins.float", "builtins.complex"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.inexact"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int32"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1, 1, 3, 5], "arg_names": ["vander_f", "x", "y", "deg", "domain", "rcond", "full", "w"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial.polyutils._AnyVanderF"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeInt_co"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._scalars._FloatLike_co"}], "uses_pep604_syntax": true}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_fit", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._CoefSeries"}, {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": ["builtins.float", "builtins.complex"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.inexact"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int32"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "_fromroots": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "numpy.polynomial.polyutils._fromroots", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["line_f", "mul_f", "roots"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.polynomial.polyutils._fromroots", "name": "_fromroots", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["line_f", "mul_f", "roots"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial.polyutils._AnyLineF"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial.polyutils._AnyMulF"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeFloat_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_fromroots", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._FloatSeries"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.polynomial.polyutils._fromroots", "name": "_fromroots", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["line_f", "mul_f", "roots"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial.polyutils._AnyLineF"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial.polyutils._AnyMulF"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeFloat_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_fromroots", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._FloatSeries"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["line_f", "mul_f", "roots"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.polynomial.polyutils._fromroots", "name": "_fromroots", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["line_f", "mul_f", "roots"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial.polyutils._AnyLineF"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial.polyutils._AnyMulF"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeComplex_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_fromroots", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._ComplexSeries"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.polynomial.polyutils._fromroots", "name": "_fromroots", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["line_f", "mul_f", "roots"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial.polyutils._AnyLineF"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial.polyutils._AnyMulF"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeComplex_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_fromroots", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._ComplexSeries"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["line_f", "mul_f", "roots"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.polynomial.polyutils._fromroots", "name": "_fromroots", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["line_f", "mul_f", "roots"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial.polyutils._AnyLineF"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial.polyutils._AnyMulF"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_fromroots", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._ObjectSeries"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.polynomial.polyutils._fromroots", "name": "_fromroots", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["line_f", "mul_f", "roots"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial.polyutils._AnyLineF"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial.polyutils._AnyMulF"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_fromroots", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._ObjectSeries"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["line_f", "mul_f", "roots"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.polynomial.polyutils._fromroots", "name": "_fromroots", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["line_f", "mul_f", "roots"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial.polyutils._AnyLineF"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial.polyutils._AnyMulF"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_fromroots", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._CoefSeries"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.polynomial.polyutils._fromroots", "name": "_fromroots", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["line_f", "mul_f", "roots"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial.polyutils._AnyLineF"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial.polyutils._AnyMulF"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_fromroots", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._CoefSeries"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["line_f", "mul_f", "roots"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial.polyutils._AnyLineF"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial.polyutils._AnyMulF"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeFloat_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_fromroots", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._FloatSeries"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["line_f", "mul_f", "roots"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial.polyutils._AnyLineF"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial.polyutils._AnyMulF"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeComplex_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_fromroots", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._ComplexSeries"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["line_f", "mul_f", "roots"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial.polyutils._AnyLineF"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial.polyutils._AnyMulF"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_fromroots", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._ObjectSeries"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["line_f", "mul_f", "roots"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial.polyutils._AnyLineF"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial.polyutils._AnyMulF"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_fromroots", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._CoefSeries"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "_gridnd": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.polyutils._gridnd", "name": "_gridnd", "type": {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "_gridnd"}], "extra_attrs": null, "type_ref": "numpy.polynomial._polytypes._FuncValND"}}}, "_nth_slice": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["i", "ndim"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.polynomial.polyutils._nth_slice", "name": "_nth_slice", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["i", "ndim"], "arg_types": ["typing.SupportsIndex", "typing.SupportsIndex"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_nth_slice", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.slice"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_pow": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "numpy.polynomial.polyutils._pow", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["mul_f", "c", "pow", "maxpower"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.polynomial.polyutils._pow", "name": "_pow", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["mul_f", "c", "pow", "maxpower"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial.polyutils._AnyMulF"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeFloat_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._AnyInt"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._AnyInt"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_pow", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._FloatSeries"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.polynomial.polyutils._pow", "name": "_pow", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["mul_f", "c", "pow", "maxpower"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial.polyutils._AnyMulF"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeFloat_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._AnyInt"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._AnyInt"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_pow", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._FloatSeries"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["mul_f", "c", "pow", "maxpower"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.polynomial.polyutils._pow", "name": "_pow", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["mul_f", "c", "pow", "maxpower"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial.polyutils._AnyMulF"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeComplex_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._AnyInt"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._AnyInt"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_pow", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._ComplexSeries"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.polynomial.polyutils._pow", "name": "_pow", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["mul_f", "c", "pow", "maxpower"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial.polyutils._AnyMulF"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeComplex_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._AnyInt"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._AnyInt"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_pow", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._ComplexSeries"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["mul_f", "c", "pow", "maxpower"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.polynomial.polyutils._pow", "name": "_pow", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["mul_f", "c", "pow", "maxpower"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial.polyutils._AnyMulF"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._AnyInt"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._AnyInt"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_pow", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._ObjectSeries"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.polynomial.polyutils._pow", "name": "_pow", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["mul_f", "c", "pow", "maxpower"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial.polyutils._AnyMulF"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._AnyInt"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._AnyInt"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_pow", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._ObjectSeries"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["mul_f", "c", "pow", "maxpower"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.polynomial.polyutils._pow", "name": "_pow", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["mul_f", "c", "pow", "maxpower"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial.polyutils._AnyMulF"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._AnyInt"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._AnyInt"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_pow", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._CoefSeries"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.polynomial.polyutils._pow", "name": "_pow", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["mul_f", "c", "pow", "maxpower"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial.polyutils._AnyMulF"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._AnyInt"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._AnyInt"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_pow", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._CoefSeries"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["mul_f", "c", "pow", "maxpower"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial.polyutils._AnyMulF"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeFloat_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._AnyInt"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._AnyInt"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_pow", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._FloatSeries"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["mul_f", "c", "pow", "maxpower"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial.polyutils._AnyMulF"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeComplex_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._AnyInt"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._AnyInt"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_pow", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._ComplexSeries"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["mul_f", "c", "pow", "maxpower"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial.polyutils._AnyMulF"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._AnyInt"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._AnyInt"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_pow", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._ObjectSeries"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["mul_f", "c", "pow", "maxpower"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial.polyutils._AnyMulF"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._AnyInt"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._AnyInt"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_pow", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._CoefSeries"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "_sub": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "numpy.polynomial.polyutils._sub", "name": "_sub", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "numpy.polynomial._polytypes._FuncBinOp"}}}, "_valnd": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.polyutils._valnd", "name": "_valnd", "type": {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "_valnd"}], "extra_attrs": null, "type_ref": "numpy.polynomial._polytypes._FuncValND"}}}, "_vander_nd": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.polyutils._vander_nd", "name": "_vander_nd", "type": {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "_vander_nd"}], "extra_attrs": null, "type_ref": "numpy.polynomial._polytypes._FuncVanderND"}}}, "_vander_nd_flat": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.polyutils._vander_nd_flat", "name": "_vander_nd_flat", "type": {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "_vander_nd_flat"}], "extra_attrs": null, "type_ref": "numpy.polynomial._polytypes._FuncVanderND"}}}, "as_series": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "numpy.polynomial.polyutils.as_series", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["alist", "trim"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.polynomial.polyutils.as_series", "name": "as_series", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["alist", "trim"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.integer"}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._FloatArray"}], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_series", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._FloatSeries"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.polynomial.polyutils.as_series", "name": "as_series", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["alist", "trim"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.integer"}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._FloatArray"}], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_series", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._FloatSeries"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["alist", "trim"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.polynomial.polyutils.as_series", "name": "as_series", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["alist", "trim"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._ComplexArray"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_series", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._ComplexSeries"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.polynomial.polyutils.as_series", "name": "as_series", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["alist", "trim"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._ComplexArray"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_series", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._ComplexSeries"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["alist", "trim"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.polynomial.polyutils.as_series", "name": "as_series", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["alist", "trim"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._ObjectArray"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_series", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._ObjectSeries"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.polynomial.polyutils.as_series", "name": "as_series", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["alist", "trim"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._ObjectArray"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_series", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._ObjectSeries"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["alist", "trim"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.polynomial.polyutils.as_series", "name": "as_series", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["alist", "trim"], "arg_types": [{".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._FloatArray"}, {".class": "TypeAliasType", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.integer"}], "type_ref": "numpy._typing._array_like.NDArray"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "typing.Iterable"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_series", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._FloatSeries"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.polynomial.polyutils.as_series", "name": "as_series", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["alist", "trim"], "arg_types": [{".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._FloatArray"}, {".class": "TypeAliasType", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.integer"}], "type_ref": "numpy._typing._array_like.NDArray"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "typing.Iterable"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_series", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._FloatSeries"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["alist", "trim"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.polynomial.polyutils.as_series", "name": "as_series", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["alist", "trim"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._ComplexArray"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_series", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._ComplexSeries"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.polynomial.polyutils.as_series", "name": "as_series", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["alist", "trim"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._ComplexArray"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_series", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._ComplexSeries"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["alist", "trim"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.polynomial.polyutils.as_series", "name": "as_series", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["alist", "trim"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._ObjectArray"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_series", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._ObjectSeries"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.polynomial.polyutils.as_series", "name": "as_series", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["alist", "trim"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._ObjectArray"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_series", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._ObjectSeries"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["alist", "trim"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.polynomial.polyutils.as_series", "name": "as_series", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["alist", "trim"], "arg_types": [{".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeFloat_co"}, "builtins.float"], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "typing.Iterable"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_series", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._FloatSeries"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.polynomial.polyutils.as_series", "name": "as_series", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["alist", "trim"], "arg_types": [{".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeFloat_co"}, "builtins.float"], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "typing.Iterable"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_series", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._FloatSeries"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["alist", "trim"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.polynomial.polyutils.as_series", "name": "as_series", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["alist", "trim"], "arg_types": [{".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeComplex_co"}, "builtins.complex"], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "typing.Iterable"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_series", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._ComplexSeries"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.polynomial.polyutils.as_series", "name": "as_series", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["alist", "trim"], "arg_types": [{".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeComplex_co"}, "builtins.complex"], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "typing.Iterable"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_series", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._ComplexSeries"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["alist", "trim"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.polynomial.polyutils.as_series", "name": "as_series", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["alist", "trim"], "arg_types": [{".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co"}, "builtins.object"], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "typing.Iterable"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_series", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._ObjectSeries"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.polynomial.polyutils.as_series", "name": "as_series", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["alist", "trim"], "arg_types": [{".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co"}, "builtins.object"], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "typing.Iterable"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_series", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._ObjectSeries"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["alist", "trim"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.integer"}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._FloatArray"}], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_series", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._FloatSeries"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["alist", "trim"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._ComplexArray"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_series", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._ComplexSeries"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["alist", "trim"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._ObjectArray"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_series", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._ObjectSeries"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["alist", "trim"], "arg_types": [{".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._FloatArray"}, {".class": "TypeAliasType", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.integer"}], "type_ref": "numpy._typing._array_like.NDArray"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "typing.Iterable"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_series", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._FloatSeries"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["alist", "trim"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._ComplexArray"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_series", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._ComplexSeries"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["alist", "trim"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._ObjectArray"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_series", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._ObjectSeries"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["alist", "trim"], "arg_types": [{".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeFloat_co"}, "builtins.float"], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "typing.Iterable"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_series", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._FloatSeries"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["alist", "trim"], "arg_types": [{".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeComplex_co"}, "builtins.complex"], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "typing.Iterable"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_series", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._ComplexSeries"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["alist", "trim"], "arg_types": [{".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co"}, "builtins.object"], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "typing.Iterable"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_series", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._ObjectSeries"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "format_float": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["x", "parens"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.polynomial.polyutils.format_float", "name": "format_float", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["x", "parens"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._scalars._FloatLike_co"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "format_float", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "getdomain": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "numpy.polynomial.polyutils.getdomain", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["x"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.polynomial.polyutils.getdomain", "name": "get<PERSON>ain", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["x"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._FloatArray"}, {".class": "TypeAliasType", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.integer"}], "type_ref": "numpy._typing._array_like.NDArray"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get<PERSON>ain", "ret_type": {".class": "TypeAliasType", "args": ["numpy.float64"], "type_ref": "numpy.polynomial._polytypes._Array2"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.polynomial.polyutils.getdomain", "name": "get<PERSON>ain", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["x"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._FloatArray"}, {".class": "TypeAliasType", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.integer"}], "type_ref": "numpy._typing._array_like.NDArray"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get<PERSON>ain", "ret_type": {".class": "TypeAliasType", "args": ["numpy.float64"], "type_ref": "numpy.polynomial._polytypes._Array2"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["x"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.polynomial.polyutils.getdomain", "name": "get<PERSON>ain", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["x"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._ComplexArray"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get<PERSON>ain", "ret_type": {".class": "TypeAliasType", "args": ["numpy.complex128"], "type_ref": "numpy.polynomial._polytypes._Array2"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.polynomial.polyutils.getdomain", "name": "get<PERSON>ain", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["x"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._ComplexArray"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get<PERSON>ain", "ret_type": {".class": "TypeAliasType", "args": ["numpy.complex128"], "type_ref": "numpy.polynomial._polytypes._Array2"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["x"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.polynomial.polyutils.getdomain", "name": "get<PERSON>ain", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["x"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._ObjectArray"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get<PERSON>ain", "ret_type": {".class": "TypeAliasType", "args": ["numpy.object_"], "type_ref": "numpy.polynomial._polytypes._Array2"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.polynomial.polyutils.getdomain", "name": "get<PERSON>ain", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["x"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._ObjectArray"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get<PERSON>ain", "ret_type": {".class": "TypeAliasType", "args": ["numpy.object_"], "type_ref": "numpy.polynomial._polytypes._Array2"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["x"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.polynomial.polyutils.getdomain", "name": "get<PERSON>ain", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["x"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeFloat_co"}, "builtins.float"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get<PERSON>ain", "ret_type": {".class": "TypeAliasType", "args": ["numpy.float64"], "type_ref": "numpy.polynomial._polytypes._Array2"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.polynomial.polyutils.getdomain", "name": "get<PERSON>ain", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["x"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeFloat_co"}, "builtins.float"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get<PERSON>ain", "ret_type": {".class": "TypeAliasType", "args": ["numpy.float64"], "type_ref": "numpy.polynomial._polytypes._Array2"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["x"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.polynomial.polyutils.getdomain", "name": "get<PERSON>ain", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["x"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeComplex_co"}, "builtins.complex"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get<PERSON>ain", "ret_type": {".class": "TypeAliasType", "args": ["numpy.complex128"], "type_ref": "numpy.polynomial._polytypes._Array2"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.polynomial.polyutils.getdomain", "name": "get<PERSON>ain", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["x"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeComplex_co"}, "builtins.complex"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get<PERSON>ain", "ret_type": {".class": "TypeAliasType", "args": ["numpy.complex128"], "type_ref": "numpy.polynomial._polytypes._Array2"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["x"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.polynomial.polyutils.getdomain", "name": "get<PERSON>ain", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["x"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co"}, "builtins.object"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get<PERSON>ain", "ret_type": {".class": "TypeAliasType", "args": ["numpy.object_"], "type_ref": "numpy.polynomial._polytypes._Array2"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.polynomial.polyutils.getdomain", "name": "get<PERSON>ain", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["x"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co"}, "builtins.object"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get<PERSON>ain", "ret_type": {".class": "TypeAliasType", "args": ["numpy.object_"], "type_ref": "numpy.polynomial._polytypes._Array2"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": ["x"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._FloatArray"}, {".class": "TypeAliasType", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.integer"}], "type_ref": "numpy._typing._array_like.NDArray"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get<PERSON>ain", "ret_type": {".class": "TypeAliasType", "args": ["numpy.float64"], "type_ref": "numpy.polynomial._polytypes._Array2"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0], "arg_names": ["x"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._ComplexArray"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get<PERSON>ain", "ret_type": {".class": "TypeAliasType", "args": ["numpy.complex128"], "type_ref": "numpy.polynomial._polytypes._Array2"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0], "arg_names": ["x"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._ObjectArray"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get<PERSON>ain", "ret_type": {".class": "TypeAliasType", "args": ["numpy.object_"], "type_ref": "numpy.polynomial._polytypes._Array2"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0], "arg_names": ["x"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeFloat_co"}, "builtins.float"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get<PERSON>ain", "ret_type": {".class": "TypeAliasType", "args": ["numpy.float64"], "type_ref": "numpy.polynomial._polytypes._Array2"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0], "arg_names": ["x"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeComplex_co"}, "builtins.complex"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get<PERSON>ain", "ret_type": {".class": "TypeAliasType", "args": ["numpy.complex128"], "type_ref": "numpy.polynomial._polytypes._Array2"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0], "arg_names": ["x"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co"}, "builtins.object"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get<PERSON>ain", "ret_type": {".class": "TypeAliasType", "args": ["numpy.object_"], "type_ref": "numpy.polynomial._polytypes._Array2"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "mapdomain": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "numpy.polynomial.polyutils.mapdomain", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["x", "old", "new"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.polynomial.polyutils.mapdomain", "name": "mapdomain", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["x", "old", "new"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._scalars._FloatLike_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeFloat_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeFloat_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "mapdomain", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.floating"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.polynomial.polyutils.mapdomain", "name": "mapdomain", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["x", "old", "new"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._scalars._FloatLike_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeFloat_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeFloat_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "mapdomain", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.floating"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["x", "old", "new"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.polynomial.polyutils.mapdomain", "name": "mapdomain", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["x", "old", "new"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._scalars._NumberLike_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeComplex_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeComplex_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "mapdomain", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.complexfloating"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.polynomial.polyutils.mapdomain", "name": "mapdomain", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["x", "old", "new"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._scalars._NumberLike_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeComplex_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeComplex_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "mapdomain", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.complexfloating"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["x", "old", "new"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.polynomial.polyutils.mapdomain", "name": "mapdomain", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["x", "old", "new"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.floating"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.integer"}], "uses_pep604_syntax": true}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.floating"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.integer"}], "uses_pep604_syntax": true}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.floating"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.integer"}], "uses_pep604_syntax": true}], "type_ref": "numpy._typing._array_like.NDArray"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "mapdomain", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._FloatSeries"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.polynomial.polyutils.mapdomain", "name": "mapdomain", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["x", "old", "new"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.floating"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.integer"}], "uses_pep604_syntax": true}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.floating"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.integer"}], "uses_pep604_syntax": true}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.floating"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.integer"}], "uses_pep604_syntax": true}], "type_ref": "numpy._typing._array_like.NDArray"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "mapdomain", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._FloatSeries"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["x", "old", "new"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.polynomial.polyutils.mapdomain", "name": "mapdomain", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["x", "old", "new"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": ["builtins.int", "builtins.float", "builtins.complex"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.number"}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": ["builtins.int", "builtins.float", "builtins.complex"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.number"}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": ["builtins.int", "builtins.float", "builtins.complex"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.number"}], "type_ref": "numpy._typing._array_like.NDArray"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "mapdomain", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._ComplexSeries"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.polynomial.polyutils.mapdomain", "name": "mapdomain", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["x", "old", "new"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": ["builtins.int", "builtins.float", "builtins.complex"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.number"}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": ["builtins.int", "builtins.float", "builtins.complex"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.number"}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": ["builtins.int", "builtins.float", "builtins.complex"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.number"}], "type_ref": "numpy._typing._array_like.NDArray"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "mapdomain", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._ComplexSeries"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["x", "old", "new"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.polynomial.polyutils.mapdomain", "name": "mapdomain", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["x", "old", "new"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "UnionType", "items": ["numpy.object_", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": ["builtins.int", "builtins.float", "builtins.complex"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.number"}], "uses_pep604_syntax": true}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "UnionType", "items": ["numpy.object_", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": ["builtins.int", "builtins.float", "builtins.complex"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.number"}], "uses_pep604_syntax": true}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "UnionType", "items": ["numpy.object_", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": ["builtins.int", "builtins.float", "builtins.complex"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.number"}], "uses_pep604_syntax": true}], "type_ref": "numpy._typing._array_like.NDArray"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "mapdomain", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._ObjectSeries"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.polynomial.polyutils.mapdomain", "name": "mapdomain", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["x", "old", "new"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "UnionType", "items": ["numpy.object_", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": ["builtins.int", "builtins.float", "builtins.complex"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.number"}], "uses_pep604_syntax": true}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "UnionType", "items": ["numpy.object_", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": ["builtins.int", "builtins.float", "builtins.complex"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.number"}], "uses_pep604_syntax": true}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "UnionType", "items": ["numpy.object_", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": ["builtins.int", "builtins.float", "builtins.complex"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.number"}], "uses_pep604_syntax": true}], "type_ref": "numpy._typing._array_like.NDArray"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "mapdomain", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._ObjectSeries"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["x", "old", "new"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.polynomial.polyutils.mapdomain", "name": "mapdomain", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["x", "old", "new"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeFloat_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeFloat_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeFloat_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "mapdomain", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._FloatSeries"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.polynomial.polyutils.mapdomain", "name": "mapdomain", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["x", "old", "new"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeFloat_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeFloat_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeFloat_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "mapdomain", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._FloatSeries"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["x", "old", "new"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.polynomial.polyutils.mapdomain", "name": "mapdomain", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["x", "old", "new"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeComplex_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeComplex_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeComplex_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "mapdomain", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._ComplexSeries"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.polynomial.polyutils.mapdomain", "name": "mapdomain", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["x", "old", "new"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeComplex_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeComplex_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeComplex_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "mapdomain", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._ComplexSeries"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["x", "old", "new"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.polynomial.polyutils.mapdomain", "name": "mapdomain", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["x", "old", "new"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "mapdomain", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._ObjectSeries"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.polynomial.polyutils.mapdomain", "name": "mapdomain", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["x", "old", "new"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "mapdomain", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._ObjectSeries"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["x", "old", "new"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.polynomial.polyutils.mapdomain", "name": "mapdomain", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["x", "old", "new"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._CoefLike_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "mapdomain", "ret_type": "builtins.object", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.polynomial.polyutils.mapdomain", "name": "mapdomain", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["x", "old", "new"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._CoefLike_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "mapdomain", "ret_type": "builtins.object", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["x", "old", "new"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._scalars._FloatLike_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeFloat_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeFloat_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "mapdomain", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.floating"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["x", "old", "new"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._scalars._NumberLike_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeComplex_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeComplex_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "mapdomain", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.complexfloating"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["x", "old", "new"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.floating"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.integer"}], "uses_pep604_syntax": true}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.floating"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.integer"}], "uses_pep604_syntax": true}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.floating"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.integer"}], "uses_pep604_syntax": true}], "type_ref": "numpy._typing._array_like.NDArray"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "mapdomain", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._FloatSeries"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["x", "old", "new"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": ["builtins.int", "builtins.float", "builtins.complex"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.number"}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": ["builtins.int", "builtins.float", "builtins.complex"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.number"}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": ["builtins.int", "builtins.float", "builtins.complex"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.number"}], "type_ref": "numpy._typing._array_like.NDArray"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "mapdomain", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._ComplexSeries"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["x", "old", "new"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "UnionType", "items": ["numpy.object_", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": ["builtins.int", "builtins.float", "builtins.complex"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.number"}], "uses_pep604_syntax": true}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "UnionType", "items": ["numpy.object_", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": ["builtins.int", "builtins.float", "builtins.complex"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.number"}], "uses_pep604_syntax": true}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "UnionType", "items": ["numpy.object_", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": ["builtins.int", "builtins.float", "builtins.complex"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.number"}], "uses_pep604_syntax": true}], "type_ref": "numpy._typing._array_like.NDArray"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "mapdomain", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._ObjectSeries"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["x", "old", "new"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeFloat_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeFloat_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeFloat_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "mapdomain", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._FloatSeries"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["x", "old", "new"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeComplex_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeComplex_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeComplex_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "mapdomain", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._ComplexSeries"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["x", "old", "new"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "mapdomain", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._ObjectSeries"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["x", "old", "new"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._CoefLike_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "mapdomain", "ret_type": "builtins.object", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "mapparms": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "numpy.polynomial.polyutils.mapparms", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["old", "new"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.polynomial.polyutils.mapparms", "name": "mapparms", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["old", "new"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.floating"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.integer"}], "uses_pep604_syntax": true}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.floating"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.integer"}], "uses_pep604_syntax": true}], "type_ref": "numpy._typing._array_like.NDArray"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "mapparms", "ret_type": {".class": "TypeAliasType", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.floating"}], "type_ref": "numpy.polynomial._polytypes._Tuple2"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.polynomial.polyutils.mapparms", "name": "mapparms", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["old", "new"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.floating"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.integer"}], "uses_pep604_syntax": true}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.floating"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.integer"}], "uses_pep604_syntax": true}], "type_ref": "numpy._typing._array_like.NDArray"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "mapparms", "ret_type": {".class": "TypeAliasType", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.floating"}], "type_ref": "numpy.polynomial._polytypes._Tuple2"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["old", "new"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.polynomial.polyutils.mapparms", "name": "mapparms", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["old", "new"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": ["builtins.int", "builtins.float", "builtins.complex"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.number"}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": ["builtins.int", "builtins.float", "builtins.complex"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.number"}], "type_ref": "numpy._typing._array_like.NDArray"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "mapparms", "ret_type": {".class": "TypeAliasType", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.complexfloating"}], "type_ref": "numpy.polynomial._polytypes._Tuple2"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.polynomial.polyutils.mapparms", "name": "mapparms", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["old", "new"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": ["builtins.int", "builtins.float", "builtins.complex"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.number"}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": ["builtins.int", "builtins.float", "builtins.complex"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.number"}], "type_ref": "numpy._typing._array_like.NDArray"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "mapparms", "ret_type": {".class": "TypeAliasType", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.complexfloating"}], "type_ref": "numpy.polynomial._polytypes._Tuple2"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["old", "new"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.polynomial.polyutils.mapparms", "name": "mapparms", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["old", "new"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "UnionType", "items": ["numpy.object_", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": ["builtins.int", "builtins.float", "builtins.complex"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.number"}], "uses_pep604_syntax": true}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "UnionType", "items": ["numpy.object_", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": ["builtins.int", "builtins.float", "builtins.complex"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.number"}], "uses_pep604_syntax": true}], "type_ref": "numpy._typing._array_like.NDArray"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "mapparms", "ret_type": {".class": "TypeAliasType", "args": ["builtins.object"], "type_ref": "numpy.polynomial._polytypes._Tuple2"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.polynomial.polyutils.mapparms", "name": "mapparms", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["old", "new"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "UnionType", "items": ["numpy.object_", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": ["builtins.int", "builtins.float", "builtins.complex"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.number"}], "uses_pep604_syntax": true}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "UnionType", "items": ["numpy.object_", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": ["builtins.int", "builtins.float", "builtins.complex"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.number"}], "uses_pep604_syntax": true}], "type_ref": "numpy._typing._array_like.NDArray"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "mapparms", "ret_type": {".class": "TypeAliasType", "args": ["builtins.object"], "type_ref": "numpy.polynomial._polytypes._Tuple2"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["old", "new"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.polynomial.polyutils.mapparms", "name": "mapparms", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["old", "new"], "arg_types": [{".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "mapparms", "ret_type": {".class": "TypeAliasType", "args": ["builtins.float"], "type_ref": "numpy.polynomial._polytypes._Tuple2"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.polynomial.polyutils.mapparms", "name": "mapparms", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["old", "new"], "arg_types": [{".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "mapparms", "ret_type": {".class": "TypeAliasType", "args": ["builtins.float"], "type_ref": "numpy.polynomial._polytypes._Tuple2"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["old", "new"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.polynomial.polyutils.mapparms", "name": "mapparms", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["old", "new"], "arg_types": [{".class": "Instance", "args": ["builtins.complex"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "Instance", "args": ["builtins.complex"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "mapparms", "ret_type": {".class": "TypeAliasType", "args": ["builtins.complex"], "type_ref": "numpy.polynomial._polytypes._Tuple2"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.polynomial.polyutils.mapparms", "name": "mapparms", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["old", "new"], "arg_types": [{".class": "Instance", "args": ["builtins.complex"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "Instance", "args": ["builtins.complex"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "mapparms", "ret_type": {".class": "TypeAliasType", "args": ["builtins.complex"], "type_ref": "numpy.polynomial._polytypes._Tuple2"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["old", "new"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.polynomial.polyutils.mapparms", "name": "mapparms", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["old", "new"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeFloat_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeFloat_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "mapparms", "ret_type": {".class": "TypeAliasType", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.floating"}], "type_ref": "numpy.polynomial._polytypes._Tuple2"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.polynomial.polyutils.mapparms", "name": "mapparms", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["old", "new"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeFloat_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeFloat_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "mapparms", "ret_type": {".class": "TypeAliasType", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.floating"}], "type_ref": "numpy.polynomial._polytypes._Tuple2"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["old", "new"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.polynomial.polyutils.mapparms", "name": "mapparms", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["old", "new"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeComplex_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeComplex_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "mapparms", "ret_type": {".class": "TypeAliasType", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.complexfloating"}], "type_ref": "numpy.polynomial._polytypes._Tuple2"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.polynomial.polyutils.mapparms", "name": "mapparms", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["old", "new"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeComplex_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeComplex_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "mapparms", "ret_type": {".class": "TypeAliasType", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.complexfloating"}], "type_ref": "numpy.polynomial._polytypes._Tuple2"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["old", "new"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.polynomial.polyutils.mapparms", "name": "mapparms", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["old", "new"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "mapparms", "ret_type": {".class": "TypeAliasType", "args": ["builtins.object"], "type_ref": "numpy.polynomial._polytypes._Tuple2"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.polynomial.polyutils.mapparms", "name": "mapparms", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["old", "new"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "mapparms", "ret_type": {".class": "TypeAliasType", "args": ["builtins.object"], "type_ref": "numpy.polynomial._polytypes._Tuple2"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["old", "new"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.floating"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.integer"}], "uses_pep604_syntax": true}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.floating"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.integer"}], "uses_pep604_syntax": true}], "type_ref": "numpy._typing._array_like.NDArray"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "mapparms", "ret_type": {".class": "TypeAliasType", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.floating"}], "type_ref": "numpy.polynomial._polytypes._Tuple2"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["old", "new"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": ["builtins.int", "builtins.float", "builtins.complex"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.number"}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": ["builtins.int", "builtins.float", "builtins.complex"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.number"}], "type_ref": "numpy._typing._array_like.NDArray"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "mapparms", "ret_type": {".class": "TypeAliasType", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.complexfloating"}], "type_ref": "numpy.polynomial._polytypes._Tuple2"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["old", "new"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "UnionType", "items": ["numpy.object_", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": ["builtins.int", "builtins.float", "builtins.complex"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.number"}], "uses_pep604_syntax": true}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "UnionType", "items": ["numpy.object_", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": ["builtins.int", "builtins.float", "builtins.complex"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.number"}], "uses_pep604_syntax": true}], "type_ref": "numpy._typing._array_like.NDArray"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "mapparms", "ret_type": {".class": "TypeAliasType", "args": ["builtins.object"], "type_ref": "numpy.polynomial._polytypes._Tuple2"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["old", "new"], "arg_types": [{".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "mapparms", "ret_type": {".class": "TypeAliasType", "args": ["builtins.float"], "type_ref": "numpy.polynomial._polytypes._Tuple2"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["old", "new"], "arg_types": [{".class": "Instance", "args": ["builtins.complex"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "Instance", "args": ["builtins.complex"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "mapparms", "ret_type": {".class": "TypeAliasType", "args": ["builtins.complex"], "type_ref": "numpy.polynomial._polytypes._Tuple2"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["old", "new"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeFloat_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeFloat_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "mapparms", "ret_type": {".class": "TypeAliasType", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.floating"}], "type_ref": "numpy.polynomial._polytypes._Tuple2"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["old", "new"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeComplex_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeComplex_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "mapparms", "ret_type": {".class": "TypeAliasType", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.complexfloating"}], "type_ref": "numpy.polynomial._polytypes._Tuple2"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["old", "new"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "mapparms", "ret_type": {".class": "TypeAliasType", "args": ["builtins.object"], "type_ref": "numpy.polynomial._polytypes._Tuple2"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "np": {".class": "SymbolTableNode", "cross_ref": "numpy", "kind": "Gdef", "module_hidden": true, "module_public": false}, "npt": {".class": "SymbolTableNode", "cross_ref": "numpy.typing", "kind": "Gdef", "module_hidden": true, "module_public": false}, "overload": {".class": "SymbolTableNode", "cross_ref": "typing.overload", "kind": "Gdef", "module_hidden": true, "module_public": false}, "trimcoef": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "numpy.polynomial.polyutils.trimcoef", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["c", "tol"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.polynomial.polyutils.trimcoef", "name": "trimcoef", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["c", "tol"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.integer"}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._FloatArray"}], "uses_pep604_syntax": true}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._scalars._FloatLike_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "trimcoef", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._FloatSeries"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.polynomial.polyutils.trimcoef", "name": "trimcoef", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["c", "tol"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.integer"}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._FloatArray"}], "uses_pep604_syntax": true}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._scalars._FloatLike_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "trimcoef", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._FloatSeries"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["c", "tol"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.polynomial.polyutils.trimcoef", "name": "trimcoef", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["c", "tol"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._ComplexArray"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._scalars._FloatLike_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "trimcoef", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._ComplexSeries"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.polynomial.polyutils.trimcoef", "name": "trimcoef", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["c", "tol"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._ComplexArray"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._scalars._FloatLike_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "trimcoef", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._ComplexSeries"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["c", "tol"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.polynomial.polyutils.trimcoef", "name": "trimcoef", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["c", "tol"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._ObjectArray"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._scalars._FloatLike_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "trimcoef", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._ObjectSeries"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.polynomial.polyutils.trimcoef", "name": "trimcoef", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["c", "tol"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._ObjectArray"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._scalars._FloatLike_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "trimcoef", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._ObjectSeries"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["c", "tol"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.polynomial.polyutils.trimcoef", "name": "trimcoef", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["c", "tol"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeFloat_co"}, "builtins.float"], "uses_pep604_syntax": true}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._scalars._FloatLike_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "trimcoef", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._FloatSeries"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.polynomial.polyutils.trimcoef", "name": "trimcoef", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["c", "tol"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeFloat_co"}, "builtins.float"], "uses_pep604_syntax": true}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._scalars._FloatLike_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "trimcoef", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._FloatSeries"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["c", "tol"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.polynomial.polyutils.trimcoef", "name": "trimcoef", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["c", "tol"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeComplex_co"}, "builtins.complex"], "uses_pep604_syntax": true}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._scalars._FloatLike_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "trimcoef", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._ComplexSeries"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.polynomial.polyutils.trimcoef", "name": "trimcoef", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["c", "tol"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeComplex_co"}, "builtins.complex"], "uses_pep604_syntax": true}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._scalars._FloatLike_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "trimcoef", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._ComplexSeries"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["c", "tol"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.polynomial.polyutils.trimcoef", "name": "trimcoef", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["c", "tol"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co"}, "builtins.object"], "uses_pep604_syntax": true}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._scalars._FloatLike_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "trimcoef", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._ObjectSeries"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.polynomial.polyutils.trimcoef", "name": "trimcoef", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["c", "tol"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co"}, "builtins.object"], "uses_pep604_syntax": true}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._scalars._FloatLike_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "trimcoef", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._ObjectSeries"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["c", "tol"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.integer"}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._FloatArray"}], "uses_pep604_syntax": true}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._scalars._FloatLike_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "trimcoef", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._FloatSeries"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["c", "tol"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._ComplexArray"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._scalars._FloatLike_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "trimcoef", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._ComplexSeries"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["c", "tol"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._ObjectArray"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._scalars._FloatLike_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "trimcoef", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._ObjectSeries"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["c", "tol"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeFloat_co"}, "builtins.float"], "uses_pep604_syntax": true}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._scalars._FloatLike_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "trimcoef", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._FloatSeries"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["c", "tol"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeComplex_co"}, "builtins.complex"], "uses_pep604_syntax": true}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._scalars._FloatLike_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "trimcoef", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._ComplexSeries"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["c", "tol"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._SeriesLikeCoef_co"}, "builtins.object"], "uses_pep604_syntax": true}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._scalars._FloatLike_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "trimcoef", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._ObjectSeries"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "trimseq": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["seq"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.polynomial.polyutils.trimseq", "name": "trimseq", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["seq"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial.polyutils._T_seq", "id": -1, "name": "_T_seq", "namespace": "numpy.polynomial.polyutils.trimseq", "upper_bound": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._CoefArray"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._CoefLike_co"}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "uses_pep604_syntax": true}, "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "trimseq", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial.polyutils._T_seq", "id": -1, "name": "_T_seq", "namespace": "numpy.polynomial.polyutils.trimseq", "upper_bound": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._CoefArray"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._CoefLike_co"}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "uses_pep604_syntax": true}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial.polyutils._T_seq", "id": -1, "name": "_T_seq", "namespace": "numpy.polynomial.polyutils.trimseq", "upper_bound": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._CoefArray"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.polynomial._polytypes._CoefLike_co"}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "uses_pep604_syntax": true}, "values": [], "variance": 0}]}}}}, "path": "c:\\Python313\\Lib\\site-packages\\numpy\\polynomial\\polyutils.pyi"}