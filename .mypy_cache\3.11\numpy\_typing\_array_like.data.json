{".class": "MypyFile", "_fullname": "numpy._typing._array_like", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "ArrayLike": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "numpy._typing._array_like.ArrayLike", "line": 97, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": ["numpy._typing._array_like._Buffer", {".class": "TypeAliasType", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "numpy.dtype"}, {".class": "UnionType", "items": ["builtins.bool", "builtins.int", "builtins.float", "builtins.complex", "builtins.str", "builtins.bytes"], "uses_pep604_syntax": true}], "type_ref": "numpy._typing._array_like._DualArrayLike"}], "uses_pep604_syntax": true}}}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef"}, "Collection": {".class": "SymbolTableNode", "cross_ref": "typing.Collection", "kind": "Gdef"}, "NDArray": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._array_like._ScalarType_co", "id": 1, "name": "_ScalarType_co", "namespace": "numpy._typing._array_like.NDArray", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 1}], "column": 0, "fullname": "numpy._typing._array_like.NDArray", "line": 41, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._shape._Shape"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._array_like._ScalarType_co", "id": 1, "name": "_ScalarType_co", "namespace": "numpy._typing._array_like.NDArray", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.dtype"}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}}}, "Protocol": {".class": "SymbolTableNode", "cross_ref": "typing.Protocol", "kind": "Gdef"}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef"}, "StringDType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 4, "fullname": "numpy._typing._array_like.StringDType", "line": 29, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "numpy.dtypes.StringDType"}}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "TypeAlias": {".class": "SymbolTableNode", "cross_ref": "typing.TypeAlias", "kind": "Gdef"}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef"}, "_32Bit": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._nbit_base._32Bit", "kind": "Gdef"}, "_64Bit": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._nbit_base._64Bit", "kind": "Gdef"}, "_ArrayLike": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._array_like._ScalarType", "id": 1, "name": "_ScalarType", "namespace": "numpy._typing._array_like._ArrayLike", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "column": 0, "fullname": "numpy._typing._array_like._ArrayLike", "line": 75, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._array_like._ScalarType", "id": 1, "name": "_ScalarType", "namespace": "numpy._typing._array_like._ArrayLike", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy.dtype"}], "extra_attrs": null, "type_ref": "numpy._typing._array_like._SupportsArray"}, {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._array_like._ScalarType", "id": 1, "name": "_ScalarType", "namespace": "numpy._typing._array_like._ArrayLike", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy.dtype"}], "extra_attrs": null, "type_ref": "numpy._typing._array_like._SupportsArray"}], "extra_attrs": null, "type_ref": "numpy._typing._nested_sequence._NestedSequence"}], "uses_pep604_syntax": true}}}, "_ArrayLikeAnyString_co": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "numpy._typing._array_like._ArrayLikeAnyString_co", "line": 162, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeString_co"}], "uses_pep604_syntax": true}}}, "_ArrayLikeBool_co": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "numpy._typing._array_like._ArrayLikeBool_co", "line": 104, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "TypeAliasType", "args": [{".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "numpy.bool"}], "extra_attrs": null, "type_ref": "numpy.dtype"}, "builtins.bool"], "type_ref": "numpy._typing._array_like._DualArrayLike"}}}, "_ArrayLikeBytes_co": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "numpy._typing._array_like._ArrayLikeBytes_co", "line": 154, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "TypeAliasType", "args": [{".class": "Instance", "args": ["numpy.bytes_"], "extra_attrs": null, "type_ref": "numpy.dtype"}, "builtins.bytes"], "type_ref": "numpy._typing._array_like._DualArrayLike"}}}, "_ArrayLikeComplex128_co": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "numpy._typing._array_like._ArrayLikeComplex128_co", "line": 171, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "TypeAliasType", "args": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.__Complex128_co"}], "extra_attrs": null, "type_ref": "numpy.dtype"}, {".class": "UnionType", "items": ["builtins.complex", "builtins.float", "builtins.int"], "uses_pep604_syntax": true}], "type_ref": "numpy._typing._array_like._DualArrayLike"}}}, "_ArrayLikeComplex_co": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "numpy._typing._array_like._ArrayLikeComplex_co", "line": 120, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "TypeAliasType", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "numpy.bool"}], "extra_attrs": null, "type_ref": "numpy.dtype"}, {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "numpy.integer"}], "extra_attrs": null, "type_ref": "numpy.dtype"}, {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "numpy.floating"}], "extra_attrs": null, "type_ref": "numpy.dtype"}, {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "numpy.complexfloating"}], "extra_attrs": null, "type_ref": "numpy.dtype"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", "builtins.int", "builtins.float", "builtins.complex"], "uses_pep604_syntax": true}], "type_ref": "numpy._typing._array_like._DualArrayLike"}}}, "_ArrayLikeDT64_co": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "numpy._typing._array_like._ArrayLikeDT64_co", "line": 137, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["datetime.date", "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.datetime64"}], "extra_attrs": null, "type_ref": "numpy.dtype"}], "extra_attrs": null, "type_ref": "numpy._typing._array_like._SupportsArray"}, {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["datetime.date", "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.datetime64"}], "extra_attrs": null, "type_ref": "numpy.dtype"}], "extra_attrs": null, "type_ref": "numpy._typing._array_like._SupportsArray"}], "extra_attrs": null, "type_ref": "numpy._typing._nested_sequence._NestedSequence"}], "uses_pep604_syntax": true}}}, "_ArrayLikeFloat64_co": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "numpy._typing._array_like._ArrayLikeFloat64_co", "line": 170, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "TypeAliasType", "args": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.__Float64_co"}], "extra_attrs": null, "type_ref": "numpy.dtype"}, {".class": "UnionType", "items": ["builtins.float", "builtins.int"], "uses_pep604_syntax": true}], "type_ref": "numpy._typing._array_like._DualArrayLike"}}}, "_ArrayLikeFloat_co": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "numpy._typing._array_like._ArrayLikeFloat_co", "line": 116, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "TypeAliasType", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "numpy.bool"}], "extra_attrs": null, "type_ref": "numpy.dtype"}, {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "numpy.integer"}], "extra_attrs": null, "type_ref": "numpy.dtype"}, {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "numpy.floating"}], "extra_attrs": null, "type_ref": "numpy.dtype"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", "builtins.int", "builtins.float"], "uses_pep604_syntax": true}], "type_ref": "numpy._typing._array_like._DualArrayLike"}}}, "_ArrayLikeInt": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "numpy._typing._array_like._ArrayLikeInt", "line": 174, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "TypeAliasType", "args": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "numpy.integer"}], "extra_attrs": null, "type_ref": "numpy.dtype"}, "builtins.int"], "type_ref": "numpy._typing._array_like._DualArrayLike"}}}, "_ArrayLikeInt_co": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "numpy._typing._array_like._ArrayLikeInt_co", "line": 112, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "TypeAliasType", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "numpy.bool"}], "extra_attrs": null, "type_ref": "numpy.dtype"}, {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "numpy.integer"}], "extra_attrs": null, "type_ref": "numpy.dtype"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", "builtins.int"], "uses_pep604_syntax": true}], "type_ref": "numpy._typing._array_like._DualArrayLike"}}}, "_ArrayLikeNumber_co": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "numpy._typing._array_like._ArrayLikeNumber_co", "line": 129, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "TypeAliasType", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "numpy.bool"}], "extra_attrs": null, "type_ref": "numpy.dtype"}, {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "UnionType", "items": ["builtins.int", "builtins.float", "builtins.complex"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.number"}], "extra_attrs": null, "type_ref": "numpy.dtype"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", "builtins.int", "builtins.float", "builtins.complex"], "uses_pep604_syntax": true}], "type_ref": "numpy._typing._array_like._DualArrayLike"}}}, "_ArrayLikeObject_co": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "numpy._typing._array_like._ArrayLikeObject_co", "line": 141, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "Instance", "args": ["numpy.object_"], "extra_attrs": null, "type_ref": "numpy.dtype"}], "extra_attrs": null, "type_ref": "numpy._typing._array_like._SupportsArray"}, {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "Instance", "args": ["numpy.object_"], "extra_attrs": null, "type_ref": "numpy.dtype"}], "extra_attrs": null, "type_ref": "numpy._typing._array_like._SupportsArray"}], "extra_attrs": null, "type_ref": "numpy._typing._nested_sequence._NestedSequence"}], "uses_pep604_syntax": true}}}, "_ArrayLikeStr_co": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "numpy._typing._array_like._ArrayLikeStr_co", "line": 150, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "TypeAliasType", "args": [{".class": "Instance", "args": ["numpy.str_"], "extra_attrs": null, "type_ref": "numpy.dtype"}, "builtins.str"], "type_ref": "numpy._typing._array_like._DualArrayLike"}}}, "_ArrayLikeString_co": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "numpy._typing._array_like._ArrayLikeString_co", "line": 158, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "TypeAliasType", "args": ["numpy.dtypes.StringDType", "builtins.str"], "type_ref": "numpy._typing._array_like._DualArrayLike"}}}, "_ArrayLikeTD64_co": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "numpy._typing._array_like._ArrayLikeTD64_co", "line": 133, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "TypeAliasType", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "numpy.bool"}], "extra_attrs": null, "type_ref": "numpy.dtype"}, {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "numpy.integer"}], "extra_attrs": null, "type_ref": "numpy.dtype"}, {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["datetime.<PERSON><PERSON><PERSON>", "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.timedelta64"}], "extra_attrs": null, "type_ref": "numpy.dtype"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", "builtins.int"], "uses_pep604_syntax": true}], "type_ref": "numpy._typing._array_like._DualArrayLike"}}}, "_ArrayLikeUInt_co": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "numpy._typing._array_like._ArrayLikeUInt_co", "line": 108, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "TypeAliasType", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "numpy.bool"}], "extra_attrs": null, "type_ref": "numpy.dtype"}, {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "numpy.unsignedinteger"}], "extra_attrs": null, "type_ref": "numpy.dtype"}], "uses_pep604_syntax": true}, "builtins.bool"], "type_ref": "numpy._typing._array_like._DualArrayLike"}}}, "_ArrayLikeUnknown": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "numpy._typing._array_like._ArrayLikeUnknown", "line": 189, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "TypeAliasType", "args": [{".class": "Instance", "args": [{".class": "UninhabitedType"}], "extra_attrs": null, "type_ref": "numpy.dtype"}, {".class": "UninhabitedType"}], "type_ref": "numpy._typing._array_like._DualArrayLike"}}}, "_ArrayLikeVoid_co": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "numpy._typing._array_like._ArrayLikeVoid_co", "line": 146, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "Instance", "args": ["numpy.void"], "extra_attrs": null, "type_ref": "numpy.dtype"}], "extra_attrs": null, "type_ref": "numpy._typing._array_like._SupportsArray"}, {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "Instance", "args": ["numpy.void"], "extra_attrs": null, "type_ref": "numpy.dtype"}], "extra_attrs": null, "type_ref": "numpy._typing._array_like._SupportsArray"}], "extra_attrs": null, "type_ref": "numpy._typing._nested_sequence._NestedSequence"}], "uses_pep604_syntax": true}}}, "_Buffer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["__buffer__", 2]], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy._typing._array_like._Buffer", "name": "_<PERSON><PERSON>er", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract", "is_protocol", "runtime_protocol"], "fullname": "numpy._typing._array_like._Buffer", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "numpy._typing._array_like", "mro": ["numpy._typing._array_like._Buffer", "builtins.object"], "names": {".class": "SymbolTable", "__buffer__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_body"], "fullname": "numpy._typing._array_like._Buffer.__buffer__", "name": "__buffer__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["numpy._typing._array_like._Buffer", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__buffer__ of _<PERSON><PERSON>er", "ret_type": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.memoryview"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._array_like._Buffer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "numpy._typing._array_like._Buffer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_DType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._array_like._DType", "name": "_DType", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.dtype"}, "values": [], "variance": 0}}, "_DType_co": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._array_like._DType_co", "name": "_DType_co", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.dtype"}, "values": [], "variance": 1}}, "_DualArrayLike": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._array_like._DType", "id": 1, "name": "_DType", "namespace": "numpy._typing._array_like._DualArrayLike", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.dtype"}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._array_like._T", "id": 2, "name": "_T", "namespace": "numpy._typing._array_like._DualArrayLike", "upper_bound": "builtins.object", "values": [], "variance": 0}], "column": 0, "fullname": "numpy._typing._array_like._DualArrayLike", "line": 83, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._array_like._DType", "id": 1, "name": "_DType", "namespace": "numpy._typing._array_like._DualArrayLike", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.dtype"}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._array_like._SupportsArray"}, {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._array_like._DType", "id": 1, "name": "_DType", "namespace": "numpy._typing._array_like._DualArrayLike", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.dtype"}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._array_like._SupportsArray"}], "extra_attrs": null, "type_ref": "numpy._typing._nested_sequence._NestedSequence"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._array_like._T", "id": 2, "name": "_T", "namespace": "numpy._typing._array_like._DualArrayLike", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._array_like._T", "id": 2, "name": "_T", "namespace": "numpy._typing._array_like._DualArrayLike", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._nested_sequence._NestedSequence"}], "uses_pep604_syntax": true}}}, "_FiniteNestedSequence": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._array_like._T", "id": 1, "name": "_T", "namespace": "numpy._typing._array_like._FiniteNestedSequence", "upper_bound": "builtins.object", "values": [], "variance": 0}], "column": 0, "fullname": "numpy._typing._array_like._FiniteNestedSequence", "line": 66, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._array_like._T", "id": 1, "name": "_T", "namespace": "numpy._typing._array_like._FiniteNestedSequence", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._array_like._T", "id": 1, "name": "_T", "namespace": "numpy._typing._array_like._FiniteNestedSequence", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._array_like._T", "id": 1, "name": "_T", "namespace": "numpy._typing._array_like._FiniteNestedSequence", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._array_like._T", "id": 1, "name": "_T", "namespace": "numpy._typing._array_like._FiniteNestedSequence", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._array_like._T", "id": 1, "name": "_T", "namespace": "numpy._typing._array_like._FiniteNestedSequence", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "uses_pep604_syntax": true}}}, "_NestedSequence": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._nested_sequence._NestedSequence", "kind": "Gdef"}, "_ScalarType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._array_like._ScalarType", "name": "_ScalarType", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}}, "_ScalarType_co": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._array_like._ScalarType_co", "name": "_ScalarType_co", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 1}}, "_Shape": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._shape._Shape", "kind": "Gdef"}, "_SupportsArray": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["__array__", 2]], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy._typing._array_like._SupportsArray", "name": "_SupportsArray", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._array_like._DType_co", "id": 1, "name": "_DType_co", "namespace": "numpy._typing._array_like._SupportsArray", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.dtype"}, "values": [], "variance": 1}]}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract", "is_protocol", "runtime_protocol"], "fullname": "numpy._typing._array_like._SupportsArray", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "numpy._typing._array_like", "mro": ["numpy._typing._array_like._SupportsArray", "builtins.object"], "names": {".class": "SymbolTable", "__array__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_body"], "fullname": "numpy._typing._array_like._SupportsArray.__array__", "name": "__array__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._array_like._DType_co", "id": 1, "name": "_DType_co", "namespace": "numpy._typing._array_like._SupportsArray", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.dtype"}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy._typing._array_like._SupportsArray"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__array__ of _SupportsArray", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._array_like._DType_co", "id": 1, "name": "_DType_co", "namespace": "numpy._typing._array_like._SupportsArray", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.dtype"}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._array_like._SupportsArray.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._array_like._DType_co", "id": 1, "name": "_DType_co", "namespace": "numpy._typing._array_like._SupportsArray", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.dtype"}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy._typing._array_like._SupportsArray"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["_DType_co"], "typeddict_type": null}}, "_SupportsArrayFunc": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["__array_function__", 2]], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy._typing._array_like._SupportsArrayFunc", "name": "_SupportsArrayFunc", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract", "is_protocol", "runtime_protocol"], "fullname": "numpy._typing._array_like._SupportsArrayFunc", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "numpy._typing._array_like", "mro": ["numpy._typing._array_like._SupportsArrayFunc", "builtins.object"], "names": {".class": "SymbolTable", "__array_function__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "func", "types", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_body"], "fullname": "numpy._typing._array_like._SupportsArrayFunc.__array_function__", "name": "__array_function__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "func", "types", "args", "kwargs"], "arg_types": ["numpy._typing._array_like._SupportsArrayFunc", {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "Instance", "args": [{".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}], "extra_attrs": null, "type_ref": "typing.Collection"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__array_function__ of _SupportsArrayFunc", "ret_type": "builtins.object", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._array_like._SupportsArrayFunc.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "numpy._typing._array_like._SupportsArrayFunc", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_T": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._array_like._T", "name": "_T", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "_UnknownType": {".class": "SymbolTableNode", "cross_ref": "typing.Never", "kind": "Gdef"}, "__Complex128_co": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "numpy._typing._array_like.__Complex128_co", "line": 169, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "Instance", "args": ["numpy._typing._nbit_base._64Bit", {".class": "UnionType", "items": ["builtins.int", "builtins.float", "builtins.complex"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.number"}, {".class": "Instance", "args": ["numpy._typing._nbit_base._32Bit", {".class": "UnionType", "items": ["builtins.int", "builtins.float", "builtins.complex"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.number"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.float16"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "numpy.integer"}, {".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "numpy.bool"}], "uses_pep604_syntax": true}}}, "__Float64_co": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "numpy._typing._array_like.__Float64_co", "line": 168, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "Instance", "args": ["numpy._typing._nbit_base._64Bit"], "extra_attrs": null, "type_ref": "numpy.floating"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.float32"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.float16"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "numpy.integer"}, {".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "numpy.bool"}], "uses_pep604_syntax": true}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy._typing._array_like.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy._typing._array_like.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy._typing._array_like.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy._typing._array_like.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy._typing._array_like.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy._typing._array_like.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "bytes_": {".class": "SymbolTableNode", "cross_ref": "numpy.bytes_", "kind": "Gdef"}, "complexfloating": {".class": "SymbolTableNode", "cross_ref": "numpy.complexfloating", "kind": "Gdef"}, "datetime64": {".class": "SymbolTableNode", "cross_ref": "numpy.datetime64", "kind": "Gdef"}, "dtype": {".class": "SymbolTableNode", "cross_ref": "numpy.dtype", "kind": "Gdef"}, "floating": {".class": "SymbolTableNode", "cross_ref": "numpy.floating", "kind": "Gdef"}, "generic": {".class": "SymbolTableNode", "cross_ref": "numpy.generic", "kind": "Gdef"}, "integer": {".class": "SymbolTableNode", "cross_ref": "numpy.integer", "kind": "Gdef"}, "ndarray": {".class": "SymbolTableNode", "cross_ref": "numpy.n<PERSON><PERSON>", "kind": "Gdef"}, "np": {".class": "SymbolTableNode", "cross_ref": "numpy", "kind": "Gdef"}, "number": {".class": "SymbolTableNode", "cross_ref": "numpy.number", "kind": "Gdef"}, "object_": {".class": "SymbolTableNode", "cross_ref": "numpy.object_", "kind": "Gdef"}, "runtime_checkable": {".class": "SymbolTableNode", "cross_ref": "typing.runtime_checkable", "kind": "Gdef"}, "str_": {".class": "SymbolTableNode", "cross_ref": "numpy.str_", "kind": "Gdef"}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef"}, "timedelta64": {".class": "SymbolTableNode", "cross_ref": "numpy.timedelta64", "kind": "Gdef"}, "unsignedinteger": {".class": "SymbolTableNode", "cross_ref": "numpy.unsignedinteger", "kind": "Gdef"}, "void": {".class": "SymbolTableNode", "cross_ref": "numpy.void", "kind": "Gdef"}}, "path": "c:\\Python313\\Lib\\site-packages\\numpy\\_typing\\_array_like.py"}