[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "landlord-assistant-2025"
version = "1.0.0"
description = "现代化斗地主辅助软件 - 2025年最新技术栈"
authors = [{name = "Developer", email = "<EMAIL>"}]
readme = "README.md"
requires-python = ">=3.11"
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: End Users/Desktop",
    "License :: OSI Approved :: MIT License",
    "Operating System :: Microsoft :: Windows",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
]
dependencies = [
    "flet>=0.24.0",
    "opencv-python>=4.9.0",
    "transformers>=4.40.0",
    "paddleocr>=2.8.0",
    "ultralytics>=8.2.0",
    "pillow>=10.3.0",
    "numpy>=1.26.0",
    "pyautogui>=0.9.54",
    "pywin32>=306",
    "pydantic>=2.7.0",
    "loguru>=0.7.0",
    "torch>=2.0.0",
    "torchvision>=0.15.0",
    "requests>=2.31.0",
    "aiohttp>=3.9.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.4.0",
    "black>=23.0.0",
    "isort>=5.12.0",
    "flake8>=6.0.0",
    "mypy>=1.5.0",
]

[project.urls]
Homepage = "https://github.com/username/landlord-assistant-2025"
Repository = "https://github.com/username/landlord-assistant-2025.git"
Issues = "https://github.com/username/landlord-assistant-2025/issues"

[project.scripts]
landlord-assistant = "src.main:main"

[tool.setuptools.packages.find]
where = ["src"]

[tool.black]
line-length = 88
target-version = ['py311']
include = '\.pyi?$'

[tool.isort]
profile = "black"
line_length = 88

[tool.mypy]
python_version = "3.11"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]