#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
OCR文字识别服务模块

提供游戏界面文字识别功能，支持多种OCR引擎
"""

import cv2
import numpy as np
import re
from typing import List, Dict, Optional, Tuple, Any
from dataclasses import dataclass, field
from enum import Enum
from pathlib import Path

from ..utils.logger import LoggerMixin
from ..utils.config import ConfigManager

# 可选依赖检查
try:
    import paddleocr
    PADDLE_AVAILABLE = True
except ImportError:
    PADDLE_AVAILABLE = False

try:
    from transformers import pipeline
    TRANSFORMERS_AVAILABLE = True
except ImportError:
    TRANSFORMERS_AVAILABLE = False

try:
    import easyocr
    EASYOCR_AVAILABLE = True
except ImportError:
    EASYOCR_AVAILABLE = False


class OCREngine(Enum):
    """OCR引擎枚举"""
    PADDLE_OCR = "PaddleOCR"
    EASY_OCR = "EasyOCR"
    TRANSFORMERS = "Transformers"
    TESSERACT = "Tesseract"


class TextType(Enum):
    """文本类型枚举"""
    CARD_COUNT = "卡牌数量"
    GAME_PHASE = "游戏阶段"
    ACTION_TEXT = "操作文本"
    SCORE = "分数"
    BUTTON_TEXT = "按钮文本"
    PLAYER_NAME = "玩家名称"


@dataclass
class TextDetection:
    """文本检测结果"""
    text: str
    bbox: Tuple[int, int, int, int]  # (x1, y1, x2, y2)
    confidence: float
    text_type: Optional[TextType] = None


@dataclass
class OCRResult:
    """OCR识别结果"""
    detections: List[TextDetection] = field(default_factory=list)
    player_names: Dict[str, str] = field(default_factory=dict)
    card_counts: Dict[str, int] = field(default_factory=dict)
    scores: Dict[str, int] = field(default_factory=dict)
    button_texts: List[str] = field(default_factory=list)
    game_phase: Optional[str] = None
    
    def __post_init__(self):
        # 设置默认值
        if not self.player_names:
            self.player_names = {}
        if not self.card_counts:
            self.card_counts = {}
        if not self.scores:
            self.scores = {}
        if not self.button_texts:
            self.button_texts = []


class OCRService(LoggerMixin):
    """OCR文字识别服务"""
    
    def __init__(self, config_manager: ConfigManager):
        super().__init__()
        self.config = config_manager
        
        # OCR引擎
        self.paddle_ocr = None
        self.easy_ocr = None
        self.transformers_pipeline = None
        
        # 当前使用的引擎
        self.current_engine = OCREngine.PADDLE_OCR
        
        # 识别参数
        self.confidence_threshold = 0.5
        self.languages = ['ch_sim', 'en']  # 中文简体和英文
        
        # 文本模式匹配
        self.text_patterns = self._init_text_patterns()
        
        # 缓存
        self._ocr_cache = {}
        
        self.logger.info("OCR服务初始化完成")
    
    def _init_text_patterns(self) -> Dict[TextType, List[str]]:
        """初始化文本模式匹配"""
        return {
            TextType.CARD_COUNT: [
                r'(\d+)张牌?',
                r'剩余(\d+)张',
                r'(\d+)cards?',
                r'(\d+)张'
            ],
            TextType.GAME_PHASE: [
                r'叫地主',
                r'抢地主', 
                r'出牌',
                r'等待',
                r'游戏结束',
                r'bidding',
                r'playing',
                r'waiting'
            ],
            TextType.ACTION_TEXT: [
                r'不要',
                r'要不起',
                r'要',
                r'pass',
                r'叫地主',
                r'抢地主',
                r'不叫',
                r'不抢'
            ],
            TextType.SCORE: [
                r'(\d+)分',
                r'得分[:：](\d+)',
                r'score[:：]?(\d+)',
                r'(\d+)points?'
            ],
            TextType.BUTTON_TEXT: [
                r'出牌',
                r'不要',
                r'提示',
                r'托管',
                r'设置',
                r'play',
                r'pass',
                r'hint',
                r'auto'
            ]
        }
    
    async def initialize_engines(self) -> bool:
        """初始化OCR引擎"""
        try:
            # 初始化PaddleOCR
            if PADDLE_AVAILABLE:
                self.paddle_ocr = paddleocr.PaddleOCR(
                    use_angle_cls=True,
                    lang='ch',
                    show_log=False
                )
                self.logger.info("PaddleOCR初始化完成")
            
            # 初始化EasyOCR
            if EASYOCR_AVAILABLE:
                self.easy_ocr = easyocr.Reader(['ch_sim', 'en'])
                self.logger.info("EasyOCR初始化完成")
            
            # 初始化Transformers
            if TRANSFORMERS_AVAILABLE:
                # 这里可以加载OCR相关的transformers模型
                pass
            
            self.logger.info("OCR引擎初始化完成")
            return True
            
        except Exception as e:
            self.logger.error(f"OCR引擎初始化失败: {e}")
            return False
    
    async def recognize_text(
        self, 
        image: np.ndarray, 
        region_name: str = "unknown"
    ) -> OCRResult:
        """识别图像中的文字"""
        try:
            # 图像预处理
            processed_img = self._preprocess_for_ocr(image)
            
            # 执行OCR识别
            if self.current_engine == OCREngine.PADDLE_OCR and self.paddle_ocr:
                detections = self._recognize_with_paddle(processed_img)
            elif self.current_engine == OCREngine.EASY_OCR and self.easy_ocr:
                detections = self._recognize_with_easyocr(processed_img)
            else:
                # 回退到简单的文本检测
                detections = self._recognize_fallback(processed_img)
            
            # 后处理和分类
            result = self._post_process_ocr_results(detections, region_name)
            
            self.logger.debug(f"OCR识别完成，检测到{len(detections)}个文本区域")
            return result
            
        except Exception as e:
            self.logger.error(f"OCR识别失败: {e}")
            return OCRResult()
    
    def _preprocess_for_ocr(self, image: np.ndarray) -> np.ndarray:
        """OCR预处理"""
        processed = image.copy()
        
        try:
            # 转换为灰度图
            if len(processed.shape) == 3:
                processed = cv2.cvtColor(processed, cv2.COLOR_BGR2GRAY)
            
            # 二值化
            _, processed = cv2.threshold(processed, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
            
            # 形态学操作去噪
            kernel = np.ones((2, 2), np.uint8)
            processed = cv2.morphologyEx(processed, cv2.MORPH_CLOSE, kernel)
            
            return processed
            
        except Exception as e:
            self.logger.warning(f"OCR预处理失败: {e}")
            return image
    
    def _recognize_with_paddle(self, image: np.ndarray) -> List[TextDetection]:
        """使用PaddleOCR识别"""
        detections = []
        
        try:
            results = self.paddle_ocr.ocr(image, cls=True)
            
            if results and results[0]:
                for line in results[0]:
                    if line:
                        bbox_points = line[0]
                        text_info = line[1]
                        
                        if text_info and len(text_info) >= 2:
                            text = text_info[0]
                            confidence = text_info[1]
                            
                            if confidence >= self.confidence_threshold:
                                # 转换bbox格式
                                x_coords = [p[0] for p in bbox_points]
                                y_coords = [p[1] for p in bbox_points]
                                bbox = (
                                    int(min(x_coords)), int(min(y_coords)),
                                    int(max(x_coords)), int(max(y_coords))
                                )
                                
                                detection = TextDetection(
                                    text=text,
                                    bbox=bbox,
                                    confidence=confidence
                                )
                                detections.append(detection)
            
        except Exception as e:
            self.logger.error(f"PaddleOCR识别失败: {e}")
        
        return detections
    
    def _recognize_with_easyocr(self, image: np.ndarray) -> List[TextDetection]:
        """使用EasyOCR识别"""
        detections = []
        
        try:
            results = self.easy_ocr.readtext(image)
            
            for result in results:
                if len(result) >= 3:
                    bbox_points = result[0]
                    text = result[1]
                    confidence = result[2]
                    
                    if confidence >= self.confidence_threshold:
                        # 转换bbox格式
                        x_coords = [p[0] for p in bbox_points]
                        y_coords = [p[1] for p in bbox_points]
                        bbox = (
                            int(min(x_coords)), int(min(y_coords)),
                            int(max(x_coords)), int(max(y_coords))
                        )
                        
                        detection = TextDetection(
                            text=text,
                            bbox=bbox,
                            confidence=confidence
                        )
                        detections.append(detection)
            
        except Exception as e:
            self.logger.error(f"EasyOCR识别失败: {e}")
        
        return detections
    
    def _recognize_fallback(self, image: np.ndarray) -> List[TextDetection]:
        """回退识别方法"""
        # 简单的文本区域检测，不进行实际OCR
        detections = []
        
        try:
            # 查找文本轮廓
            contours, _ = cv2.findContours(image, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            for contour in contours:
                x, y, w, h = cv2.boundingRect(contour)
                
                # 过滤太小的区域
                if w > 20 and h > 10:
                    detection = TextDetection(
                        text="[文本区域]",
                        bbox=(x, y, x + w, y + h),
                        confidence=0.5
                    )
                    detections.append(detection)
            
        except Exception as e:
            self.logger.error(f"回退识别失败: {e}")

        return detections

    def _post_process_ocr_results(self, detections: List[TextDetection], region_name: str) -> OCRResult:
        """后处理OCR结果"""
        result = OCRResult(detections=detections)

        try:
            for detection in detections:
                # 分类文本类型
                text_type = self._classify_text_type(detection.text)
                detection.text_type = text_type

                # 根据类型提取信息
                if text_type == TextType.CARD_COUNT:
                    count = self._extract_card_count(detection.text)
                    if count is not None:
                        result.card_counts[region_name] = count

                elif text_type == TextType.SCORE:
                    score = self._extract_score(detection.text)
                    if score is not None:
                        result.scores[region_name] = score

                elif text_type == TextType.GAME_PHASE:
                    result.game_phase = detection.text

                elif text_type == TextType.BUTTON_TEXT:
                    result.button_texts.append(detection.text)

                elif text_type == TextType.PLAYER_NAME:
                    result.player_names[region_name] = detection.text

            return result

        except Exception as e:
            self.logger.error(f"OCR结果后处理失败: {e}")
            return result

    def _classify_text_type(self, text: str) -> Optional[TextType]:
        """分类文本类型"""
        text_lower = text.lower().strip()

        for text_type, patterns in self.text_patterns.items():
            for pattern in patterns:
                if re.search(pattern, text_lower, re.IGNORECASE):
                    return text_type

        return None

    def _extract_card_count(self, text: str) -> Optional[int]:
        """提取卡牌数量"""
        patterns = [
            r'(\d+)张',
            r'剩余(\d+)',
            r'(\d+)cards?'
        ]

        for pattern in patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                try:
                    return int(match.group(1))
                except (ValueError, IndexError):
                    continue

        return None

    def _extract_score(self, text: str) -> Optional[int]:
        """提取分数"""
        patterns = [
            r'(\d+)分',
            r'得分[:：](\d+)',
            r'score[:：]?(\d+)',
            r'(\d+)points?'
        ]

        for pattern in patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                try:
                    return int(match.group(1))
                except (ValueError, IndexError):
                    continue

        return None

    def get_available_engines(self) -> List[str]:
        """获取可用的OCR引擎"""
        engines = []

        if PADDLE_AVAILABLE:
            engines.append("PaddleOCR")
        if EASYOCR_AVAILABLE:
            engines.append("EasyOCR")
        if TRANSFORMERS_AVAILABLE:
            engines.append("Transformers")

        return engines

    def set_engine(self, engine: OCREngine) -> bool:
        """设置OCR引擎"""
        try:
            if engine == OCREngine.PADDLE_OCR and PADDLE_AVAILABLE:
                self.current_engine = engine
                return True
            elif engine == OCREngine.EASY_OCR and EASYOCR_AVAILABLE:
                self.current_engine = engine
                return True
            elif engine == OCREngine.TRANSFORMERS and TRANSFORMERS_AVAILABLE:
                self.current_engine = engine
                return True
            else:
                self.logger.warning(f"OCR引擎 {engine.value} 不可用")
                return False

        except Exception as e:
            self.logger.error(f"设置OCR引擎失败: {e}")
            return False

    def clear_cache(self):
        """清空缓存"""
        self._ocr_cache.clear()
        self.logger.info("OCR缓存已清空")

    def get_status(self) -> Dict[str, Any]:
        """获取服务状态"""
        return {
            "current_engine": self.current_engine.value,
            "available_engines": self.get_available_engines(),
            "confidence_threshold": self.confidence_threshold,
            "languages": self.languages,
            "cache_size": len(self._ocr_cache)
        }
