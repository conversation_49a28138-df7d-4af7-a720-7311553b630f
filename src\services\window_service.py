# -*- coding: utf-8 -*-
"""
窗口管理服务

负责窗口检测、选择、锁定和截图等功能
"""

import time
import threading
from typing import List, Dict, Optional, Tuple, Callable
from dataclasses import dataclass
from pathlib import Path

try:
    import win32gui
    import win32process
    import win32con
    import win32api
except ImportError:
    print("Warning: pywin32 not installed. Window management features will be limited.")
    win32gui = None

import numpy as np
from PIL import ImageGrab, Image
import cv2

from ..utils.logger import LoggerMixin


@dataclass
class WindowInfo:
    """窗口信息数据类"""
    hwnd: int
    title: str
    class_name: str
    process_name: str
    process_id: int
    rect: Tuple[int, int, int, int]  # (left, top, right, bottom)
    is_visible: bool
    is_minimized: bool
    
    @property
    def width(self) -> int:
        """窗口宽度"""
        return self.rect[2] - self.rect[0]
    
    @property
    def height(self) -> int:
        """窗口高度"""
        return self.rect[3] - self.rect[1]
    
    @property
    def center(self) -> Tuple[int, int]:
        """窗口中心点"""
        return (
            self.rect[0] + self.width // 2,
            self.rect[1] + self.height // 2
        )
    
    def __str__(self) -> str:
        return f"Window('{self.title}', {self.width}x{self.height})"


@dataclass
class GameRegion:
    """游戏区域配置"""
    name: str
    x: int
    y: int
    width: int
    height: int
    description: str = ""
    
    @property
    def rect(self) -> Tuple[int, int, int, int]:
        """区域矩形 (left, top, right, bottom)"""
        return (self.x, self.y, self.x + self.width, self.y + self.height)
    
    def contains_point(self, x: int, y: int) -> bool:
        """检查点是否在区域内"""
        return self.x <= x <= self.x + self.width and self.y <= y <= self.y + self.height
    
    def __str__(self) -> str:
        """转换为字符串"""
        return f"Region('{self.name}', {self.x}, {self.y}, {self.width}x{self.height})"


class WindowService(LoggerMixin):
    """窗口管理服务"""
    
    def __init__(self):
        super().__init__()
        
        # 窗口状态
        self.target_window: Optional[WindowInfo] = None
        self.is_monitoring = False
        self.monitor_thread: Optional[threading.Thread] = None
        
        # 游戏区域配置
        self.game_regions: Dict[str, GameRegion] = {}
        self._init_default_regions()
        
        # 回调函数
        self.on_window_lost: Optional[Callable] = None
        self.on_window_found: Optional[Callable] = None
        
        # 常见斗地主游戏窗口标题关键词
        self.game_keywords = [
            "斗地主", "欢乐斗地主", "QQ游戏", "腾讯游戏",
            "JJ斗地主", "途游斗地主", "波克斗地主",
            "landlord", "doudizhu"
        ]
        
        self.logger.info("窗口管理服务初始化完成")
    
    def _init_default_regions(self):
        """初始化默认游戏区域"""
        # 这些是常见的斗地主游戏区域配置
        # 实际使用时需要根据具体游戏调整
        default_regions = {
            "user_hand": GameRegion(
                name="用户手牌区域",
                x=200, y=400, width=400, height=120,
                description="用户手牌显示区域"
            ),
            "table_cards": GameRegion(
                name="桌面出牌区域",
                x=300, y=200, width=200, height=150,
                description="桌面出牌显示区域"
            ),
            "left_player": GameRegion(
                name="左侧玩家区域",
                x=50, y=150, width=150, height=200,
                description="左侧玩家信息区域"
            ),
            "right_player": GameRegion(
                name="右侧玩家区域",
                x=600, y=150, width=150, height=200,
                description="右侧玩家信息区域"
            ),
            "game_info": GameRegion(
                name="游戏信息区域",
                x=300, y=50, width=200, height=100,
                description="游戏状态信息区域"
            )
        }
        
        self.game_regions.update(default_regions)
        self.logger.debug(f"初始化了 {len(default_regions)} 个默认游戏区域")
    
    def get_all_windows(self) -> List[WindowInfo]:
        """获取所有窗口信息"""
        if not win32gui:
            self.logger.error("pywin32 not available")
            return []
        
        windows = []
        
        def enum_windows_callback(hwnd, windows_list):
            try:
                # 获取窗口标题
                title = win32gui.GetWindowText(hwnd)
                if not title:
                    return True
                
                # 获取窗口矩形
                rect = win32gui.GetWindowRect(hwnd)
                
                # 获取进程信息
                try:
                    _, process_id = win32process.GetWindowThreadProcessId(hwnd)
                    process_handle = win32api.OpenProcess(
                        win32con.PROCESS_QUERY_INFORMATION | win32con.PROCESS_VM_READ,
                        False, process_id
                    )
                    process_name = win32process.GetModuleFileNameEx(process_handle, 0)
                    process_name = Path(process_name).name
                    win32api.CloseHandle(process_handle)
                except:
                    process_name = "Unknown"
                    process_id = 0
                
                # 获取窗口类名
                class_name = win32gui.GetClassName(hwnd)
                
                # 检查窗口状态
                try:
                    is_visible = win32gui.IsWindowVisible(hwnd)
                    is_minimized = win32gui.IsIconic(hwnd)
                except:
                    is_visible = False
                    is_minimized = True
                
                # 创建窗口信息对象
                window_info = WindowInfo(
                    hwnd=hwnd,
                    title=title,
                    class_name=class_name,
                    process_name=process_name,
                    process_id=process_id,
                    rect=rect,
                    is_visible=is_visible,
                    is_minimized=is_minimized
                )
                
                # 过滤掉太小的窗口
                if window_info.width > 100 and window_info.height > 100:
                    windows_list.append(window_info)
                    
            except Exception as e:
                self.logger.debug(f"获取窗口信息失败: {e}")
            
            return True
        
        try:
            win32gui.EnumWindows(enum_windows_callback, windows)
            self.logger.debug(f"找到 {len(windows)} 个有效窗口")
        except Exception as e:
            self.logger.error(f"枚举窗口失败: {e}")
        
        return windows
    
    def find_game_windows(self) -> List[WindowInfo]:
        """查找可能的斗地主游戏窗口"""
        all_windows = self.get_all_windows()
        game_windows = []
        
        for window in all_windows:
            # 检查标题是否包含游戏关键词
            title_lower = window.title.lower()
            if any(keyword.lower() in title_lower for keyword in self.game_keywords):
                game_windows.append(window)
                continue
            
            # 检查进程名是否包含游戏关键字
            process_lower = window.process_name.lower()
            if any(keyword.lower() in process_lower for keyword in self.game_keywords):
                game_windows.append(window)
        
        self.logger.info(f"找到 {len(game_windows)} 个可能的游戏窗口")
        return game_windows
    
    def select_window(self, window_info: WindowInfo) -> bool:
        """选择目标窗口"""
        try:
            # 验证窗口是否仍然有效
            if not win32gui.IsWindow(window_info.hwnd):
                self.logger.error("选择的窗口已无效")
                return False
            
            self.target_window = window_info
            
            # 如果窗口被最小化，恢复它
            if window_info.is_minimized:
                win32gui.ShowWindow(window_info.hwnd, win32con.SW_RESTORE)
                time.sleep(0.5)
            
            # 将窗口置于前台
            win32gui.SetForegroundWindow(window_info.hwnd)
            
            # 更新窗口信息
            self.update_window_info()
            
            # 自动调整游戏区域
            self._auto_adjust_regions()
            
            self.logger.info(f"已选择窗口: {window_info}")
            
            # 触发回调
            if self.on_window_found:
                self.on_window_found(window_info)
            
            return True
            
        except Exception as e:
            self.logger.error(f"选择窗口失败: {e}")
            return False
    
    def update_window_info(self) -> bool:
        """更新目标窗口信息"""
        if not self.target_window:
            return False
        
        try:
            hwnd = self.target_window.hwnd
            
            # 检查窗口是否仍然存在
            if not win32gui.IsWindow(hwnd):
                self.logger.warning("目标窗口已不存在")
                self.target_window = None
                return False
            
            # 更新窗口信息
            rect = win32gui.GetWindowRect(hwnd)
            title = win32gui.GetWindowText(hwnd)
            is_visible = win32gui.IsWindowVisible(hwnd)
            is_minimized = win32gui.IsIconic(hwnd)
            
            # 更新窗口对象
            self.target_window.rect = rect
            self.target_window.title = title
            self.target_window.is_visible = is_visible
            self.target_window.is_minimized = is_minimized
            
            return True
            
        except Exception as e:
            self.logger.error(f"更新窗口信息失败: {e}")
            return False
    
    def _auto_adjust_regions(self):
        """根据窗口大小自动调整游戏区域"""
        if not self.target_window:
            return
        
        window_width = self.target_window.width
        window_height = self.target_window.height
        
        # 根据窗口大小按比例调整区域
        # 这里使用相对比例，可以适应不同分辨率
        adjustments = {
            "user_hand": {
                "x": int(window_width * 0.25),
                "y": int(window_height * 0.7),
                "width": int(window_width * 0.5),
                "height": int(window_height * 0.2)
            },
            "table_cards": {
                "x": int(window_width * 0.375),
                "y": int(window_height * 0.35),
                "width": int(window_width * 0.25),
                "height": int(window_height * 0.25)
            },
            "left_player": {
                "x": int(window_width * 0.05),
                "y": int(window_height * 0.25),
                "width": int(window_width * 0.2),
                "height": int(window_height * 0.35)
            },
            "right_player": {
                "x": int(window_width * 0.75),
                "y": int(window_height * 0.25),
                "width": int(window_width * 0.2),
                "height": int(window_height * 0.35)
            },
            "game_info": {
                "x": int(window_width * 0.375),
                "y": int(window_height * 0.05),
                "width": int(window_width * 0.25),
                "height": int(window_height * 0.15)
            }
        }
        
        # 应用调整
        for region_name, params in adjustments.items():
            if region_name in self.game_regions:
                region = self.game_regions[region_name]
                region.x = params["x"]
                region.y = params["y"]
                region.width = params["width"]
                region.height = params["height"]
        
        self.logger.debug("已自动调整游戏区域")
    
    def capture_screenshot(self, region_name: str = None) -> Optional[np.ndarray]:
        """截取窗口或指定区域
        
        Args:
            region_name: 区域名称，如果为None则截取整个窗口
            
        Returns:
            截图的numpy数组 (BGR格式)
        """
        if not self.target_window:
            self.logger.error("未选择目标窗口")
            return None
        
        try:
            # 更新窗口信息
            if not self.update_window_info():
                return None
            
            # 确定截图区域
            if region_name and region_name in self.game_regions:
                region = self.game_regions[region_name]
                # 转换为绝对坐标
                left = self.target_window.rect[0] + region.x
                top = self.target_window.rect[1] + region.y
                right = left + region.width
                bottom = top + region.height
            else:
                # 截取整个窗口
                left, top, right, bottom = self.target_window.rect
            
            # 使用PIL截图
            screenshot = ImageGrab.grab(bbox=(left, top, right, bottom))
            
            # 转换为numpy数组 (RGB -> BGR)
            screenshot_np = np.array(screenshot)
            screenshot_bgr = cv2.cvtColor(screenshot_np, cv2.COLOR_RGB2BGR)
            
            return screenshot_bgr
            
        except Exception as e:
            self.logger.error(f"截图失败: {e}")
            return None
    
    def save_screenshot(self, filepath: str, region_name: str = None) -> bool:
        """保存截图到文件"""
        screenshot = self.capture_screenshot(region_name)
        if screenshot is not None:
            cv2.imwrite(filepath, screenshot)
            self.logger.debug(f"截图已保存: {filepath}")
            return True
        else:
            self.logger.error(f"保存截图失败: {filepath}")
            return False
    
    def start_monitoring(self, interval: float = 1.0):
        """开始监控目标窗口"""
        if self.is_monitoring:
            self.logger.warning("窗口监控已在运行")
            return
        
        if not self.target_window:
            self.logger.error("未选择目标窗口，无法开始监控")
            return
        
        self.is_monitoring = True
        
        def monitor_thread():
            self._monitor_window_loop(interval)
        
        self.monitor_thread = threading.Thread(
            target=monitor_thread,
            daemon=True,
            name="WindowMonitor"
        )
        self.monitor_thread.start()
        
        self.logger.info(f"开始监控窗口，间隔: {interval}秒")
    
    def stop_monitoring(self):
        """停止监控窗口"""
        if not self.is_monitoring:
            return
        
        self.is_monitoring = False
        
        if self.monitor_thread and self.monitor_thread.is_alive():
            self.monitor_thread.join(timeout=2.0)
        
        self.logger.info("已停止窗口监控")
    
    def _monitor_window_loop(self, interval: float):
        """窗口监控线程"""
        while self.is_monitoring:
            try:
                if not self.update_window_info():
                    # 窗口丢失
                    if self.on_window_lost:
                        self.on_window_lost()
                    break
                
                time.sleep(interval)
                
            except Exception as e:
                self.logger.error(f"窗口监控异常: {e}")
                break
        
        self.is_monitoring = False
    
    # 回调设置方法
    def set_window_lost_callback(self, callback: Callable):
        """设置窗口丢失回调"""
        self.on_window_lost = callback
    
    def set_window_found_callback(self, callback: Callable):
        """设置窗口找到回调"""
        self.on_window_found = callback
    
    # 游戏区域管理方法
    def get_game_region(self, name: str) -> Optional[GameRegion]:
        """获取游戏区域"""
        return self.game_regions.get(name)
    
    def set_game_region(self, name: str, x: int, y: int, width: int, height: int, description: str = ""):
        """设置游戏区域"""
        self.game_regions[name] = GameRegion(name, x, y, width, height, description)
        self.logger.debug(f"设置游戏区域: {name} ({x}, {y}, {width}, {height})")
    
    def get_all_regions(self) -> Dict[str, GameRegion]:
        """获取所有游戏区域"""
        return self.game_regions.copy()
    
    # 状态查询方法
    def is_window_selected(self) -> bool:
        """是否已选择窗口"""
        return self.target_window is not None
    
    def get_current_window(self) -> Optional[WindowInfo]:
        """获取当前窗口信息"""
        return self.target_window
    
    def bring_to_front(self) -> bool:
        """将窗口置于前台"""
        if not self.target_window:
            return False
        
        try:
            hwnd = self.target_window.hwnd
            
            # 如果窗口被最小化，先恢复
            if win32gui.IsIconic(hwnd):
                win32gui.ShowWindow(hwnd, win32con.SW_RESTORE)
                time.sleep(0.2)
            
            # 置于前台
            win32gui.SetForegroundWindow(hwnd)
            return True
            
        except Exception as e:
            self.logger.error(f"置于前台失败: {e}")
            return False
    
    def cleanup(self):
        """清理资源"""
        self.stop_monitoring()
        self.target_window = None
        self.logger.info("窗口服务已清理")
