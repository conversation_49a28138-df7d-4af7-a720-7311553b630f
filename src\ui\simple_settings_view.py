#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单设置视图
临时的设置界面，用于基本配置
"""

import flet as ft
from typing import Optional, Callable
from ..utils.logger import LoggerMixin


class SimpleSettingsView(ft.Column, LoggerMixin):
    """简单设置视图"""
    
    def __init__(
        self,
        page: ft.Page,
        config_manager=None,
        on_back_click: Optional[Callable] = None
    ):
        super().__init__()
        self.page = page
        self.config = config_manager
        self.on_back_click = on_back_click
        
        # 构建界面
        self.controls = [self.build()]
        
        self.logger.info("简单设置视图初始化完成")
    
    def build(self) -> ft.Control:
        """构建界面"""
        return ft.Container(
            content=ft.Column([
                # 头部
                self._build_header(),
                ft.Divider(height=2),
                
                # 设置内容
                ft.Container(
                    content=ft.Column([
                        # 基本设置
                        self._build_basic_settings(),
                        ft.Divider(height=1),
                        
                        # 识别设置
                        self._build_recognition_settings(),
                        ft.Divider(height=1),
                        
                        # 高级设置
                        self._build_advanced_settings(),
                        
                    ], scroll=ft.ScrollMode.AUTO),
                    expand=True,
                    padding=20
                ),
                
                # 底部按钮
                self._build_footer(),
                
            ]),
            padding=20,
            expand=True
        )
    
    def _build_header(self) -> ft.Control:
        """构建头部"""
        return ft.Row([
            ft.IconButton(
                icon=ft.Icons.ARROW_BACK,
                tooltip="返回",
                on_click=lambda _: self.on_back_click() if self.on_back_click else None
            ),
            ft.Text(
                "设置",
                size=24,
                weight=ft.FontWeight.BOLD,
                color=ft.Colors.BLUE
            ),
            ft.Container(expand=True),
            ft.Icon(ft.Icons.SETTINGS, size=32, color=ft.Colors.BLUE)
        ])
    
    def _build_basic_settings(self) -> ft.Control:
        """构建基本设置"""
        return ft.Container(
            content=ft.Column([
                ft.Text("基本设置", size=18, weight=ft.FontWeight.BOLD),
                ft.Divider(height=1),
                
                # 监控间隔
                ft.Row([
                    ft.Text("监控间隔:", size=14, width=120),
                    ft.Slider(
                        min=0.5,
                        max=5.0,
                        value=2.0,
                        divisions=9,
                        label="{value}秒",
                        width=200,
                        on_change=self._on_interval_change
                    ),
                    ft.Text("2.0秒", size=12, color=ft.Colors.GREY_600)
                ]),
                
                # 置信度阈值
                ft.Row([
                    ft.Text("置信度阈值:", size=14, width=120),
                    ft.Slider(
                        min=0.1,
                        max=1.0,
                        value=0.7,
                        divisions=9,
                        label="{value}",
                        width=200,
                        on_change=self._on_confidence_change
                    ),
                    ft.Text("0.7", size=12, color=ft.Colors.GREY_600)
                ]),
                
                # 自动开始监控
                ft.Row([
                    ft.Text("自动开始监控:", size=14, width=120),
                    ft.Switch(
                        value=False,
                        on_change=self._on_auto_start_change
                    ),
                    ft.Text("选择窗口后自动开始", size=12, color=ft.Colors.GREY_600)
                ]),
                
            ]),
            bgcolor=ft.Colors.BLUE_50,
            padding=15,
            border_radius=10
        )
    
    def _build_recognition_settings(self) -> ft.Control:
        """构建识别设置"""
        return ft.Container(
            content=ft.Column([
                ft.Text("识别设置", size=18, weight=ft.FontWeight.BOLD),
                ft.Divider(height=1),
                
                # 识别方法
                ft.Row([
                    ft.Text("识别方法:", size=14, width=120),
                    ft.Dropdown(
                        width=200,
                        value="模板匹配",
                        options=[
                            ft.dropdown.Option("模板匹配"),
                            ft.dropdown.Option("YOLO检测"),
                            ft.dropdown.Option("深度学习"),
                            ft.dropdown.Option("混合方法"),
                        ],
                        on_change=self._on_method_change
                    )
                ]),
                
                # OCR引擎
                ft.Row([
                    ft.Text("OCR引擎:", size=14, width=120),
                    ft.Dropdown(
                        width=200,
                        value="PaddleOCR",
                        options=[
                            ft.dropdown.Option("PaddleOCR"),
                            ft.dropdown.Option("EasyOCR"),
                            ft.dropdown.Option("Tesseract"),
                        ],
                        on_change=self._on_ocr_engine_change
                    )
                ]),
                
                # 图像预处理
                ft.Row([
                    ft.Text("图像预处理:", size=14, width=120),
                    ft.Column([
                        ft.Row([
                            ft.Checkbox(
                                value=True,
                                on_change=self._on_normalize_change
                            ),
                            ft.Text("亮度归一化", size=12)
                        ]),
                        ft.Row([
                            ft.Checkbox(
                                value=True,
                                on_change=self._on_contrast_change
                            ),
                            ft.Text("对比度增强", size=12)
                        ])
                    ])
                ]),
                
            ]),
            bgcolor=ft.Colors.GREEN_50,
            padding=15,
            border_radius=10
        )
    
    def _build_advanced_settings(self) -> ft.Control:
        """构建高级设置"""
        return ft.Container(
            content=ft.Column([
                ft.Text("高级设置", size=18, weight=ft.FontWeight.BOLD),
                ft.Divider(height=1),
                
                # 日志级别
                ft.Row([
                    ft.Text("日志级别:", size=14, width=120),
                    ft.Dropdown(
                        width=200,
                        value="INFO",
                        options=[
                            ft.dropdown.Option("DEBUG"),
                            ft.dropdown.Option("INFO"),
                            ft.dropdown.Option("WARNING"),
                            ft.dropdown.Option("ERROR"),
                        ],
                        on_change=self._on_log_level_change
                    )
                ]),
                
                # 缓存设置
                ft.Row([
                    ft.Text("启用缓存:", size=14, width=120),
                    ft.Switch(
                        value=True,
                        on_change=self._on_cache_change
                    ),
                    ft.Text("缓存识别结果以提高性能", size=12, color=ft.Colors.GREY_600)
                ]),
                
                # 性能模式
                ft.Row([
                    ft.Text("性能模式:", size=14, width=120),
                    ft.Dropdown(
                        width=200,
                        value="平衡",
                        options=[
                            ft.dropdown.Option("高性能"),
                            ft.dropdown.Option("平衡"),
                            ft.dropdown.Option("高精度"),
                        ],
                        on_change=self._on_performance_change
                    )
                ]),
                
            ]),
            bgcolor=ft.Colors.ORANGE_50,
            padding=15,
            border_radius=10
        )
    
    def _build_footer(self) -> ft.Control:
        """构建底部按钮"""
        return ft.Row([
            ft.ElevatedButton(
                text="重置默认",
                icon=ft.Icons.RESTORE,
                on_click=self._reset_defaults
            ),
            ft.Container(expand=True),
            ft.ElevatedButton(
                text="保存设置",
                icon=ft.Icons.SAVE,
                bgcolor=ft.Colors.BLUE,
                color=ft.Colors.WHITE,
                on_click=self._save_settings
            )
        ])
    
    # 事件处理方法
    def _on_interval_change(self, e):
        """监控间隔改变"""
        self._show_message(f"监控间隔设置为: {e.control.value:.1f}秒")
    
    def _on_confidence_change(self, e):
        """置信度阈值改变"""
        self._show_message(f"置信度阈值设置为: {e.control.value:.1f}")
    
    def _on_auto_start_change(self, e):
        """自动开始改变"""
        status = "启用" if e.control.value else "禁用"
        self._show_message(f"自动开始监控: {status}")
    
    def _on_method_change(self, e):
        """识别方法改变"""
        self._show_message(f"识别方法设置为: {e.control.value}")
    
    def _on_ocr_engine_change(self, e):
        """OCR引擎改变"""
        self._show_message(f"OCR引擎设置为: {e.control.value}")
    
    def _on_normalize_change(self, e):
        """亮度归一化改变"""
        status = "启用" if e.control.value else "禁用"
        self._show_message(f"亮度归一化: {status}")
    
    def _on_contrast_change(self, e):
        """对比度增强改变"""
        status = "启用" if e.control.value else "禁用"
        self._show_message(f"对比度增强: {status}")
    
    def _on_log_level_change(self, e):
        """日志级别改变"""
        self._show_message(f"日志级别设置为: {e.control.value}")
    
    def _on_cache_change(self, e):
        """缓存设置改变"""
        status = "启用" if e.control.value else "禁用"
        self._show_message(f"缓存: {status}")
    
    def _on_performance_change(self, e):
        """性能模式改变"""
        self._show_message(f"性能模式设置为: {e.control.value}")
    
    def _reset_defaults(self, e):
        """重置默认设置"""
        self._show_message("设置已重置为默认值")
    
    def _save_settings(self, e):
        """保存设置"""
        self._show_message("设置已保存")
        # 这里可以添加实际的保存逻辑
    
    def _show_message(self, message: str):
        """显示消息"""
        if self.page:
            snack_bar = ft.SnackBar(
                content=ft.Text(message),
                duration=3000
            )
            self.page.snack_bar = snack_bar
            snack_bar.open = True
            self.page.update()
