{".class": "MypyFile", "_fullname": "pydantic._internal._namespace_utils", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef"}, "Generator": {".class": "SymbolTableNode", "cross_ref": "typing.Generator", "kind": "Gdef"}, "GlobalsNamespace": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "pydantic._internal._namespace_utils.GlobalsNamespace", "line": 11, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "Iterator": {".class": "SymbolTableNode", "cross_ref": "typing.Iterator", "kind": "Gdef"}, "LazyLocalNamespace": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic._internal._namespace_utils.LazyLocalNamespace", "name": "LazyLocalNamespace", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pydantic._internal._namespace_utils.LazyLocalNamespace", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "pydantic._internal._namespace_utils", "mro": ["pydantic._internal._namespace_utils.LazyLocalNamespace", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "__contains__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic._internal._namespace_utils.LazyLocalNamespace.__contains__", "name": "__contains__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["pydantic._internal._namespace_utils.LazyLocalNamespace", "builtins.object"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__contains__ of LazyLocalNamespace", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__getitem__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic._internal._namespace_utils.LazyLocalNamespace.__getitem__", "name": "__getitem__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["pydantic._internal._namespace_utils.LazyLocalNamespace", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__getitem__ of LazyLocalNamespace", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2], "arg_names": ["self", "namespaces"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic._internal._namespace_utils.LazyLocalNamespace.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 2], "arg_names": ["self", "namespaces"], "arg_types": ["pydantic._internal._namespace_utils.LazyLocalNamespace", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic._internal._namespace_utils.MappingNamespace"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of LazyLocalNamespace", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__iter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic._internal._namespace_utils.LazyLocalNamespace.__iter__", "name": "__iter__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["pydantic._internal._namespace_utils.LazyLocalNamespace"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__iter__ of LazyLocalNamespace", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__len__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic._internal._namespace_utils.LazyLocalNamespace.__len__", "name": "__len__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["pydantic._internal._namespace_utils.LazyLocalNamespace"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__len__ of LazyLocalNamespace", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_namespaces": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pydantic._internal._namespace_utils.LazyLocalNamespace._namespaces", "name": "_namespaces", "type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic._internal._namespace_utils.MappingNamespace"}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "data": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "pydantic._internal._namespace_utils.LazyLocalNamespace.data", "name": "data", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pydantic._internal._namespace_utils.LazyLocalNamespace"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "data of LazyLocalNamespace", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_settable_property", "is_ready", "is_inferred"], "fullname": "pydantic._internal._namespace_utils.LazyLocalNamespace.data", "name": "data", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pydantic._internal._namespace_utils.LazyLocalNamespace"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "data of LazyLocalNamespace", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic._internal._namespace_utils.LazyLocalNamespace.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic._internal._namespace_utils.LazyLocalNamespace", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Mapping": {".class": "SymbolTableNode", "cross_ref": "typing.Mapping", "kind": "Gdef"}, "MappingNamespace": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "pydantic._internal._namespace_utils.MappingNamespace", "line": 18, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "typing.Mapping"}}}, "NamedTuple": {".class": "SymbolTableNode", "cross_ref": "typing.NamedTuple", "kind": "Gdef"}, "NamespacesTuple": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "typing.Mapping"}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic._internal._namespace_utils.NamespacesTuple", "name": "NamespacesTuple", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_named_tuple"], "fullname": "pydantic._internal._namespace_utils.NamespacesTuple", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"namedtuple": {"fields": ["globals", "locals"]}}, "module_name": "pydantic._internal._namespace_utils", "mro": ["pydantic._internal._namespace_utils.NamespacesTuple", "builtins.tuple", "typing.Sequence", "typing.Reversible", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "_NT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic._internal._namespace_utils.NamespacesTuple._NT", "name": "_NT", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic._internal._namespace_utils.GlobalsNamespace"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic._internal._namespace_utils.MappingNamespace"}], "partial_fallback": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "typing.Mapping"}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, "__annotations__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "pydantic._internal._namespace_utils.NamespacesTuple.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "pydantic._internal._namespace_utils.NamespacesTuple.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "pydantic._internal._namespace_utils.NamespacesTuple.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "globals"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "locals"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "__new__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["_cls", "globals", "locals"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static"], "fullname": "pydantic._internal._namespace_utils.NamespacesTuple.__new__", "name": "__new__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["_cls", "globals", "locals"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic._internal._namespace_utils.NamespacesTuple._NT", "id": -1, "name": "_NT", "namespace": "pydantic._internal._namespace_utils.NamespacesTuple.__new__", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic._internal._namespace_utils.GlobalsNamespace"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic._internal._namespace_utils.MappingNamespace"}], "partial_fallback": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "typing.Mapping"}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic._internal._namespace_utils.GlobalsNamespace"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic._internal._namespace_utils.MappingNamespace"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__new__ of NamespacesTuple", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic._internal._namespace_utils.NamespacesTuple._NT", "id": -1, "name": "_NT", "namespace": "pydantic._internal._namespace_utils.NamespacesTuple.__new__", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic._internal._namespace_utils.GlobalsNamespace"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic._internal._namespace_utils.MappingNamespace"}], "partial_fallback": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "typing.Mapping"}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic._internal._namespace_utils.NamespacesTuple._NT", "id": -1, "name": "_NT", "namespace": "pydantic._internal._namespace_utils.NamespacesTuple.__new__", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic._internal._namespace_utils.GlobalsNamespace"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic._internal._namespace_utils.MappingNamespace"}], "partial_fallback": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "typing.Mapping"}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_asdict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["_self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic._internal._namespace_utils.NamespacesTuple._asdict", "name": "_asdict", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["_self"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic._internal._namespace_utils.NamespacesTuple._NT", "id": -1, "name": "_NT", "namespace": "pydantic._internal._namespace_utils.NamespacesTuple._asdict", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic._internal._namespace_utils.GlobalsNamespace"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic._internal._namespace_utils.MappingNamespace"}], "partial_fallback": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "typing.Mapping"}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_asdict of NamespacesTuple", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic._internal._namespace_utils.NamespacesTuple._NT", "id": -1, "name": "_NT", "namespace": "pydantic._internal._namespace_utils.NamespacesTuple._asdict", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic._internal._namespace_utils.GlobalsNamespace"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic._internal._namespace_utils.MappingNamespace"}], "partial_fallback": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "typing.Mapping"}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_field_defaults": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "pydantic._internal._namespace_utils.NamespacesTuple._field_defaults", "name": "_field_defaults", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_field_types": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "pydantic._internal._namespace_utils.NamespacesTuple._field_types", "name": "_field_types", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "pydantic._internal._namespace_utils.NamespacesTuple._fields", "name": "_fields", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_make": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "pydantic._internal._namespace_utils.NamespacesTuple._make", "name": "_make", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic._internal._namespace_utils.NamespacesTuple._NT", "id": -1, "name": "_NT", "namespace": "pydantic._internal._namespace_utils.NamespacesTuple._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic._internal._namespace_utils.GlobalsNamespace"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic._internal._namespace_utils.MappingNamespace"}], "partial_fallback": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "typing.Mapping"}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_make of NamespacesTuple", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic._internal._namespace_utils.NamespacesTuple._NT", "id": -1, "name": "_NT", "namespace": "pydantic._internal._namespace_utils.NamespacesTuple._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic._internal._namespace_utils.GlobalsNamespace"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic._internal._namespace_utils.MappingNamespace"}], "partial_fallback": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "typing.Mapping"}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic._internal._namespace_utils.NamespacesTuple._NT", "id": -1, "name": "_NT", "namespace": "pydantic._internal._namespace_utils.NamespacesTuple._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic._internal._namespace_utils.GlobalsNamespace"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic._internal._namespace_utils.MappingNamespace"}], "partial_fallback": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "typing.Mapping"}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_classmethod", "is_ready"], "fullname": "pydantic._internal._namespace_utils.NamespacesTuple._make", "name": "_make", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic._internal._namespace_utils.NamespacesTuple._NT", "id": -1, "name": "_NT", "namespace": "pydantic._internal._namespace_utils.NamespacesTuple._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic._internal._namespace_utils.GlobalsNamespace"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic._internal._namespace_utils.MappingNamespace"}], "partial_fallback": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "typing.Mapping"}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_make of NamespacesTuple", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic._internal._namespace_utils.NamespacesTuple._NT", "id": -1, "name": "_NT", "namespace": "pydantic._internal._namespace_utils.NamespacesTuple._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic._internal._namespace_utils.GlobalsNamespace"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic._internal._namespace_utils.MappingNamespace"}], "partial_fallback": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "typing.Mapping"}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic._internal._namespace_utils.NamespacesTuple._NT", "id": -1, "name": "_NT", "namespace": "pydantic._internal._namespace_utils.NamespacesTuple._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic._internal._namespace_utils.GlobalsNamespace"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic._internal._namespace_utils.MappingNamespace"}], "partial_fallback": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "typing.Mapping"}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}}, "plugin_generated": true}, "_replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5], "arg_names": ["_self", "globals", "locals"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic._internal._namespace_utils.NamespacesTuple._replace", "name": "_replace", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5], "arg_names": ["_self", "globals", "locals"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic._internal._namespace_utils.NamespacesTuple._NT", "id": -1, "name": "_NT", "namespace": "pydantic._internal._namespace_utils.NamespacesTuple._replace", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic._internal._namespace_utils.GlobalsNamespace"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic._internal._namespace_utils.MappingNamespace"}], "partial_fallback": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "typing.Mapping"}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic._internal._namespace_utils.GlobalsNamespace"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic._internal._namespace_utils.MappingNamespace"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_replace of NamespacesTuple", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic._internal._namespace_utils.NamespacesTuple._NT", "id": -1, "name": "_NT", "namespace": "pydantic._internal._namespace_utils.NamespacesTuple._replace", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic._internal._namespace_utils.GlobalsNamespace"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic._internal._namespace_utils.MappingNamespace"}], "partial_fallback": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "typing.Mapping"}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic._internal._namespace_utils.NamespacesTuple._NT", "id": -1, "name": "_NT", "namespace": "pydantic._internal._namespace_utils.NamespacesTuple._replace", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic._internal._namespace_utils.GlobalsNamespace"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic._internal._namespace_utils.MappingNamespace"}], "partial_fallback": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "typing.Mapping"}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_source": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "pydantic._internal._namespace_utils.NamespacesTuple._source", "name": "_source", "type": "builtins.str"}}, "globals": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "pydantic._internal._namespace_utils.NamespacesTuple.globals", "name": "globals", "type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic._internal._namespace_utils.GlobalsNamespace"}}}, "globals-redefinition": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._namespace_utils.NamespacesTuple.globals", "kind": "<PERSON><PERSON><PERSON>"}, "locals": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "pydantic._internal._namespace_utils.NamespacesTuple.locals", "name": "locals", "type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic._internal._namespace_utils.MappingNamespace"}}}, "locals-redefinition": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._namespace_utils.NamespacesTuple.locals", "kind": "<PERSON><PERSON><PERSON>"}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic._internal._namespace_utils.NamespacesTuple.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic._internal._namespace_utils.GlobalsNamespace"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic._internal._namespace_utils.MappingNamespace"}], "partial_fallback": "pydantic._internal._namespace_utils.NamespacesTuple"}, "values": [], "variance": 0}, "slots": null, "tuple_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic._internal._namespace_utils.GlobalsNamespace"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic._internal._namespace_utils.MappingNamespace"}], "partial_fallback": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "typing.Mapping"}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_vars": [], "typeddict_type": null}}, "NsResolver": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic._internal._namespace_utils.NsResolver", "name": "NsResolver", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pydantic._internal._namespace_utils.NsResolver", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pydantic._internal._namespace_utils", "mro": ["pydantic._internal._namespace_utils.NsResolver", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "namespaces_tuple", "parent_namespace"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic._internal._namespace_utils.NsResolver.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "namespaces_tuple", "parent_namespace"], "arg_types": ["pydantic._internal._namespace_utils.NsResolver", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic._internal._namespace_utils.NamespacesTuple"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic._internal._namespace_utils.MappingNamespace"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of NsResolver", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_base_ns_tuple": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pydantic._internal._namespace_utils.NsResolver._base_ns_tuple", "name": "_base_ns_tuple", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic._internal._namespace_utils.GlobalsNamespace"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic._internal._namespace_utils.MappingNamespace"}], "partial_fallback": "pydantic._internal._namespace_utils.NamespacesTuple"}}}, "_parent_ns": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pydantic._internal._namespace_utils.NsResolver._parent_ns", "name": "_parent_ns", "type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic._internal._namespace_utils.MappingNamespace"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "_types_stack": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "pydantic._internal._namespace_utils.NsResolver._types_stack", "name": "_types_stack", "type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}, "typing_extensions.TypeAliasType"], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "push": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "pydantic._internal._namespace_utils.NsResolver.push", "name": "push", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["pydantic._internal._namespace_utils.NsResolver", {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}, "typing_extensions.TypeAliasType"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "push of NsResolver", "ret_type": {".class": "Instance", "args": [{".class": "NoneType"}, {".class": "NoneType"}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Generator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "pydantic._internal._namespace_utils.NsResolver.push", "name": "push", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["pydantic._internal._namespace_utils.NsResolver", {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}, "typing_extensions.TypeAliasType"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "push of NsResolver", "ret_type": {".class": "Instance", "args": [{".class": "NoneType"}, {".class": "NoneType"}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "contextlib._GeneratorContextManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "types_namespace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "pydantic._internal._namespace_utils.NsResolver.types_namespace", "name": "types_namespace", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pydantic._internal._namespace_utils.NsResolver"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "types_namespace of NsResolver", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic._internal._namespace_utils.NamespacesTuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_settable_property", "is_ready", "is_inferred"], "fullname": "pydantic._internal._namespace_utils.NsResolver.types_namespace", "name": "types_namespace", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pydantic._internal._namespace_utils.NsResolver"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "types_namespace of NsResolver", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic._internal._namespace_utils.NamespacesTuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic._internal._namespace_utils.NsResolver.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic._internal._namespace_utils.NsResolver", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ParamSpec": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.ParamSpec", "kind": "Gdef"}, "TypeAlias": {".class": "SymbolTableNode", "cross_ref": "typing.TypeAlias", "kind": "Gdef"}, "TypeAliasType": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.TypeAliasType", "kind": "Gdef"}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef"}, "TypeVarTuple": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.TypeVarTuple", "kind": "Gdef"}, "_TypeVarLike": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "pydantic._internal._namespace_utils._TypeVarLike", "line": 27, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": ["typing.TypeVar", "typing_extensions.ParamSpec", "typing_extensions.TypeVarTuple"], "uses_pep604_syntax": true}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic._internal._namespace_utils.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic._internal._namespace_utils.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic._internal._namespace_utils.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic._internal._namespace_utils.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic._internal._namespace_utils.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic._internal._namespace_utils.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "cached_property": {".class": "SymbolTableNode", "cross_ref": "functools.cached_property", "kind": "Gdef"}, "contextmanager": {".class": "SymbolTableNode", "cross_ref": "contextlib.contextmanager", "kind": "Gdef"}, "get_module_ns_of": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["obj"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic._internal._namespace_utils.get_module_ns_of", "name": "get_module_ns_of", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["obj"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_module_ns_of", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "ns_for_function": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["obj", "parent_namespace"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic._internal._namespace_utils.ns_for_function", "name": "ns_for_function", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["obj", "parent_namespace"], "arg_types": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic._internal._namespace_utils.MappingNamespace"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ns_for_function", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic._internal._namespace_utils.NamespacesTuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef"}}, "path": "c:\\Python313\\Lib\\site-packages\\pydantic\\_internal\\_namespace_utils.py"}