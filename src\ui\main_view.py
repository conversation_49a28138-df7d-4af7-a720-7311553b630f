#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
主视图界面
斗地主助手的主要用户界面，包含游戏状态显示、出牌建议、实时分析等功能
"""

import asyncio
from typing import List, Dict, Optional, Callable, Any
from datetime import datetime

import flet as ft
from loguru import logger

from ..utils.logger import LoggerMixin
from ..utils.config import ConfigManager


class MainView(ft.Column, LoggerMixin):
    """主视图界面"""
    
    def __init__(
        self,
        page: ft.Page,
        config_manager: ConfigManager,
        window_service,
        vision_service,
        ocr_service,
        strategy_service,
        on_settings_click: Optional[Callable] = None,
        on_window_select_click: Optional[Callable] = None
    ):
        super().__init__()
        self.page = page
        self.config = config_manager
        self.window_service = window_service
        self.vision_service = vision_service
        self.ocr_service = ocr_service
        self.strategy_service = strategy_service
        
        # 回调函数
        self.on_settings_click = on_settings_click
        self.on_window_select_click = on_window_select_click
        
        # 界面组件
        self.status_bar = None
        self.game_info_panel = None
        self.card_display = None
        self.suggestion_panel = None
        self.analysis_panel = None
        self.control_panel = None
        
        # 状态
        self.is_monitoring = False
        self.current_game_state = None
        self.last_update_time = None
        
        # 定时器
        self.update_timer = None
        
        # 构建界面
        self.controls = [self.build()]
        
        self.logger.info("主视图初始化完成")
    
    def build(self) -> ft.Control:
        """构建界面"""
        return ft.Container(
            content=ft.Column([
                # 头部 - 固定高度
                self._build_header(),
                ft.Divider(height=1),

                # 主要内容区域 - 可扩展
                ft.Container(
                    content=ft.Row([
                        # 左侧面板
                        ft.Container(
                            content=ft.Column([
                                self._build_game_info_panel(),
                                ft.Divider(height=1),
                                self._build_card_display(),
                            ], scroll=ft.ScrollMode.AUTO),
                            width=400,
                            padding=10
                        ),
                        ft.VerticalDivider(width=1),
                        # 右侧面板
                        ft.Container(
                            expand=True,
                            content=ft.Column([
                                self._build_suggestion_panel(),
                                ft.Divider(height=1),
                                self._build_analysis_panel(),
                            ], scroll=ft.ScrollMode.AUTO),
                            padding=10
                        )
                    ]),
                    expand=True  # 主要内容区域可以扩展
                ),

                # 控制面板 - 固定在底部
                ft.Divider(height=1),
                self._build_control_panel(),
            ]),
            padding=10,
            expand=True
        )
    
    def _build_header(self) -> ft.Control:
        """构建头部"""
        self.status_bar = ft.Row([
            ft.Icon(ft.Icons.CASINO, color=ft.Colors.BLUE),
            ft.Text("斗地主助手", size=20, weight=ft.FontWeight.BOLD),
            ft.Container(expand=True),

            # 快速窗口选择按钮
            ft.ElevatedButton(
                text="选择窗口",
                icon=ft.Icons.WINDOW,
                on_click=self._on_window_select_button_click,
                bgcolor=ft.Colors.BLUE,
                color=ft.Colors.WHITE,
                height=35
            ),
            ft.Container(width=10),

            # 连接状态
            ft.Container(
                content=ft.Row([
                    ft.Icon(ft.Icons.CIRCLE, color=ft.Colors.RED, size=12),
                    ft.Text("未连接", size=12, color=ft.Colors.RED)
                ]),
                bgcolor=ft.Colors.RED_50,
                padding=ft.padding.symmetric(horizontal=8, vertical=4),
                border_radius=12
            ),
            ft.Container(width=10),

            # 设置按钮
            ft.IconButton(
                icon=ft.Icons.SETTINGS,
                tooltip="设置",
                on_click=lambda _: self.on_settings_click() if self.on_settings_click else None
            )
        ])

        return self.status_bar
    
    def _build_game_info_panel(self) -> ft.Control:
        """构建游戏信息面板"""
        self.game_info_panel = ft.Container(
            content=ft.Column([
                ft.Text("游戏状态", size=16, weight=ft.FontWeight.BOLD),
                ft.Divider(height=1),
                
                # 玩家信息
                ft.Row([
                    ft.Text("当前玩家:", size=12),
                    ft.Text("未知", size=12, weight=ft.FontWeight.BOLD, color=ft.Colors.BLUE)
                ]),
                
                ft.Row([
                    ft.Text("游戏阶段:", size=12),
                    ft.Text("未知", size=12, weight=ft.FontWeight.BOLD, color=ft.Colors.GREEN)
                ]),
                
                ft.Row([
                    ft.Text("地主身份:", size=12),
                    ft.Text("未知", size=12, weight=ft.FontWeight.BOLD, color=ft.Colors.ORANGE)
                ]),
                
                ft.Divider(height=1),
                
                # 牌数统计
                ft.Text("牌数统计", size=14, weight=ft.FontWeight.BOLD),
                ft.Column([
                    ft.Row([
                        ft.Text("我:", size=12),
                        ft.Text("17", size=12, weight=ft.FontWeight.BOLD)
                    ]),
                    ft.Row([
                        ft.Text("左侧:", size=12),
                        ft.Text("17", size=12, weight=ft.FontWeight.BOLD)
                    ]),
                    ft.Row([
                        ft.Text("右侧:", size=12),
                        ft.Text("17", size=12, weight=ft.FontWeight.BOLD)
                    ])
                ]),
                
                ft.Divider(height=1),
                
                # 胜率显示
                ft.Text("胜率分析", size=14, weight=ft.FontWeight.BOLD),
                ft.Container(
                    content=ft.Column([
                        ft.Text("50%", size=24, weight=ft.FontWeight.BOLD, color=ft.Colors.BLUE),
                        ft.ProgressBar(value=0.5, color=ft.Colors.BLUE, bgcolor=ft.Colors.BLUE_100)
                    ]),
                    alignment=ft.alignment.center
                )
            ]),
            bgcolor=ft.Colors.BLUE_50,
            padding=15,
            border_radius=10
        )
        
        return self.game_info_panel
    
    def _build_card_display(self) -> ft.Control:
        """构建手牌显示"""
        self.card_display = ft.Container(
            content=ft.Column([
                ft.Text("我的手牌", size=16, weight=ft.FontWeight.BOLD),
                ft.Divider(height=1),
                
                # 手牌区域
                ft.Container(
                    content=ft.Text(
                        "等待识别手牌...",
                        size=12,
                        color=ft.Colors.GREY_600,
                        text_align=ft.TextAlign.CENTER
                    ),
                    height=120,
                    bgcolor=ft.Colors.GREY_100,
                    border_radius=8,
                    alignment=ft.alignment.center
                ),
                
                # 手牌统计
                ft.Row([
                    ft.Text("总数:", size=12),
                    ft.Text("0", size=12, weight=ft.FontWeight.BOLD),
                    ft.Container(expand=True),
                    ft.Text("大牌:", size=12),
                    ft.Text("0", size=12, weight=ft.FontWeight.BOLD),
                    ft.Container(expand=True),
                    ft.Text("炸弹:", size=12),
                    ft.Text("0", size=12, weight=ft.FontWeight.BOLD)
                ])
            ]),
            bgcolor=ft.Colors.GREEN_50,
            padding=15,
            border_radius=10
        )
        
        return self.card_display
    
    def _build_suggestion_panel(self) -> ft.Control:
        """构建出牌建议面板"""
        self.suggestion_panel = ft.Container(
            content=ft.Column([
                ft.Row([
                    ft.Text("出牌建议", size=16, weight=ft.FontWeight.BOLD),
                    ft.Container(expand=True),
                    ft.IconButton(
                        icon=ft.Icons.REFRESH,
                        tooltip="刷新建议",
                        on_click=self._refresh_suggestions
                    )
                ]),
                ft.Divider(height=1),
                
                # 建议列表
                ft.Container(
                    content=ft.Column([
                        self._create_suggestion_card(
                            "等待分析",
                            "暂无出牌建议",
                            0.0,
                            "低",
                            is_placeholder=True
                        )
                    ]),
                    height=300,
                    bgcolor=ft.Colors.GREY_50,
                    border_radius=8,
                    padding=10
                )
            ]),
            bgcolor=ft.Colors.ORANGE_50,
            padding=15,
            border_radius=10
        )
        
        return self.suggestion_panel
    
    def _build_analysis_panel(self) -> ft.Control:
        """构建分析面板"""
        self.analysis_panel = ft.Container(
            content=ft.Column([
                ft.Text("局面分析", size=16, weight=ft.FontWeight.BOLD),
                ft.Divider(height=1),
                
                # 分析标签页
                ft.Tabs(
                    selected_index=0,
                    tabs=[
                        ft.Tab(
                            text="威胁分析",
                            content=ft.Container(
                                content=ft.Column([
                                    ft.Text("暂无威胁信息", size=12, color=ft.Colors.GREY_600)
                                ]),
                                padding=10
                            )
                        ),
                        ft.Tab(
                            text="机会分析",
                            content=ft.Container(
                                content=ft.Column([
                                    ft.Text("暂无机会信息", size=12, color=ft.Colors.GREY_600)
                                ]),
                                padding=10
                            )
                        ),
                        ft.Tab(
                            text="策略建议",
                            content=ft.Container(
                                content=ft.Column([
                                    ft.Text("等待游戏开始后提供策略建议", size=12, color=ft.Colors.GREY_600)
                                ]),
                                padding=10
                            )
                        )
                    ]
                )
            ]),
            bgcolor=ft.Colors.PURPLE_50,
            padding=15,
            border_radius=10
        )
        
        return self.analysis_panel

    def _build_control_panel(self) -> ft.Control:
        """构建控制面板"""
        self.control_panel = ft.Container(
            content=ft.Row([
                ft.ElevatedButton(
                    text="选择窗口",
                    icon=ft.Icons.WINDOW,
                    on_click=self._on_window_select_button_click,
                    height=50,
                    width=150
                ),
                ft.Container(width=10),  # 间距
                ft.ElevatedButton(
                    text="开始监控",
                    icon=ft.Icons.PLAY_ARROW,
                    on_click=self._toggle_monitoring,
                    bgcolor=ft.Colors.GREEN,
                    color=ft.Colors.WHITE,
                    height=50,
                    width=150
                ),
                ft.Container(width=10),  # 间距
                ft.ElevatedButton(
                    text="手动分析",
                    icon=ft.Icons.ANALYTICS,
                    on_click=self._manual_analysis,
                    height=50,
                    width=150
                ),
                ft.Container(expand=True),
                ft.Text("最后更新: 从未", size=12, color=ft.Colors.GREY_600)
            ]),
            bgcolor=ft.Colors.GREY_100,
            padding=15,
            border_radius=10,
            height=80  # 确保控制面板有足够的高度
        )

        return self.control_panel

    def _create_suggestion_card(
        self,
        title: str,
        description: str,
        confidence: float,
        risk: str,
        is_placeholder: bool = False
    ) -> ft.Control:
        """创建建议卡片"""
        # 置信度颜色
        if confidence >= 0.8:
            confidence_color = ft.Colors.GREEN
        elif confidence >= 0.6:
            confidence_color = ft.Colors.ORANGE
        else:
            confidence_color = ft.Colors.RED

        # 风险颜色
        risk_colors = {
            "低": ft.Colors.GREEN,
            "中": ft.Colors.ORANGE,
            "高": ft.Colors.RED
        }
        risk_color = risk_colors.get(risk, ft.Colors.GREY)

        return ft.Container(
            content=ft.Column([
                ft.Row([
                    ft.Text(title, size=14, weight=ft.FontWeight.BOLD),
                    ft.Container(expand=True),
                    ft.Container(
                        content=ft.Text(f"{confidence:.0%}", size=12, color=ft.Colors.WHITE),
                        bgcolor=confidence_color,
                        padding=ft.padding.symmetric(horizontal=8, vertical=2),
                        border_radius=10
                    )
                ]),
                ft.Text(description, size=12, color=ft.Colors.GREY_700),
                ft.Row([
                    ft.Text("风险:", size=10),
                    ft.Container(
                        content=ft.Text(risk, size=10, color=ft.Colors.WHITE),
                        bgcolor=risk_color,
                        padding=ft.padding.symmetric(horizontal=6, vertical=1),
                        border_radius=8
                    ),
                    ft.Container(expand=True),
                    ft.TextButton(
                        text="采用",
                        on_click=lambda _: self._adopt_suggestion(title) if not is_placeholder else None,
                        disabled=is_placeholder
                    )
                ])
            ]),
            bgcolor=ft.Colors.WHITE,
            padding=10,
            border_radius=8,
            border=ft.border.all(1, ft.Colors.GREY_300),
            margin=ft.margin.only(bottom=8)
        )

    async def _toggle_monitoring(self, e) -> None:
        """切换监控状态"""
        try:
            if not self.is_monitoring:
                # 开始监控
                self.is_monitoring = True
                e.control.text = "停止监控"
                e.control.icon = ft.Icons.STOP
                e.control.bgcolor = ft.Colors.RED

                # 更新状态栏
                self._update_status("监控中", ft.Colors.GREEN)

                # 启动定时更新
                await self._start_monitoring()

            else:
                # 停止监控
                self.is_monitoring = False
                e.control.text = "开始监控"
                e.control.icon = ft.Icons.PLAY_ARROW
                e.control.bgcolor = ft.Colors.GREEN

                # 更新状态栏
                self._update_status("已停止", ft.Colors.RED)

                # 停止定时更新
                await self._stop_monitoring()

            await self.page.update_async()

        except Exception as ex:
            self.logger.error(f"切换监控状态失败: {ex}")
            await self._show_message(f"操作失败: {ex}")

    async def _start_monitoring(self) -> None:
        """开始监控"""
        try:
            # 启动定时器
            self.update_timer = asyncio.create_task(self._monitoring_loop())
            self.logger.info("开始游戏监控")

        except Exception as e:
            self.logger.error(f"启动监控失败: {e}")
            raise

    async def _stop_monitoring(self) -> None:
        """停止监控"""
        try:
            if self.update_timer:
                self.update_timer.cancel()
                self.update_timer = None

            self.logger.info("停止游戏监控")

        except Exception as e:
            self.logger.error(f"停止监控失败: {e}")

    async def _monitoring_loop(self) -> None:
        """监控循环"""
        while self.is_monitoring:
            try:
                await self._update_game_state()

                # 等待下次更新
                interval = self.config.get('vision.recognition_interval', 2.0)
                await asyncio.sleep(interval)

            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"监控循环错误: {e}")
                await asyncio.sleep(1.0)  # 错误后短暂等待

    async def _update_game_state(self) -> None:
        """更新游戏状态"""
        try:
            # 这里可以添加实际的游戏状态更新逻辑
            # 暂时只更新时间
            self.last_update_time = datetime.now()

        except Exception as e:
            self.logger.error(f"更新游戏状态失败: {e}")

    def _update_status(self, status: str, color) -> None:
        """更新状态栏"""
        if self.status_bar and len(self.status_bar.controls) >= 4:
            status_container = self.status_bar.controls[3]
            if hasattr(status_container, 'content'):
                status_row = status_container.content
                if hasattr(status_row, 'controls') and len(status_row.controls) >= 2:
                    status_row.controls[0].color = color
                    status_row.controls[1].value = status
                    status_row.controls[1].color = color
                status_container.bgcolor = f"{color}_50"

    async def _show_message(self, message: str):
        """显示消息"""
        if self.page:
            snack_bar = ft.SnackBar(
                content=ft.Text(message),
                duration=3000
            )
            self.page.snack_bar = snack_bar
            snack_bar.open = True
            await self.page.update_async()

    def _refresh_suggestions(self, e):
        """刷新建议"""
        self._show_message("刷新建议功能开发中...")

    def _manual_analysis(self, e):
        """手动分析"""
        self._show_message("手动分析功能开发中...")

    def _adopt_suggestion(self, suggestion_title: str):
        """采用建议"""
        self._show_message(f"采用建议: {suggestion_title}")

    def _on_window_select_button_click(self, e):
        """窗口选择按钮点击事件"""
        self.logger.info("用户点击了选择窗口按钮")
        if self.on_window_select_click:
            self.on_window_select_click()
        else:
            self.logger.warning("窗口选择回调函数未设置")

    def set_target_window(self, window_info):
        """设置目标窗口"""
        self.logger.info(f"设置目标窗口: {window_info}")
        self._update_status("已连接", ft.Colors.GREEN)

    def update_game_info(self, game_info: Dict[str, Any]):
        """更新游戏信息"""
        try:
            # 这里可以根据game_info更新界面
            self.logger.debug(f"更新游戏信息: {game_info}")
        except Exception as e:
            self.logger.error(f"更新游戏信息失败: {e}")

    def update_suggestions(self, suggestions: List[Dict[str, Any]]):
        """更新出牌建议"""
        try:
            # 这里可以根据suggestions更新建议面板
            self.logger.debug(f"更新出牌建议: {len(suggestions)}条")
        except Exception as e:
            self.logger.error(f"更新出牌建议失败: {e}")
