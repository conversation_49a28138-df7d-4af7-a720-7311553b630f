{"data_mtime": 1750100862, "dep_lines": [19, 20, 21, 22, 7, 16, 17, 19, 28, 1, 3, 4, 5, 6, 8, 9, 10, 13, 15, 16, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 5, 5, 5, 10, 5, 20, 25, 5, 10, 5, 5, 5, 5, 5, 5, 5, 10, 20, 5, 30, 30, 30, 30, 30, 30], "dependencies": ["pydantic._internal._typing_extra", "pydantic._internal._core_utils", "pydantic._internal._forward_ref", "pydantic._internal._utils", "collections.abc", "typing_inspection.typing_objects", "typing_inspection.introspection", "pydantic._internal", "pydantic.main", "__future__", "sys", "types", "typing", "collections", "contextlib", "<PERSON><PERSON><PERSON>", "itertools", "weakref", "typing_extensions", "typing_inspection", "builtins", "_collections_abc", "_contextvars", "_frozen_importlib", "_typeshed", "abc", "pydantic._internal._model_construction"], "hash": "9be73218095a7ff6df407ef5276d02fc07e1b150", "id": "pydantic._internal._generics", "ignore_all": true, "interface_hash": "64bf54165c176e3a9e495e47357ba910c2d0cdb7", "mtime": 1748795478, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Python313\\Lib\\site-packages\\pydantic\\_internal\\_generics.py", "plugin_data": null, "size": 23849, "suppressed": [], "version_id": "1.15.0"}