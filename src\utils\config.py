"""配置管理模块

配置管理模块

使用Pydantic进行配置验证和管理。"""

import json
import os
from pathlib import Path
from typing import Dict, Any, Optional

from pydantic import BaseModel, Field
from loguru import logger


class AppConfig(BaseModel):
    """应用配置模型"""
    app_name: str = Field(default="斗地主助手 2025", description="应用名称")
    version: str = Field(default="1.0.0", description="版本号")
    debug: bool = Field(default=False, description="调试模式")
    log_level: str = Field(default="INFO", description="日志级别")
    
    # 窗口配置
    window_width: int = Field(default=800, description="窗口宽度")
    window_height: int = Field(default=600, description="窗口高度")
    window_min_width: int = Field(default=600, description="最小窗口宽度")
    window_min_height: int = Field(default=400, description="最小窗口高度")
    
    # OCR配置
    ocr_confidence_threshold: float = Field(default=0.8, description="OCR置信度阈值")
    ocr_enabled_models: Dict[str, bool] = Field(
        default={
            "paddle_ocr": True,   # 默认启用
            "mistral_ocr": False,  # 需要额外下载
            "h2ovl": False         # 需要额外下载
        },
        description="启用的OCR模型"
    )
    
    # 视觉识别配置
    vision_detection_interval: float = Field(default=1.0, description="检测间隔秒")
    vision_confidence_threshold: float = Field(default=0.7, description="视觉检测置信度阈值")
    
    # 策略配置
    strategy_difficulty: str = Field(default="medium", description="策略难度: easy/medium/hard")
    strategy_risk_level: str = Field(default="balanced", description="风险偏好: conservative/balanced/aggressive")
    
    # 界面配置
    theme_mode: str = Field(default="light", description="主题模式: light/dark/auto")
    language: str = Field(default="zh_CN", description="界面语言")
    
    # 高级配置
    gpu_acceleration: bool = Field(default=True, description="GPU加速")
    max_memory_usage: int = Field(default=2048, description="最大内存使用MB")
    
    class Config:
        """Pydantic配置"""
        extra = "allow"  # 允许额外字段
        validate_assignment = True  # 赋值时验证


class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_file: str = "config.json"):
        self.config_file = Path(config_file)
        self.config: Optional[AppConfig] = None
        self.load_config()
    
    def load_config(self) -> AppConfig:
        """加载配置"""
        try:
            if self.config_file.exists():
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)
                self.config = AppConfig(**config_data)
                logger.info(f"📋 从文件加载配置: {self.config_file}")
            else:
                self.config = AppConfig()
                logger.info("📋 使用默认配置")
                # 保存默认配置到文件
                self.save_config()
        except Exception as e:
            self.config = AppConfig()
            logger.warning(f"⚠️ 配置加载失败，使用默认配置: {e}")
        
        return self.config
    
    def save_config(self) -> bool:
        """保存配置"""
        try:
            if not self.config:
                logger.error("❌ 没有配置可保存")
                return False
            
            # 确保配置目录存在
            self.config_file.parent.mkdir(parents=True, exist_ok=True)
            
            # 保存配置
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(
                    self.config.dict(),
                    f,
                    indent=2,
                    ensure_ascii=False
                )
            
            logger.info(f"💾 配置已保存到: {self.config_file}")
            return True
            
        except Exception as e:
            logger.error(f"❌ 配置保存失败: {e}")
            return False
    
    def update_config(self, **kwargs) -> bool:
        """更新配置"""
        try:
            if not self.config:
                self.config = AppConfig()
            
            for key, value in kwargs.items():
                if hasattr(self.config, key):
                    setattr(self.config, key, value)
                    logger.debug(f"🔧 更新配置: {key} = {value}")
                else:
                    logger.warning(f"⚠️ 未知配置项: {key}")
            
            return self.save_config()
        except Exception as e:
            logger.error(f"❌ 配置更新失败: {e}")
            return False
    
    def get_config(self) -> AppConfig:
        """获取配置"""
        if not self.config:
            self.load_config()
        return self.config
    
    def reset_config(self) -> bool:
        """重置为默认配置"""
        try:
            self.config = AppConfig()
            logger.info("🔄 配置已重置为默认值")
            return self.save_config()
        except Exception as e:
            logger.error(f"❌ 配置重置失败: {e}")
            return False


# 全局配置管理器实例
_config_manager = ConfigManager()


def load_config() -> AppConfig:
    """加载应用配置"""
    return _config_manager.load_config()


def save_config() -> bool:
    """保存应用配置"""
    return _config_manager.save_config()


def update_config(**kwargs) -> bool:
    """更新应用配置"""
    return _config_manager.update_config(**kwargs)


def get_config() -> AppConfig:
    """获取应用配置"""
    return _config_manager.get_config()


def reset_config() -> bool:
    """重置应用配置"""
    return _config_manager.reset_config()
