{".class": "MypyFile", "_fullname": "numpy._typing._extended_precision", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "_128Bit": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._nbit_base._128Bit", "kind": "Gdef"}, "_256Bit": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._nbit_base._256Bit", "kind": "Gdef"}, "_80Bit": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._nbit_base._80Bit", "kind": "Gdef"}, "_96Bit": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._nbit_base._96Bit", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy._typing._extended_precision.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy._typing._extended_precision.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy._typing._extended_precision.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy._typing._extended_precision.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy._typing._extended_precision.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy._typing._extended_precision.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "complex160": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "numpy._typing._extended_precision.complex160", "line": 24, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "Instance", "args": ["numpy._typing._nbit_base._80Bit", "numpy._typing._nbit_base._80Bit"], "extra_attrs": null, "type_ref": "numpy.complexfloating"}}}, "complex192": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "numpy._typing._extended_precision.complex192", "line": 25, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "Instance", "args": ["numpy._typing._nbit_base._96Bit", "numpy._typing._nbit_base._96Bit"], "extra_attrs": null, "type_ref": "numpy.complexfloating"}}}, "complex256": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "numpy._typing._extended_precision.complex256", "line": 26, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "Instance", "args": ["numpy._typing._nbit_base._128Bit", "numpy._typing._nbit_base._128Bit"], "extra_attrs": null, "type_ref": "numpy.complexfloating"}}}, "complex512": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "numpy._typing._extended_precision.complex512", "line": 27, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "Instance", "args": ["numpy._typing._nbit_base._256Bit", "numpy._typing._nbit_base._256Bit"], "extra_attrs": null, "type_ref": "numpy.complexfloating"}}}, "float128": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "numpy._typing._extended_precision.float128", "line": 22, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "Instance", "args": ["numpy._typing._nbit_base._128Bit"], "extra_attrs": null, "type_ref": "numpy.floating"}}}, "float256": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "numpy._typing._extended_precision.float256", "line": 23, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "Instance", "args": ["numpy._typing._nbit_base._256Bit"], "extra_attrs": null, "type_ref": "numpy.floating"}}}, "float80": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "numpy._typing._extended_precision.float80", "line": 20, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "Instance", "args": ["numpy._typing._nbit_base._80Bit"], "extra_attrs": null, "type_ref": "numpy.floating"}}}, "float96": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "numpy._typing._extended_precision.float96", "line": 21, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "Instance", "args": ["numpy._typing._nbit_base._96Bit"], "extra_attrs": null, "type_ref": "numpy.floating"}}}, "int128": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "numpy._typing._extended_precision.int128", "line": 18, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "Instance", "args": ["numpy._typing._nbit_base._128Bit"], "extra_attrs": null, "type_ref": "numpy.<PERSON><PERSON><PERSON>r"}}}, "int256": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "numpy._typing._extended_precision.int256", "line": 19, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "Instance", "args": ["numpy._typing._nbit_base._256Bit"], "extra_attrs": null, "type_ref": "numpy.<PERSON><PERSON><PERSON>r"}}}, "np": {".class": "SymbolTableNode", "cross_ref": "numpy", "kind": "Gdef"}, "uint128": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "numpy._typing._extended_precision.uint128", "line": 16, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "Instance", "args": ["numpy._typing._nbit_base._128Bit"], "extra_attrs": null, "type_ref": "numpy.unsignedinteger"}}}, "uint256": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "numpy._typing._extended_precision.uint256", "line": 17, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "Instance", "args": ["numpy._typing._nbit_base._256Bit"], "extra_attrs": null, "type_ref": "numpy.unsignedinteger"}}}}, "path": "c:\\Python313\\Lib\\site-packages\\numpy\\_typing\\_extended_precision.py"}