{".class": "MypyFile", "_fullname": "pydantic._internal._decorators_v1", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "Parameter": {".class": "SymbolTableNode", "cross_ref": "inspect.Parameter", "kind": "Gdef"}, "Protocol": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.Protocol", "kind": "Gdef"}, "PydanticUserError": {".class": "SymbolTableNode", "cross_ref": "pydantic.errors.PydanticUserError", "kind": "Gdef"}, "RootValidatorFieldsTuple": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "pydantic._internal._decorators_v1.RootValidatorFieldsTuple", "line": 110, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "RootValidatorValues": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "pydantic._internal._decorators_v1.RootValidatorValues", "line": 108, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "V1OnlyValueValidator": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["__call__", 2]], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic._internal._decorators_v1.V1OnlyValueValidator", "name": "V1OnlyValueValidator", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract", "is_protocol"], "fullname": "pydantic._internal._decorators_v1.V1OnlyValueValidator", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "pydantic._internal._decorators_v1", "mro": ["pydantic._internal._decorators_v1.V1OnlyValueValidator", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0, 0], "arg_names": ["self", null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_body"], "fullname": "pydantic._internal._decorators_v1.V1OnlyValueValidator.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", null], "arg_types": ["pydantic._internal._decorators_v1.V1OnlyValueValidator", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of V1OnlyValueValidator", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic._internal._decorators_v1.V1OnlyValueValidator.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic._internal._decorators_v1.V1OnlyValueValidator", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "V1RootValidatorFunction": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["__call__", 2]], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic._internal._decorators_v1.V1RootValidatorFunction", "name": "V1RootValidatorFunction", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract", "is_protocol"], "fullname": "pydantic._internal._decorators_v1.V1RootValidatorFunction", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "pydantic._internal._decorators_v1", "mro": ["pydantic._internal._decorators_v1.V1RootValidatorFunction", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0, 0], "arg_names": ["self", null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_body"], "fullname": "pydantic._internal._decorators_v1.V1RootValidatorFunction.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", null], "arg_types": ["pydantic._internal._decorators_v1.V1RootValidatorFunction", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic._internal._decorators_v1.RootValidatorValues"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of V1RootValidatorFunction", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic._internal._decorators_v1.RootValidatorValues"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic._internal._decorators_v1.V1RootValidatorFunction.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic._internal._decorators_v1.V1RootValidatorFunction", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "V1Validator": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "pydantic._internal._decorators_v1.V1Validator", "line": 45, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": ["pydantic._internal._decorators_v1.V1ValidatorWithValues", "pydantic._internal._decorators_v1.V1ValidatorWithValuesKwOnly", "pydantic._internal._decorators_v1.V1ValidatorWithKwargs", "pydantic._internal._decorators_v1.V1ValidatorWithValuesAndKwargs"], "uses_pep604_syntax": false}}}, "V1ValidatorWithKwargs": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["__call__", 2]], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic._internal._decorators_v1.V1ValidatorWithKwargs", "name": "V1ValidatorWithKwargs", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract", "is_protocol"], "fullname": "pydantic._internal._decorators_v1.V1ValidatorWithKwargs", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "pydantic._internal._decorators_v1", "mro": ["pydantic._internal._decorators_v1.V1ValidatorWithKwargs", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0, 0, 4], "arg_names": ["self", null, "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_body"], "fullname": "pydantic._internal._decorators_v1.V1ValidatorWithKwargs.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", null, "kwargs"], "arg_types": ["pydantic._internal._decorators_v1.V1ValidatorWithKwargs", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of V1ValidatorWithKwargs", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic._internal._decorators_v1.V1ValidatorWithKwargs.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic._internal._decorators_v1.V1ValidatorWithKwargs", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "V1ValidatorWithValues": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["__call__", 2]], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic._internal._decorators_v1.V1ValidatorWithValues", "name": "V1ValidatorWithValues", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract", "is_protocol"], "fullname": "pydantic._internal._decorators_v1.V1ValidatorWithValues", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "pydantic._internal._decorators_v1", "mro": ["pydantic._internal._decorators_v1.V1ValidatorWithValues", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0, 0, 0], "arg_names": ["self", null, "values"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_body"], "fullname": "pydantic._internal._decorators_v1.V1ValidatorWithValues.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", null, "values"], "arg_types": ["pydantic._internal._decorators_v1.V1ValidatorWithValues", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of V1ValidatorWithValues", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic._internal._decorators_v1.V1ValidatorWithValues.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic._internal._decorators_v1.V1ValidatorWithValues", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "V1ValidatorWithValuesAndKwargs": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["__call__", 2]], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic._internal._decorators_v1.V1ValidatorWithValuesAndKwargs", "name": "V1ValidatorWithValuesAndKwargs", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract", "is_protocol"], "fullname": "pydantic._internal._decorators_v1.V1ValidatorWithValuesAndKwargs", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "pydantic._internal._decorators_v1", "mro": ["pydantic._internal._decorators_v1.V1ValidatorWithValuesAndKwargs", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", null, "values", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_body"], "fullname": "pydantic._internal._decorators_v1.V1ValidatorWithValuesAndKwargs.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", null, "values", "kwargs"], "arg_types": ["pydantic._internal._decorators_v1.V1ValidatorWithValuesAndKwargs", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of V1ValidatorWithValuesAndKwargs", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic._internal._decorators_v1.V1ValidatorWithValuesAndKwargs.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic._internal._decorators_v1.V1ValidatorWithValuesAndKwargs", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "V1ValidatorWithValuesKwOnly": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["__call__", 2]], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic._internal._decorators_v1.V1ValidatorWithValuesKwOnly", "name": "V1ValidatorWithValuesKwOnly", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract", "is_protocol"], "fullname": "pydantic._internal._decorators_v1.V1ValidatorWithValuesKwOnly", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "pydantic._internal._decorators_v1", "mro": ["pydantic._internal._decorators_v1.V1ValidatorWithValuesKwOnly", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0, 0, 3], "arg_names": ["self", null, "values"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_body"], "fullname": "pydantic._internal._decorators_v1.V1ValidatorWithValuesKwOnly.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 3], "arg_names": ["self", null, "values"], "arg_types": ["pydantic._internal._decorators_v1.V1ValidatorWithValuesKwOnly", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of V1ValidatorWithValuesKwOnly", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic._internal._decorators_v1.V1ValidatorWithValuesKwOnly.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic._internal._decorators_v1.V1ValidatorWithValuesKwOnly", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "V2CoreAfterRootValidator": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["__call__", 2]], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic._internal._decorators_v1.V2CoreAfterRootValidator", "name": "V2CoreAfterRootValidator", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract", "is_protocol"], "fullname": "pydantic._internal._decorators_v1.V2CoreAfterRootValidator", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "pydantic._internal._decorators_v1", "mro": ["pydantic._internal._decorators_v1.V2CoreAfterRootValidator", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0, 0, 0], "arg_names": ["self", null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_body"], "fullname": "pydantic._internal._decorators_v1.V2CoreAfterRootValidator.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", null, null], "arg_types": ["pydantic._internal._decorators_v1.V2CoreAfterRootValidator", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic._internal._decorators_v1.RootValidatorFieldsTuple"}, "pydantic_core.core_schema.ValidationInfo"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of V2CoreAfterRootValidator", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic._internal._decorators_v1.RootValidatorFieldsTuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic._internal._decorators_v1.V2CoreAfterRootValidator.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic._internal._decorators_v1.V2CoreAfterRootValidator", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "V2CoreBeforeRootValidator": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["__call__", 2]], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic._internal._decorators_v1.V2CoreBeforeRootValidator", "name": "V2CoreBeforeRootValidator", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract", "is_protocol"], "fullname": "pydantic._internal._decorators_v1.V2CoreBeforeRootValidator", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "pydantic._internal._decorators_v1", "mro": ["pydantic._internal._decorators_v1.V2CoreBeforeRootValidator", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0, 0, 0], "arg_names": ["self", null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_body"], "fullname": "pydantic._internal._decorators_v1.V2CoreBeforeRootValidator.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", null, null], "arg_types": ["pydantic._internal._decorators_v1.V2CoreBeforeRootValidator", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic._internal._decorators_v1.RootValidatorValues"}, "pydantic_core.core_schema.ValidationInfo"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of V2CoreBeforeRootValidator", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic._internal._decorators_v1.RootValidatorValues"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic._internal._decorators_v1.V2CoreBeforeRootValidator.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic._internal._decorators_v1.V2CoreBeforeRootValidator", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic._internal._decorators_v1.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic._internal._decorators_v1.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic._internal._decorators_v1.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic._internal._decorators_v1.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic._internal._decorators_v1.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic._internal._decorators_v1.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "can_be_keyword": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["param"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic._internal._decorators_v1.can_be_keyword", "name": "can_be_keyword", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["param"], "arg_types": ["inspect.Parameter"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "can_be_keyword", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "can_be_positional": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._utils.can_be_positional", "kind": "Gdef"}, "cast": {".class": "SymbolTableNode", "cross_ref": "typing.cast", "kind": "Gdef"}, "core_schema": {".class": "SymbolTableNode", "cross_ref": "pydantic_core.core_schema", "kind": "Gdef"}, "make_generic_v1_field_validator": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["validator"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic._internal._decorators_v1.make_generic_v1_field_validator", "name": "make_generic_v1_field_validator", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["validator"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic._internal._decorators_v1.V1Validator"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "make_generic_v1_field_validator", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.WithInfoValidatorFunction"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "make_v1_generic_root_validator": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["validator", "pre"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic._internal._decorators_v1.make_v1_generic_root_validator", "name": "make_v1_generic_root_validator", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["validator", "pre"], "arg_types": ["pydantic._internal._decorators_v1.V1RootValidatorFunction", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "make_v1_generic_root_validator", "ret_type": {".class": "UnionType", "items": ["pydantic._internal._decorators_v1.V2CoreBeforeRootValidator", "pydantic._internal._decorators_v1.V2CoreAfterRootValidator"], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "signature": {".class": "SymbolTableNode", "cross_ref": "inspect.signature", "kind": "Gdef"}}, "path": "c:\\Python313\\Lib\\site-packages\\pydantic\\_internal\\_decorators_v1.py"}