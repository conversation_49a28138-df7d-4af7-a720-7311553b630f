{"data_mtime": 1750100861, "dep_lines": [7, 8, 9, 14, 15, 1, 2, 3, 5, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 25, 25, 10, 5, 5, 5, 5, 30, 30, 30, 30, 30], "dependencies": ["pydantic.v1.typing", "pydantic.v1.utils", "pydantic.v1.version", "pydantic.v1.fields", "pydantic.v1.main", "json", "enum", "typing", "typing_extensions", "builtins", "_frozen_importlib", "_typeshed", "abc", "json.decoder", "json.encoder"], "hash": "6581db36b9f61bed44ed1870e4f551529b9da21e", "id": "pydantic.v1.config", "ignore_all": true, "interface_hash": "ca1fdbc004e43fba1f070caa2f78e2f0450f6485", "mtime": 1748795478, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Python313\\Lib\\site-packages\\pydantic\\v1\\config.py", "plugin_data": null, "size": 6532, "suppressed": [], "version_id": "1.15.0"}