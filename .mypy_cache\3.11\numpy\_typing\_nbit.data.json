{".class": "MypyFile", "_fullname": "numpy._typing._nbit", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "TypeAlias": {".class": "SymbolTableNode", "cross_ref": "typing.TypeAlias", "kind": "Gdef"}, "_128Bit": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._nbit_base._128Bit", "kind": "Gdef"}, "_16Bit": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._nbit_base._16Bit", "kind": "Gdef"}, "_32Bit": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._nbit_base._32Bit", "kind": "Gdef"}, "_64Bit": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._nbit_base._64Bit", "kind": "Gdef"}, "_8Bit": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._nbit_base._8Bit", "kind": "Gdef"}, "_96Bit": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._nbit_base._96Bit", "kind": "Gdef"}, "_NBitByte": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "numpy._typing._nbit._NBitByte", "line": 8, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "numpy._typing._nbit_base._8Bit"}}, "_NBitDouble": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "numpy._typing._nbit._NBitDouble", "line": 18, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "numpy._typing._nbit_base._64Bit"}}, "_NBitHalf": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "numpy._typing._nbit._NBitHalf", "line": 16, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "numpy._typing._nbit_base._16Bit"}}, "_NBitInt": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "numpy._typing._nbit._NBitInt", "line": 12, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._nbit._NBitIntP"}}}, "_NBitIntC": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "numpy._typing._nbit._NBitIntC", "line": 10, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "numpy._typing._nbit_base._32Bit"}}, "_NBitIntP": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "numpy._typing._nbit._NBitIntP", "line": 11, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": ["numpy._typing._nbit_base._32Bit", "numpy._typing._nbit_base._64Bit"], "uses_pep604_syntax": true}}}, "_NBitLong": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "numpy._typing._nbit._NBitLong", "line": 13, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": ["numpy._typing._nbit_base._32Bit", "numpy._typing._nbit_base._64Bit"], "uses_pep604_syntax": true}}}, "_NBitLongDouble": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "numpy._typing._nbit._NBitLongDouble", "line": 19, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": ["numpy._typing._nbit_base._64Bit", "numpy._typing._nbit_base._96Bit", "numpy._typing._nbit_base._128Bit"], "uses_pep604_syntax": true}}}, "_NBitLongLong": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "numpy._typing._nbit._NBitLongLong", "line": 14, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "numpy._typing._nbit_base._64Bit"}}, "_NBitShort": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "numpy._typing._nbit._NBitShort", "line": 9, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "numpy._typing._nbit_base._16Bit"}}, "_NBitSingle": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "numpy._typing._nbit._NBitSingle", "line": 17, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "numpy._typing._nbit_base._32Bit"}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy._typing._nbit.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy._typing._nbit.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy._typing._nbit.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy._typing._nbit.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy._typing._nbit.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy._typing._nbit.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}}, "path": "c:\\Python313\\Lib\\site-packages\\numpy\\_typing\\_nbit.py"}