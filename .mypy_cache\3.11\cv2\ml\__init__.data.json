{".class": "MypyFile", "_fullname": "cv2.ml", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "ANN_MLP": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["cv2.ml.StatModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "cv2.ml.ANN_M<PERSON>", "name": "ANN_MLP", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "cv2.ml.ANN_M<PERSON>", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "cv2.ml", "mro": ["cv2.ml.ANN_M<PERSON>", "cv2.ml.StatModel", "cv2.<PERSON><PERSON><PERSON><PERSON>", "builtins.object"], "names": {".class": "SymbolTable", "create": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["cls"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "cv2.ml.ANN_MLP.create", "name": "create", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "cv2.ml.ANN_M<PERSON>"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create of ANN_MLP", "ret_type": "cv2.ml.ANN_M<PERSON>", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "cv2.ml.ANN_MLP.create", "name": "create", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "cv2.ml.ANN_M<PERSON>"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create of ANN_MLP", "ret_type": "cv2.ml.ANN_M<PERSON>", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "getAnnealCoolingRatio": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.ml.ANN_MLP.getAnnealCoolingRatio", "name": "getAnnealCoolingRatio", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cv2.ml.ANN_M<PERSON>"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getAnnealCoolingRatio of ANN_MLP", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "getAnnealFinalT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.ml.ANN_MLP.getAnnealFinalT", "name": "getAnnealFinalT", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cv2.ml.ANN_M<PERSON>"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getAnnealFinalT of ANN_MLP", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "getAnnealInitialT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.ml.ANN_MLP.getAnnealInitialT", "name": "getAnnealInitialT", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cv2.ml.ANN_M<PERSON>"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getAnnealInitialT of ANN_MLP", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "getAnnealItePerStep": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.ml.ANN_MLP.getAnnealItePerStep", "name": "getAnnealItePerStep", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cv2.ml.ANN_M<PERSON>"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getAnnealItePerStep of ANN_MLP", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "getBackpropMomentumScale": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.ml.ANN_MLP.getBackpropMomentumScale", "name": "getBackpropMomentumScale", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cv2.ml.ANN_M<PERSON>"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getBackpropMomentumScale of ANN_MLP", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "getBackpropWeightScale": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.ml.ANN_MLP.getBackpropWeightScale", "name": "getBackpropWeightScale", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cv2.ml.ANN_M<PERSON>"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getBackpropWeightScale of ANN_MLP", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "getLayerSizes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.ml.ANN_MLP.getLayerSizes", "name": "getLayerSizes", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cv2.ml.ANN_M<PERSON>"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getLayerSizes of ANN_MLP", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "getRpropDW0": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.ml.ANN_MLP.getRpropDW0", "name": "getRpropDW0", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cv2.ml.ANN_M<PERSON>"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getRpropDW0 of ANN_MLP", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "getRpropDWMax": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.ml.ANN_MLP.getRpropDWMax", "name": "getRpropDWMax", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cv2.ml.ANN_M<PERSON>"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getRpropDWMax of ANN_MLP", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "getRpropDWMin": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.ml.ANN_MLP.getRpropDWMin", "name": "getRpropDWMin", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cv2.ml.ANN_M<PERSON>"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getRpropDWMin of ANN_MLP", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "getRpropDWMinus": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.ml.ANN_MLP.getRpropDWMinus", "name": "getRpropDWMinus", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cv2.ml.ANN_M<PERSON>"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getRpropDWMinus of ANN_MLP", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "getRpropDWPlus": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.ml.ANN_MLP.getRpropDWPlus", "name": "getRpropDWPlus", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cv2.ml.ANN_M<PERSON>"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getRpropDWPlus of ANN_MLP", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "getTermCriteria": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.ml.ANN_MLP.getTermCriteria", "name": "getTermCriteria", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cv2.ml.ANN_M<PERSON>"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getTermCriteria of ANN_MLP", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.TermCriteria"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "getTrainMethod": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.ml.ANN_MLP.getTrainMethod", "name": "getTrainMethod", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cv2.ml.ANN_M<PERSON>"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getTrainMethod of ANN_MLP", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "getWeights": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "layerIdx"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.ml.ANN_MLP.getWeights", "name": "getWeights", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "layerIdx"], "arg_types": ["cv2.ml.ANN_M<PERSON>", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getWeights of ANN_MLP", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "load": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "filepath"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "cv2.ml.ANN_MLP.load", "name": "load", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "filepath"], "arg_types": [{".class": "TypeType", "item": "cv2.ml.ANN_M<PERSON>"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "load of ANN_MLP", "ret_type": "cv2.ml.ANN_M<PERSON>", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "cv2.ml.ANN_MLP.load", "name": "load", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "filepath"], "arg_types": [{".class": "TypeType", "item": "cv2.ml.ANN_M<PERSON>"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "load of ANN_MLP", "ret_type": "cv2.ml.ANN_M<PERSON>", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "setActivationFunction": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "type", "param1", "param2"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.ml.ANN_MLP.setActivationFunction", "name": "setActivationFunction", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "type", "param1", "param2"], "arg_types": ["cv2.ml.ANN_M<PERSON>", "builtins.int", "builtins.float", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "setActivationFunction of ANN_MLP", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "setAnnealCoolingRatio": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "val"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.ml.ANN_MLP.setAnnealCoolingRatio", "name": "setAnnealCoolingRatio", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "val"], "arg_types": ["cv2.ml.ANN_M<PERSON>", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "setAnnealCoolingRatio of ANN_MLP", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "setAnnealFinalT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "val"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.ml.ANN_MLP.setAnnealFinalT", "name": "setAnnealFinalT", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "val"], "arg_types": ["cv2.ml.ANN_M<PERSON>", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "setAnnealFinalT of ANN_MLP", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "setAnnealInitialT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "val"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.ml.ANN_MLP.setAnnealInitialT", "name": "setAnnealInitialT", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "val"], "arg_types": ["cv2.ml.ANN_M<PERSON>", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "setAnnealInitialT of ANN_MLP", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "setAnnealItePerStep": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "val"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.ml.ANN_MLP.setAnnealItePerStep", "name": "setAnnealItePerStep", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "val"], "arg_types": ["cv2.ml.ANN_M<PERSON>", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "setAnnealItePerStep of ANN_MLP", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "setBackpropMomentumScale": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "val"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.ml.ANN_MLP.setBackpropMomentumScale", "name": "setBackpropMomentumScale", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "val"], "arg_types": ["cv2.ml.ANN_M<PERSON>", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "setBackpropMomentumScale of ANN_MLP", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "setBackpropWeightScale": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "val"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.ml.ANN_MLP.setBackpropWeightScale", "name": "setBackpropWeightScale", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "val"], "arg_types": ["cv2.ml.ANN_M<PERSON>", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "setBackpropWeightScale of ANN_MLP", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "setLayerSizes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "cv2.ml.ANN_MLP.setLayerSizes", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "_layer_sizes"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.ml.ANN_MLP.setLayerSizes", "name": "setLayerSizes", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "_layer_sizes"], "arg_types": ["cv2.ml.ANN_M<PERSON>", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "setLayerSizes of ANN_MLP", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.ml.ANN_MLP.setLayerSizes", "name": "setLayerSizes", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "_layer_sizes"], "arg_types": ["cv2.ml.ANN_M<PERSON>", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "setLayerSizes of ANN_MLP", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "_layer_sizes"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.ml.ANN_MLP.setLayerSizes", "name": "setLayerSizes", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "_layer_sizes"], "arg_types": ["cv2.ml.ANN_M<PERSON>", "cv2.UMat"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "setLayerSizes of ANN_MLP", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.ml.ANN_MLP.setLayerSizes", "name": "setLayerSizes", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "_layer_sizes"], "arg_types": ["cv2.ml.ANN_M<PERSON>", "cv2.UMat"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "setLayerSizes of ANN_MLP", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "_layer_sizes"], "arg_types": ["cv2.ml.ANN_M<PERSON>", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "setLayerSizes of ANN_MLP", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "_layer_sizes"], "arg_types": ["cv2.ml.ANN_M<PERSON>", "cv2.UMat"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "setLayerSizes of ANN_MLP", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "setRpropDW0": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "val"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.ml.ANN_MLP.setRpropDW0", "name": "setRpropDW0", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "val"], "arg_types": ["cv2.ml.ANN_M<PERSON>", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "setRpropDW0 of ANN_MLP", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "setRpropDWMax": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "val"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.ml.ANN_MLP.setRpropDWMax", "name": "setRpropDWMax", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "val"], "arg_types": ["cv2.ml.ANN_M<PERSON>", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "setRpropDWMax of ANN_MLP", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "setRpropDWMin": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "val"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.ml.ANN_MLP.setRpropDWMin", "name": "setRpropDWMin", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "val"], "arg_types": ["cv2.ml.ANN_M<PERSON>", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "setRpropDWMin of ANN_MLP", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "setRpropDWMinus": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "val"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.ml.ANN_MLP.setRpropDWMinus", "name": "setRpropDWMinus", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "val"], "arg_types": ["cv2.ml.ANN_M<PERSON>", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "setRpropDWMinus of ANN_MLP", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "setRpropDWPlus": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "val"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.ml.ANN_MLP.setRpropDWPlus", "name": "setRpropDWPlus", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "val"], "arg_types": ["cv2.ml.ANN_M<PERSON>", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "setRpropDWPlus of ANN_MLP", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "setTermCriteria": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "val"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.ml.ANN_MLP.setTermCriteria", "name": "setTermCriteria", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "val"], "arg_types": ["cv2.ml.ANN_M<PERSON>", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.TermCriteria"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "setTermCriteria of ANN_MLP", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "setTrainMethod": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "method", "param1", "param2"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.ml.ANN_MLP.setTrainMethod", "name": "setTrainMethod", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "method", "param1", "param2"], "arg_types": ["cv2.ml.ANN_M<PERSON>", "builtins.int", "builtins.float", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "setTrainMethod of ANN_MLP", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cv2.ml.ANN_MLP.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cv2.ml.ANN_M<PERSON>", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ANN_MLP_ANNEAL": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.ml.ANN_MLP_ANNEAL", "name": "ANN_MLP_ANNEAL", "type": "builtins.int"}}, "ANN_MLP_ActivationFunctions": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "cv2.ml.ANN_MLP_ActivationFunctions", "line": 117, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "builtins.int"}}, "ANN_MLP_BACKPROP": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.ml.ANN_MLP_BACKPROP", "name": "ANN_MLP_BACKPROP", "type": "builtins.int"}}, "ANN_MLP_GAUSSIAN": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.ml.ANN_MLP_GAUSSIAN", "name": "ANN_MLP_GAUSSIAN", "type": "builtins.int"}}, "ANN_MLP_IDENTITY": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.ml.ANN_MLP_IDENTITY", "name": "ANN_MLP_IDENTITY", "type": "builtins.int"}}, "ANN_MLP_LEAKYRELU": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.ml.ANN_MLP_LEAKYRELU", "name": "ANN_MLP_LEAKYRELU", "type": "builtins.int"}}, "ANN_MLP_NO_INPUT_SCALE": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.ml.ANN_MLP_NO_INPUT_SCALE", "name": "ANN_MLP_NO_INPUT_SCALE", "type": "builtins.int"}}, "ANN_MLP_NO_OUTPUT_SCALE": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.ml.ANN_MLP_NO_OUTPUT_SCALE", "name": "ANN_MLP_NO_OUTPUT_SCALE", "type": "builtins.int"}}, "ANN_MLP_RELU": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.ml.ANN_MLP_RELU", "name": "ANN_MLP_RELU", "type": "builtins.int"}}, "ANN_MLP_RPROP": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.ml.ANN_MLP_RPROP", "name": "ANN_MLP_RPROP", "type": "builtins.int"}}, "ANN_MLP_SIGMOID_SYM": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.ml.ANN_MLP_SIGMOID_SYM", "name": "ANN_MLP_SIGMOID_SYM", "type": "builtins.int"}}, "ANN_MLP_TrainFlags": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "cv2.ml.ANN_MLP_TrainFlags", "line": 123, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "builtins.int"}}, "ANN_MLP_TrainingMethods": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "cv2.ml.ANN_MLP_TrainingMethods", "line": 109, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "builtins.int"}}, "ANN_MLP_UPDATE_WEIGHTS": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.ml.ANN_MLP_UPDATE_WEIGHTS", "name": "ANN_MLP_UPDATE_WEIGHTS", "type": "builtins.int"}}, "BOOST_DISCRETE": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.ml.BOOST_DISCRETE", "name": "BOOST_DISCRETE", "type": "builtins.int"}}, "BOOST_GENTLE": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.ml.BOOST_GENTLE", "name": "BOOST_GENTLE", "type": "builtins.int"}}, "BOOST_LOGIT": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.ml.BOOST_LOGIT", "name": "BOOST_LOGIT", "type": "builtins.int"}}, "BOOST_REAL": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.ml.BOOST_REAL", "name": "BOOST_REAL", "type": "builtins.int"}}, "Boost": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["cv2.ml.DTrees"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "cv2.ml.<PERSON><PERSON>", "name": "Boost", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "cv2.ml.<PERSON><PERSON>", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "cv2.ml", "mro": ["cv2.ml.<PERSON><PERSON>", "cv2.ml.DTrees", "cv2.ml.StatModel", "cv2.<PERSON><PERSON><PERSON><PERSON>", "builtins.object"], "names": {".class": "SymbolTable", "create": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["cls"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "cv2.ml.Boost.create", "name": "create", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "cv2.ml.<PERSON><PERSON>"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create of Boost", "ret_type": "cv2.ml.<PERSON><PERSON>", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "cv2.ml.Boost.create", "name": "create", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "cv2.ml.<PERSON><PERSON>"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create of Boost", "ret_type": "cv2.ml.<PERSON><PERSON>", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "getBoostType": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.ml.Boost.getBoostType", "name": "getBoostType", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cv2.ml.<PERSON><PERSON>"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getBoostType of Boost", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "getWeakCount": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.ml.Boost.getWeakCount", "name": "getWeakCount", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cv2.ml.<PERSON><PERSON>"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getWeakCount of Boost", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "getWeightTrimRate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.ml.Boost.getWeightTrimRate", "name": "getWeightTrimRate", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cv2.ml.<PERSON><PERSON>"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getWeightTrimRate of Boost", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "load": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["cls", "filepath", "nodeName"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "cv2.ml.Boost.load", "name": "load", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["cls", "filepath", "nodeName"], "arg_types": [{".class": "TypeType", "item": "cv2.ml.<PERSON><PERSON>"}, "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "load of Boost", "ret_type": "cv2.ml.<PERSON><PERSON>", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "cv2.ml.Boost.load", "name": "load", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["cls", "filepath", "nodeName"], "arg_types": [{".class": "TypeType", "item": "cv2.ml.<PERSON><PERSON>"}, "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "load of Boost", "ret_type": "cv2.ml.<PERSON><PERSON>", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "setBoostType": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "val"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.ml.Boost.setBoostType", "name": "setBoostType", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "val"], "arg_types": ["cv2.ml.<PERSON><PERSON>", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "setBoostType of Boost", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "setWeakCount": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "val"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.ml.Boost.setWeakCount", "name": "setWeakCount", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "val"], "arg_types": ["cv2.ml.<PERSON><PERSON>", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "setWeakCount of Boost", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "setWeightTrimRate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "val"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.ml.Boost.setWeightTrimRate", "name": "setWeightTrimRate", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "val"], "arg_types": ["cv2.ml.<PERSON><PERSON>", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "setWeightTrimRate of Boost", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cv2.ml.<PERSON><PERSON>.<PERSON>", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cv2.ml.<PERSON><PERSON>", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Boost_DISCRETE": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.ml.Boost_DISCRETE", "name": "Boost_DISCRETE", "type": "builtins.int"}}, "Boost_GENTLE": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.ml.<PERSON><PERSON>_GENTLE", "name": "Boost_GENTLE", "type": "builtins.int"}}, "Boost_LOGIT": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.ml.Boost_LOGIT", "name": "Boost_LOGIT", "type": "builtins.int"}}, "Boost_REAL": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.ml.<PERSON>ost_REAL", "name": "Boost_REAL", "type": "builtins.int"}}, "Boost_Types": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "cv2.ml.Boost_Types", "line": 103, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "builtins.int"}}, "COL_SAMPLE": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.ml.COL_SAMPLE", "name": "COL_SAMPLE", "type": "builtins.int"}}, "DTREES_PREDICT_AUTO": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.ml.DTREES_PREDICT_AUTO", "name": "DTREES_PREDICT_AUTO", "type": "builtins.int"}}, "DTREES_PREDICT_MASK": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.ml.DTREES_PREDICT_MASK", "name": "DTREES_PREDICT_MASK", "type": "builtins.int"}}, "DTREES_PREDICT_MAX_VOTE": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.ml.DTREES_PREDICT_MAX_VOTE", "name": "DTREES_PREDICT_MAX_VOTE", "type": "builtins.int"}}, "DTREES_PREDICT_SUM": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.ml.DTREES_PREDICT_SUM", "name": "DTREES_PREDICT_SUM", "type": "builtins.int"}}, "DTrees": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["cv2.ml.StatModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "cv2.ml.DTrees", "name": "DTrees", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "cv2.ml.DTrees", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "cv2.ml", "mro": ["cv2.ml.DTrees", "cv2.ml.StatModel", "cv2.<PERSON><PERSON><PERSON><PERSON>", "builtins.object"], "names": {".class": "SymbolTable", "create": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["cls"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "cv2.ml.DTrees.create", "name": "create", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "cv2.ml.DTrees"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create of DTrees", "ret_type": "cv2.ml.DTrees", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "cv2.ml.DTrees.create", "name": "create", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "cv2.ml.DTrees"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create of DTrees", "ret_type": "cv2.ml.DTrees", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "getCVFolds": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.ml.DTrees.getCVFolds", "name": "getCVFolds", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cv2.ml.DTrees"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getCVFolds of DTrees", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "getMaxCategories": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.ml.DTrees.getMaxCategories", "name": "getMaxCategories", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cv2.ml.DTrees"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getMaxCategories of DTrees", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "getMaxDepth": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.ml.DTrees.getMaxDepth", "name": "getMaxDepth", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cv2.ml.DTrees"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getMaxDepth of DTrees", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "getMinSampleCount": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.ml.DTrees.getMinSampleCount", "name": "getMinSampleCount", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cv2.ml.DTrees"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getMinSampleCount of DTrees", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "getPriors": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.ml.DTrees.getPriors", "name": "getPriors", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cv2.ml.DTrees"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getPriors of DTrees", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "getRegressionAccuracy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.ml.DTrees.getRegressionAccuracy", "name": "getRegressionAccuracy", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cv2.ml.DTrees"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getRegressionAccuracy of DTrees", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "getTruncatePrunedTree": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.ml.DTrees.getTruncatePrunedTree", "name": "getTruncatePrunedTree", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cv2.ml.DTrees"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getTruncatePrunedTree of DTrees", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "getUse1SERule": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.ml.DTrees.getUse1SERule", "name": "getUse1SERule", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cv2.ml.DTrees"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getUse1SERule of DTrees", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "getUseSurrogates": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.ml.DTrees.getUseSurrogates", "name": "getUseSurrogates", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cv2.ml.DTrees"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getUseSurrogates of DTrees", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "load": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["cls", "filepath", "nodeName"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "cv2.ml.DTrees.load", "name": "load", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["cls", "filepath", "nodeName"], "arg_types": [{".class": "TypeType", "item": "cv2.ml.DTrees"}, "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "load of DTrees", "ret_type": "cv2.ml.DTrees", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "cv2.ml.DTrees.load", "name": "load", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["cls", "filepath", "nodeName"], "arg_types": [{".class": "TypeType", "item": "cv2.ml.DTrees"}, "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "load of DTrees", "ret_type": "cv2.ml.DTrees", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "setCVFolds": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "val"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.ml.DTrees.setCVFolds", "name": "setCVFolds", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "val"], "arg_types": ["cv2.ml.DTrees", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "setCVFolds of DTrees", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "setMaxCategories": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "val"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.ml.DTrees.setMaxCategories", "name": "setMaxCategories", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "val"], "arg_types": ["cv2.ml.DTrees", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "setMaxCategories of DTrees", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "setMaxDepth": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "val"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.ml.DTrees.setMaxDepth", "name": "setMaxDepth", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "val"], "arg_types": ["cv2.ml.DTrees", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "setMaxDepth of DTrees", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "setMinSampleCount": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "val"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.ml.DTrees.setMinSampleCount", "name": "setMinSampleCount", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "val"], "arg_types": ["cv2.ml.DTrees", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "setMinSampleCount of DTrees", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "setPriors": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "val"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.ml.DTrees.setPriors", "name": "setPriors", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "val"], "arg_types": ["cv2.ml.DTrees", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "setPriors of DTrees", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "setRegressionAccuracy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "val"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.ml.DTrees.setRegressionAccuracy", "name": "setRegressionAccuracy", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "val"], "arg_types": ["cv2.ml.DTrees", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "setRegressionAccuracy of DTrees", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "setTruncatePrunedTree": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "val"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.ml.DTrees.setTruncatePrunedTree", "name": "setTruncatePrunedTree", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "val"], "arg_types": ["cv2.ml.DTrees", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "setTruncatePrunedTree of DTrees", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "setUse1SERule": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "val"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.ml.DTrees.setUse1SERule", "name": "setUse1SERule", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "val"], "arg_types": ["cv2.ml.DTrees", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "setUse1SERule of DTrees", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "setUseSurrogates": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "val"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.ml.DTrees.setUseSurrogates", "name": "setUseSurrogates", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "val"], "arg_types": ["cv2.ml.DTrees", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "setUseSurrogates of DTrees", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cv2.ml.DTrees.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cv2.ml.DTrees", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DTrees_Flags": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "cv2.ml.DTrees_Flags", "line": 92, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "builtins.int"}}, "DTrees_PREDICT_AUTO": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.ml.DTrees_PREDICT_AUTO", "name": "DTrees_PREDICT_AUTO", "type": "builtins.int"}}, "DTrees_PREDICT_MASK": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.ml.DTrees_PREDICT_MASK", "name": "DTrees_PREDICT_MASK", "type": "builtins.int"}}, "DTrees_PREDICT_MAX_VOTE": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.ml.DTrees_PREDICT_MAX_VOTE", "name": "DTrees_PREDICT_MAX_VOTE", "type": "builtins.int"}}, "DTrees_PREDICT_SUM": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.ml.DTrees_PREDICT_SUM", "name": "DTrees_PREDICT_SUM", "type": "builtins.int"}}, "EM": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["cv2.ml.StatModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "cv2.ml.EM", "name": "EM", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "cv2.ml.EM", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "cv2.ml", "mro": ["cv2.ml.EM", "cv2.ml.StatModel", "cv2.<PERSON><PERSON><PERSON><PERSON>", "builtins.object"], "names": {".class": "SymbolTable", "create": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["cls"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "cv2.ml.EM.create", "name": "create", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "cv2.ml.EM"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create of EM", "ret_type": "cv2.ml.EM", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "cv2.ml.EM.create", "name": "create", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "cv2.ml.EM"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create of EM", "ret_type": "cv2.ml.EM", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "getClustersNumber": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.ml.EM.getClustersNumber", "name": "getClustersNumber", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cv2.ml.EM"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getClustersNumber of EM", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "getCovarianceMatrixType": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.ml.EM.getCovarianceMatrixType", "name": "getCovarianceMatrixType", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cv2.ml.EM"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getCovarianceMatrixType of EM", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "getCovs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "covs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.ml.EM.getCovs", "name": "getCovs", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "covs"], "arg_types": ["cv2.ml.EM", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getCovs of EM", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "getMeans": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.ml.EM.getMeans", "name": "getMeans", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cv2.ml.EM"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getMeans of EM", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "getTermCriteria": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.ml.EM.getTermCriteria", "name": "getTermCriteria", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cv2.ml.EM"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getTermCriteria of EM", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.TermCriteria"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "getWeights": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.ml.EM.getWeights", "name": "getWeights", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cv2.ml.EM"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getWeights of EM", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "load": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["cls", "filepath", "nodeName"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "cv2.ml.EM.load", "name": "load", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["cls", "filepath", "nodeName"], "arg_types": [{".class": "TypeType", "item": "cv2.ml.EM"}, "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "load of EM", "ret_type": "cv2.ml.EM", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "cv2.ml.EM.load", "name": "load", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["cls", "filepath", "nodeName"], "arg_types": [{".class": "TypeType", "item": "cv2.ml.EM"}, "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "load of EM", "ret_type": "cv2.ml.EM", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "predict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "cv2.ml.EM.predict", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "samples", "results", "flags"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.ml.EM.predict", "name": "predict", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "samples", "results", "flags"], "arg_types": ["cv2.ml.EM", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "predict of EM", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.float", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.ml.EM.predict", "name": "predict", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "samples", "results", "flags"], "arg_types": ["cv2.ml.EM", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "predict of EM", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.float", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "samples", "results", "flags"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.ml.EM.predict", "name": "predict", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "samples", "results", "flags"], "arg_types": ["cv2.ml.EM", "cv2.UMat", {".class": "UnionType", "items": ["cv2.UMat", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "predict of EM", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.float", "cv2.UMat"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.ml.EM.predict", "name": "predict", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "samples", "results", "flags"], "arg_types": ["cv2.ml.EM", "cv2.UMat", {".class": "UnionType", "items": ["cv2.UMat", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "predict of EM", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.float", "cv2.UMat"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "samples", "results", "flags"], "arg_types": ["cv2.ml.EM", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "predict of EM", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.float", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "samples", "results", "flags"], "arg_types": ["cv2.ml.EM", "cv2.UMat", {".class": "UnionType", "items": ["cv2.UMat", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "predict of EM", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.float", "cv2.UMat"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "predict2": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "cv2.ml.EM.predict2", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "sample", "probs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.ml.EM.predict2", "name": "predict2", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "sample", "probs"], "arg_types": ["cv2.ml.EM", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "predict2 of EM", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.Vec2d"}, {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.ml.EM.predict2", "name": "predict2", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "sample", "probs"], "arg_types": ["cv2.ml.EM", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "predict2 of EM", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.Vec2d"}, {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "sample", "probs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.ml.EM.predict2", "name": "predict2", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "sample", "probs"], "arg_types": ["cv2.ml.EM", "cv2.UMat", {".class": "UnionType", "items": ["cv2.UMat", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "predict2 of EM", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.Vec2d"}, "cv2.UMat"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.ml.EM.predict2", "name": "predict2", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "sample", "probs"], "arg_types": ["cv2.ml.EM", "cv2.UMat", {".class": "UnionType", "items": ["cv2.UMat", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "predict2 of EM", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.Vec2d"}, "cv2.UMat"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "sample", "probs"], "arg_types": ["cv2.ml.EM", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "predict2 of EM", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.Vec2d"}, {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "sample", "probs"], "arg_types": ["cv2.ml.EM", "cv2.UMat", {".class": "UnionType", "items": ["cv2.UMat", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "predict2 of EM", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.Vec2d"}, "cv2.UMat"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "setClustersNumber": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "val"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.ml.EM.setClustersNumber", "name": "setClustersNumber", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "val"], "arg_types": ["cv2.ml.EM", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "setClustersNumber of EM", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "setCovarianceMatrixType": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "val"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.ml.EM.setCovarianceMatrixType", "name": "setCovarianceMatrixType", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "val"], "arg_types": ["cv2.ml.EM", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "setCovarianceMatrixType of EM", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "setTermCriteria": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "val"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.ml.EM.setTermCriteria", "name": "setTermCriteria", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "val"], "arg_types": ["cv2.ml.EM", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.TermCriteria"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "setTermCriteria of EM", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "trainE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "cv2.ml.EM.trainE", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1, 1, 1], "arg_names": ["self", "samples", "means0", "covs0", "weights0", "logLikelihoods", "labels", "probs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.ml.EM.trainE", "name": "trainE", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1, 1, 1], "arg_names": ["self", "samples", "means0", "covs0", "weights0", "logLikelihoods", "labels", "probs"], "arg_types": ["cv2.ml.EM", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "trainE of EM", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.bool", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.ml.EM.trainE", "name": "trainE", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1, 1, 1], "arg_names": ["self", "samples", "means0", "covs0", "weights0", "logLikelihoods", "labels", "probs"], "arg_types": ["cv2.ml.EM", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "trainE of EM", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.bool", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1, 1, 1], "arg_names": ["self", "samples", "means0", "covs0", "weights0", "logLikelihoods", "labels", "probs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.ml.EM.trainE", "name": "trainE", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1, 1, 1], "arg_names": ["self", "samples", "means0", "covs0", "weights0", "logLikelihoods", "labels", "probs"], "arg_types": ["cv2.ml.EM", "cv2.UMat", "cv2.UMat", {".class": "UnionType", "items": ["cv2.UMat", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["cv2.UMat", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["cv2.UMat", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["cv2.UMat", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["cv2.UMat", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "trainE of EM", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.bool", "cv2.UMat", "cv2.UMat", "cv2.UMat"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.ml.EM.trainE", "name": "trainE", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1, 1, 1], "arg_names": ["self", "samples", "means0", "covs0", "weights0", "logLikelihoods", "labels", "probs"], "arg_types": ["cv2.ml.EM", "cv2.UMat", "cv2.UMat", {".class": "UnionType", "items": ["cv2.UMat", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["cv2.UMat", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["cv2.UMat", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["cv2.UMat", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["cv2.UMat", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "trainE of EM", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.bool", "cv2.UMat", "cv2.UMat", "cv2.UMat"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1, 1, 1], "arg_names": ["self", "samples", "means0", "covs0", "weights0", "logLikelihoods", "labels", "probs"], "arg_types": ["cv2.ml.EM", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "trainE of EM", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.bool", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1, 1, 1], "arg_names": ["self", "samples", "means0", "covs0", "weights0", "logLikelihoods", "labels", "probs"], "arg_types": ["cv2.ml.EM", "cv2.UMat", "cv2.UMat", {".class": "UnionType", "items": ["cv2.UMat", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["cv2.UMat", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["cv2.UMat", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["cv2.UMat", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["cv2.UMat", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "trainE of EM", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.bool", "cv2.UMat", "cv2.UMat", "cv2.UMat"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "trainEM": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "cv2.ml.EM.trainEM", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "samples", "logLikelihoods", "labels", "probs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.ml.EM.trainEM", "name": "trainEM", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "samples", "logLikelihoods", "labels", "probs"], "arg_types": ["cv2.ml.EM", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "trainEM of EM", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.bool", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.ml.EM.trainEM", "name": "trainEM", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "samples", "logLikelihoods", "labels", "probs"], "arg_types": ["cv2.ml.EM", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "trainEM of EM", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.bool", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "samples", "logLikelihoods", "labels", "probs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.ml.EM.trainEM", "name": "trainEM", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "samples", "logLikelihoods", "labels", "probs"], "arg_types": ["cv2.ml.EM", "cv2.UMat", {".class": "UnionType", "items": ["cv2.UMat", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["cv2.UMat", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["cv2.UMat", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "trainEM of EM", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.bool", "cv2.UMat", "cv2.UMat", "cv2.UMat"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.ml.EM.trainEM", "name": "trainEM", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "samples", "logLikelihoods", "labels", "probs"], "arg_types": ["cv2.ml.EM", "cv2.UMat", {".class": "UnionType", "items": ["cv2.UMat", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["cv2.UMat", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["cv2.UMat", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "trainEM of EM", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.bool", "cv2.UMat", "cv2.UMat", "cv2.UMat"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "samples", "logLikelihoods", "labels", "probs"], "arg_types": ["cv2.ml.EM", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "trainEM of EM", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.bool", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "samples", "logLikelihoods", "labels", "probs"], "arg_types": ["cv2.ml.EM", "cv2.UMat", {".class": "UnionType", "items": ["cv2.UMat", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["cv2.UMat", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["cv2.UMat", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "trainEM of EM", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.bool", "cv2.UMat", "cv2.UMat", "cv2.UMat"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "trainM": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "cv2.ml.EM.trainM", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1], "arg_names": ["self", "samples", "probs0", "logLikelihoods", "labels", "probs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.ml.EM.trainM", "name": "trainM", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1], "arg_names": ["self", "samples", "probs0", "logLikelihoods", "labels", "probs"], "arg_types": ["cv2.ml.EM", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "trainM of EM", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.bool", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.ml.EM.trainM", "name": "trainM", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1], "arg_names": ["self", "samples", "probs0", "logLikelihoods", "labels", "probs"], "arg_types": ["cv2.ml.EM", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "trainM of EM", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.bool", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1], "arg_names": ["self", "samples", "probs0", "logLikelihoods", "labels", "probs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.ml.EM.trainM", "name": "trainM", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1], "arg_names": ["self", "samples", "probs0", "logLikelihoods", "labels", "probs"], "arg_types": ["cv2.ml.EM", "cv2.UMat", "cv2.UMat", {".class": "UnionType", "items": ["cv2.UMat", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["cv2.UMat", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["cv2.UMat", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "trainM of EM", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.bool", "cv2.UMat", "cv2.UMat", "cv2.UMat"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.ml.EM.trainM", "name": "trainM", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1], "arg_names": ["self", "samples", "probs0", "logLikelihoods", "labels", "probs"], "arg_types": ["cv2.ml.EM", "cv2.UMat", "cv2.UMat", {".class": "UnionType", "items": ["cv2.UMat", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["cv2.UMat", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["cv2.UMat", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "trainM of EM", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.bool", "cv2.UMat", "cv2.UMat", "cv2.UMat"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1], "arg_names": ["self", "samples", "probs0", "logLikelihoods", "labels", "probs"], "arg_types": ["cv2.ml.EM", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "trainM of EM", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.bool", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1], "arg_names": ["self", "samples", "probs0", "logLikelihoods", "labels", "probs"], "arg_types": ["cv2.ml.EM", "cv2.UMat", "cv2.UMat", {".class": "UnionType", "items": ["cv2.UMat", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["cv2.UMat", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["cv2.UMat", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "trainM of EM", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.bool", "cv2.UMat", "cv2.UMat", "cv2.UMat"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cv2.ml.EM.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cv2.ml.EM", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "EM_COV_MAT_DEFAULT": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.ml.EM_COV_MAT_DEFAULT", "name": "EM_COV_MAT_DEFAULT", "type": "builtins.int"}}, "EM_COV_MAT_DIAGONAL": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.ml.EM_COV_MAT_DIAGONAL", "name": "EM_COV_MAT_DIAGONAL", "type": "builtins.int"}}, "EM_COV_MAT_GENERIC": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.ml.EM_COV_MAT_GENERIC", "name": "EM_COV_MAT_GENERIC", "type": "builtins.int"}}, "EM_COV_MAT_SPHERICAL": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.ml.EM_COV_MAT_SPHERICAL", "name": "EM_COV_MAT_SPHERICAL", "type": "builtins.int"}}, "EM_DEFAULT_MAX_ITERS": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.ml.EM_DEFAULT_MAX_ITERS", "name": "EM_DEFAULT_MAX_ITERS", "type": "builtins.int"}}, "EM_DEFAULT_NCLUSTERS": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.ml.EM_DEFAULT_NCLUSTERS", "name": "EM_DEFAULT_NCLUSTERS", "type": "builtins.int"}}, "EM_START_AUTO_STEP": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.ml.EM_START_AUTO_STEP", "name": "EM_START_AUTO_STEP", "type": "builtins.int"}}, "EM_START_E_STEP": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.ml.EM_START_E_STEP", "name": "EM_START_E_STEP", "type": "builtins.int"}}, "EM_START_M_STEP": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.ml.EM_START_M_STEP", "name": "EM_START_M_STEP", "type": "builtins.int"}}, "EM_Types": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "cv2.ml.EM_Types", "line": 75, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "builtins.int"}}, "ErrorTypes": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "cv2.ml.ErrorTypes", "line": 17, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "builtins.int"}}, "KNEAREST_BRUTE_FORCE": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.ml.KNEAREST_BRUTE_FORCE", "name": "KNEAREST_BRUTE_FORCE", "type": "builtins.int"}}, "KNEAREST_KDTREE": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.ml.KNEAREST_KDTREE", "name": "KNEAREST_KDTREE", "type": "builtins.int"}}, "KNearest": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["cv2.ml.StatModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "cv2.ml.<PERSON><PERSON><PERSON><PERSON>", "name": "K<PERSON><PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "cv2.ml.<PERSON><PERSON><PERSON><PERSON>", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "cv2.ml", "mro": ["cv2.ml.<PERSON><PERSON><PERSON><PERSON>", "cv2.ml.StatModel", "cv2.<PERSON><PERSON><PERSON><PERSON>", "builtins.object"], "names": {".class": "SymbolTable", "create": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["cls"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "cv2.ml.KNearest.create", "name": "create", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "cv2.ml.<PERSON><PERSON><PERSON><PERSON>"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create of KNearest", "ret_type": "cv2.ml.<PERSON><PERSON><PERSON><PERSON>", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "cv2.ml.KNearest.create", "name": "create", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "cv2.ml.<PERSON><PERSON><PERSON><PERSON>"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create of KNearest", "ret_type": "cv2.ml.<PERSON><PERSON><PERSON><PERSON>", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "findNearest": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "cv2.ml.KNearest.findNearest", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1], "arg_names": ["self", "samples", "k", "results", "neighborResponses", "dist"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.ml.KNearest.findNearest", "name": "findNearest", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1], "arg_names": ["self", "samples", "k", "results", "neighborResponses", "dist"], "arg_types": ["cv2.ml.<PERSON><PERSON><PERSON><PERSON>", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, "builtins.int", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "findNearest of KNearest", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.float", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.ml.KNearest.findNearest", "name": "findNearest", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1], "arg_names": ["self", "samples", "k", "results", "neighborResponses", "dist"], "arg_types": ["cv2.ml.<PERSON><PERSON><PERSON><PERSON>", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, "builtins.int", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "findNearest of KNearest", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.float", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1], "arg_names": ["self", "samples", "k", "results", "neighborResponses", "dist"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.ml.KNearest.findNearest", "name": "findNearest", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1], "arg_names": ["self", "samples", "k", "results", "neighborResponses", "dist"], "arg_types": ["cv2.ml.<PERSON><PERSON><PERSON><PERSON>", "cv2.UMat", "builtins.int", {".class": "UnionType", "items": ["cv2.UMat", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["cv2.UMat", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["cv2.UMat", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "findNearest of KNearest", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.float", "cv2.UMat", "cv2.UMat", "cv2.UMat"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.ml.KNearest.findNearest", "name": "findNearest", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1], "arg_names": ["self", "samples", "k", "results", "neighborResponses", "dist"], "arg_types": ["cv2.ml.<PERSON><PERSON><PERSON><PERSON>", "cv2.UMat", "builtins.int", {".class": "UnionType", "items": ["cv2.UMat", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["cv2.UMat", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["cv2.UMat", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "findNearest of KNearest", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.float", "cv2.UMat", "cv2.UMat", "cv2.UMat"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1], "arg_names": ["self", "samples", "k", "results", "neighborResponses", "dist"], "arg_types": ["cv2.ml.<PERSON><PERSON><PERSON><PERSON>", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, "builtins.int", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "findNearest of KNearest", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.float", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1], "arg_names": ["self", "samples", "k", "results", "neighborResponses", "dist"], "arg_types": ["cv2.ml.<PERSON><PERSON><PERSON><PERSON>", "cv2.UMat", "builtins.int", {".class": "UnionType", "items": ["cv2.UMat", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["cv2.UMat", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["cv2.UMat", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "findNearest of KNearest", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.float", "cv2.UMat", "cv2.UMat", "cv2.UMat"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "getAlgorithmType": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.ml.KNearest.getAlgorithmType", "name": "getAlgorithmType", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cv2.ml.<PERSON><PERSON><PERSON><PERSON>"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getAlgorithmType of KNearest", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "getDefaultK": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.ml.KNearest.getDefaultK", "name": "getDefaultK", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cv2.ml.<PERSON><PERSON><PERSON><PERSON>"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getDefaultK of KNearest", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "getEmax": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.ml.KNearest.getEmax", "name": "getEmax", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cv2.ml.<PERSON><PERSON><PERSON><PERSON>"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getEmax of KNearest", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "getIsClassifier": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.ml.KNearest.getIsClassifier", "name": "getIsClassifier", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cv2.ml.<PERSON><PERSON><PERSON><PERSON>"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getIsClassifier of KNearest", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "load": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "filepath"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "cv2.ml.KNearest.load", "name": "load", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "filepath"], "arg_types": [{".class": "TypeType", "item": "cv2.ml.<PERSON><PERSON><PERSON><PERSON>"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "load of K<PERSON>earest", "ret_type": "cv2.ml.<PERSON><PERSON><PERSON><PERSON>", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "cv2.ml.KNearest.load", "name": "load", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "filepath"], "arg_types": [{".class": "TypeType", "item": "cv2.ml.<PERSON><PERSON><PERSON><PERSON>"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "load of K<PERSON>earest", "ret_type": "cv2.ml.<PERSON><PERSON><PERSON><PERSON>", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "setAlgorithmType": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "val"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.ml.KNearest.setAlgorithmType", "name": "setAlgorithmType", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "val"], "arg_types": ["cv2.ml.<PERSON><PERSON><PERSON><PERSON>", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "setAlgorithmType of KNearest", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "setDefaultK": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "val"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.ml.KNearest.setDefaultK", "name": "setDefaultK", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "val"], "arg_types": ["cv2.ml.<PERSON><PERSON><PERSON><PERSON>", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "setDefaultK of KNearest", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "setEmax": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "val"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.ml.KNearest.setEmax", "name": "setEmax", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "val"], "arg_types": ["cv2.ml.<PERSON><PERSON><PERSON><PERSON>", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "setEmax of KNearest", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "setIsClassifier": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "val"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.ml.KNearest.setIsClassifier", "name": "setIsClassifier", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "val"], "arg_types": ["cv2.ml.<PERSON><PERSON><PERSON><PERSON>", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "setIsClassifier of KNearest", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cv2.ml.KNearest.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cv2.ml.<PERSON><PERSON><PERSON><PERSON>", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "KNearest_BRUTE_FORCE": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.ml.<PERSON><PERSON><PERSON><PERSON>_BRUTE_FORCE", "name": "KNearest_BRUTE_FORCE", "type": "builtins.int"}}, "KNearest_KDTREE": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.ml.KNearest_KDTREE", "name": "KNearest_KDTREE", "type": "builtins.int"}}, "KNearest_Types": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "cv2.ml.KNearest_Types", "line": 41, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "builtins.int"}}, "LOGISTIC_REGRESSION_BATCH": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.ml.LOGISTIC_REGRESSION_BATCH", "name": "LOGISTIC_REGRESSION_BATCH", "type": "builtins.int"}}, "LOGISTIC_REGRESSION_MINI_BATCH": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.ml.LOGISTIC_REGRESSION_MINI_BATCH", "name": "LOGISTIC_REGRESSION_MINI_BATCH", "type": "builtins.int"}}, "LOGISTIC_REGRESSION_REG_DISABLE": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.ml.LOGISTIC_REGRESSION_REG_DISABLE", "name": "LOGISTIC_REGRESSION_REG_DISABLE", "type": "builtins.int"}}, "LOGISTIC_REGRESSION_REG_L1": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.ml.LOGISTIC_REGRESSION_REG_L1", "name": "LOGISTIC_REGRESSION_REG_L1", "type": "builtins.int"}}, "LOGISTIC_REGRESSION_REG_L2": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.ml.LOGISTIC_REGRESSION_REG_L2", "name": "LOGISTIC_REGRESSION_REG_L2", "type": "builtins.int"}}, "LogisticRegression": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["cv2.ml.StatModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "cv2.ml.LogisticRegression", "name": "LogisticRegression", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "cv2.ml.LogisticRegression", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "cv2.ml", "mro": ["cv2.ml.LogisticRegression", "cv2.ml.StatModel", "cv2.<PERSON><PERSON><PERSON><PERSON>", "builtins.object"], "names": {".class": "SymbolTable", "create": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["cls"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "cv2.ml.LogisticRegression.create", "name": "create", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "cv2.ml.LogisticRegression"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create of LogisticRegression", "ret_type": "cv2.ml.LogisticRegression", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "cv2.ml.LogisticRegression.create", "name": "create", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "cv2.ml.LogisticRegression"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create of LogisticRegression", "ret_type": "cv2.ml.LogisticRegression", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "getIterations": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.ml.LogisticRegression.getIterations", "name": "getIterations", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cv2.ml.LogisticRegression"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getIterations of LogisticRegression", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "getLearningRate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.ml.LogisticRegression.getLearningRate", "name": "getLearningRate", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cv2.ml.LogisticRegression"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getLearningRate of LogisticRegression", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "getMiniBatchSize": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.ml.LogisticRegression.getMiniBatchSize", "name": "getMiniBatchSize", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cv2.ml.LogisticRegression"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getMiniBatchSize of LogisticRegression", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "getRegularization": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.ml.LogisticRegression.getRegularization", "name": "getRegularization", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cv2.ml.LogisticRegression"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getRegularization of LogisticRegression", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "getTermCriteria": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.ml.LogisticRegression.getTermCriteria", "name": "getTermCriteria", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cv2.ml.LogisticRegression"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getTermCriteria of LogisticRegression", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.TermCriteria"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "getTrainMethod": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.ml.LogisticRegression.getTrainMethod", "name": "getTrainMethod", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cv2.ml.LogisticRegression"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getTrainMethod of LogisticRegression", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_learnt_thetas": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.ml.LogisticRegression.get_learnt_thetas", "name": "get_learnt_thetas", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cv2.ml.LogisticRegression"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_learnt_thetas of LogisticRegression", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "load": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["cls", "filepath", "nodeName"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "cv2.ml.LogisticRegression.load", "name": "load", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["cls", "filepath", "nodeName"], "arg_types": [{".class": "TypeType", "item": "cv2.ml.LogisticRegression"}, "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "load of LogisticRegression", "ret_type": "cv2.ml.LogisticRegression", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "cv2.ml.LogisticRegression.load", "name": "load", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["cls", "filepath", "nodeName"], "arg_types": [{".class": "TypeType", "item": "cv2.ml.LogisticRegression"}, "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "load of LogisticRegression", "ret_type": "cv2.ml.LogisticRegression", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "predict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "cv2.ml.LogisticRegression.predict", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "samples", "results", "flags"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.ml.LogisticRegression.predict", "name": "predict", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "samples", "results", "flags"], "arg_types": ["cv2.ml.LogisticRegression", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "predict of LogisticRegression", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.float", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.ml.LogisticRegression.predict", "name": "predict", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "samples", "results", "flags"], "arg_types": ["cv2.ml.LogisticRegression", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "predict of LogisticRegression", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.float", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "samples", "results", "flags"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.ml.LogisticRegression.predict", "name": "predict", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "samples", "results", "flags"], "arg_types": ["cv2.ml.LogisticRegression", "cv2.UMat", {".class": "UnionType", "items": ["cv2.UMat", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "predict of LogisticRegression", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.float", "cv2.UMat"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.ml.LogisticRegression.predict", "name": "predict", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "samples", "results", "flags"], "arg_types": ["cv2.ml.LogisticRegression", "cv2.UMat", {".class": "UnionType", "items": ["cv2.UMat", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "predict of LogisticRegression", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.float", "cv2.UMat"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "samples", "results", "flags"], "arg_types": ["cv2.ml.LogisticRegression", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "predict of LogisticRegression", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.float", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "samples", "results", "flags"], "arg_types": ["cv2.ml.LogisticRegression", "cv2.UMat", {".class": "UnionType", "items": ["cv2.UMat", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "predict of LogisticRegression", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.float", "cv2.UMat"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "setIterations": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "val"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.ml.LogisticRegression.setIterations", "name": "setIterations", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "val"], "arg_types": ["cv2.ml.LogisticRegression", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "setIterations of LogisticRegression", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "setLearningRate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "val"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.ml.LogisticRegression.setLearningRate", "name": "setLearningRate", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "val"], "arg_types": ["cv2.ml.LogisticRegression", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "setLearningRate of LogisticRegression", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "setMiniBatchSize": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "val"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.ml.LogisticRegression.setMiniBatchSize", "name": "setMiniBatchSize", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "val"], "arg_types": ["cv2.ml.LogisticRegression", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "setMiniBatchSize of LogisticRegression", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "setRegularization": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "val"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.ml.LogisticRegression.setRegularization", "name": "setRegularization", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "val"], "arg_types": ["cv2.ml.LogisticRegression", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "setRegularization of LogisticRegression", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "setTermCriteria": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "val"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.ml.LogisticRegression.setTermCriteria", "name": "setTermCriteria", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "val"], "arg_types": ["cv2.ml.LogisticRegression", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.TermCriteria"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "setTermCriteria of LogisticRegression", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "setTrainMethod": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "val"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.ml.LogisticRegression.setTrainMethod", "name": "setTrainMethod", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "val"], "arg_types": ["cv2.ml.LogisticRegression", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "setTrainMethod of LogisticRegression", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cv2.ml.LogisticRegression.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cv2.ml.LogisticRegression", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "LogisticRegression_BATCH": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.ml.LogisticRegression_BATCH", "name": "LogisticRegression_BATCH", "type": "builtins.int"}}, "LogisticRegression_MINI_BATCH": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.ml.LogisticRegression_MINI_BATCH", "name": "LogisticRegression_MINI_BATCH", "type": "builtins.int"}}, "LogisticRegression_Methods": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "cv2.ml.LogisticRegression_Methods", "line": 139, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "builtins.int"}}, "LogisticRegression_REG_DISABLE": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.ml.LogisticRegression_REG_DISABLE", "name": "LogisticRegression_REG_DISABLE", "type": "builtins.int"}}, "LogisticRegression_REG_L1": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.ml.LogisticRegression_REG_L1", "name": "LogisticRegression_REG_L1", "type": "builtins.int"}}, "LogisticRegression_REG_L2": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.ml.LogisticRegression_REG_L2", "name": "LogisticRegression_REG_L2", "type": "builtins.int"}}, "LogisticRegression_RegKinds": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "cv2.ml.LogisticRegression_RegKinds", "line": 132, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "builtins.int"}}, "NormalBayesClassifier": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["cv2.ml.StatModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "cv2.ml.NormalBayesClassifier", "name": "NormalBayesClassifier", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "cv2.ml.NormalBayesClassifier", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "cv2.ml", "mro": ["cv2.ml.NormalBayesClassifier", "cv2.ml.StatModel", "cv2.<PERSON><PERSON><PERSON><PERSON>", "builtins.object"], "names": {".class": "SymbolTable", "create": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["cls"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "cv2.ml.NormalBayesClassifier.create", "name": "create", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "cv2.ml.NormalBayesClassifier"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create of NormalBayesClassifier", "ret_type": "cv2.ml.NormalBayesClassifier", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "cv2.ml.NormalBayesClassifier.create", "name": "create", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "cv2.ml.NormalBayesClassifier"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create of NormalBayesClassifier", "ret_type": "cv2.ml.NormalBayesClassifier", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "load": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["cls", "filepath", "nodeName"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "cv2.ml.NormalBayesClassifier.load", "name": "load", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["cls", "filepath", "nodeName"], "arg_types": [{".class": "TypeType", "item": "cv2.ml.NormalBayesClassifier"}, "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "load of NormalBayesClassifier", "ret_type": "cv2.ml.NormalBayesClassifier", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "cv2.ml.NormalBayesClassifier.load", "name": "load", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["cls", "filepath", "nodeName"], "arg_types": [{".class": "TypeType", "item": "cv2.ml.NormalBayesClassifier"}, "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "load of NormalBayesClassifier", "ret_type": "cv2.ml.NormalBayesClassifier", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "predictProb": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "cv2.ml.NormalBayesClassifier.predictProb", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "inputs", "outputs", "outputProbs", "flags"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.ml.NormalBayesClassifier.predictProb", "name": "predictProb", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "inputs", "outputs", "outputProbs", "flags"], "arg_types": ["cv2.ml.NormalBayesClassifier", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "predictProb of NormalBayesClassifier", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.float", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.ml.NormalBayesClassifier.predictProb", "name": "predictProb", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "inputs", "outputs", "outputProbs", "flags"], "arg_types": ["cv2.ml.NormalBayesClassifier", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "predictProb of NormalBayesClassifier", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.float", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "inputs", "outputs", "outputProbs", "flags"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.ml.NormalBayesClassifier.predictProb", "name": "predictProb", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "inputs", "outputs", "outputProbs", "flags"], "arg_types": ["cv2.ml.NormalBayesClassifier", "cv2.UMat", {".class": "UnionType", "items": ["cv2.UMat", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["cv2.UMat", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "predictProb of NormalBayesClassifier", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.float", "cv2.UMat", "cv2.UMat"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.ml.NormalBayesClassifier.predictProb", "name": "predictProb", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "inputs", "outputs", "outputProbs", "flags"], "arg_types": ["cv2.ml.NormalBayesClassifier", "cv2.UMat", {".class": "UnionType", "items": ["cv2.UMat", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["cv2.UMat", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "predictProb of NormalBayesClassifier", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.float", "cv2.UMat", "cv2.UMat"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "inputs", "outputs", "outputProbs", "flags"], "arg_types": ["cv2.ml.NormalBayesClassifier", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "predictProb of NormalBayesClassifier", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.float", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "inputs", "outputs", "outputProbs", "flags"], "arg_types": ["cv2.ml.NormalBayesClassifier", "cv2.UMat", {".class": "UnionType", "items": ["cv2.UMat", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["cv2.UMat", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "predictProb of NormalBayesClassifier", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.float", "cv2.UMat", "cv2.UMat"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cv2.ml.NormalBayesClassifier.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cv2.ml.NormalBayesClassifier", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ParamGrid": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "cv2.ml.ParamGrid", "name": "ParamGrid", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "cv2.ml.ParamGrid", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "cv2.ml", "mro": ["cv2.ml.ParamGrid", "builtins.object"], "names": {".class": "SymbolTable", "create": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["cls", "minVal", "maxVal", "logstep"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "cv2.ml.ParamGrid.create", "name": "create", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["cls", "minVal", "maxVal", "logstep"], "arg_types": [{".class": "TypeType", "item": "cv2.ml.ParamGrid"}, "builtins.float", "builtins.float", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create of ParamGrid", "ret_type": "cv2.ml.ParamGrid", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "cv2.ml.ParamGrid.create", "name": "create", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["cls", "minVal", "maxVal", "logstep"], "arg_types": [{".class": "TypeType", "item": "cv2.ml.ParamGrid"}, "builtins.float", "builtins.float", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create of ParamGrid", "ret_type": "cv2.ml.ParamGrid", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "logStep": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "cv2.ml.ParamGrid.logStep", "name": "logStep", "type": "builtins.float"}}, "maxVal": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "cv2.ml.ParamGrid.maxVal", "name": "maxVal", "type": "builtins.float"}}, "minVal": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "cv2.ml.ParamGrid.minVal", "name": "minVal", "type": "builtins.float"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cv2.ml.ParamGrid.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cv2.ml.ParamGrid", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ROW_SAMPLE": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.ml.ROW_SAMPLE", "name": "ROW_SAMPLE", "type": "builtins.int"}}, "RTrees": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["cv2.ml.DTrees"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "cv2.ml.R<PERSON><PERSON>s", "name": "RTrees", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "cv2.ml.R<PERSON><PERSON>s", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "cv2.ml", "mro": ["cv2.ml.R<PERSON><PERSON>s", "cv2.ml.DTrees", "cv2.ml.StatModel", "cv2.<PERSON><PERSON><PERSON><PERSON>", "builtins.object"], "names": {".class": "SymbolTable", "create": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["cls"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "cv2.ml.RTrees.create", "name": "create", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "cv2.ml.R<PERSON><PERSON>s"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create of RTrees", "ret_type": "cv2.ml.R<PERSON><PERSON>s", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "cv2.ml.RTrees.create", "name": "create", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "cv2.ml.R<PERSON><PERSON>s"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create of RTrees", "ret_type": "cv2.ml.R<PERSON><PERSON>s", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "getActiveVarCount": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.ml.RTrees.getActiveVarCount", "name": "getActiveVarCount", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cv2.ml.R<PERSON><PERSON>s"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getActiveVarCount of RTrees", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "getCalculateVarImportance": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.ml.RTrees.getCalculateVarImportance", "name": "getCalculateVarImportance", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cv2.ml.R<PERSON><PERSON>s"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getCalculateVarImportance of RTrees", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "getOOBError": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.ml.RTrees.getOOBError", "name": "getOOBError", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cv2.ml.R<PERSON><PERSON>s"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get<PERSON><PERSON><PERSON><PERSON><PERSON> of RTrees", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "getTermCriteria": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.ml.RTrees.getTermCriteria", "name": "getTermCriteria", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cv2.ml.R<PERSON><PERSON>s"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getTermCriteria of RTrees", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.TermCriteria"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "getVarImportance": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.ml.RTrees.getVarImportance", "name": "getVarImportance", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cv2.ml.R<PERSON><PERSON>s"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getVarImportance of RTrees", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "getVotes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "cv2.ml.RTrees.getVotes", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "samples", "flags", "results"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.ml.RTrees.getVotes", "name": "getVotes", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "samples", "flags", "results"], "arg_types": ["cv2.ml.R<PERSON><PERSON>s", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, "builtins.int", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getVotes of RTrees", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.ml.RTrees.getVotes", "name": "getVotes", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "samples", "flags", "results"], "arg_types": ["cv2.ml.R<PERSON><PERSON>s", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, "builtins.int", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getVotes of RTrees", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "samples", "flags", "results"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.ml.RTrees.getVotes", "name": "getVotes", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "samples", "flags", "results"], "arg_types": ["cv2.ml.R<PERSON><PERSON>s", "cv2.UMat", "builtins.int", {".class": "UnionType", "items": ["cv2.UMat", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getVotes of RTrees", "ret_type": "cv2.UMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.ml.RTrees.getVotes", "name": "getVotes", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "samples", "flags", "results"], "arg_types": ["cv2.ml.R<PERSON><PERSON>s", "cv2.UMat", "builtins.int", {".class": "UnionType", "items": ["cv2.UMat", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getVotes of RTrees", "ret_type": "cv2.UMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "samples", "flags", "results"], "arg_types": ["cv2.ml.R<PERSON><PERSON>s", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, "builtins.int", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getVotes of RTrees", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "samples", "flags", "results"], "arg_types": ["cv2.ml.R<PERSON><PERSON>s", "cv2.UMat", "builtins.int", {".class": "UnionType", "items": ["cv2.UMat", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getVotes of RTrees", "ret_type": "cv2.UMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "load": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["cls", "filepath", "nodeName"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "cv2.ml.RTrees.load", "name": "load", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["cls", "filepath", "nodeName"], "arg_types": [{".class": "TypeType", "item": "cv2.ml.R<PERSON><PERSON>s"}, "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "load of RTrees", "ret_type": "cv2.ml.R<PERSON><PERSON>s", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "cv2.ml.RTrees.load", "name": "load", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["cls", "filepath", "nodeName"], "arg_types": [{".class": "TypeType", "item": "cv2.ml.R<PERSON><PERSON>s"}, "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "load of RTrees", "ret_type": "cv2.ml.R<PERSON><PERSON>s", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "setActiveVarCount": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "val"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.ml.RTrees.setActiveVarCount", "name": "setActiveVarCount", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "val"], "arg_types": ["cv2.ml.R<PERSON><PERSON>s", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "setActiveVarCount of RTrees", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "setCalculateVarImportance": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "val"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.ml.RTrees.setCalculateVarImportance", "name": "setCalculateVarImportance", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "val"], "arg_types": ["cv2.ml.R<PERSON><PERSON>s", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "setCalculateVarImportance of RTrees", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "setTermCriteria": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "val"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.ml.RTrees.setTermCriteria", "name": "setTermCriteria", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "val"], "arg_types": ["cv2.ml.R<PERSON><PERSON>s", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.TermCriteria"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "setTermCriteria of RTrees", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cv2.ml.RTrees.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cv2.ml.R<PERSON><PERSON>s", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "STAT_MODEL_COMPRESSED_INPUT": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.ml.STAT_MODEL_COMPRESSED_INPUT", "name": "STAT_MODEL_COMPRESSED_INPUT", "type": "builtins.int"}}, "STAT_MODEL_PREPROCESSED_INPUT": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.ml.STAT_MODEL_PREPROCESSED_INPUT", "name": "STAT_MODEL_PREPROCESSED_INPUT", "type": "builtins.int"}}, "STAT_MODEL_RAW_OUTPUT": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.ml.STAT_MODEL_RAW_OUTPUT", "name": "STAT_MODEL_RAW_OUTPUT", "type": "builtins.int"}}, "STAT_MODEL_UPDATE_MODEL": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.ml.STAT_MODEL_UPDATE_MODEL", "name": "STAT_MODEL_UPDATE_MODEL", "type": "builtins.int"}}, "SVM": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["cv2.ml.StatModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "cv2.ml.SVM", "name": "SVM", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "cv2.ml.SVM", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "cv2.ml", "mro": ["cv2.ml.SVM", "cv2.ml.StatModel", "cv2.<PERSON><PERSON><PERSON><PERSON>", "builtins.object"], "names": {".class": "SymbolTable", "create": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["cls"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "cv2.ml.SVM.create", "name": "create", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "cv2.ml.SVM"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create of SVM", "ret_type": "cv2.ml.SVM", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "cv2.ml.SVM.create", "name": "create", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "cv2.ml.SVM"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create of SVM", "ret_type": "cv2.ml.SVM", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "getC": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.ml.SVM.getC", "name": "getC", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cv2.ml.SVM"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getC of SVM", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "getClassWeights": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.ml.SVM.getClassWeights", "name": "getClassWeights", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cv2.ml.SVM"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getClassWeights of SVM", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "getCoef0": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.ml.SVM.getCoef0", "name": "getCoef0", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cv2.ml.SVM"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getCoef0 of SVM", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "getDecisionFunction": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "cv2.ml.SVM.getDecisionFunction", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "i", "alpha", "svidx"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.ml.SVM.getDecisionFunction", "name": "getDecisionFunction", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "i", "alpha", "svidx"], "arg_types": ["cv2.ml.SVM", "builtins.int", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getDecisionFunction of SVM", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.float", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.ml.SVM.getDecisionFunction", "name": "getDecisionFunction", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "i", "alpha", "svidx"], "arg_types": ["cv2.ml.SVM", "builtins.int", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getDecisionFunction of SVM", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.float", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "i", "alpha", "svidx"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.ml.SVM.getDecisionFunction", "name": "getDecisionFunction", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "i", "alpha", "svidx"], "arg_types": ["cv2.ml.SVM", "builtins.int", {".class": "UnionType", "items": ["cv2.UMat", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["cv2.UMat", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getDecisionFunction of SVM", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.float", "cv2.UMat", "cv2.UMat"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.ml.SVM.getDecisionFunction", "name": "getDecisionFunction", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "i", "alpha", "svidx"], "arg_types": ["cv2.ml.SVM", "builtins.int", {".class": "UnionType", "items": ["cv2.UMat", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["cv2.UMat", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getDecisionFunction of SVM", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.float", "cv2.UMat", "cv2.UMat"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "i", "alpha", "svidx"], "arg_types": ["cv2.ml.SVM", "builtins.int", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getDecisionFunction of SVM", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.float", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "i", "alpha", "svidx"], "arg_types": ["cv2.ml.SVM", "builtins.int", {".class": "UnionType", "items": ["cv2.UMat", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["cv2.UMat", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getDecisionFunction of SVM", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.float", "cv2.UMat", "cv2.UMat"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "getDefaultGridPtr": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["param_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "cv2.ml.SVM.getDefaultGridPtr", "name": "getDefaultGridPtr", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["param_id"], "arg_types": ["builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getDefaultGridPtr of SVM", "ret_type": "cv2.ml.ParamGrid", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "cv2.ml.SVM.getDefaultGridPtr", "name": "getDefaultGridPtr", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["param_id"], "arg_types": ["builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getDefaultGridPtr of SVM", "ret_type": "cv2.ml.ParamGrid", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "getDegree": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.ml.SVM.getDegree", "name": "getDegree", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cv2.ml.SVM"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getDegree of SVM", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "getGamma": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.ml.SVM.getGamma", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cv2.ml.SVM"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getGamma of SVM", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "getKernelType": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.ml.SVM.getKernelType", "name": "getKernelType", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cv2.ml.SVM"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getKernelType of SVM", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "getNu": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.ml.SVM.getNu", "name": "getNu", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cv2.ml.SVM"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getNu of SVM", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "getP": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.ml.SVM.getP", "name": "getP", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cv2.ml.SVM"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getP of SVM", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "getSupportVectors": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.ml.SVM.getSupportVectors", "name": "getSupportVectors", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cv2.ml.SVM"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getSupportVectors of SVM", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "getTermCriteria": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.ml.SVM.getTermCriteria", "name": "getTermCriteria", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cv2.ml.SVM"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getTermCriteria of SVM", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.TermCriteria"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "getType": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.ml.SVM.getType", "name": "getType", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cv2.ml.SVM"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getType of SVM", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "getUncompressedSupportVectors": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.ml.SVM.getUncompressedSupportVectors", "name": "getUncompressedSupportVectors", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cv2.ml.SVM"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getUncompressedSupportVectors of SVM", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "load": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "filepath"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "cv2.ml.SVM.load", "name": "load", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "filepath"], "arg_types": [{".class": "TypeType", "item": "cv2.ml.SVM"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "load of SVM", "ret_type": "cv2.ml.SVM", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "cv2.ml.SVM.load", "name": "load", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "filepath"], "arg_types": [{".class": "TypeType", "item": "cv2.ml.SVM"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "load of SVM", "ret_type": "cv2.ml.SVM", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "setC": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "val"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.ml.SVM.setC", "name": "setC", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "val"], "arg_types": ["cv2.ml.SVM", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "setC of SVM", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "setClassWeights": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "val"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.ml.SVM.setClassWeights", "name": "setClassWeights", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "val"], "arg_types": ["cv2.ml.SVM", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "setClassWeights of SVM", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "setCoef0": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "val"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.ml.SVM.setCoef0", "name": "setCoef0", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "val"], "arg_types": ["cv2.ml.SVM", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "setCoef0 of SVM", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "setDegree": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "val"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.ml.SVM.setDegree", "name": "setDegree", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "val"], "arg_types": ["cv2.ml.SVM", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "setDegree of SVM", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "setGamma": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "val"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.ml.SVM.setGamma", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "val"], "arg_types": ["cv2.ml.SVM", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "setGamma of SVM", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "setKernel": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "kernelType"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.ml.SVM.setKernel", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "kernelType"], "arg_types": ["cv2.ml.SVM", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "setKernel of SVM", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "setNu": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "val"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.ml.SVM.setNu", "name": "setNu", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "val"], "arg_types": ["cv2.ml.SVM", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "setNu of SVM", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "setP": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "val"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.ml.SVM.setP", "name": "setP", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "val"], "arg_types": ["cv2.ml.SVM", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "setP of SVM", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "setTermCriteria": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "val"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.ml.SVM.setTermCriteria", "name": "setTermCriteria", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "val"], "arg_types": ["cv2.ml.SVM", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.TermCriteria"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "setTermCriteria of SVM", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "setType": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "val"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.ml.SVM.setType", "name": "setType", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "val"], "arg_types": ["cv2.ml.SVM", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "setType of SVM", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "trainAuto": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "cv2.ml.SVM.trainAuto", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "samples", "layout", "responses", "kFold", "Cgrid", "gammaGrid", "pGrid", "nuGrid", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "degreeGrid", "balanced"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.ml.SVM.trainAuto", "name": "trainAuto", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "samples", "layout", "responses", "kFold", "Cgrid", "gammaGrid", "pGrid", "nuGrid", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "degreeGrid", "balanced"], "arg_types": ["cv2.ml.SVM", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, "builtins.int", "cv2.ml.ParamGrid", "cv2.ml.ParamGrid", "cv2.ml.ParamGrid", "cv2.ml.ParamGrid", "cv2.ml.ParamGrid", "cv2.ml.ParamGrid", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "trainAuto of SVM", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.ml.SVM.trainAuto", "name": "trainAuto", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "samples", "layout", "responses", "kFold", "Cgrid", "gammaGrid", "pGrid", "nuGrid", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "degreeGrid", "balanced"], "arg_types": ["cv2.ml.SVM", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, "builtins.int", "cv2.ml.ParamGrid", "cv2.ml.ParamGrid", "cv2.ml.ParamGrid", "cv2.ml.ParamGrid", "cv2.ml.ParamGrid", "cv2.ml.ParamGrid", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "trainAuto of SVM", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "samples", "layout", "responses", "kFold", "Cgrid", "gammaGrid", "pGrid", "nuGrid", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "degreeGrid", "balanced"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.ml.SVM.trainAuto", "name": "trainAuto", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "samples", "layout", "responses", "kFold", "Cgrid", "gammaGrid", "pGrid", "nuGrid", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "degreeGrid", "balanced"], "arg_types": ["cv2.ml.SVM", "cv2.UMat", "builtins.int", "cv2.UMat", "builtins.int", "cv2.ml.ParamGrid", "cv2.ml.ParamGrid", "cv2.ml.ParamGrid", "cv2.ml.ParamGrid", "cv2.ml.ParamGrid", "cv2.ml.ParamGrid", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "trainAuto of SVM", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.ml.SVM.trainAuto", "name": "trainAuto", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "samples", "layout", "responses", "kFold", "Cgrid", "gammaGrid", "pGrid", "nuGrid", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "degreeGrid", "balanced"], "arg_types": ["cv2.ml.SVM", "cv2.UMat", "builtins.int", "cv2.UMat", "builtins.int", "cv2.ml.ParamGrid", "cv2.ml.ParamGrid", "cv2.ml.ParamGrid", "cv2.ml.ParamGrid", "cv2.ml.ParamGrid", "cv2.ml.ParamGrid", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "trainAuto of SVM", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "samples", "layout", "responses", "kFold", "Cgrid", "gammaGrid", "pGrid", "nuGrid", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "degreeGrid", "balanced"], "arg_types": ["cv2.ml.SVM", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, "builtins.int", "cv2.ml.ParamGrid", "cv2.ml.ParamGrid", "cv2.ml.ParamGrid", "cv2.ml.ParamGrid", "cv2.ml.ParamGrid", "cv2.ml.ParamGrid", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "trainAuto of SVM", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "samples", "layout", "responses", "kFold", "Cgrid", "gammaGrid", "pGrid", "nuGrid", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "degreeGrid", "balanced"], "arg_types": ["cv2.ml.SVM", "cv2.UMat", "builtins.int", "cv2.UMat", "builtins.int", "cv2.ml.ParamGrid", "cv2.ml.ParamGrid", "cv2.ml.ParamGrid", "cv2.ml.ParamGrid", "cv2.ml.ParamGrid", "cv2.ml.ParamGrid", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "trainAuto of SVM", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cv2.ml.SVM.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cv2.ml.SVM", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SVMSGD": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["cv2.ml.StatModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "cv2.ml.SVMSGD", "name": "SVMSGD", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "cv2.ml.SVMSGD", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "cv2.ml", "mro": ["cv2.ml.SVMSGD", "cv2.ml.StatModel", "cv2.<PERSON><PERSON><PERSON><PERSON>", "builtins.object"], "names": {".class": "SymbolTable", "create": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["cls"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "cv2.ml.SVMSGD.create", "name": "create", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "cv2.ml.SVMSGD"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create of SVMSGD", "ret_type": "cv2.ml.SVMSGD", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "cv2.ml.SVMSGD.create", "name": "create", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "cv2.ml.SVMSGD"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create of SVMSGD", "ret_type": "cv2.ml.SVMSGD", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "getInitialStepSize": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.ml.SVMSGD.getInitialStepSize", "name": "getInitialStepSize", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cv2.ml.SVMSGD"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getInitialStepSize of SVMSGD", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "getMarginRegularization": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.ml.SVMSGD.getMarginRegularization", "name": "getMarginRegularization", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cv2.ml.SVMSGD"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getMarginRegularization of SVMSGD", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "getMarginType": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.ml.SVMSGD.getMarginType", "name": "getMarginType", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cv2.ml.SVMSGD"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getMarginType of SVMSGD", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "getShift": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.ml.SVMSGD.getShift", "name": "getShift", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cv2.ml.SVMSGD"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getShift of SVMSGD", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "getStepDecreasingPower": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.ml.SVMSGD.getStepDecreasingPower", "name": "getStepDecreasingPower", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cv2.ml.SVMSGD"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getStepDecreasingPower of SVMSGD", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "getSvmsgdType": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.ml.SVMSGD.getSvmsgdType", "name": "getSvmsgdType", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cv2.ml.SVMSGD"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getSvmsgdType of SVMSGD", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "getTermCriteria": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.ml.SVMSGD.getTermCriteria", "name": "getTermCriteria", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cv2.ml.SVMSGD"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getTermCriteria of SVMSGD", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.TermCriteria"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "getWeights": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.ml.SVMSGD.getWeights", "name": "getWeights", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cv2.ml.SVMSGD"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getWeights of SVMSGD", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "load": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["cls", "filepath", "nodeName"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "cv2.ml.SVMSGD.load", "name": "load", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["cls", "filepath", "nodeName"], "arg_types": [{".class": "TypeType", "item": "cv2.ml.SVMSGD"}, "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "load of SVMSGD", "ret_type": "cv2.ml.SVMSGD", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "cv2.ml.SVMSGD.load", "name": "load", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["cls", "filepath", "nodeName"], "arg_types": [{".class": "TypeType", "item": "cv2.ml.SVMSGD"}, "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "load of SVMSGD", "ret_type": "cv2.ml.SVMSGD", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "setInitialStepSize": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "InitialStepSize"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.ml.SVMSGD.setInitialStepSize", "name": "setInitialStepSize", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "InitialStepSize"], "arg_types": ["cv2.ml.SVMSGD", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "setInitialStepSize of SVMSGD", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "setMarginRegularization": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "marginRegularization"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.ml.SVMSGD.setMarginRegularization", "name": "setMarginRegularization", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "marginRegularization"], "arg_types": ["cv2.ml.SVMSGD", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "setMarginRegularization of SVMSGD", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "setMarginType": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "marginType"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.ml.SVMSGD.setMarginType", "name": "setMarginType", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "marginType"], "arg_types": ["cv2.ml.SVMSGD", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "setMarginType of SVMSGD", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "setOptimalParameters": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "svmsgdType", "marginType"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.ml.SVMSGD.setOptimalParameters", "name": "setOptimalParameters", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "svmsgdType", "marginType"], "arg_types": ["cv2.ml.SVMSGD", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "setOptimalParameters of SVMSGD", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "setStepDecreasingPower": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.ml.SVMSGD.setStepDecreasingPower", "name": "setStepDecreasingPower", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "arg_types": ["cv2.ml.SVMSGD", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "setStepDecreasingPower of SVMSGD", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "setSvmsgdType": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "svmsgdType"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.ml.SVMSGD.setSvmsgdType", "name": "setSvmsgdType", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "svmsgdType"], "arg_types": ["cv2.ml.SVMSGD", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "setSvmsgdType of SVMSGD", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "setTermCriteria": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "val"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.ml.SVMSGD.setTermCriteria", "name": "setTermCriteria", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "val"], "arg_types": ["cv2.ml.SVMSGD", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.TermCriteria"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "setTermCriteria of SVMSGD", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cv2.ml.SVMSGD.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cv2.ml.SVMSGD", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SVMSGD_ASGD": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.ml.SVMSGD_ASGD", "name": "SVMSGD_ASGD", "type": "builtins.int"}}, "SVMSGD_HARD_MARGIN": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.ml.SVMSGD_HARD_MARGIN", "name": "SVMSGD_HARD_MARGIN", "type": "builtins.int"}}, "SVMSGD_MarginType": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "cv2.ml.SVMSGD_MarginType", "line": 149, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "builtins.int"}}, "SVMSGD_SGD": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.ml.SVMSGD_SGD", "name": "SVMSGD_SGD", "type": "builtins.int"}}, "SVMSGD_SOFT_MARGIN": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.ml.SVMSGD_SOFT_MARGIN", "name": "SVMSGD_SOFT_MARGIN", "type": "builtins.int"}}, "SVMSGD_SvmsgdType": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "cv2.ml.SVMSGD_SvmsgdType", "line": 144, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "builtins.int"}}, "SVM_C": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.ml.SVM_C", "name": "SVM_C", "type": "builtins.int"}}, "SVM_CHI2": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.ml.SVM_CHI2", "name": "SVM_CHI2", "type": "builtins.int"}}, "SVM_COEF": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.ml.SVM_COEF", "name": "SVM_COEF", "type": "builtins.int"}}, "SVM_CUSTOM": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.ml.SVM_CUSTOM", "name": "SVM_CUSTOM", "type": "builtins.int"}}, "SVM_C_SVC": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.ml.SVM_C_SVC", "name": "SVM_C_SVC", "type": "builtins.int"}}, "SVM_DEGREE": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.ml.SVM_DEGREE", "name": "SVM_DEGREE", "type": "builtins.int"}}, "SVM_EPS_SVR": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.ml.SVM_EPS_SVR", "name": "SVM_EPS_SVR", "type": "builtins.int"}}, "SVM_GAMMA": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.ml.SVM_GAMMA", "name": "SVM_GAMMA", "type": "builtins.int"}}, "SVM_INTER": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.ml.SVM_INTER", "name": "SVM_INTER", "type": "builtins.int"}}, "SVM_KernelTypes": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "cv2.ml.SVM_KernelTypes", "line": 59, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "builtins.int"}}, "SVM_LINEAR": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.ml.SVM_LINEAR", "name": "SVM_LINEAR", "type": "builtins.int"}}, "SVM_NU": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.ml.SVM_NU", "name": "SVM_NU", "type": "builtins.int"}}, "SVM_NU_SVC": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.ml.SVM_NU_SVC", "name": "SVM_NU_SVC", "type": "builtins.int"}}, "SVM_NU_SVR": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.ml.SVM_NU_SVR", "name": "SVM_NU_SVR", "type": "builtins.int"}}, "SVM_ONE_CLASS": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.ml.SVM_ONE_CLASS", "name": "SVM_ONE_CLASS", "type": "builtins.int"}}, "SVM_P": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.ml.SVM_P", "name": "SVM_P", "type": "builtins.int"}}, "SVM_POLY": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.ml.SVM_POLY", "name": "SVM_POLY", "type": "builtins.int"}}, "SVM_ParamTypes": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "cv2.ml.SVM_ParamTypes", "line": 68, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "builtins.int"}}, "SVM_RBF": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.ml.SVM_RBF", "name": "SVM_RBF", "type": "builtins.int"}}, "SVM_SIGMOID": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.ml.SVM_SIGMOID", "name": "SVM_SIGMOID", "type": "builtins.int"}}, "SVM_Types": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "cv2.ml.SVM_Types", "line": 49, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "builtins.int"}}, "SampleTypes": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "cv2.ml.SampleTypes", "line": 22, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "builtins.int"}}, "StatModel": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["cv2.<PERSON><PERSON><PERSON><PERSON>"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "cv2.ml.StatModel", "name": "StatModel", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "cv2.ml.StatModel", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "cv2.ml", "mro": ["cv2.ml.StatModel", "cv2.<PERSON><PERSON><PERSON><PERSON>", "builtins.object"], "names": {".class": "SymbolTable", "calcError": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "cv2.ml.StatModel.calcError", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "data", "test", "resp"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.ml.StatModel.calcError", "name": "calcError", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "data", "test", "resp"], "arg_types": ["cv2.ml.StatModel", "cv2.ml.TrainData", "builtins.bool", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "calcError of StatModel", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.float", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.ml.StatModel.calcError", "name": "calcError", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "data", "test", "resp"], "arg_types": ["cv2.ml.StatModel", "cv2.ml.TrainData", "builtins.bool", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "calcError of StatModel", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.float", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "data", "test", "resp"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.ml.StatModel.calcError", "name": "calcError", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "data", "test", "resp"], "arg_types": ["cv2.ml.StatModel", "cv2.ml.TrainData", "builtins.bool", {".class": "UnionType", "items": ["cv2.UMat", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "calcError of StatModel", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.float", "cv2.UMat"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.ml.StatModel.calcError", "name": "calcError", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "data", "test", "resp"], "arg_types": ["cv2.ml.StatModel", "cv2.ml.TrainData", "builtins.bool", {".class": "UnionType", "items": ["cv2.UMat", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "calcError of StatModel", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.float", "cv2.UMat"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "data", "test", "resp"], "arg_types": ["cv2.ml.StatModel", "cv2.ml.TrainData", "builtins.bool", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "calcError of StatModel", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.float", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "data", "test", "resp"], "arg_types": ["cv2.ml.StatModel", "cv2.ml.TrainData", "builtins.bool", {".class": "UnionType", "items": ["cv2.UMat", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "calcError of StatModel", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.float", "cv2.UMat"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "empty": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.ml.StatModel.empty", "name": "empty", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cv2.ml.StatModel"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "empty of StatModel", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "getVarCount": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.ml.StatModel.getVarCount", "name": "getVarCount", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cv2.ml.StatModel"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getVarCount of StatModel", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "isClassifier": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.ml.StatModel.isClassifier", "name": "isClassifier", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cv2.ml.StatModel"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "isClassifier of StatModel", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "isTrained": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.ml.StatModel.isTrained", "name": "isTrained", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cv2.ml.StatModel"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "isTrained of StatModel", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "predict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "cv2.ml.StatModel.predict", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "samples", "results", "flags"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.ml.StatModel.predict", "name": "predict", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "samples", "results", "flags"], "arg_types": ["cv2.ml.StatModel", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "predict of StatModel", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.float", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.ml.StatModel.predict", "name": "predict", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "samples", "results", "flags"], "arg_types": ["cv2.ml.StatModel", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "predict of StatModel", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.float", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "samples", "results", "flags"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.ml.StatModel.predict", "name": "predict", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "samples", "results", "flags"], "arg_types": ["cv2.ml.StatModel", "cv2.UMat", {".class": "UnionType", "items": ["cv2.UMat", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "predict of StatModel", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.float", "cv2.UMat"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.ml.StatModel.predict", "name": "predict", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "samples", "results", "flags"], "arg_types": ["cv2.ml.StatModel", "cv2.UMat", {".class": "UnionType", "items": ["cv2.UMat", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "predict of StatModel", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.float", "cv2.UMat"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "samples", "results", "flags"], "arg_types": ["cv2.ml.StatModel", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "predict of StatModel", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.float", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "samples", "results", "flags"], "arg_types": ["cv2.ml.StatModel", "cv2.UMat", {".class": "UnionType", "items": ["cv2.UMat", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "predict of StatModel", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.float", "cv2.UMat"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "train": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "cv2.ml.StatModel.train", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "trainData", "flags"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.ml.StatModel.train", "name": "train", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "trainData", "flags"], "arg_types": ["cv2.ml.StatModel", "cv2.ml.TrainData", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "train of StatModel", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.ml.StatModel.train", "name": "train", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "trainData", "flags"], "arg_types": ["cv2.ml.StatModel", "cv2.ml.TrainData", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "train of StatModel", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "samples", "layout", "responses"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.ml.StatModel.train", "name": "train", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "samples", "layout", "responses"], "arg_types": ["cv2.ml.StatModel", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "train of StatModel", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.ml.StatModel.train", "name": "train", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "samples", "layout", "responses"], "arg_types": ["cv2.ml.StatModel", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "train of StatModel", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "samples", "layout", "responses"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.ml.StatModel.train", "name": "train", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "samples", "layout", "responses"], "arg_types": ["cv2.ml.StatModel", "cv2.UMat", "builtins.int", "cv2.UMat"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "train of StatModel", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.ml.StatModel.train", "name": "train", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "samples", "layout", "responses"], "arg_types": ["cv2.ml.StatModel", "cv2.UMat", "builtins.int", "cv2.UMat"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "train of StatModel", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "trainData", "flags"], "arg_types": ["cv2.ml.StatModel", "cv2.ml.TrainData", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "train of StatModel", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "samples", "layout", "responses"], "arg_types": ["cv2.ml.StatModel", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "train of StatModel", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "samples", "layout", "responses"], "arg_types": ["cv2.ml.StatModel", "cv2.UMat", "builtins.int", "cv2.UMat"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "train of StatModel", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cv2.ml.StatModel.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cv2.ml.StatModel", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "StatModel_COMPRESSED_INPUT": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.ml.StatModel_COMPRESSED_INPUT", "name": "StatModel_COMPRESSED_INPUT", "type": "builtins.int"}}, "StatModel_Flags": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "cv2.ml.StatModel_Flags", "line": 34, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "builtins.int"}}, "StatModel_PREPROCESSED_INPUT": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.ml.StatModel_PREPROCESSED_INPUT", "name": "StatModel_PREPROCESSED_INPUT", "type": "builtins.int"}}, "StatModel_RAW_OUTPUT": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.ml.StatModel_RAW_OUTPUT", "name": "StatModel_RAW_OUTPUT", "type": "builtins.int"}}, "StatModel_UPDATE_MODEL": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.ml.StatModel_UPDATE_MODEL", "name": "StatModel_UPDATE_MODEL", "type": "builtins.int"}}, "TEST_ERROR": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.ml.TEST_ERROR", "name": "TEST_ERROR", "type": "builtins.int"}}, "TRAIN_ERROR": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.ml.TRAIN_ERROR", "name": "TRAIN_ERROR", "type": "builtins.int"}}, "TrainData": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "cv2.ml.TrainData", "name": "TrainData", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "cv2.ml.TrainData", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "cv2.ml", "mro": ["cv2.ml.TrainData", "builtins.object"], "names": {".class": "SymbolTable", "create": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": ["is_class"], "fullname": "cv2.ml.TrainData.create", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1, 1, 1, 1], "arg_names": ["cls", "samples", "layout", "responses", "varIdx", "sampleIdx", "sampleWeights", "varType"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_overload", "is_decorated"], "fullname": "cv2.ml.TrainData.create", "name": "create", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1, 1, 1, 1], "arg_names": ["cls", "samples", "layout", "responses", "varIdx", "sampleIdx", "sampleWeights", "varType"], "arg_types": [{".class": "TypeType", "item": "cv2.ml.TrainData"}, {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create of TrainData", "ret_type": "cv2.ml.TrainData", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_classmethod", "is_ready", "is_inferred"], "fullname": "cv2.ml.TrainData.create", "name": "create", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1, 1, 1, 1], "arg_names": ["cls", "samples", "layout", "responses", "varIdx", "sampleIdx", "sampleWeights", "varType"], "arg_types": [{".class": "TypeType", "item": "cv2.ml.TrainData"}, {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create of TrainData", "ret_type": "cv2.ml.TrainData", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1, 1, 1, 1], "arg_names": ["cls", "samples", "layout", "responses", "varIdx", "sampleIdx", "sampleWeights", "varType"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_overload", "is_decorated"], "fullname": "cv2.ml.TrainData.create", "name": "create", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1, 1, 1, 1], "arg_names": ["cls", "samples", "layout", "responses", "varIdx", "sampleIdx", "sampleWeights", "varType"], "arg_types": [{".class": "TypeType", "item": "cv2.ml.TrainData"}, "cv2.UMat", "builtins.int", "cv2.UMat", {".class": "UnionType", "items": ["cv2.UMat", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["cv2.UMat", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["cv2.UMat", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["cv2.UMat", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create of TrainData", "ret_type": "cv2.ml.TrainData", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_classmethod", "is_ready", "is_inferred"], "fullname": "cv2.ml.TrainData.create", "name": "create", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1, 1, 1, 1], "arg_names": ["cls", "samples", "layout", "responses", "varIdx", "sampleIdx", "sampleWeights", "varType"], "arg_types": [{".class": "TypeType", "item": "cv2.ml.TrainData"}, "cv2.UMat", "builtins.int", "cv2.UMat", {".class": "UnionType", "items": ["cv2.UMat", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["cv2.UMat", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["cv2.UMat", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["cv2.UMat", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create of TrainData", "ret_type": "cv2.ml.TrainData", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1, 1, 1, 1], "arg_names": ["cls", "samples", "layout", "responses", "varIdx", "sampleIdx", "sampleWeights", "varType"], "arg_types": [{".class": "TypeType", "item": "cv2.ml.TrainData"}, {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create of TrainData", "ret_type": "cv2.ml.TrainData", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1, 1, 1, 1], "arg_names": ["cls", "samples", "layout", "responses", "varIdx", "sampleIdx", "sampleWeights", "varType"], "arg_types": [{".class": "TypeType", "item": "cv2.ml.TrainData"}, "cv2.UMat", "builtins.int", "cv2.UMat", {".class": "UnionType", "items": ["cv2.UMat", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["cv2.UMat", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["cv2.UMat", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["cv2.UMat", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create of TrainData", "ret_type": "cv2.ml.TrainData", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "getCatCount": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "vi"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.ml.TrainData.getCatCount", "name": "getCatCount", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "vi"], "arg_types": ["cv2.ml.TrainData", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getCatCount of TrainData", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "getCatMap": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.ml.TrainData.getCatMap", "name": "getCatMap", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cv2.ml.TrainData"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getCatMap of TrainData", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "getCatOfs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.ml.TrainData.getCatOfs", "name": "getCatOfs", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cv2.ml.TrainData"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getCatOfs of TrainData", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "getClassLabels": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.ml.TrainData.getClassLabels", "name": "getClassLabels", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cv2.ml.TrainData"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getClassLabels of TrainData", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "getDefaultSubstValues": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.ml.TrainData.getDefaultSubstValues", "name": "getDefaultSubstValues", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cv2.ml.TrainData"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getDefaultSubstValues of TrainData", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "getLayout": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.ml.TrainData.getLayout", "name": "getLayout", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cv2.ml.TrainData"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getLayout of TrainData", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "getMissing": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.ml.TrainData.getMissing", "name": "getMissing", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cv2.ml.TrainData"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getMissing of TrainData", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "getNAllVars": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.ml.TrainData.getNAllVars", "name": "getNAllVars", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cv2.ml.TrainData"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getNAllVars of TrainData", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "getNSamples": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.ml.TrainData.getNSamples", "name": "getNSamples", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cv2.ml.TrainData"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getNSamples of TrainData", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "getNTestSamples": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.ml.TrainData.getNTestSamples", "name": "getNTestSamples", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cv2.ml.TrainData"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getNTestSamples of TrainData", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "getNTrainSamples": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.ml.TrainData.getNTrainSamples", "name": "getNTrainSamples", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cv2.ml.TrainData"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getNTrainSamples of TrainData", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "getNVars": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.ml.TrainData.getNVars", "name": "getNVars", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cv2.ml.TrainData"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getNVars of TrainData", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "getNames": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "names"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.ml.TrainData.getNames", "name": "getNames", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "names"], "arg_types": ["cv2.ml.TrainData", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getNames of TrainData", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "getNormCatResponses": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.ml.TrainData.getNormCatResponses", "name": "getNormCatResponses", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cv2.ml.TrainData"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getNormCatResponses of TrainData", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "getResponseType": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.ml.TrainData.getResponseType", "name": "getResponseType", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cv2.ml.TrainData"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getResponseType of TrainData", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "getResponses": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.ml.TrainData.getResponses", "name": "getResponses", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cv2.ml.TrainData"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getResponses of TrainData", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "getSample": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "cv2.ml.TrainData.getSample", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "varIdx", "sidx", "buf"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.ml.TrainData.getSample", "name": "getSample", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "varIdx", "sidx", "buf"], "arg_types": ["cv2.ml.TrainData", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, "builtins.int", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getSample of TrainData", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.ml.TrainData.getSample", "name": "getSample", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "varIdx", "sidx", "buf"], "arg_types": ["cv2.ml.TrainData", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, "builtins.int", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getSample of TrainData", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "varIdx", "sidx", "buf"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.ml.TrainData.getSample", "name": "getSample", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "varIdx", "sidx", "buf"], "arg_types": ["cv2.ml.TrainData", "cv2.UMat", "builtins.int", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getSample of TrainData", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.ml.TrainData.getSample", "name": "getSample", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "varIdx", "sidx", "buf"], "arg_types": ["cv2.ml.TrainData", "cv2.UMat", "builtins.int", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getSample of TrainData", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "varIdx", "sidx", "buf"], "arg_types": ["cv2.ml.TrainData", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, "builtins.int", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getSample of TrainData", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "varIdx", "sidx", "buf"], "arg_types": ["cv2.ml.TrainData", "cv2.UMat", "builtins.int", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getSample of TrainData", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "getSampleWeights": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.ml.TrainData.getSampleWeights", "name": "getSampleWeights", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cv2.ml.TrainData"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getSampleWeights of TrainData", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "getSamples": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.ml.TrainData.getSamples", "name": "getSamples", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cv2.ml.TrainData"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getSamples of TrainData", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "getSubMatrix": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["matrix", "idx", "layout"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "cv2.ml.TrainData.getSubMatrix", "name": "getSubMatrix", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["matrix", "idx", "layout"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getSubMatrix of TrainData", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "cv2.ml.TrainData.getSubMatrix", "name": "getSubMatrix", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["matrix", "idx", "layout"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getSubMatrix of TrainData", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "getSubVector": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["vec", "idx"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "cv2.ml.TrainData.getSubVector", "name": "getSubVector", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["vec", "idx"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getSubVector of TrainData", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "cv2.ml.TrainData.getSubVector", "name": "getSubVector", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["vec", "idx"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getSubVector of TrainData", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "getTestNormCatResponses": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.ml.TrainData.getTestNormCatResponses", "name": "getTestNormCatResponses", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cv2.ml.TrainData"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getTestNormCatResponses of TrainData", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "getTestResponses": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.ml.TrainData.getTestResponses", "name": "getTestResponses", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cv2.ml.TrainData"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getTestResponses of TrainData", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "getTestSampleIdx": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.ml.TrainData.getTestSampleIdx", "name": "getTestSampleIdx", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cv2.ml.TrainData"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getTestSampleIdx of TrainData", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "getTestSampleWeights": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.ml.TrainData.getTestSampleWeights", "name": "getTestSampleWeights", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cv2.ml.TrainData"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getTestSampleWeights of TrainData", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "getTestSamples": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.ml.TrainData.getTestSamples", "name": "getTestSamples", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cv2.ml.TrainData"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getTestSamples of TrainData", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "getTrainNormCatResponses": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.ml.TrainData.getTrainNormCatResponses", "name": "getTrainNormCatResponses", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cv2.ml.TrainData"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getTrainNormCatResponses of TrainData", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "getTrainResponses": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.ml.TrainData.getTrainResponses", "name": "getTrainResponses", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cv2.ml.TrainData"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getTrainResponses of TrainData", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "getTrainSampleIdx": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.ml.TrainData.getTrainSampleIdx", "name": "getTrainSampleIdx", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cv2.ml.TrainData"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getTrainSampleIdx of TrainData", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "getTrainSampleWeights": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.ml.TrainData.getTrainSampleWeights", "name": "getTrainSampleWeights", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cv2.ml.TrainData"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getTrainSampleWeights of TrainData", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "getTrainSamples": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "layout", "compressSamples", "compressVars"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.ml.TrainData.getTrainSamples", "name": "getTrainSamples", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "layout", "compressSamples", "compressVars"], "arg_types": ["cv2.ml.TrainData", "builtins.int", "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getTrainSamples of TrainData", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "getValues": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "cv2.ml.TrainData.getValues", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "vi", "sidx", "values"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.ml.TrainData.getValues", "name": "getV<PERSON>ues", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "vi", "sidx", "values"], "arg_types": ["cv2.ml.TrainData", "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getValues of TrainData", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.ml.TrainData.getValues", "name": "getV<PERSON>ues", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "vi", "sidx", "values"], "arg_types": ["cv2.ml.TrainData", "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getValues of TrainData", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "vi", "sidx", "values"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.ml.TrainData.getValues", "name": "getV<PERSON>ues", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "vi", "sidx", "values"], "arg_types": ["cv2.ml.TrainData", "builtins.int", "cv2.UMat", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getValues of TrainData", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.ml.TrainData.getValues", "name": "getV<PERSON>ues", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "vi", "sidx", "values"], "arg_types": ["cv2.ml.TrainData", "builtins.int", "cv2.UMat", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getValues of TrainData", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "vi", "sidx", "values"], "arg_types": ["cv2.ml.TrainData", "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getValues of TrainData", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "vi", "sidx", "values"], "arg_types": ["cv2.ml.TrainData", "builtins.int", "cv2.UMat", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getValues of TrainData", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "getVarIdx": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.ml.TrainData.getVarIdx", "name": "getVarIdx", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cv2.ml.TrainData"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getVarIdx of TrainData", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "getVarSymbolFlags": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.ml.TrainData.getVarSymbolFlags", "name": "getVarSymbolFlags", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cv2.ml.TrainData"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getVarSymbolFlags of TrainData", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "getVarType": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.ml.TrainData.getVarType", "name": "getVarType", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cv2.ml.TrainData"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getVarType of TrainData", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.MatLike"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "setTrainTestSplit": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "count", "shuffle"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.ml.TrainData.setTrainTestSplit", "name": "setTrainTestSplit", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "count", "shuffle"], "arg_types": ["cv2.ml.TrainData", "builtins.int", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "setTrainTestSplit of TrainData", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "setTrainTestSplitRatio": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "ratio", "shuffle"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.ml.TrainData.setTrainTestSplitRatio", "name": "setTrainTestSplitRatio", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "ratio", "shuffle"], "arg_types": ["cv2.ml.TrainData", "builtins.float", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "setTrainTestSplitRatio of TrainData", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "shuffleTrainTest": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.ml.TrainData.shuffleTrainTest", "name": "shuffleTrainTest", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cv2.ml.TrainData"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "shuffleTrainTest of TrainData", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cv2.ml.TrainData.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cv2.ml.TrainData", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "VAR_CATEGORICAL": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.ml.VAR_CATEGORICAL", "name": "VAR_CATEGORICAL", "type": "builtins.int"}}, "VAR_NUMERICAL": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.ml.VAR_NUMERICAL", "name": "VAR_NUMERICAL", "type": "builtins.int"}}, "VAR_ORDERED": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.ml.VAR_ORDERED", "name": "VAR_ORDERED", "type": "builtins.int"}}, "VariableTypes": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "cv2.ml.VariableTypes", "line": 12, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "builtins.int"}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "cv2.ml.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.ml.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.ml.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.ml.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.ml.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.ml.__package__", "name": "__package__", "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.ml.__path__", "name": "__path__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.ml.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_typing": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef", "module_hidden": true, "module_public": false}, "cv2": {".class": "SymbolTableNode", "cross_ref": "cv2", "kind": "Gdef", "module_hidden": true, "module_public": false}}, "path": "c:\\Python313\\Lib\\site-packages\\cv2\\ml\\__init__.pyi"}