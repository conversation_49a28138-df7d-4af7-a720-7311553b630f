{".class": "MypyFile", "_fullname": "numpy.lib.scimath", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "__all__": {".class": "SymbolTableNode", "cross_ref": "numpy.lib._scimath_impl.__all__", "kind": "Gdef", "module_public": false}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.lib.scimath.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.lib.scimath.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.lib.scimath.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.lib.scimath.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.lib.scimath.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.lib.scimath.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "arccos": {".class": "SymbolTableNode", "cross_ref": "numpy.lib._scimath_impl.arccos", "kind": "Gdef"}, "arcsin": {".class": "SymbolTableNode", "cross_ref": "numpy.lib._scimath_impl.arcsin", "kind": "Gdef"}, "arctanh": {".class": "SymbolTableNode", "cross_ref": "numpy.lib._scimath_impl.arctanh", "kind": "Gdef"}, "log": {".class": "SymbolTableNode", "cross_ref": "numpy.lib._scimath_impl.log", "kind": "Gdef"}, "log10": {".class": "SymbolTableNode", "cross_ref": "numpy.lib._scimath_impl.log10", "kind": "Gdef"}, "log2": {".class": "SymbolTableNode", "cross_ref": "numpy.lib._scimath_impl.log2", "kind": "Gdef"}, "logn": {".class": "SymbolTableNode", "cross_ref": "numpy.lib._scimath_impl.logn", "kind": "Gdef"}, "power": {".class": "SymbolTableNode", "cross_ref": "numpy.lib._scimath_impl.power", "kind": "Gdef"}, "sqrt": {".class": "SymbolTableNode", "cross_ref": "numpy.lib._scimath_impl.sqrt", "kind": "Gdef"}}, "path": "c:\\Python313\\Lib\\site-packages\\numpy\\lib\\scimath.pyi"}