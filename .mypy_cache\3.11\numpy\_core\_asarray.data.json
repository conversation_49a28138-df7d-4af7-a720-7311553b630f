{".class": "MypyFile", "_fullname": "numpy._core._asarray", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DTypeLike": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._dtype_like.DTypeLike", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing.Literal", "kind": "Gdef", "module_hidden": true, "module_public": false}, "NDArray": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._array_like.NDArray", "kind": "Gdef", "module_hidden": true, "module_public": false}, "TypeAlias": {".class": "SymbolTableNode", "cross_ref": "typing.TypeAlias", "kind": "Gdef", "module_hidden": true, "module_public": false}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_ArrayType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._core._asarray._ArrayType", "name": "_ArrayType", "upper_bound": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._shape._Shape"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.dtype"}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}, "values": [], "variance": 0}}, "_E": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "numpy._core._asarray._E", "line": 15, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "E"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "ENSUREARRAY"}], "uses_pep604_syntax": false}}}, "_Requirements": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "numpy._core._asarray._Requirements", "line": 8, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "C"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "C_CONTIGUOUS"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "CONTIGUOUS"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "F"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "F_CONTIGUOUS"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "FORTRAN"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "A"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "ALIGNED"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "W"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "WRITEABLE"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "O"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "OWNDATA"}], "uses_pep604_syntax": false}}}, "_RequirementsWithE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "numpy._core._asarray._RequirementsWithE", "line": 16, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._core._asarray._Requirements"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._core._asarray._E"}], "uses_pep604_syntax": true}}}, "_SupportsArrayFunc": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._array_like._SupportsArrayFunc", "kind": "Gdef", "module_hidden": true, "module_public": false}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy._core._asarray.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy._core._asarray.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy._core._asarray.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy._core._asarray.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy._core._asarray.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy._core._asarray.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "overload": {".class": "SymbolTableNode", "cross_ref": "typing.overload", "kind": "Gdef", "module_hidden": true, "module_public": false}, "require": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "numpy._core._asarray.require", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 5], "arg_names": ["a", "dtype", "requirements", "like"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy._core._asarray.require", "name": "require", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 5], "arg_names": ["a", "dtype", "requirements", "like"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._core._asarray._ArrayType", "id": -1, "name": "_ArrayType", "namespace": "numpy._core._asarray.require#0", "upper_bound": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._shape._Shape"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.dtype"}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}, "values": [], "variance": 0}, {".class": "NoneType"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._core._asarray._Requirements"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._core._asarray._Requirements"}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "uses_pep604_syntax": true}, "numpy._typing._array_like._SupportsArrayFunc"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "require", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._core._asarray._ArrayType", "id": -1, "name": "_ArrayType", "namespace": "numpy._core._asarray.require#0", "upper_bound": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._shape._Shape"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.dtype"}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._core._asarray._ArrayType", "id": -1, "name": "_ArrayType", "namespace": "numpy._core._asarray.require#0", "upper_bound": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._shape._Shape"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.dtype"}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}, "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy._core._asarray.require", "name": "require", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 5], "arg_names": ["a", "dtype", "requirements", "like"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._core._asarray._ArrayType", "id": -1, "name": "_ArrayType", "namespace": "numpy._core._asarray.require#0", "upper_bound": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._shape._Shape"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.dtype"}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}, "values": [], "variance": 0}, {".class": "NoneType"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._core._asarray._Requirements"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._core._asarray._Requirements"}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "uses_pep604_syntax": true}, "numpy._typing._array_like._SupportsArrayFunc"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "require", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._core._asarray._ArrayType", "id": -1, "name": "_ArrayType", "namespace": "numpy._core._asarray.require#0", "upper_bound": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._shape._Shape"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.dtype"}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._core._asarray._ArrayType", "id": -1, "name": "_ArrayType", "namespace": "numpy._core._asarray.require#0", "upper_bound": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._shape._Shape"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.dtype"}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}, "values": [], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 5], "arg_names": ["a", "dtype", "requirements", "like"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy._core._asarray.require", "name": "require", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 5], "arg_names": ["a", "dtype", "requirements", "like"], "arg_types": ["builtins.object", {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._dtype_like.DTypeLike"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._core._asarray._E"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._core._asarray._RequirementsWithE"}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "uses_pep604_syntax": true}, "numpy._typing._array_like._SupportsArrayFunc"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "require", "ret_type": {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy._core._asarray.require", "name": "require", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 5], "arg_names": ["a", "dtype", "requirements", "like"], "arg_types": ["builtins.object", {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._dtype_like.DTypeLike"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._core._asarray._E"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._core._asarray._RequirementsWithE"}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "uses_pep604_syntax": true}, "numpy._typing._array_like._SupportsArrayFunc"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "require", "ret_type": {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 5], "arg_names": ["a", "dtype", "requirements", "like"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy._core._asarray.require", "name": "require", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 5], "arg_names": ["a", "dtype", "requirements", "like"], "arg_types": ["builtins.object", {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._dtype_like.DTypeLike"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._core._asarray._Requirements"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._core._asarray._Requirements"}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "uses_pep604_syntax": true}, "numpy._typing._array_like._SupportsArrayFunc"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "require", "ret_type": {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy._core._asarray.require", "name": "require", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 5], "arg_names": ["a", "dtype", "requirements", "like"], "arg_types": ["builtins.object", {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._dtype_like.DTypeLike"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._core._asarray._Requirements"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._core._asarray._Requirements"}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "uses_pep604_syntax": true}, "numpy._typing._array_like._SupportsArrayFunc"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "require", "ret_type": {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 1, 1, 5], "arg_names": ["a", "dtype", "requirements", "like"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._core._asarray._ArrayType", "id": -1, "name": "_ArrayType", "namespace": "numpy._core._asarray.require#0", "upper_bound": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._shape._Shape"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.dtype"}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}, "values": [], "variance": 0}, {".class": "NoneType"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._core._asarray._Requirements"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._core._asarray._Requirements"}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "uses_pep604_syntax": true}, "numpy._typing._array_like._SupportsArrayFunc"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "require", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._core._asarray._ArrayType", "id": -1, "name": "_ArrayType", "namespace": "numpy._core._asarray.require#0", "upper_bound": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._shape._Shape"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.dtype"}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._core._asarray._ArrayType", "id": -1, "name": "_ArrayType", "namespace": "numpy._core._asarray.require#0", "upper_bound": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._shape._Shape"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.dtype"}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}, "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0, 1, 1, 5], "arg_names": ["a", "dtype", "requirements", "like"], "arg_types": ["builtins.object", {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._dtype_like.DTypeLike"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._core._asarray._E"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._core._asarray._RequirementsWithE"}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "uses_pep604_syntax": true}, "numpy._typing._array_like._SupportsArrayFunc"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "require", "ret_type": {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 1, 1, 5], "arg_names": ["a", "dtype", "requirements", "like"], "arg_types": ["builtins.object", {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._dtype_like.DTypeLike"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._core._asarray._Requirements"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._core._asarray._Requirements"}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "uses_pep604_syntax": true}, "numpy._typing._array_like._SupportsArrayFunc"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "require", "ret_type": {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}}, "path": "c:\\Python313\\Lib\\site-packages\\numpy\\_core\\_asarray.pyi"}