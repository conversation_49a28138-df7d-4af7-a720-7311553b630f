{".class": "MypyFile", "_fullname": "cv2.gapi.wip", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "GOutputs": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "cv2.gapi.wip.GOutputs", "name": "GOutputs", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "cv2.gapi.wip.GOutputs", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "cv2.gapi.wip", "mro": ["cv2.gapi.wip.GOutputs", "builtins.object"], "names": {".class": "SymbolTable", "getGArray": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "type"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.gapi.wip.GOutputs.getGArray", "name": "getGArray", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "type"], "arg_types": ["cv2.gapi.wip.GOutputs", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getGArray of GOutputs", "ret_type": "cv2.GArrayT", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "getGMat": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.gapi.wip.GOutputs.getGMat", "name": "getGMat", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cv2.gapi.wip.GOutputs"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getGMat of GOutputs", "ret_type": "cv2.GMat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "getGOpaque": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "type"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.gapi.wip.GOutputs.getGOpaque", "name": "getGOpaque", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "type"], "arg_types": ["cv2.gapi.wip.GOutputs", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getGOpaque of GOutputs", "ret_type": "cv2.GOpaqueT", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "getGScalar": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.gapi.wip.GOutputs.getGScalar", "name": "getGScalar", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cv2.gapi.wip.GOutputs"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getGScalar of GOutputs", "ret_type": "cv2.GScalar", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cv2.gapi.wip.GOutputs.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cv2.gapi.wip.GOutputs", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "IStreamSource": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "cv2.gapi.wip.IStreamSource", "name": "IStreamSource", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "cv2.gapi.wip.IStreamSource", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "cv2.gapi.wip", "mro": ["cv2.gapi.wip.IStreamSource", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cv2.gapi.wip.IStreamSource.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cv2.gapi.wip.IStreamSource", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "cv2.gapi.wip.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.gapi.wip.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.gapi.wip.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.gapi.wip.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.gapi.wip.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.gapi.wip.__package__", "name": "__package__", "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.gapi.wip.__path__", "name": "__path__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.gapi.wip.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_typing": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef", "module_hidden": true, "module_public": false}, "cv2": {".class": "SymbolTableNode", "cross_ref": "cv2", "kind": "Gdef", "module_hidden": true, "module_public": false}, "draw": {".class": "SymbolTableNode", "cross_ref": "cv2.gapi.wip.draw", "kind": "Gdef", "module_public": false}, "get_streaming_source": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["pipeline", "appsinkName", "outputType"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.gapi.wip.get_streaming_source", "name": "get_streaming_source", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["pipeline", "appsinkName", "outputType"], "arg_types": ["cv2.gapi.wip.gst.GStreamerPipeline", "builtins.str", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_streaming_source", "ret_type": "cv2.gapi.wip.IStreamSource", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "gst": {".class": "SymbolTableNode", "cross_ref": "cv2.gapi.wip.gst", "kind": "Gdef", "module_public": false}, "make_capture_src": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "cv2.gapi.wip.make_capture_src", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["path", "properties"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.gapi.wip.make_capture_src", "name": "make_capture_src", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["path", "properties"], "arg_types": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.map_int_and_double"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "make_capture_src", "ret_type": "cv2.gapi.wip.IStreamSource", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.gapi.wip.make_capture_src", "name": "make_capture_src", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["path", "properties"], "arg_types": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.map_int_and_double"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "make_capture_src", "ret_type": "cv2.gapi.wip.IStreamSource", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["id", "properties"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cv2.gapi.wip.make_capture_src", "name": "make_capture_src", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["id", "properties"], "arg_types": ["builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.map_int_and_double"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "make_capture_src", "ret_type": "cv2.gapi.wip.IStreamSource", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cv2.gapi.wip.make_capture_src", "name": "make_capture_src", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["id", "properties"], "arg_types": ["builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.map_int_and_double"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "make_capture_src", "ret_type": "cv2.gapi.wip.IStreamSource", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["path", "properties"], "arg_types": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.map_int_and_double"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "make_capture_src", "ret_type": "cv2.gapi.wip.IStreamSource", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["id", "properties"], "arg_types": ["builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "cv2.typing.map_int_and_double"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "make_capture_src", "ret_type": "cv2.gapi.wip.IStreamSource", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "make_gst_src": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["pipeline", "outputType"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cv2.gapi.wip.make_gst_src", "name": "make_gst_src", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["pipeline", "outputType"], "arg_types": ["builtins.str", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "make_gst_src", "ret_type": "cv2.gapi.wip.IStreamSource", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "onevpl": {".class": "SymbolTableNode", "cross_ref": "cv2.gapi.wip.onevpl", "kind": "Gdef", "module_public": false}}, "path": "c:\\Python313\\Lib\\site-packages\\cv2\\gapi\\wip\\__init__.pyi"}