{".class": "MypyFile", "_fullname": "pydantic.json_schema", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Annotated": {".class": "SymbolTableNode", "cross_ref": "typing.Annotated", "kind": "Gdef"}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "AnyType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.json_schema.AnyType", "name": "AnyType", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "BaseModel": {".class": "SymbolTableNode", "cross_ref": "pydantic.main.BaseModel", "kind": "Gdef"}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef"}, "ComputedField": {".class": "SymbolTableNode", "cross_ref": "pydantic_core.core_schema.ComputedField", "kind": "Gdef"}, "ConfigDict": {".class": "SymbolTableNode", "cross_ref": "pydantic.config.ConfigDict", "kind": "Gdef"}, "CoreModeRef": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "pydantic.json_schema.CoreModeRef", "line": 131, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "TupleType", "implicit": false, "items": ["pydantic.json_schema.CoreRef", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.json_schema.JsonSchemaMode"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "CoreRef": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.str"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic.json_schema.CoreRef", "name": "CoreRef", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_newtype"], "fullname": "pydantic.json_schema.CoreRef", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pydantic.json_schema", "mro": ["pydantic.json_schema.CoreRef", "builtins.str", "typing.Sequence", "typing.Reversible", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "item"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.json_schema.CoreRef.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "item"], "arg_types": ["pydantic.json_schema.CoreRef", "builtins.str"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of CoreRef", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CoreSchema": {".class": "SymbolTableNode", "cross_ref": "pydantic_core.core_schema.CoreSchema", "kind": "Gdef"}, "CoreSchemaField": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._core_utils.CoreSchemaField", "kind": "Gdef"}, "CoreSchemaOrField": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._core_utils.CoreSchemaOrField", "kind": "Gdef"}, "CoreSchemaOrFieldType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "pydantic.json_schema.CoreSchemaOrFieldType", "line": 67, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "invalid"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "any"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "none"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "bool"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "int"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "float"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "decimal"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "str"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "bytes"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "date"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "time"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "datetime"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "<PERSON><PERSON><PERSON>"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "literal"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "enum"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "is-instance"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "is-subclass"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "callable"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "list"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "tuple"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "set"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "frozenset"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "generator"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "dict"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "function-after"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "function-before"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "function-wrap"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "function-plain"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "default"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "nullable"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "union"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "tagged-union"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "chain"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "lax-or-strict"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "json-or-python"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "typed-dict"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "model-fields"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "model"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "dataclass-args"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "dataclass"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "arguments"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "arguments-v3"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "call"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "custom-error"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "json"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "url"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "multi-host-url"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "definitions"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "definition-ref"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "uuid"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "complex"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "model-field"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "dataclass-field"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "typed-dict-field"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "computed-field"}], "uses_pep604_syntax": false}}}, "Counter": {".class": "SymbolTableNode", "cross_ref": "collections.Counter", "kind": "Gdef"}, "DEFAULT_REF_TEMPLATE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "pydantic.json_schema.DEFAULT_REF_TEMPLATE", "name": "DEFAULT_REF_TEMPLATE", "type": "builtins.str"}}, "DefsRef": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.str"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic.json_schema.DefsRef", "name": "DefsRef", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_newtype"], "fullname": "pydantic.json_schema.DefsRef", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pydantic.json_schema", "mro": ["pydantic.json_schema.DefsRef", "builtins.str", "typing.Sequence", "typing.Reversible", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "item"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.json_schema.DefsRef.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "item"], "arg_types": ["pydantic.json_schema.DefsRef", "builtins.str"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of DefsRef", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Enum": {".class": "SymbolTableNode", "cross_ref": "enum.Enum", "kind": "Gdef"}, "Examples": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic.json_schema.Examples", "name": "Examples", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pydantic.json_schema.Examples", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pydantic.json_schema", "mro": ["pydantic.json_schema.Examples", "builtins.object"], "names": {".class": "SymbolTable", "__get_pydantic_json_schema__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "core_schema", "handler"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.json_schema.Examples.__get_pydantic_json_schema__", "name": "__get_pydantic_json_schema__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "core_schema", "handler"], "arg_types": ["pydantic.json_schema.Examples", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, "pydantic.annotated_handlers.GetJsonSchemaHandler"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__get_pydantic_json_schema__ of Examples", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.json_schema.JsonSchemaValue"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__hash__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.json_schema.Examples.__hash__", "name": "__hash__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pydantic.json_schema.Examples"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__hash__ of Examples", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "pydantic.json_schema.Examples.__init__", "impl": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "examples", "mode"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload"], "fullname": "pydantic.json_schema.Examples.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "examples", "mode"], "arg_types": ["pydantic.json_schema.Examples", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "validation"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "serialization"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Examples", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "examples", "mode"], "dataclass_transform_spec": null, "deprecated": "overload def (self: pydantic.json_schema.Examples, examples: builtins.dict[builtins.str, Any], mode: Union[Literal['validation'], Literal['serialization'], None] =) of function pydantic.json_schema.Examples.__init__ is deprecated: Using a dict for `examples` is deprecated since v2.9 and will be removed in v3.0. Use a list instead.", "flags": ["is_overload", "is_decorated"], "fullname": "pydantic.json_schema.Examples.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "examples", "mode"], "arg_types": ["pydantic.json_schema.Examples", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "validation"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "serialization"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Examples", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "pydantic.json_schema.Examples.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "examples", "mode"], "arg_types": ["pydantic.json_schema.Examples", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "validation"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "serialization"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Examples", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "examples", "mode"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "pydantic.json_schema.Examples.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "examples", "mode"], "arg_types": ["pydantic.json_schema.Examples", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "validation"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "serialization"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Examples", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "pydantic.json_schema.Examples.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "examples", "mode"], "arg_types": ["pydantic.json_schema.Examples", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "validation"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "serialization"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Examples", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "examples", "mode"], "arg_types": ["pydantic.json_schema.Examples", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "validation"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "serialization"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Examples", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "examples", "mode"], "arg_types": ["pydantic.json_schema.Examples", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "validation"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "serialization"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Examples", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "examples": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pydantic.json_schema.Examples.examples", "name": "examples", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}}}, "mode": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pydantic.json_schema.Examples.mode", "name": "mode", "type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "validation"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "serialization"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.json_schema.Examples.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.json_schema.Examples", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "GenerateJsonSchema": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic.json_schema.GenerateJsonSchema", "name": "GenerateJsonSchema", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pydantic.json_schema.GenerateJsonSchema", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pydantic.json_schema", "mro": ["pydantic.json_schema.GenerateJsonSchema", "builtins.object"], "names": {".class": "SymbolTable", "ValidationsMapping": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic.json_schema.GenerateJsonSchema.ValidationsMapping", "name": "ValidationsMapping", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pydantic.json_schema.GenerateJsonSchema.ValidationsMapping", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pydantic.json_schema", "mro": ["pydantic.json_schema.GenerateJsonSchema.ValidationsMapping", "builtins.object"], "names": {".class": "SymbolTable", "array": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "pydantic.json_schema.GenerateJsonSchema.ValidationsMapping.array", "name": "array", "type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "bytes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "pydantic.json_schema.GenerateJsonSchema.ValidationsMapping.bytes", "name": "bytes", "type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "numeric": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "pydantic.json_schema.GenerateJsonSchema.ValidationsMapping.numeric", "name": "numeric", "type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "object": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "pydantic.json_schema.GenerateJsonSchema.ValidationsMapping.object", "name": "object", "type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "string": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "pydantic.json_schema.GenerateJsonSchema.ValidationsMapping.string", "name": "string", "type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.json_schema.GenerateJsonSchema.ValidationsMapping.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.json_schema.GenerateJsonSchema.ValidationsMapping", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "by_alias", "ref_template"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.json_schema.GenerateJsonSchema.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "by_alias", "ref_template"], "arg_types": ["pydantic.json_schema.GenerateJsonSchema", "builtins.bool", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of GenerateJsonSchema", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_build_definitions_remapping": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.json_schema.GenerateJsonSchema._build_definitions_remapping", "name": "_build_definitions_remapping", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pydantic.json_schema.GenerateJsonSchema"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_build_definitions_remapping of GenerateJsonSchema", "ret_type": "pydantic.json_schema._DefinitionsRemapping", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_collision_counter": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "pydantic.json_schema.GenerateJsonSchema._collision_counter", "name": "_collision_counter", "type": {".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_collision_index": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "pydantic.json_schema.GenerateJsonSchema._collision_index", "name": "_collision_index", "type": {".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_common_set_schema": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "schema"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.json_schema.GenerateJsonSchema._common_set_schema", "name": "_common_set_schema", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "schema"], "arg_types": ["pydantic.json_schema.GenerateJsonSchema", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.SetSchema"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.FrozenSetSchema"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_common_set_schema of GenerateJsonSchema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.json_schema.JsonSchemaValue"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_config": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "pydantic.json_schema.GenerateJsonSchema._config", "name": "_config", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pydantic.json_schema.GenerateJsonSchema"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_config of GenerateJsonSchema", "ret_type": "pydantic._internal._config.ConfigWrapper", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "pydantic.json_schema.GenerateJsonSchema._config", "name": "_config", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pydantic.json_schema.GenerateJsonSchema"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_config of GenerateJsonSchema", "ret_type": "pydantic._internal._config.ConfigWrapper", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_config_wrapper_stack": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pydantic.json_schema.GenerateJsonSchema._config_wrapper_stack", "name": "_config_wrapper_stack", "type": "pydantic._internal._config.ConfigWrapperStack"}}, "_core_defs_invalid_for_json_schema": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "pydantic.json_schema.GenerateJsonSchema._core_defs_invalid_for_json_schema", "name": "_core_defs_invalid_for_json_schema", "type": {".class": "Instance", "args": ["pydantic.json_schema.DefsRef", "pydantic.errors.PydanticInvalidForJsonSchema"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_extract_discriminator": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "schema", "one_of_choices"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.json_schema.GenerateJsonSchema._extract_discriminator", "name": "_extract_discriminator", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "schema", "one_of_choices"], "arg_types": ["pydantic.json_schema.GenerateJsonSchema", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.TaggedUnionSchema"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.config.JsonDict"}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_extract_discriminator of GenerateJsonSchema", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_garbage_collect_definitions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "schema"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.json_schema.GenerateJsonSchema._garbage_collect_definitions", "name": "_garbage_collect_definitions", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "schema"], "arg_types": ["pydantic.json_schema.GenerateJsonSchema", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.json_schema.JsonSchemaValue"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_garbage_collect_definitions of GenerateJsonSchema", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_alias_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "field", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.json_schema.GenerateJsonSchema._get_alias_name", "name": "_get_alias_name", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "field", "name"], "arg_types": ["pydantic.json_schema.GenerateJsonSchema", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic._internal._core_utils.CoreSchemaField"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_alias_name of GenerateJsonSchema", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_mode": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "pydantic.json_schema.GenerateJsonSchema._mode", "name": "_mode", "type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.json_schema.JsonSchemaMode"}}}, "_name_required_computed_fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["computed_fields"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "pydantic.json_schema.GenerateJsonSchema._name_required_computed_fields", "name": "_name_required_computed_fields", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["computed_fields"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.ComputedField"}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_name_required_computed_fields of GenerateJsonSchema", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.bool", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.ComputedField"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "pydantic.json_schema.GenerateJsonSchema._name_required_computed_fields", "name": "_name_required_computed_fields", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["computed_fields"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.ComputedField"}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_name_required_computed_fields of GenerateJsonSchema", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.bool", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.ComputedField"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_named_required_fields_schema": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "named_required_fields"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.json_schema.GenerateJsonSchema._named_required_fields_schema", "name": "_named_required_fields_schema", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "named_required_fields"], "arg_types": ["pydantic.json_schema.GenerateJsonSchema", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.bool", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic._internal._core_utils.CoreSchemaField"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_named_required_fields_schema of GenerateJsonSchema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.json_schema.JsonSchemaValue"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_prioritized_defsref_choices": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "pydantic.json_schema.GenerateJsonSchema._prioritized_defsref_choices", "name": "_prioritized_defsref_choices", "type": {".class": "Instance", "args": ["pydantic.json_schema.DefsRef", {".class": "Instance", "args": ["pydantic.json_schema.DefsRef"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_schema_type_to_method": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pydantic.json_schema.GenerateJsonSchema._schema_type_to_method", "name": "_schema_type_to_method", "type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.json_schema.CoreSchemaOrFieldType"}, {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic._internal._core_utils.CoreSchemaOrField"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.json_schema.JsonSchemaValue"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_sort_recursive": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "value", "parent_key"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.json_schema.GenerateJsonSchema._sort_recursive", "name": "_sort_recursive", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "value", "parent_key"], "arg_types": ["pydantic.json_schema.GenerateJsonSchema", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_sort_recursive of GenerateJsonSchema", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_update_class_schema": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "json_schema", "cls", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.json_schema.GenerateJsonSchema._update_class_schema", "name": "_update_class_schema", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "json_schema", "cls", "config"], "arg_types": ["pydantic.json_schema.GenerateJsonSchema", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.json_schema.JsonSchemaValue"}, {".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.config.ConfigDict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_update_class_schema of GenerateJsonSchema", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_used": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pydantic.json_schema.GenerateJsonSchema._used", "name": "_used", "type": "builtins.bool"}}, "any_schema": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "schema"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.json_schema.GenerateJsonSchema.any_schema", "name": "any_schema", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "schema"], "arg_types": ["pydantic.json_schema.GenerateJsonSchema", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.AnySchema"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "any_schema of GenerateJsonSchema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.json_schema.JsonSchemaValue"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "arguments_schema": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "schema"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.json_schema.GenerateJsonSchema.arguments_schema", "name": "arguments_schema", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "schema"], "arg_types": ["pydantic.json_schema.GenerateJsonSchema", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.ArgumentsSchema"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "arguments_schema of GenerateJsonSchema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.json_schema.JsonSchemaValue"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "arguments_v3_schema": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "schema"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.json_schema.GenerateJsonSchema.arguments_v3_schema", "name": "arguments_v3_schema", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "schema"], "arg_types": ["pydantic.json_schema.GenerateJsonSchema", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.ArgumentsV3Schema"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "arguments_v3_schema of GenerateJsonSchema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.json_schema.JsonSchemaValue"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "bool_schema": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "schema"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.json_schema.GenerateJsonSchema.bool_schema", "name": "bool_schema", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "schema"], "arg_types": ["pydantic.json_schema.GenerateJsonSchema", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.BoolSchema"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "bool_schema of GenerateJsonSchema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.json_schema.JsonSchemaValue"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "build_schema_type_to_method": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.json_schema.GenerateJsonSchema.build_schema_type_to_method", "name": "build_schema_type_to_method", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pydantic.json_schema.GenerateJsonSchema"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "build_schema_type_to_method of GenerateJsonSchema", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.json_schema.CoreSchemaOrFieldType"}, {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic._internal._core_utils.CoreSchemaOrField"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.json_schema.JsonSchemaValue"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "by_alias": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pydantic.json_schema.GenerateJsonSchema.by_alias", "name": "by_alias", "type": "builtins.bool"}}, "bytes_schema": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "schema"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.json_schema.GenerateJsonSchema.bytes_schema", "name": "bytes_schema", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "schema"], "arg_types": ["pydantic.json_schema.GenerateJsonSchema", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.BytesSchema"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "bytes_schema of GenerateJsonSchema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.json_schema.JsonSchemaValue"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "call_schema": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "schema"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.json_schema.GenerateJsonSchema.call_schema", "name": "call_schema", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "schema"], "arg_types": ["pydantic.json_schema.GenerateJsonSchema", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CallSchema"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "call_schema of GenerateJsonSchema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.json_schema.JsonSchemaValue"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "callable_schema": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "schema"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.json_schema.GenerateJsonSchema.callable_schema", "name": "callable_schema", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "schema"], "arg_types": ["pydantic.json_schema.GenerateJsonSchema", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CallableSchema"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "callable_schema of GenerateJsonSchema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.json_schema.JsonSchemaValue"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "chain_schema": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "schema"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.json_schema.GenerateJsonSchema.chain_schema", "name": "chain_schema", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "schema"], "arg_types": ["pydantic.json_schema.GenerateJsonSchema", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.ChainSchema"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "chain_schema of GenerateJsonSchema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.json_schema.JsonSchemaValue"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "complex_schema": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "schema"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.json_schema.GenerateJsonSchema.complex_schema", "name": "complex_schema", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "schema"], "arg_types": ["pydantic.json_schema.GenerateJsonSchema", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.ComplexSchema"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "complex_schema of GenerateJsonSchema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.json_schema.JsonSchemaValue"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "computed_field_schema": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "schema"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.json_schema.GenerateJsonSchema.computed_field_schema", "name": "computed_field_schema", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "schema"], "arg_types": ["pydantic.json_schema.GenerateJsonSchema", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.ComputedField"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "computed_field_schema of GenerateJsonSchema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.json_schema.JsonSchemaValue"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "core_to_defs_refs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "pydantic.json_schema.GenerateJsonSchema.core_to_defs_refs", "name": "core_to_defs_refs", "type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.json_schema.CoreModeRef"}, "pydantic.json_schema.DefsRef"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "core_to_json_refs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "pydantic.json_schema.GenerateJsonSchema.core_to_json_refs", "name": "core_to_json_refs", "type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.json_schema.CoreModeRef"}, "pydantic.json_schema.JsonRef"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "custom_error_schema": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "schema"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.json_schema.GenerateJsonSchema.custom_error_schema", "name": "custom_error_schema", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "schema"], "arg_types": ["pydantic.json_schema.GenerateJsonSchema", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CustomErrorSchema"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "custom_error_schema of GenerateJsonSchema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.json_schema.JsonSchemaValue"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "dataclass_args_schema": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "schema"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.json_schema.GenerateJsonSchema.dataclass_args_schema", "name": "dataclass_args_schema", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "schema"], "arg_types": ["pydantic.json_schema.GenerateJsonSchema", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.DataclassArgsSchema"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "dataclass_args_schema of GenerateJsonSchema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.json_schema.JsonSchemaValue"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "dataclass_field_schema": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "schema"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.json_schema.GenerateJsonSchema.dataclass_field_schema", "name": "dataclass_field_schema", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "schema"], "arg_types": ["pydantic.json_schema.GenerateJsonSchema", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.DataclassField"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "dataclass_field_schema of GenerateJsonSchema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.json_schema.JsonSchemaValue"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "dataclass_schema": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "schema"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.json_schema.GenerateJsonSchema.dataclass_schema", "name": "dataclass_schema", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "schema"], "arg_types": ["pydantic.json_schema.GenerateJsonSchema", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.DataclassSchema"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "dataclass_schema of GenerateJsonSchema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.json_schema.JsonSchemaValue"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "date_schema": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "schema"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.json_schema.GenerateJsonSchema.date_schema", "name": "date_schema", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "schema"], "arg_types": ["pydantic.json_schema.GenerateJsonSchema", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.DateSchema"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "date_schema of GenerateJsonSchema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.json_schema.JsonSchemaValue"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "datetime_schema": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "schema"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.json_schema.GenerateJsonSchema.datetime_schema", "name": "datetime_schema", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "schema"], "arg_types": ["pydantic.json_schema.GenerateJsonSchema", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.DatetimeSchema"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "datetime_schema of GenerateJsonSchema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.json_schema.JsonSchemaValue"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "decimal_schema": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "schema"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.json_schema.GenerateJsonSchema.decimal_schema", "name": "decimal_schema", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "schema"], "arg_types": ["pydantic.json_schema.GenerateJsonSchema", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.DecimalSchema"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "decimal_schema of GenerateJsonSchema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.json_schema.JsonSchemaValue"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "default_schema": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "schema"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.json_schema.GenerateJsonSchema.default_schema", "name": "default_schema", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "schema"], "arg_types": ["pydantic.json_schema.GenerateJsonSchema", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.WithDefaultSchema"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "default_schema of GenerateJsonSchema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.json_schema.JsonSchemaValue"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "definition_ref_schema": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "schema"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.json_schema.GenerateJsonSchema.definition_ref_schema", "name": "definition_ref_schema", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "schema"], "arg_types": ["pydantic.json_schema.GenerateJsonSchema", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.DefinitionReferenceSchema"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "definition_ref_schema of GenerateJsonSchema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.json_schema.JsonSchemaValue"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "definitions": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "pydantic.json_schema.GenerateJsonSchema.definitions", "name": "definitions", "type": {".class": "Instance", "args": ["pydantic.json_schema.DefsRef", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.json_schema.JsonSchemaValue"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "definitions_schema": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "schema"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.json_schema.GenerateJsonSchema.definitions_schema", "name": "definitions_schema", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "schema"], "arg_types": ["pydantic.json_schema.GenerateJsonSchema", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.DefinitionsSchema"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "definitions_schema of GenerateJsonSchema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.json_schema.JsonSchemaValue"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "defs_to_core_refs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "pydantic.json_schema.GenerateJsonSchema.defs_to_core_refs", "name": "defs_to_core_refs", "type": {".class": "Instance", "args": ["pydantic.json_schema.DefsRef", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.json_schema.CoreModeRef"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "dict_schema": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "schema"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.json_schema.GenerateJsonSchema.dict_schema", "name": "dict_schema", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "schema"], "arg_types": ["pydantic.json_schema.GenerateJsonSchema", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.DictSchema"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "dict_schema of GenerateJsonSchema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.json_schema.JsonSchemaValue"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "emit_warning": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "kind", "detail"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.json_schema.GenerateJsonSchema.emit_warning", "name": "emit_warning", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "kind", "detail"], "arg_types": ["pydantic.json_schema.GenerateJsonSchema", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.json_schema.JsonSchemaWarningKind"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "emit_warning of GenerateJsonSchema", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "encode_default": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "dft"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.json_schema.GenerateJsonSchema.encode_default", "name": "encode_default", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "dft"], "arg_types": ["pydantic.json_schema.GenerateJsonSchema", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "encode_default of GenerateJsonSchema", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "enum_schema": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "schema"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.json_schema.GenerateJsonSchema.enum_schema", "name": "enum_schema", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "schema"], "arg_types": ["pydantic.json_schema.GenerateJsonSchema", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.EnumSchema"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "enum_schema of GenerateJsonSchema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.json_schema.JsonSchemaValue"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "field_is_present": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "field"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.json_schema.GenerateJsonSchema.field_is_present", "name": "field_is_present", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "field"], "arg_types": ["pydantic.json_schema.GenerateJsonSchema", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic._internal._core_utils.CoreSchemaField"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "field_is_present of GenerateJsonSchema", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "field_is_required": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "field", "total"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.json_schema.GenerateJsonSchema.field_is_required", "name": "field_is_required", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "field", "total"], "arg_types": ["pydantic.json_schema.GenerateJsonSchema", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.ModelField"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.DataclassField"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.TypedDictField"}], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "field_is_required of GenerateJsonSchema", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "field_title_should_be_set": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "schema"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.json_schema.GenerateJsonSchema.field_title_should_be_set", "name": "field_title_should_be_set", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "schema"], "arg_types": ["pydantic.json_schema.GenerateJsonSchema", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic._internal._core_utils.CoreSchemaOrField"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "field_title_should_be_set of GenerateJsonSchema", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "float_schema": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "schema"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.json_schema.GenerateJsonSchema.float_schema", "name": "float_schema", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "schema"], "arg_types": ["pydantic.json_schema.GenerateJsonSchema", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.FloatSchema"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "float_schema of GenerateJsonSchema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.json_schema.JsonSchemaValue"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "frozenset_schema": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "schema"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.json_schema.GenerateJsonSchema.frozenset_schema", "name": "frozenset_schema", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "schema"], "arg_types": ["pydantic.json_schema.GenerateJsonSchema", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.FrozenSetSchema"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "frozenset_schema of GenerateJsonSchema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.json_schema.JsonSchemaValue"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "function_after_schema": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "schema"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.json_schema.GenerateJsonSchema.function_after_schema", "name": "function_after_schema", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "schema"], "arg_types": ["pydantic.json_schema.GenerateJsonSchema", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.AfterValidatorFunctionSchema"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "function_after_schema of GenerateJsonSchema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.json_schema.JsonSchemaValue"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "function_before_schema": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "schema"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.json_schema.GenerateJsonSchema.function_before_schema", "name": "function_before_schema", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "schema"], "arg_types": ["pydantic.json_schema.GenerateJsonSchema", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.BeforeValidatorFunctionSchema"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "function_before_schema of GenerateJsonSchema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.json_schema.JsonSchemaValue"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "function_plain_schema": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "schema"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.json_schema.GenerateJsonSchema.function_plain_schema", "name": "function_plain_schema", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "schema"], "arg_types": ["pydantic.json_schema.GenerateJsonSchema", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.PlainValidatorFunctionSchema"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "function_plain_schema of GenerateJsonSchema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.json_schema.JsonSchemaValue"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "function_wrap_schema": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "schema"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.json_schema.GenerateJsonSchema.function_wrap_schema", "name": "function_wrap_schema", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "schema"], "arg_types": ["pydantic.json_schema.GenerateJsonSchema", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.WrapValidatorFunctionSchema"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "function_wrap_schema of GenerateJsonSchema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.json_schema.JsonSchemaValue"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "generate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "schema", "mode"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.json_schema.GenerateJsonSchema.generate", "name": "generate", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "schema", "mode"], "arg_types": ["pydantic.json_schema.GenerateJsonSchema", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.json_schema.JsonSchemaMode"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "generate of GenerateJsonSchema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.json_schema.JsonSchemaValue"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "generate_definitions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "inputs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.json_schema.GenerateJsonSchema.generate_definitions", "name": "generate_definitions", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "inputs"], "arg_types": ["pydantic.json_schema.GenerateJsonSchema", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.json_schema.JsonSchemaKeyT", "id": -1, "name": "JsonSchemaKeyT", "namespace": "pydantic.json_schema.GenerateJsonSchema.generate_definitions", "upper_bound": "<PERSON>.<PERSON>", "values": [], "variance": 0}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.json_schema.JsonSchemaMode"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "generate_definitions of GenerateJsonSchema", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.json_schema.JsonSchemaKeyT", "id": -1, "name": "JsonSchemaKeyT", "namespace": "pydantic.json_schema.GenerateJsonSchema.generate_definitions", "upper_bound": "<PERSON>.<PERSON>", "values": [], "variance": 0}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.json_schema.JsonSchemaMode"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.json_schema.JsonSchemaValue"}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["pydantic.json_schema.DefsRef", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.json_schema.JsonSchemaValue"}], "extra_attrs": null, "type_ref": "builtins.dict"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.json_schema.JsonSchemaKeyT", "id": -1, "name": "JsonSchemaKeyT", "namespace": "pydantic.json_schema.GenerateJsonSchema.generate_definitions", "upper_bound": "<PERSON>.<PERSON>", "values": [], "variance": 0}]}}}, "generate_inner": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "schema"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.json_schema.GenerateJsonSchema.generate_inner", "name": "generate_inner", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "schema"], "arg_types": ["pydantic.json_schema.GenerateJsonSchema", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic._internal._core_utils.CoreSchemaOrField"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "generate_inner of GenerateJsonSchema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.json_schema.JsonSchemaValue"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "generator_schema": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "schema"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.json_schema.GenerateJsonSchema.generator_schema", "name": "generator_schema", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "schema"], "arg_types": ["pydantic.json_schema.GenerateJsonSchema", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.GeneratorSchema"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "generator_schema of GenerateJsonSchema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.json_schema.JsonSchemaValue"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_argument_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "argument"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.json_schema.GenerateJsonSchema.get_argument_name", "name": "get_argument_name", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "argument"], "arg_types": ["pydantic.json_schema.GenerateJsonSchema", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.ArgumentsParameter"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.ArgumentsV3Parameter"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_argument_name of GenerateJsonSchema", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_cache_defs_ref_schema": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "core_ref"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.json_schema.GenerateJsonSchema.get_cache_defs_ref_schema", "name": "get_cache_defs_ref_schema", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "core_ref"], "arg_types": ["pydantic.json_schema.GenerateJsonSchema", "pydantic.json_schema.CoreRef"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_cache_defs_ref_schema of GenerateJsonSchema", "ret_type": {".class": "TupleType", "implicit": false, "items": ["pydantic.json_schema.DefsRef", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.json_schema.JsonSchemaValue"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_default_value": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "schema"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.json_schema.GenerateJsonSchema.get_default_value", "name": "get_default_value", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "schema"], "arg_types": ["pydantic.json_schema.GenerateJsonSchema", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.WithDefaultSchema"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_default_value of GenerateJsonSchema", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_defs_ref": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "core_mode_ref"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.json_schema.GenerateJsonSchema.get_defs_ref", "name": "get_defs_ref", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "core_mode_ref"], "arg_types": ["pydantic.json_schema.GenerateJsonSchema", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.json_schema.CoreModeRef"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_defs_ref of GenerateJsonSchema", "ret_type": "pydantic.json_schema.DefsRef", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_flattened_anyof": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "schemas"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.json_schema.GenerateJsonSchema.get_flattened_anyof", "name": "get_flattened_anyof", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "schemas"], "arg_types": ["pydantic.json_schema.GenerateJsonSchema", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.json_schema.JsonSchemaValue"}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_flattened_anyof of GenerateJsonSchema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.json_schema.JsonSchemaValue"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_json_ref_counts": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "json_schema"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.json_schema.GenerateJsonSchema.get_json_ref_counts", "name": "get_json_ref_counts", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "json_schema"], "arg_types": ["pydantic.json_schema.GenerateJsonSchema", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.json_schema.JsonSchemaValue"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_json_ref_counts of GenerateJsonSchema", "ret_type": {".class": "Instance", "args": ["pydantic.json_schema.JsonRef", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_schema_from_definitions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "json_ref"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.json_schema.GenerateJsonSchema.get_schema_from_definitions", "name": "get_schema_from_definitions", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "json_ref"], "arg_types": ["pydantic.json_schema.GenerateJsonSchema", "pydantic.json_schema.JsonRef"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_schema_from_definitions of GenerateJsonSchema", "ret_type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.json_schema.JsonSchemaValue"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_title_from_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.json_schema.GenerateJsonSchema.get_title_from_name", "name": "get_title_from_name", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "name"], "arg_types": ["pydantic.json_schema.GenerateJsonSchema", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_title_from_name of GenerateJsonSchema", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "handle_invalid_for_json_schema": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "schema", "error_info"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.json_schema.GenerateJsonSchema.handle_invalid_for_json_schema", "name": "handle_invalid_for_json_schema", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "schema", "error_info"], "arg_types": ["pydantic.json_schema.GenerateJsonSchema", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic._internal._core_utils.CoreSchemaOrField"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "handle_invalid_for_json_schema of GenerateJsonSchema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.json_schema.JsonSchemaValue"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "handle_ref_overrides": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "json_schema"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.json_schema.GenerateJsonSchema.handle_ref_overrides", "name": "handle_ref_overrides", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "json_schema"], "arg_types": ["pydantic.json_schema.GenerateJsonSchema", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.json_schema.JsonSchemaValue"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "handle_ref_overrides of GenerateJsonSchema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.json_schema.JsonSchemaValue"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "ignored_warning_kinds": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "pydantic.json_schema.GenerateJsonSchema.ignored_warning_kinds", "name": "ignored_warning_kinds", "type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.json_schema.JsonSchemaWarningKind"}], "extra_attrs": null, "type_ref": "builtins.set"}}}, "int_schema": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "schema"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.json_schema.GenerateJsonSchema.int_schema", "name": "int_schema", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "schema"], "arg_types": ["pydantic.json_schema.GenerateJsonSchema", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.IntSchema"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "int_schema of GenerateJsonSchema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.json_schema.JsonSchemaValue"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "invalid_schema": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "schema"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.json_schema.GenerateJsonSchema.invalid_schema", "name": "invalid_schema", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "schema"], "arg_types": ["pydantic.json_schema.GenerateJsonSchema", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.InvalidSchema"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "invalid_schema of GenerateJsonSchema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.json_schema.JsonSchemaValue"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_instance_schema": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "schema"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.json_schema.GenerateJsonSchema.is_instance_schema", "name": "is_instance_schema", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "schema"], "arg_types": ["pydantic.json_schema.GenerateJsonSchema", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.IsInstanceSchema"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_instance_schema of GenerateJsonSchema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.json_schema.JsonSchemaValue"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_subclass_schema": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "schema"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.json_schema.GenerateJsonSchema.is_subclass_schema", "name": "is_subclass_schema", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "schema"], "arg_types": ["pydantic.json_schema.GenerateJsonSchema", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.IsSubclassSchema"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_subclass_schema of GenerateJsonSchema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.json_schema.JsonSchemaValue"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "json_or_python_schema": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "schema"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.json_schema.GenerateJsonSchema.json_or_python_schema", "name": "json_or_python_schema", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "schema"], "arg_types": ["pydantic.json_schema.GenerateJsonSchema", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.JsonOrPythonSchema"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "json_or_python_schema of GenerateJsonSchema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.json_schema.JsonSchemaValue"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "json_schema": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "schema"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.json_schema.GenerateJsonSchema.json_schema", "name": "json_schema", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "schema"], "arg_types": ["pydantic.json_schema.GenerateJsonSchema", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.JsonSchema"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "json_schema of GenerateJsonSchema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.json_schema.JsonSchemaValue"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "json_to_defs_refs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "pydantic.json_schema.GenerateJsonSchema.json_to_defs_refs", "name": "json_to_defs_refs", "type": {".class": "Instance", "args": ["pydantic.json_schema.JsonRef", "pydantic.json_schema.DefsRef"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "kw_arguments_schema": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "arguments", "var_kwargs_schema"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.json_schema.GenerateJsonSchema.kw_arguments_schema", "name": "kw_arguments_schema", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "arguments", "var_kwargs_schema"], "arg_types": ["pydantic.json_schema.GenerateJsonSchema", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.ArgumentsParameter"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "kw_arguments_schema of GenerateJsonSchema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.json_schema.JsonSchemaValue"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "lax_or_strict_schema": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "schema"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.json_schema.GenerateJsonSchema.lax_or_strict_schema", "name": "lax_or_strict_schema", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "schema"], "arg_types": ["pydantic.json_schema.GenerateJsonSchema", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.LaxOrStrictSchema"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "lax_or_strict_schema of GenerateJsonSchema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.json_schema.JsonSchemaValue"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "list_schema": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "schema"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.json_schema.GenerateJsonSchema.list_schema", "name": "list_schema", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "schema"], "arg_types": ["pydantic.json_schema.GenerateJsonSchema", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.ListSchema"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "list_schema of GenerateJsonSchema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.json_schema.JsonSchemaValue"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "literal_schema": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "schema"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.json_schema.GenerateJsonSchema.literal_schema", "name": "literal_schema", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "schema"], "arg_types": ["pydantic.json_schema.GenerateJsonSchema", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.LiteralSchema"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "literal_schema of GenerateJsonSchema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.json_schema.JsonSchemaValue"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "mode": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "pydantic.json_schema.GenerateJsonSchema.mode", "name": "mode", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pydantic.json_schema.GenerateJsonSchema"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "mode of GenerateJsonSchema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.json_schema.JsonSchemaMode"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "pydantic.json_schema.GenerateJsonSchema.mode", "name": "mode", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pydantic.json_schema.GenerateJsonSchema"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "mode of GenerateJsonSchema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.json_schema.JsonSchemaMode"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "model_field_schema": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "schema"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.json_schema.GenerateJsonSchema.model_field_schema", "name": "model_field_schema", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "schema"], "arg_types": ["pydantic.json_schema.GenerateJsonSchema", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.ModelField"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "model_field_schema of GenerateJsonSchema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.json_schema.JsonSchemaValue"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "model_fields_schema": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "schema"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.json_schema.GenerateJsonSchema.model_fields_schema", "name": "model_fields_schema", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "schema"], "arg_types": ["pydantic.json_schema.GenerateJsonSchema", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.ModelFieldsSchema"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "model_fields_schema of GenerateJsonSchema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.json_schema.JsonSchemaValue"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "model_schema": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "schema"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.json_schema.GenerateJsonSchema.model_schema", "name": "model_schema", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "schema"], "arg_types": ["pydantic.json_schema.GenerateJsonSchema", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.ModelSchema"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "model_schema of GenerateJsonSchema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.json_schema.JsonSchemaValue"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "multi_host_url_schema": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "schema"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.json_schema.GenerateJsonSchema.multi_host_url_schema", "name": "multi_host_url_schema", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "schema"], "arg_types": ["pydantic.json_schema.GenerateJsonSchema", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.MultiHostUrlSchema"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "multi_host_url_schema of GenerateJsonSchema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.json_schema.JsonSchemaValue"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "none_schema": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "schema"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.json_schema.GenerateJsonSchema.none_schema", "name": "none_schema", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "schema"], "arg_types": ["pydantic.json_schema.GenerateJsonSchema", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.NoneSchema"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "none_schema of GenerateJsonSchema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.json_schema.JsonSchemaValue"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "normalize_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.json_schema.GenerateJsonSchema.normalize_name", "name": "normalize_name", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "name"], "arg_types": ["pydantic.json_schema.GenerateJsonSchema", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "normalize_name of GenerateJsonSchema", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "nullable_schema": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "schema"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.json_schema.GenerateJsonSchema.nullable_schema", "name": "nullable_schema", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "schema"], "arg_types": ["pydantic.json_schema.GenerateJsonSchema", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.NullableSchema"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "nullable_schema of GenerateJsonSchema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.json_schema.JsonSchemaValue"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "p_arguments_schema": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "arguments", "var_args_schema"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.json_schema.GenerateJsonSchema.p_arguments_schema", "name": "p_arguments_schema", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "arguments", "var_args_schema"], "arg_types": ["pydantic.json_schema.GenerateJsonSchema", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.ArgumentsParameter"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "p_arguments_schema of GenerateJsonSchema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.json_schema.JsonSchemaValue"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "ref_template": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pydantic.json_schema.GenerateJsonSchema.ref_template", "name": "ref_template", "type": "builtins.str"}}, "render_warning_message": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "kind", "detail"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.json_schema.GenerateJsonSchema.render_warning_message", "name": "render_warning_message", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "kind", "detail"], "arg_types": ["pydantic.json_schema.GenerateJsonSchema", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.json_schema.JsonSchemaWarningKind"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "render_warning_message of GenerateJsonSchema", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "resolve_ref_schema": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "json_schema"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.json_schema.GenerateJsonSchema.resolve_ref_schema", "name": "resolve_ref_schema", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "json_schema"], "arg_types": ["pydantic.json_schema.GenerateJsonSchema", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.json_schema.JsonSchemaValue"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "resolve_ref_schema of GenerateJsonSchema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.json_schema.JsonSchemaValue"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "schema_dialect": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "pydantic.json_schema.GenerateJsonSchema.schema_dialect", "name": "schema_dialect", "type": "builtins.str"}}, "ser_schema": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "schema"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.json_schema.GenerateJsonSchema.ser_schema", "name": "ser_schema", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "schema"], "arg_types": ["pydantic.json_schema.GenerateJsonSchema", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.SerSchema"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.IncExSeqSerSchema"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.IncExDictSerSchema"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ser_schema of GenerateJsonSchema", "ret_type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.json_schema.JsonSchemaValue"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_schema": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "schema"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.json_schema.GenerateJsonSchema.set_schema", "name": "set_schema", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "schema"], "arg_types": ["pydantic.json_schema.GenerateJsonSchema", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.SetSchema"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_schema of GenerateJsonSchema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.json_schema.JsonSchemaValue"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "sort": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "value", "parent_key"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.json_schema.GenerateJsonSchema.sort", "name": "sort", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "value", "parent_key"], "arg_types": ["pydantic.json_schema.GenerateJsonSchema", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.json_schema.JsonSchemaValue"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "sort of GenerateJsonSchema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.json_schema.JsonSchemaValue"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "str_schema": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "schema"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.json_schema.GenerateJsonSchema.str_schema", "name": "str_schema", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "schema"], "arg_types": ["pydantic.json_schema.GenerateJsonSchema", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.StringSchema"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "str_schema of GenerateJsonSchema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.json_schema.JsonSchemaValue"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "tagged_union_schema": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "schema"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.json_schema.GenerateJsonSchema.tagged_union_schema", "name": "tagged_union_schema", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "schema"], "arg_types": ["pydantic.json_schema.GenerateJsonSchema", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.TaggedUnionSchema"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "tagged_union_schema of GenerateJsonSchema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.json_schema.JsonSchemaValue"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "time_schema": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "schema"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.json_schema.GenerateJsonSchema.time_schema", "name": "time_schema", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "schema"], "arg_types": ["pydantic.json_schema.GenerateJsonSchema", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.TimeSchema"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "time_schema of GenerateJsonSchema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.json_schema.JsonSchemaValue"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "timedelta_schema": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "schema"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.json_schema.GenerateJsonSchema.timedelta_schema", "name": "timedelta_schema", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "schema"], "arg_types": ["pydantic.json_schema.GenerateJsonSchema", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.TimedeltaSchema"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "timedelta_schema of GenerateJsonSchema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.json_schema.JsonSchemaValue"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "tuple_positional_schema": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "schema"], "dataclass_transform_spec": null, "deprecated": "function pydantic.json_schema.GenerateJsonSchema.tuple_positional_schema is deprecated: `tuple_positional_schema` is deprecated. Use `tuple_schema` instead.", "flags": ["is_final", "is_decorated"], "fullname": "pydantic.json_schema.GenerateJsonSchema.tuple_positional_schema", "name": "tuple_positional_schema", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "schema"], "arg_types": ["pydantic.json_schema.GenerateJsonSchema", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.TupleSchema"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "tuple_positional_schema of GenerateJsonSchema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.json_schema.JsonSchemaValue"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred"], "fullname": "pydantic.json_schema.GenerateJsonSchema.tuple_positional_schema", "name": "tuple_positional_schema", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "schema"], "arg_types": ["pydantic.json_schema.GenerateJsonSchema", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.TupleSchema"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "tuple_positional_schema of GenerateJsonSchema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.json_schema.JsonSchemaValue"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "tuple_schema": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "schema"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.json_schema.GenerateJsonSchema.tuple_schema", "name": "tuple_schema", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "schema"], "arg_types": ["pydantic.json_schema.GenerateJsonSchema", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.TupleSchema"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "tuple_schema of GenerateJsonSchema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.json_schema.JsonSchemaValue"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "tuple_variable_schema": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "schema"], "dataclass_transform_spec": null, "deprecated": "function pydantic.json_schema.GenerateJsonSchema.tuple_variable_schema is deprecated: `tuple_variable_schema` is deprecated. Use `tuple_schema` instead.", "flags": ["is_final", "is_decorated"], "fullname": "pydantic.json_schema.GenerateJsonSchema.tuple_variable_schema", "name": "tuple_variable_schema", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "schema"], "arg_types": ["pydantic.json_schema.GenerateJsonSchema", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.TupleSchema"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "tuple_variable_schema of GenerateJsonSchema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.json_schema.JsonSchemaValue"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred"], "fullname": "pydantic.json_schema.GenerateJsonSchema.tuple_variable_schema", "name": "tuple_variable_schema", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "schema"], "arg_types": ["pydantic.json_schema.GenerateJsonSchema", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.TupleSchema"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "tuple_variable_schema of GenerateJsonSchema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.json_schema.JsonSchemaValue"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "typed_dict_field_schema": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "schema"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.json_schema.GenerateJsonSchema.typed_dict_field_schema", "name": "typed_dict_field_schema", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "schema"], "arg_types": ["pydantic.json_schema.GenerateJsonSchema", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.TypedDictField"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "typed_dict_field_schema of GenerateJsonSchema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.json_schema.JsonSchemaValue"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "typed_dict_schema": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "schema"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.json_schema.GenerateJsonSchema.typed_dict_schema", "name": "typed_dict_schema", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "schema"], "arg_types": ["pydantic.json_schema.GenerateJsonSchema", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.TypedDictSchema"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "typed_dict_schema of GenerateJsonSchema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.json_schema.JsonSchemaValue"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "union_schema": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "schema"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.json_schema.GenerateJsonSchema.union_schema", "name": "union_schema", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "schema"], "arg_types": ["pydantic.json_schema.GenerateJsonSchema", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.UnionSchema"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "union_schema of GenerateJsonSchema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.json_schema.JsonSchemaValue"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "update_with_validations": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "json_schema", "core_schema", "mapping"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.json_schema.GenerateJsonSchema.update_with_validations", "name": "update_with_validations", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "json_schema", "core_schema", "mapping"], "arg_types": ["pydantic.json_schema.GenerateJsonSchema", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.json_schema.JsonSchemaValue"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "update_with_validations of GenerateJsonSchema", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "url_schema": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "schema"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.json_schema.GenerateJsonSchema.url_schema", "name": "url_schema", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "schema"], "arg_types": ["pydantic.json_schema.GenerateJsonSchema", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.UrlSchema"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "url_schema of GenerateJsonSchema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.json_schema.JsonSchemaValue"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "uuid_schema": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "schema"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.json_schema.GenerateJsonSchema.uuid_schema", "name": "uuid_schema", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "schema"], "arg_types": ["pydantic.json_schema.GenerateJsonSchema", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.UuidSchema"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "uuid_schema of GenerateJsonSchema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.json_schema.JsonSchemaValue"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.json_schema.GenerateJsonSchema.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.json_schema.GenerateJsonSchema", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "GetJsonSchemaFunction": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._schema_generation_shared.GetJsonSchemaFunction", "kind": "Gdef"}, "GetJsonSchemaHandler": {".class": "SymbolTableNode", "cross_ref": "pydantic.annotated_handlers.GetJsonSchemaHandler", "kind": "Gdef"}, "Hashable": {".class": "SymbolTableNode", "cross_ref": "<PERSON>.<PERSON>", "kind": "Gdef"}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef"}, "JsonDict": {".class": "SymbolTableNode", "cross_ref": "pydantic.config.JsonDict", "kind": "Gdef"}, "JsonRef": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.str"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic.json_schema.JsonRef", "name": "JsonRef", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_newtype"], "fullname": "pydantic.json_schema.JsonRef", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pydantic.json_schema", "mro": ["pydantic.json_schema.JsonRef", "builtins.str", "typing.Sequence", "typing.Reversible", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "item"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.json_schema.JsonRef.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "item"], "arg_types": ["pydantic.json_schema.JsonRef", "builtins.str"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of JsonRef", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "JsonSchemaKeyT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.json_schema.JsonSchemaKeyT", "name": "JsonSchemaKeyT", "upper_bound": "<PERSON>.<PERSON>", "values": [], "variance": 0}}, "JsonSchemaMode": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "pydantic.json_schema.JsonSchemaMode", "line": 79, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "validation"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "serialization"}], "uses_pep604_syntax": false}}}, "JsonSchemaValue": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "pydantic.json_schema.JsonSchemaValue", "line": 74, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "JsonSchemaWarningKind": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "pydantic.json_schema.JsonSchemaWarningKind", "line": 92, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "skipped-choice"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "non-serializable-default"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "skipped-discriminator"}], "uses_pep604_syntax": false}}}, "JsonValue": {".class": "SymbolTableNode", "cross_ref": "pydantic.config.JsonValue", "kind": "Gdef"}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing.Literal", "kind": "Gdef"}, "NewType": {".class": "SymbolTableNode", "cross_ref": "typing.NewType", "kind": "Gdef"}, "NoDefault": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pydantic.json_schema.NoDefault", "name": "No<PERSON><PERSON><PERSON>", "type": "builtins.object"}}, "Pattern": {".class": "SymbolTableNode", "cross_ref": "<PERSON><PERSON>", "kind": "Gdef"}, "PydanticDataclass": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._dataclasses.PydanticDataclass", "kind": "Gdef"}, "PydanticDeprecatedSince26": {".class": "SymbolTableNode", "cross_ref": "pydantic.warnings.PydanticDeprecatedSince26", "kind": "Gdef"}, "PydanticDeprecatedSince29": {".class": "SymbolTableNode", "cross_ref": "pydantic.warnings.PydanticDeprecatedSince29", "kind": "Gdef"}, "PydanticInvalidForJsonSchema": {".class": "SymbolTableNode", "cross_ref": "pydantic.errors.PydanticInvalidForJsonSchema", "kind": "Gdef"}, "PydanticJsonSchemaWarning": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.UserWarning"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic.json_schema.PydanticJsonSchemaWarning", "name": "PydanticJsonSchemaWarning", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pydantic.json_schema.PydanticJsonSchemaWarning", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pydantic.json_schema", "mro": ["pydantic.json_schema.PydanticJsonSchemaWarning", "builtins.UserWarning", "builtins.Warning", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.json_schema.PydanticJsonSchemaWarning.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.json_schema.PydanticJsonSchemaWarning", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "PydanticOmit": {".class": "SymbolTableNode", "cross_ref": "pydantic_core._pydantic_core.PydanticOmit", "kind": "Gdef"}, "PydanticSchemaGenerationError": {".class": "SymbolTableNode", "cross_ref": "pydantic.errors.PydanticSchemaGenerationError", "kind": "Gdef"}, "PydanticUserError": {".class": "SymbolTableNode", "cross_ref": "pydantic.errors.PydanticUserError", "kind": "Gdef"}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef"}, "SkipJsonSchema": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.json_schema.AnyType", "id": 1, "name": "AnyType", "namespace": "pydantic.json_schema.SkipJsonSchema", "upper_bound": "builtins.object", "values": [], "variance": 0}], "column": 4, "fullname": "pydantic.json_schema.SkipJsonSchema", "line": 2625, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.json_schema.AnyType", "id": 1, "name": "AnyType", "namespace": "pydantic.json_schema.SkipJsonSchema", "upper_bound": "builtins.object", "values": [], "variance": 0}}}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "TypeAlias": {".class": "SymbolTableNode", "cross_ref": "typing.TypeAlias", "kind": "Gdef"}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "WithJsonSchema": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic.json_schema.WithJsonSchema", "name": "WithJsonSchema", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pydantic.json_schema.WithJsonSchema", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 2501, "name": "json_schema", "type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.json_schema.JsonSchemaValue"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 2502, "name": "mode", "type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "validation"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "serialization"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}], "frozen": false}, "dataclass_tag": {}}, "module_name": "pydantic.json_schema", "mro": ["pydantic.json_schema.WithJsonSchema", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "pydantic.json_schema.WithJsonSchema.__dataclass_fields__", "name": "__dataclass_fields__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__get_pydantic_json_schema__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "core_schema", "handler"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.json_schema.WithJsonSchema.__get_pydantic_json_schema__", "name": "__get_pydantic_json_schema__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "core_schema", "handler"], "arg_types": ["pydantic.json_schema.WithJsonSchema", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, "pydantic.annotated_handlers.GetJsonSchemaHandler"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__get_pydantic_json_schema__ of WithJsonSchema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.json_schema.JsonSchemaValue"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__hash__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.json_schema.WithJsonSchema.__hash__", "name": "__hash__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pydantic.json_schema.WithJsonSchema"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__hash__ of WithJsonSchema", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "json_schema", "mode"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.json_schema.WithJsonSchema.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "json_schema", "mode"], "arg_types": ["pydantic.json_schema.WithJsonSchema", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.json_schema.JsonSchemaValue"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "validation"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "serialization"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of WithJsonSchema", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "pydantic.json_schema.WithJsonSchema.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "json_schema"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "mode"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5], "arg_names": ["json_schema", "mode"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "pydantic.json_schema.WithJsonSchema.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5], "arg_names": ["json_schema", "mode"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.json_schema.JsonSchemaValue"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "validation"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "serialization"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of WithJsonSchema", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "pydantic.json_schema.WithJsonSchema.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5], "arg_names": ["json_schema", "mode"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.json_schema.JsonSchemaValue"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "validation"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "serialization"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of WithJsonSchema", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "json_schema": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "pydantic.json_schema.WithJsonSchema.json_schema", "name": "json_schema", "type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.json_schema.JsonSchemaValue"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "mode": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "pydantic.json_schema.WithJsonSchema.mode", "name": "mode", "type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "validation"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "serialization"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.json_schema.WithJsonSchema.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.json_schema.WithJsonSchema", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_DefinitionsRemapping": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic.json_schema._DefinitionsRemapping", "name": "_DefinitionsRemapping", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pydantic.json_schema._DefinitionsRemapping", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 137, "name": "defs_remapping", "type": {".class": "Instance", "args": ["pydantic.json_schema.DefsRef", "pydantic.json_schema.DefsRef"], "extra_attrs": null, "type_ref": "builtins.dict"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 138, "name": "json_remapping", "type": {".class": "Instance", "args": ["pydantic.json_schema.JsonRef", "pydantic.json_schema.JsonRef"], "extra_attrs": null, "type_ref": "builtins.dict"}}], "frozen": false}, "dataclass_tag": {}}, "module_name": "pydantic.json_schema", "mro": ["pydantic.json_schema._DefinitionsRemapping", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "pydantic.json_schema._DefinitionsRemapping.__dataclass_fields__", "name": "__dataclass_fields__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "defs_remapping", "json_remapping"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.json_schema._DefinitionsRemapping.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "defs_remapping", "json_remapping"], "arg_types": ["pydantic.json_schema._DefinitionsRemapping", {".class": "Instance", "args": ["pydantic.json_schema.DefsRef", "pydantic.json_schema.DefsRef"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["pydantic.json_schema.JsonRef", "pydantic.json_schema.JsonRef"], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of _DefinitionsRemapping", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "pydantic.json_schema._DefinitionsRemapping.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "defs_remapping"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "json_remapping"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5], "arg_names": ["defs_remapping", "json_remapping"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "pydantic.json_schema._DefinitionsRemapping.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5], "arg_names": ["defs_remapping", "json_remapping"], "arg_types": [{".class": "Instance", "args": ["pydantic.json_schema.DefsRef", "pydantic.json_schema.DefsRef"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["pydantic.json_schema.JsonRef", "pydantic.json_schema.JsonRef"], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of _DefinitionsRemapping", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "pydantic.json_schema._DefinitionsRemapping.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5], "arg_names": ["defs_remapping", "json_remapping"], "arg_types": [{".class": "Instance", "args": ["pydantic.json_schema.DefsRef", "pydantic.json_schema.DefsRef"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["pydantic.json_schema.JsonRef", "pydantic.json_schema.JsonRef"], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of _DefinitionsRemapping", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "defs_remapping": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "pydantic.json_schema._DefinitionsRemapping.defs_remapping", "name": "defs_remapping", "type": {".class": "Instance", "args": ["pydantic.json_schema.DefsRef", "pydantic.json_schema.DefsRef"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "from_prioritized_choices": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["prioritized_choices", "defs_to_json", "definitions"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "pydantic.json_schema._DefinitionsRemapping.from_prioritized_choices", "name": "from_prioritized_choices", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["prioritized_choices", "defs_to_json", "definitions"], "arg_types": [{".class": "Instance", "args": ["pydantic.json_schema.DefsRef", {".class": "Instance", "args": ["pydantic.json_schema.DefsRef"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["pydantic.json_schema.DefsRef", "pydantic.json_schema.JsonRef"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["pydantic.json_schema.DefsRef", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.json_schema.JsonSchemaValue"}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_prioritized_choices of _DefinitionsRemapping", "ret_type": "pydantic.json_schema._DefinitionsRemapping", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "pydantic.json_schema._DefinitionsRemapping.from_prioritized_choices", "name": "from_prioritized_choices", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["prioritized_choices", "defs_to_json", "definitions"], "arg_types": [{".class": "Instance", "args": ["pydantic.json_schema.DefsRef", {".class": "Instance", "args": ["pydantic.json_schema.DefsRef"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["pydantic.json_schema.DefsRef", "pydantic.json_schema.JsonRef"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["pydantic.json_schema.DefsRef", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.json_schema.JsonSchemaValue"}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_prioritized_choices of _DefinitionsRemapping", "ret_type": "pydantic.json_schema._DefinitionsRemapping", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "json_remapping": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "pydantic.json_schema._DefinitionsRemapping.json_remapping", "name": "json_remapping", "type": {".class": "Instance", "args": ["pydantic.json_schema.JsonRef", "pydantic.json_schema.JsonRef"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "remap_defs_ref": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "ref"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.json_schema._DefinitionsRemapping.remap_defs_ref", "name": "remap_defs_ref", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "ref"], "arg_types": ["pydantic.json_schema._DefinitionsRemapping", "pydantic.json_schema.DefsRef"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "remap_defs_ref of _DefinitionsRemapping", "ret_type": "pydantic.json_schema.DefsRef", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "remap_json_ref": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "ref"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.json_schema._DefinitionsRemapping.remap_json_ref", "name": "remap_json_ref", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "ref"], "arg_types": ["pydantic.json_schema._DefinitionsRemapping", "pydantic.json_schema.JsonRef"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "remap_json_ref of _DefinitionsRemapping", "ret_type": "pydantic.json_schema.JsonRef", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "remap_json_schema": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "schema"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.json_schema._DefinitionsRemapping.remap_json_schema", "name": "remap_json_schema", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "schema"], "arg_types": ["pydantic.json_schema._DefinitionsRemapping", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "remap_json_schema of _DefinitionsRemapping", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.json_schema._DefinitionsRemapping.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.json_schema._DefinitionsRemapping", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_HashableJsonValue": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "pydantic.json_schema._HashableJsonValue", "line": 2467, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": ["builtins.int", "builtins.float", "builtins.str", "builtins.bool", {".class": "NoneType"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.json_schema._HashableJsonValue"}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.json_schema._HashableJsonValue"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "uses_pep604_syntax": false}}}, "_MODE_TITLE_MAPPING": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "pydantic.json_schema._MODE_TITLE_MAPPING", "name": "_MODE_TITLE_MAPPING", "type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.json_schema.JsonSchemaMode"}, "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic.json_schema.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic.json_schema.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic.json_schema.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic.json_schema.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic.json_schema.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic.json_schema.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "_config": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._config", "kind": "Gdef"}, "_core_metadata": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._core_metadata", "kind": "Gdef"}, "_core_utils": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._core_utils", "kind": "Gdef"}, "_decorators": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._decorators", "kind": "Gdef"}, "_deduplicate_schemas": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["schemas"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.json_schema._deduplicate_schemas", "name": "_deduplicate_schemas", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["schemas"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.config.JsonDict"}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_deduplicate_schemas", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.config.JsonDict"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_all_json_refs": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["item"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.json_schema._get_all_json_refs", "name": "_get_all_json_refs", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["item"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_all_json_refs", "ret_type": {".class": "Instance", "args": ["pydantic.json_schema.JsonRef"], "extra_attrs": null, "type_ref": "builtins.set"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_typed_dict_config": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["cls"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.json_schema._get_typed_dict_config", "name": "_get_typed_dict_config", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_typed_dict_config", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.config.ConfigDict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_internal_dataclass": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._internal_dataclass", "kind": "Gdef"}, "_make_json_hashable": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["value"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.json_schema._make_json_hashable", "name": "_make_json_hashable", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["value"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.config.JsonValue"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_make_json_hashable", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.json_schema._HashableJsonValue"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_mock_val_ser": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._mock_val_ser", "kind": "Gdef"}, "_schema_generation_shared": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._schema_generation_shared", "kind": "Gdef"}, "assert_never": {".class": "SymbolTableNode", "cross_ref": "typing.assert_never", "kind": "Gdef"}, "cast": {".class": "SymbolTableNode", "cross_ref": "typing.cast", "kind": "Gdef"}, "core_schema": {".class": "SymbolTableNode", "cross_ref": "pydantic_core.core_schema", "kind": "Gdef"}, "dataclasses": {".class": "SymbolTableNode", "cross_ref": "dataclasses", "kind": "Gdef"}, "deepcopy": {".class": "SymbolTableNode", "cross_ref": "copy.deepcopy", "kind": "Gdef"}, "defaultdict": {".class": "SymbolTableNode", "cross_ref": "collections.defaultdict", "kind": "Gdef"}, "deprecated": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.deprecated", "kind": "Gdef"}, "final": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.final", "kind": "Gdef"}, "get_literal_values": {".class": "SymbolTableNode", "cross_ref": "typing_inspection.introspection.get_literal_values", "kind": "Gdef"}, "inspect": {".class": "SymbolTableNode", "cross_ref": "inspect", "kind": "Gdef"}, "math": {".class": "SymbolTableNode", "cross_ref": "math", "kind": "Gdef"}, "model_json_schema": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["cls", "by_alias", "ref_template", "schema_generator", "mode"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.json_schema.model_json_schema", "name": "model_json_schema", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["cls", "by_alias", "ref_template", "schema_generator", "mode"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeType", "item": "pydantic.main.BaseModel"}, {".class": "TypeType", "item": "pydantic._internal._dataclasses.PydanticDataclass"}], "uses_pep604_syntax": true}, "builtins.bool", "builtins.str", {".class": "TypeType", "item": "pydantic.json_schema.GenerateJsonSchema"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.json_schema.JsonSchemaMode"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "model_json_schema", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "models_json_schema": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 5], "arg_names": ["models", "by_alias", "title", "description", "ref_template", "schema_generator"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.json_schema.models_json_schema", "name": "models_json_schema", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5], "arg_names": ["models", "by_alias", "title", "description", "ref_template", "schema_generator"], "arg_types": [{".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": [{".class": "TypeType", "item": "pydantic.main.BaseModel"}, {".class": "TypeType", "item": "pydantic._internal._dataclasses.PydanticDataclass"}], "uses_pep604_syntax": true}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.json_schema.JsonSchemaMode"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}, "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.str", {".class": "TypeType", "item": "pydantic.json_schema.GenerateJsonSchema"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "models_json_schema", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": [{".class": "TypeType", "item": "pydantic.main.BaseModel"}, {".class": "TypeType", "item": "pydantic._internal._dataclasses.PydanticDataclass"}], "uses_pep604_syntax": true}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.json_schema.JsonSchemaMode"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.json_schema.JsonSchemaValue"}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.json_schema.JsonSchemaValue"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef"}, "overload": {".class": "SymbolTableNode", "cross_ref": "typing.overload", "kind": "Gdef"}, "pydantic_core": {".class": "SymbolTableNode", "cross_ref": "pydantic_core", "kind": "Gdef"}, "re": {".class": "SymbolTableNode", "cross_ref": "re", "kind": "Gdef"}, "to_jsonable_python": {".class": "SymbolTableNode", "cross_ref": "pydantic_core._pydantic_core.to_jsonable_python", "kind": "Gdef"}, "warnings": {".class": "SymbolTableNode", "cross_ref": "warnings", "kind": "Gdef"}}, "path": "c:\\Python313\\Lib\\site-packages\\pydantic\\json_schema.py"}