{".class": "MypyFile", "_fullname": "numpy.polynomial.polynomial", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "ABCPolyBase": {".class": "SymbolTableNode", "cross_ref": "numpy.polynomial._polybase.ABCPolyBase", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Final": {".class": "SymbolTableNode", "cross_ref": "typing.Final", "kind": "Gdef", "module_hidden": true, "module_public": false}, "L": {".class": "SymbolTableNode", "cross_ref": "typing.Literal", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Polynomial": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "NoneType"}], "extra_attrs": null, "type_ref": "numpy.polynomial._polybase.ABCPolyBase"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy.polynomial.polynomial.Polynomial", "name": "Polynomial", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "numpy.polynomial.polynomial.Polynomial", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "numpy.polynomial.polynomial", "mro": ["numpy.polynomial.polynomial.Polynomial", "numpy.polynomial._polybase.ABCPolyBase", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial.polynomial.Polynomial.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "numpy.polynomial.polynomial.Polynomial", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_Array1": {".class": "SymbolTableNode", "cross_ref": "numpy.polynomial._polytypes._Array1", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_Array2": {".class": "SymbolTableNode", "cross_ref": "numpy.polynomial._polytypes._Array2", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_FuncBinOp": {".class": "SymbolTableNode", "cross_ref": "numpy.polynomial._polytypes._FuncBinOp", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_FuncCompanion": {".class": "SymbolTableNode", "cross_ref": "numpy.polynomial._polytypes._FuncCompanion", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_FuncDer": {".class": "SymbolTableNode", "cross_ref": "numpy.polynomial._polytypes._FuncDer", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_FuncFit": {".class": "SymbolTableNode", "cross_ref": "numpy.polynomial._polytypes._FuncFit", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_FuncFromRoots": {".class": "SymbolTableNode", "cross_ref": "numpy.polynomial._polytypes._FuncFromRoots", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_FuncInteg": {".class": "SymbolTableNode", "cross_ref": "numpy.polynomial._polytypes._FuncInteg", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_FuncLine": {".class": "SymbolTableNode", "cross_ref": "numpy.polynomial._polytypes._FuncLine", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_FuncPow": {".class": "SymbolTableNode", "cross_ref": "numpy.polynomial._polytypes._FuncPow", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_FuncRoots": {".class": "SymbolTableNode", "cross_ref": "numpy.polynomial._polytypes._FuncRoots", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_FuncUnOp": {".class": "SymbolTableNode", "cross_ref": "numpy.polynomial._polytypes._FuncUnOp", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_FuncVal": {".class": "SymbolTableNode", "cross_ref": "numpy.polynomial._polytypes._FuncVal", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_FuncVal2D": {".class": "SymbolTableNode", "cross_ref": "numpy.polynomial._polytypes._FuncVal2D", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_FuncVal3D": {".class": "SymbolTableNode", "cross_ref": "numpy.polynomial._polytypes._FuncVal3D", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_FuncValFromRoots": {".class": "SymbolTableNode", "cross_ref": "numpy.polynomial._polytypes._FuncValFromRoots", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_FuncVander": {".class": "SymbolTableNode", "cross_ref": "numpy.polynomial._polytypes._FuncVander", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_FuncVander2D": {".class": "SymbolTableNode", "cross_ref": "numpy.polynomial._polytypes._FuncVander2D", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_FuncVander3D": {".class": "SymbolTableNode", "cross_ref": "numpy.polynomial._polytypes._FuncVander3D", "kind": "Gdef", "module_hidden": true, "module_public": false}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "numpy.polynomial.polynomial.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.polynomial.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.polynomial.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.polynomial.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.polynomial.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.polynomial.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.polynomial.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "np": {".class": "SymbolTableNode", "cross_ref": "numpy", "kind": "Gdef", "module_hidden": true, "module_public": false}, "polyadd": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.polynomial.polyadd", "name": "polyadd", "type": {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "polyadd"}], "extra_attrs": null, "type_ref": "numpy.polynomial._polytypes._FuncBinOp"}}}, "polycompanion": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.polynomial.polycompanion", "name": "polycompanion", "type": {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "polycompanion"}], "extra_attrs": null, "type_ref": "numpy.polynomial._polytypes._FuncCompanion"}}}, "polyder": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.polynomial.polyder", "name": "polyder", "type": {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "polyder"}], "extra_attrs": null, "type_ref": "numpy.polynomial._polytypes._FuncDer"}}}, "polydiv": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.polynomial.polydiv", "name": "polydiv", "type": {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "polydiv"}], "extra_attrs": null, "type_ref": "numpy.polynomial._polytypes._FuncBinOp"}}}, "polydomain": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "numpy.polynomial.polynomial.polydomain", "name": "polydomain", "type": {".class": "TypeAliasType", "args": ["numpy.float64"], "type_ref": "numpy.polynomial._polytypes._Array2"}}}, "polyfit": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.polynomial.polyfit", "name": "polyfit", "type": {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "polyfit"}], "extra_attrs": null, "type_ref": "numpy.polynomial._polytypes._FuncFit"}}}, "polyfromroots": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.polynomial.polyfromroots", "name": "polyfromroots", "type": {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "polyfromroots"}], "extra_attrs": null, "type_ref": "numpy.polynomial._polytypes._FuncFromRoots"}}}, "polygrid2d": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.polynomial.polygrid2d", "name": "polygrid2d", "type": {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "polygrid2d"}], "extra_attrs": null, "type_ref": "numpy.polynomial._polytypes._FuncVal2D"}}}, "polygrid3d": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.polynomial.polygrid3d", "name": "polygrid3d", "type": {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "polygrid3d"}], "extra_attrs": null, "type_ref": "numpy.polynomial._polytypes._FuncVal3D"}}}, "polyint": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.polynomial.polyint", "name": "polyint", "type": {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "polyint"}], "extra_attrs": null, "type_ref": "numpy.polynomial._polytypes._FuncInteg"}}}, "polyline": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.polynomial.polyline", "name": "polyline", "type": {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "Polyline"}], "extra_attrs": null, "type_ref": "numpy.polynomial._polytypes._FuncLine"}}}, "polymul": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.polynomial.polymul", "name": "polymul", "type": {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "polymul"}], "extra_attrs": null, "type_ref": "numpy.polynomial._polytypes._FuncBinOp"}}}, "polymulx": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.polynomial.polymulx", "name": "polymulx", "type": {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "polymulx"}], "extra_attrs": null, "type_ref": "numpy.polynomial._polytypes._FuncUnOp"}}}, "polyone": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "numpy.polynomial.polynomial.polyone", "name": "polyone", "type": {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.int_"}], "type_ref": "numpy.polynomial._polytypes._Array1"}}}, "polypow": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.polynomial.polypow", "name": "polypow", "type": {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "polypow"}], "extra_attrs": null, "type_ref": "numpy.polynomial._polytypes._FuncPow"}}}, "polyroots": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.polynomial.polyroots", "name": "polyroots", "type": {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "polyroots"}], "extra_attrs": null, "type_ref": "numpy.polynomial._polytypes._FuncRoots"}}}, "polysub": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.polynomial.polysub", "name": "polysub", "type": {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "polysub"}], "extra_attrs": null, "type_ref": "numpy.polynomial._polytypes._FuncBinOp"}}}, "polytrim": {".class": "SymbolTableNode", "cross_ref": "numpy.polynomial.polyutils.trimcoef", "kind": "Gdef"}, "polyval": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.polynomial.polyval", "name": "polyval", "type": {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "polyval"}], "extra_attrs": null, "type_ref": "numpy.polynomial._polytypes._FuncVal"}}}, "polyval2d": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.polynomial.polyval2d", "name": "polyval2d", "type": {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "polyval2d"}], "extra_attrs": null, "type_ref": "numpy.polynomial._polytypes._FuncVal2D"}}}, "polyval3d": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.polynomial.polyval3d", "name": "polyval3d", "type": {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "polyval3d"}], "extra_attrs": null, "type_ref": "numpy.polynomial._polytypes._FuncVal3D"}}}, "polyvalfromroots": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.polynomial.polyvalfromroots", "name": "polyvalfromroots", "type": {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "polyvalfromroots"}], "extra_attrs": null, "type_ref": "numpy.polynomial._polytypes._FuncValFromRoots"}}}, "polyvander": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.polynomial.polyvander", "name": "polyvander", "type": {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "polyvander"}], "extra_attrs": null, "type_ref": "numpy.polynomial._polytypes._FuncVander"}}}, "polyvander2d": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.polynomial.polyvander2d", "name": "polyvander2d", "type": {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "polyvander2d"}], "extra_attrs": null, "type_ref": "numpy.polynomial._polytypes._FuncVander2D"}}}, "polyvander3d": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.polynomial.polyvander3d", "name": "polyvander3d", "type": {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "polyvander3d"}], "extra_attrs": null, "type_ref": "numpy.polynomial._polytypes._FuncVander3D"}}}, "polyx": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "numpy.polynomial.polynomial.polyx", "name": "polyx", "type": {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.int_"}], "type_ref": "numpy.polynomial._polytypes._Array2"}}}, "polyzero": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "numpy.polynomial.polynomial.polyzero", "name": "polyzero", "type": {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.int_"}], "type_ref": "numpy.polynomial._polytypes._Array1"}}}}, "path": "c:\\Python313\\Lib\\site-packages\\numpy\\polynomial\\polynomial.pyi"}