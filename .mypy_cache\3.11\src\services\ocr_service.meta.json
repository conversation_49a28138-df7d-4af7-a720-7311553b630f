{"data_mtime": 1750101514, "dep_lines": [17, 18, 9, 10, 11, 12, 13, 14, 15, 1, 1, 1, 1, 1, 22, 28, 34], "dep_prios": [5, 5, 10, 10, 10, 5, 5, 5, 5, 5, 30, 30, 30, 30, 10, 5, 10], "dependencies": ["src.utils.logger", "src.utils.config", "cv2", "numpy", "re", "typing", "dataclasses", "enum", "pathlib", "builtins", "_frozen_importlib", "_typeshed", "abc", "src.utils"], "hash": "b11692d091bd96ec078a3ccf7b0b774771d9ae77", "id": "src.services.ocr_service", "ignore_all": true, "interface_hash": "54a9eb4c088a07cc94afb682664501b363538c3d", "mtime": 1750101489, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "E:\\db\\0617\\src\\services\\ocr_service.py", "plugin_data": null, "size": 15378, "suppressed": ["paddleocr", "transformers", "easyocr"], "version_id": "1.15.0"}