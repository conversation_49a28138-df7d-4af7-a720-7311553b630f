{".class": "MypyFile", "_fullname": "numpy._typing", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ArrayLike": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._array_like.ArrayLike", "kind": "Gdef"}, "DTypeLike": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._dtype_like.DTypeLike", "kind": "Gdef"}, "NBitBase": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._nbit_base.NBitBase", "kind": "Gdef"}, "NDArray": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._array_like.NDArray", "kind": "Gdef"}, "_128Bit": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._nbit_base._128Bit", "kind": "Gdef"}, "_16Bit": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._nbit_base._16Bit", "kind": "Gdef"}, "_256Bit": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._nbit_base._256Bit", "kind": "Gdef"}, "_32Bit": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._nbit_base._32Bit", "kind": "Gdef"}, "_64Bit": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._nbit_base._64Bit", "kind": "Gdef"}, "_80Bit": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._nbit_base._80Bit", "kind": "Gdef"}, "_8Bit": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._nbit_base._8Bit", "kind": "Gdef"}, "_96Bit": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._nbit_base._96Bit", "kind": "Gdef"}, "_ArrayLike": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._array_like._ArrayLike", "kind": "Gdef"}, "_ArrayLikeAnyString_co": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._array_like._ArrayLikeAnyString_co", "kind": "Gdef"}, "_ArrayLikeBool_co": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._array_like._ArrayLikeBool_co", "kind": "Gdef"}, "_ArrayLikeBytes_co": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._array_like._ArrayLikeBytes_co", "kind": "Gdef"}, "_ArrayLikeComplex128_co": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._array_like._ArrayLikeComplex128_co", "kind": "Gdef"}, "_ArrayLikeComplex_co": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._array_like._ArrayLikeComplex_co", "kind": "Gdef"}, "_ArrayLikeDT64_co": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._array_like._ArrayLikeDT64_co", "kind": "Gdef"}, "_ArrayLikeFloat64_co": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._array_like._ArrayLikeFloat64_co", "kind": "Gdef"}, "_ArrayLikeFloat_co": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._array_like._ArrayLikeFloat_co", "kind": "Gdef"}, "_ArrayLikeInt": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._array_like._ArrayLikeInt", "kind": "Gdef"}, "_ArrayLikeInt_co": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._array_like._ArrayLikeInt_co", "kind": "Gdef"}, "_ArrayLikeNumber_co": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._array_like._ArrayLikeNumber_co", "kind": "Gdef"}, "_ArrayLikeObject_co": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._array_like._ArrayLikeObject_co", "kind": "Gdef"}, "_ArrayLikeStr_co": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._array_like._ArrayLikeStr_co", "kind": "Gdef"}, "_ArrayLikeString_co": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._array_like._ArrayLikeString_co", "kind": "Gdef"}, "_ArrayLikeTD64_co": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._array_like._ArrayLikeTD64_co", "kind": "Gdef"}, "_ArrayLikeUInt_co": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._array_like._ArrayLikeUInt_co", "kind": "Gdef"}, "_ArrayLikeUnknown": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._array_like._ArrayLikeUnknown", "kind": "Gdef"}, "_ArrayLikeVoid_co": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._array_like._ArrayLikeVoid_co", "kind": "Gdef"}, "_BoolCodes": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._char_codes._BoolCodes", "kind": "Gdef"}, "_BoolLike_co": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._scalars._BoolLike_co", "kind": "Gdef"}, "_ByteCodes": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._char_codes._ByteCodes", "kind": "Gdef"}, "_BytesCodes": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._char_codes._BytesCodes", "kind": "Gdef"}, "_CDoubleCodes": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._char_codes._CDoubleCodes", "kind": "Gdef"}, "_CLongDoubleCodes": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._char_codes._CLongDoubleCodes", "kind": "Gdef"}, "_CSingleCodes": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._char_codes._CSingleCodes", "kind": "Gdef"}, "_CharLike_co": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._scalars._CharLike_co", "kind": "Gdef"}, "_CharacterCodes": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._char_codes._CharacterCodes", "kind": "Gdef"}, "_Complex128Codes": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._char_codes._Complex128Codes", "kind": "Gdef"}, "_Complex64Codes": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._char_codes._Complex64Codes", "kind": "Gdef"}, "_ComplexFloatingCodes": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._char_codes._ComplexFloatingCodes", "kind": "Gdef"}, "_ComplexLike_co": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._scalars._ComplexLike_co", "kind": "Gdef"}, "_DT64Codes": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._char_codes._DT64Codes", "kind": "Gdef"}, "_DTypeLike": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._dtype_like._DTypeLike", "kind": "Gdef"}, "_DTypeLikeBool": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._dtype_like._DTypeLikeBool", "kind": "Gdef"}, "_DTypeLikeBytes": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._dtype_like._DTypeLikeBytes", "kind": "Gdef"}, "_DTypeLikeComplex": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._dtype_like._DTypeLikeComplex", "kind": "Gdef"}, "_DTypeLikeComplex_co": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._dtype_like._DTypeLikeComplex_co", "kind": "Gdef"}, "_DTypeLikeDT64": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._dtype_like._DTypeLikeDT64", "kind": "Gdef"}, "_DTypeLikeFloat": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._dtype_like._DTypeLikeFloat", "kind": "Gdef"}, "_DTypeLikeInt": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._dtype_like._DTypeLikeInt", "kind": "Gdef"}, "_DTypeLikeObject": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._dtype_like._DTypeLikeObject", "kind": "Gdef"}, "_DTypeLikeStr": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._dtype_like._DTypeLikeStr", "kind": "Gdef"}, "_DTypeLikeTD64": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._dtype_like._DTypeLikeTD64", "kind": "Gdef"}, "_DTypeLikeUInt": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._dtype_like._DTypeLikeUInt", "kind": "Gdef"}, "_DTypeLikeVoid": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._dtype_like._DTypeLikeVoid", "kind": "Gdef"}, "_DoubleCodes": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._char_codes._DoubleCodes", "kind": "Gdef"}, "_FiniteNestedSequence": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._array_like._FiniteNestedSequence", "kind": "Gdef"}, "_FlexibleCodes": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._char_codes._FlexibleCodes", "kind": "Gdef"}, "_Float16Codes": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._char_codes._Float16Codes", "kind": "Gdef"}, "_Float32Codes": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._char_codes._Float32Codes", "kind": "Gdef"}, "_Float64Codes": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._char_codes._Float64Codes", "kind": "Gdef"}, "_FloatLike_co": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._scalars._FloatLike_co", "kind": "Gdef"}, "_FloatingCodes": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._char_codes._FloatingCodes", "kind": "Gdef"}, "_GUFunc_Nin2_Nout1": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._ufunc._GUFunc_Nin2_Nout1", "kind": "Gdef"}, "_GenericCodes": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._char_codes._GenericCodes", "kind": "Gdef"}, "_HalfCodes": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._char_codes._HalfCodes", "kind": "Gdef"}, "_InexactCodes": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._char_codes._InexactCodes", "kind": "Gdef"}, "_Int16Codes": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._char_codes._Int16Codes", "kind": "Gdef"}, "_Int32Codes": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._char_codes._Int32Codes", "kind": "Gdef"}, "_Int64Codes": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._char_codes._Int64Codes", "kind": "Gdef"}, "_Int8Codes": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._char_codes._Int8Codes", "kind": "Gdef"}, "_IntCCodes": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._char_codes._IntCCodes", "kind": "Gdef"}, "_IntCodes": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._char_codes._IntCodes", "kind": "Gdef"}, "_IntLike_co": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._scalars._IntLike_co", "kind": "Gdef"}, "_IntPCodes": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._char_codes._IntPCodes", "kind": "Gdef"}, "_IntegerCodes": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._char_codes._IntegerCodes", "kind": "Gdef"}, "_LongCodes": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._char_codes._LongCodes", "kind": "Gdef"}, "_LongDoubleCodes": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._char_codes._LongDoubleCodes", "kind": "Gdef"}, "_LongLongCodes": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._char_codes._LongLongCodes", "kind": "Gdef"}, "_NBitByte": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._nbit._NBitByte", "kind": "Gdef"}, "_NBitDouble": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._nbit._NBitDouble", "kind": "Gdef"}, "_NBitHalf": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._nbit._NBitHalf", "kind": "Gdef"}, "_NBitInt": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._nbit._NBitInt", "kind": "Gdef"}, "_NBitIntC": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._nbit._NBitIntC", "kind": "Gdef"}, "_NBitIntP": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._nbit._NBitIntP", "kind": "Gdef"}, "_NBitLong": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._nbit._NBitLong", "kind": "Gdef"}, "_NBitLongDouble": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._nbit._NBitLongDouble", "kind": "Gdef"}, "_NBitLongLong": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._nbit._NBitLongLong", "kind": "Gdef"}, "_NBitShort": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._nbit._NBitShort", "kind": "Gdef"}, "_NBitSingle": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._nbit._NBitSingle", "kind": "Gdef"}, "_NestedSequence": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._nested_sequence._NestedSequence", "kind": "Gdef"}, "_NumberCodes": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._char_codes._NumberCodes", "kind": "Gdef"}, "_NumberLike_co": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._scalars._NumberLike_co", "kind": "Gdef"}, "_ObjectCodes": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._char_codes._ObjectCodes", "kind": "Gdef"}, "_ScalarLike_co": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._scalars._ScalarLike_co", "kind": "Gdef"}, "_Shape": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._shape._Shape", "kind": "Gdef"}, "_ShapeLike": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._shape._ShapeLike", "kind": "Gdef"}, "_ShortCodes": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._char_codes._ShortCodes", "kind": "Gdef"}, "_SignedIntegerCodes": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._char_codes._SignedIntegerCodes", "kind": "Gdef"}, "_SingleCodes": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._char_codes._SingleCodes", "kind": "Gdef"}, "_StrCodes": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._char_codes._StrCodes", "kind": "Gdef"}, "_StringCodes": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._char_codes._StringCodes", "kind": "Gdef"}, "_SupportsArray": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._array_like._SupportsArray", "kind": "Gdef"}, "_SupportsArrayFunc": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._array_like._SupportsArrayFunc", "kind": "Gdef"}, "_SupportsDType": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._dtype_like._SupportsDType", "kind": "Gdef"}, "_TD64Codes": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._char_codes._TD64Codes", "kind": "Gdef"}, "_TD64Like_co": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._scalars._TD64Like_co", "kind": "Gdef"}, "_UByteCodes": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._char_codes._UByteCodes", "kind": "Gdef"}, "_UFunc_Nin1_Nout1": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._ufunc._UFunc_Nin1_Nout1", "kind": "Gdef"}, "_UFunc_Nin1_Nout2": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._ufunc._UFunc_Nin1_Nout2", "kind": "Gdef"}, "_UFunc_Nin2_Nout1": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._ufunc._UFunc_Nin2_Nout1", "kind": "Gdef"}, "_UFunc_Nin2_Nout2": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._ufunc._UFunc_Nin2_Nout2", "kind": "Gdef"}, "_UInt16Codes": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._char_codes._UInt16Codes", "kind": "Gdef"}, "_UInt32Codes": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._char_codes._UInt32Codes", "kind": "Gdef"}, "_UInt64Codes": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._char_codes._UInt64Codes", "kind": "Gdef"}, "_UInt8Codes": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._char_codes._UInt8Codes", "kind": "Gdef"}, "_UIntCCodes": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._char_codes._UIntCCodes", "kind": "Gdef"}, "_UIntCodes": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._char_codes._UIntCodes", "kind": "Gdef"}, "_UIntLike_co": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._scalars._UIntLike_co", "kind": "Gdef"}, "_UIntPCodes": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._char_codes._UIntPCodes", "kind": "Gdef"}, "_ULongCodes": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._char_codes._ULongCodes", "kind": "Gdef"}, "_ULongLongCodes": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._char_codes._ULongLongCodes", "kind": "Gdef"}, "_UShortCodes": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._char_codes._UShortCodes", "kind": "Gdef"}, "_UnknownType": {".class": "SymbolTableNode", "cross_ref": "typing.Never", "kind": "Gdef"}, "_UnsignedIntegerCodes": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._char_codes._UnsignedIntegerCodes", "kind": "Gdef"}, "_VoidCodes": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._char_codes._VoidCodes", "kind": "Gdef"}, "_VoidDTypeLike": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._dtype_like._VoidDTypeLike", "kind": "Gdef"}, "_VoidLike_co": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._scalars._VoidLike_co", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy._typing.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy._typing.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy._typing.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy._typing.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy._typing.__package__", "name": "__package__", "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy._typing.__path__", "name": "__path__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy._typing.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}}, "path": "c:\\Python313\\Lib\\site-packages\\numpy\\_typing\\__init__.py"}