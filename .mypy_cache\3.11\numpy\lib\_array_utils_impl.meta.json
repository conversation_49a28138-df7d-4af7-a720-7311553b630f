{"data_mtime": 1750100846, "dep_lines": [4, 1, 3, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 30, 30], "dependencies": ["numpy.typing", "typing", "numpy", "builtins", "_frozen_importlib", "abc"], "hash": "fcf441156031dc9339ca0f33fbc6af5d0e54b9a9", "id": "numpy.lib._array_utils_impl", "ignore_all": true, "interface_hash": "1b3211f4a50c67fc3cecce510b9a9df1a0f9b22c", "mtime": 1748795461, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Python313\\Lib\\site-packages\\numpy\\lib\\_array_utils_impl.pyi", "plugin_data": null, "size": 818, "suppressed": [], "version_id": "1.15.0"}