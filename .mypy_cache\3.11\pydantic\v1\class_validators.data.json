{".class": "MypyFile", "_fullname": "pydantic.v1.class_validators", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "AnyCallable": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.typing.AnyCallable", "kind": "Gdef"}, "AnyClassMethod": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.typing.AnyClassMethod", "kind": "Gdef"}, "BaseConfig": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.config.BaseConfig", "kind": "Gdef"}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef"}, "ChainMap": {".class": "SymbolTableNode", "cross_ref": "collections.ChainMap", "kind": "Gdef"}, "ConfigError": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.errors.ConfigError", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "FunctionType": {".class": "SymbolTableNode", "cross_ref": "types.FunctionType", "kind": "Gdef"}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "ModelField": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.fields.ModelField", "kind": "Gdef"}, "ModelOrDc": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.types.ModelOrDc", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "ROOT_KEY": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.utils.ROOT_KEY", "kind": "Gdef"}, "ROOT_VALIDATOR_CONFIG_KEY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "pydantic.v1.class_validators.ROOT_VALIDATOR_CONFIG_KEY", "name": "ROOT_VALIDATOR_CONFIG_KEY", "type": "builtins.str"}}, "Set": {".class": "SymbolTableNode", "cross_ref": "typing.Set", "kind": "Gdef"}, "Signature": {".class": "SymbolTableNode", "cross_ref": "inspect.Signature", "kind": "Gdef"}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef"}, "Type": {".class": "SymbolTableNode", "cross_ref": "typing.Type", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "VALIDATOR_CONFIG_KEY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "pydantic.v1.class_validators.VALIDATOR_CONFIG_KEY", "name": "VALIDATOR_CONFIG_KEY", "type": "builtins.str"}}, "Validator": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic.v1.class_validators.Validator", "name": "Validator", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pydantic.v1.class_validators.Validator", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pydantic.v1.class_validators", "mro": ["pydantic.v1.class_validators.Validator", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1], "arg_names": ["self", "func", "pre", "each_item", "always", "check_fields", "skip_on_failure"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.v1.class_validators.Validator.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1], "arg_names": ["self", "func", "pre", "each_item", "always", "check_fields", "skip_on_failure"], "arg_types": ["pydantic.v1.class_validators.Validator", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.typing.AnyCallable"}, "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Validator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "pydantic.v1.class_validators.Validator.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "always": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pydantic.v1.class_validators.Validator.always", "name": "always", "type": "builtins.bool"}}, "check_fields": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pydantic.v1.class_validators.Validator.check_fields", "name": "check_fields", "type": "builtins.bool"}}, "each_item": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pydantic.v1.class_validators.Validator.each_item", "name": "each_item", "type": "builtins.bool"}}, "func": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pydantic.v1.class_validators.Validator.func", "name": "func", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pre": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pydantic.v1.class_validators.Validator.pre", "name": "pre", "type": "builtins.bool"}}, "skip_on_failure": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pydantic.v1.class_validators.Validator.skip_on_failure", "name": "skip_on_failure", "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.v1.class_validators.Validator.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.v1.class_validators.Validator", "values": [], "variance": 0}, "slots": ["always", "check_fields", "each_item", "func", "pre", "skip_on_failure"], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ValidatorCallable": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 4, "fullname": "pydantic.v1.class_validators.ValidatorCallable", "line": 43, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": [null, null, null, null, null], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.types.ModelOrDc"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}, "pydantic.v1.fields.ModelField", {".class": "TypeType", "item": "pydantic.v1.config.BaseConfig"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "ValidatorGroup": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic.v1.class_validators.ValidatorGroup", "name": "ValidatorGroup", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pydantic.v1.class_validators.ValidatorGroup", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pydantic.v1.class_validators", "mro": ["pydantic.v1.class_validators.ValidatorGroup", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "validators"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.v1.class_validators.ValidatorGroup.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "validators"], "arg_types": ["pydantic.v1.class_validators.ValidatorGroup", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.class_validators.ValidatorListDict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ValidatorGroup", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "check_for_unused": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.v1.class_validators.ValidatorGroup.check_for_unused", "name": "check_for_unused", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pydantic.v1.class_validators.ValidatorGroup"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "check_for_unused of ValidatorGroup", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_validators": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.v1.class_validators.ValidatorGroup.get_validators", "name": "get_validators", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "name"], "arg_types": ["pydantic.v1.class_validators.ValidatorGroup", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_validators of ValidatorGroup", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "pydantic.v1.class_validators.Validator"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "used_validators": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pydantic.v1.class_validators.ValidatorGroup.used_validators", "name": "used_validators", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}}}, "validators": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pydantic.v1.class_validators.ValidatorGroup.validators", "name": "validators", "type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.class_validators.ValidatorListDict"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.v1.class_validators.ValidatorGroup.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.v1.class_validators.ValidatorGroup", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ValidatorListDict": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 4, "fullname": "pydantic.v1.class_validators.ValidatorListDict", "line": 45, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["pydantic.v1.class_validators.Validator"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "ValidatorsList": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 4, "fullname": "pydantic.v1.class_validators.ValidatorsList", "line": 44, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.class_validators.ValidatorCallable"}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "_FUNCS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "pydantic.v1.class_validators._FUNCS", "name": "_FUNCS", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic.v1.class_validators.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic.v1.class_validators.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic.v1.class_validators.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic.v1.class_validators.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic.v1.class_validators.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic.v1.class_validators.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_generic_validator_basic": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["validator", "sig", "args"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.v1.class_validators._generic_validator_basic", "name": "_generic_validator_basic", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["validator", "sig", "args"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.typing.AnyCallable"}, "inspect.Signature", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_generic_validator_basic", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.class_validators.ValidatorCallable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_generic_validator_cls": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["validator", "sig", "args"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.v1.class_validators._generic_validator_cls", "name": "_generic_validator_cls", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["validator", "sig", "args"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.typing.AnyCallable"}, "inspect.Signature", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_generic_validator_cls", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.class_validators.ValidatorCallable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_prepare_validator": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["function", "allow_reuse"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.v1.class_validators._prepare_validator", "name": "_prepare_validator", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["function", "allow_reuse"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.typing.AnyCallable"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_prepare_validator", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.typing.AnyClassMethod"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "all_kwargs": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pydantic.v1.class_validators.all_kwargs", "name": "all_kwargs", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}}}, "chain": {".class": "SymbolTableNode", "cross_ref": "itertools.chain", "kind": "Gdef"}, "extract_root_validators": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["namespace"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.v1.class_validators.extract_root_validators", "name": "extract_root_validators", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["namespace"], "arg_types": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "extract_root_validators", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.typing.AnyCallable"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.bool", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.typing.AnyCallable"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "extract_validators": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["namespace"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.v1.class_validators.extract_validators", "name": "extract_validators", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["namespace"], "arg_types": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "extract_validators", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["pydantic.v1.class_validators.Validator"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "gather_all_validators": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["type_"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.v1.class_validators.gather_all_validators", "name": "gather_all_validators", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["type_"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.types.ModelOrDc"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "gather_all_validators", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.typing.AnyClassMethod"}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "in_ipython": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.utils.in_ipython", "kind": "Gdef"}, "inherit_validators": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["base_validators", "validators"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.v1.class_validators.inherit_validators", "name": "inherit_validators", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["base_validators", "validators"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.class_validators.ValidatorListDict"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.class_validators.ValidatorListDict"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "inherit_validators", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.class_validators.ValidatorListDict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "make_generic_validator": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["validator"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.v1.class_validators.make_generic_validator", "name": "make_generic_validator", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["validator"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.typing.AnyCallable"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "make_generic_validator", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.class_validators.ValidatorCallable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "overload": {".class": "SymbolTableNode", "cross_ref": "typing.overload", "kind": "Gdef"}, "partial": {".class": "SymbolTableNode", "cross_ref": "functools.partial", "kind": "Gdef"}, "partialmethod": {".class": "SymbolTableNode", "cross_ref": "functools.partialmethod", "kind": "Gdef"}, "prep_validators": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["v_funcs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.v1.class_validators.prep_validators", "name": "prep_validators", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["v_funcs"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.typing.AnyCallable"}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "prep_validators", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.class_validators.ValidatorsList"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "root_validator": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "pydantic.v1.class_validators.root_validator", "impl": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1, 5, 5, 5], "arg_names": ["_func", "pre", "allow_reuse", "skip_on_failure"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload"], "fullname": "pydantic.v1.class_validators.root_validator", "name": "root_validator", "type": {".class": "CallableType", "arg_kinds": [1, 5, 5, 5], "arg_names": ["_func", "pre", "allow_reuse", "skip_on_failure"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.typing.AnyCallable"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "root_validator", "ret_type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.typing.AnyClassMethod"}, {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.typing.AnyCallable"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.typing.AnyClassMethod"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["_func"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "pydantic.v1.class_validators.root_validator", "name": "root_validator", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["_func"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.typing.AnyCallable"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "root_validator", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.typing.AnyClassMethod"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "pydantic.v1.class_validators.root_validator", "name": "root_validator", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["_func"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.typing.AnyCallable"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "root_validator", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.typing.AnyClassMethod"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5], "arg_names": ["pre", "allow_reuse", "skip_on_failure"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "pydantic.v1.class_validators.root_validator", "name": "root_validator", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5], "arg_names": ["pre", "allow_reuse", "skip_on_failure"], "arg_types": ["builtins.bool", "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "root_validator", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.typing.AnyCallable"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.typing.AnyClassMethod"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "pydantic.v1.class_validators.root_validator", "name": "root_validator", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5], "arg_names": ["pre", "allow_reuse", "skip_on_failure"], "arg_types": ["builtins.bool", "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "root_validator", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.typing.AnyCallable"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.typing.AnyClassMethod"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": ["_func"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.typing.AnyCallable"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "root_validator", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.typing.AnyClassMethod"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [5, 5, 5], "arg_names": ["pre", "allow_reuse", "skip_on_failure"], "arg_types": ["builtins.bool", "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "root_validator", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.typing.AnyCallable"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.typing.AnyClassMethod"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "validator": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [2, 5, 5, 5, 5, 5, 5], "arg_names": ["fields", "pre", "each_item", "always", "check_fields", "whole", "allow_reuse"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.v1.class_validators.validator", "name": "validator", "type": {".class": "CallableType", "arg_kinds": [2, 5, 5, 5, 5, 5, 5], "arg_names": ["fields", "pre", "each_item", "always", "check_fields", "whole", "allow_reuse"], "arg_types": ["builtins.str", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "validator", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.typing.AnyCallable"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.v1.typing.AnyClassMethod"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "warnings": {".class": "SymbolTableNode", "cross_ref": "warnings", "kind": "Gdef"}, "wraps": {".class": "SymbolTableNode", "cross_ref": "functools.wraps", "kind": "Gdef"}}, "path": "c:\\Python313\\Lib\\site-packages\\pydantic\\v1\\class_validators.py"}