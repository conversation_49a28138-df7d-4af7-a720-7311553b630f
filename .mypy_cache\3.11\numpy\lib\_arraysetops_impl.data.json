{".class": "MypyFile", "_fullname": "numpy.lib._arraysetops_impl", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ArrayLike": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._array_like.ArrayLike", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Generic": {".class": "SymbolTableNode", "cross_ref": "typing.Generic", "kind": "Gdef", "module_hidden": true, "module_public": false}, "L": {".class": "SymbolTableNode", "cross_ref": "typing.Literal", "kind": "Gdef", "module_hidden": true, "module_public": false}, "NDArray": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._array_like.NDArray", "kind": "Gdef", "module_hidden": true, "module_public": false}, "NamedTuple": {".class": "SymbolTableNode", "cross_ref": "typing.NamedTuple", "kind": "Gdef", "module_hidden": true, "module_public": false}, "SupportsIndex": {".class": "SymbolTableNode", "cross_ref": "typing.SupportsIndex", "kind": "Gdef", "module_hidden": true, "module_public": false}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef", "module_hidden": true, "module_public": false}, "UniqueAllResult": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy.lib._arraysetops_impl.UniqueAllResult", "name": "UniqueAllResult", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._SCT", "id": 1, "name": "_SCT", "namespace": "numpy.lib._arraysetops_impl.UniqueAllResult", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}]}, "deletable_attributes": [], "deprecated": null, "flags": ["is_named_tuple"], "fullname": "numpy.lib._arraysetops_impl.UniqueAllResult", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"namedtuple": {"fields": ["values", "indices", "inverse_indices", "counts"]}}, "module_name": "numpy.lib._arraysetops_impl", "mro": ["numpy.lib._arraysetops_impl.UniqueAllResult", "builtins.tuple", "typing.Sequence", "typing.Reversible", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "_NT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl.UniqueAllResult._NT", "name": "_NT", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._SCT", "id": 1, "name": "_SCT", "namespace": "numpy.lib._arraysetops_impl.UniqueAllResult", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, "__annotations__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "numpy.lib._arraysetops_impl.UniqueAllResult.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "numpy.lib._arraysetops_impl.UniqueAllResult.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "numpy.lib._arraysetops_impl.UniqueAllResult.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "values"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "indices"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "inverse_indices"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "counts"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "__new__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["_cls", "values", "indices", "inverse_indices", "counts"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static"], "fullname": "numpy.lib._arraysetops_impl.UniqueAllResult.__new__", "name": "__new__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["_cls", "values", "indices", "inverse_indices", "counts"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl.UniqueAllResult._NT", "id": -1, "name": "_NT", "namespace": "numpy.lib._arraysetops_impl.UniqueAllResult.__new__", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._SCT", "id": 1, "name": "_SCT", "namespace": "numpy.lib._arraysetops_impl.UniqueAllResult", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._SCT", "id": 1, "name": "_SCT", "namespace": "numpy.lib._arraysetops_impl.UniqueAllResult", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__new__ of UniqueAllResult", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl.UniqueAllResult._NT", "id": -1, "name": "_NT", "namespace": "numpy.lib._arraysetops_impl.UniqueAllResult.__new__", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._SCT", "id": 1, "name": "_SCT", "namespace": "numpy.lib._arraysetops_impl.UniqueAllResult", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl.UniqueAllResult._NT", "id": -1, "name": "_NT", "namespace": "numpy.lib._arraysetops_impl.UniqueAllResult.__new__", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._SCT", "id": 1, "name": "_SCT", "namespace": "numpy.lib._arraysetops_impl.UniqueAllResult", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_asdict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["_self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.lib._arraysetops_impl.UniqueAllResult._asdict", "name": "_asdict", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["_self"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl.UniqueAllResult._NT", "id": -1, "name": "_NT", "namespace": "numpy.lib._arraysetops_impl.UniqueAllResult._asdict", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._SCT", "id": 1, "name": "_SCT", "namespace": "numpy.lib._arraysetops_impl.UniqueAllResult", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_asdict of UniqueAllResult", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl.UniqueAllResult._NT", "id": -1, "name": "_NT", "namespace": "numpy.lib._arraysetops_impl.UniqueAllResult._asdict", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._SCT", "id": 1, "name": "_SCT", "namespace": "numpy.lib._arraysetops_impl.UniqueAllResult", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_field_defaults": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "numpy.lib._arraysetops_impl.UniqueAllResult._field_defaults", "name": "_field_defaults", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_field_types": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "numpy.lib._arraysetops_impl.UniqueAllResult._field_types", "name": "_field_types", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "numpy.lib._arraysetops_impl.UniqueAllResult._fields", "name": "_fields", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_make": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "numpy.lib._arraysetops_impl.UniqueAllResult._make", "name": "_make", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl.UniqueAllResult._NT", "id": -1, "name": "_NT", "namespace": "numpy.lib._arraysetops_impl.UniqueAllResult._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._SCT", "id": 1, "name": "_SCT", "namespace": "numpy.lib._arraysetops_impl.UniqueAllResult", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_make of UniqueAllResult", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl.UniqueAllResult._NT", "id": -1, "name": "_NT", "namespace": "numpy.lib._arraysetops_impl.UniqueAllResult._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._SCT", "id": 1, "name": "_SCT", "namespace": "numpy.lib._arraysetops_impl.UniqueAllResult", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl.UniqueAllResult._NT", "id": -1, "name": "_NT", "namespace": "numpy.lib._arraysetops_impl.UniqueAllResult._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._SCT", "id": 1, "name": "_SCT", "namespace": "numpy.lib._arraysetops_impl.UniqueAllResult", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_classmethod", "is_ready"], "fullname": "numpy.lib._arraysetops_impl.UniqueAllResult._make", "name": "_make", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl.UniqueAllResult._NT", "id": -1, "name": "_NT", "namespace": "numpy.lib._arraysetops_impl.UniqueAllResult._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._SCT", "id": 1, "name": "_SCT", "namespace": "numpy.lib._arraysetops_impl.UniqueAllResult", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_make of UniqueAllResult", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl.UniqueAllResult._NT", "id": -1, "name": "_NT", "namespace": "numpy.lib._arraysetops_impl.UniqueAllResult._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._SCT", "id": 1, "name": "_SCT", "namespace": "numpy.lib._arraysetops_impl.UniqueAllResult", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl.UniqueAllResult._NT", "id": -1, "name": "_NT", "namespace": "numpy.lib._arraysetops_impl.UniqueAllResult._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._SCT", "id": 1, "name": "_SCT", "namespace": "numpy.lib._arraysetops_impl.UniqueAllResult", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}}, "plugin_generated": true}, "_replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5], "arg_names": ["_self", "values", "indices", "inverse_indices", "counts"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.lib._arraysetops_impl.UniqueAllResult._replace", "name": "_replace", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5], "arg_names": ["_self", "values", "indices", "inverse_indices", "counts"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl.UniqueAllResult._NT", "id": -1, "name": "_NT", "namespace": "numpy.lib._arraysetops_impl.UniqueAllResult._replace", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._SCT", "id": 1, "name": "_SCT", "namespace": "numpy.lib._arraysetops_impl.UniqueAllResult", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._SCT", "id": 1, "name": "_SCT", "namespace": "numpy.lib._arraysetops_impl.UniqueAllResult", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_replace of UniqueAllResult", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl.UniqueAllResult._NT", "id": -1, "name": "_NT", "namespace": "numpy.lib._arraysetops_impl.UniqueAllResult._replace", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._SCT", "id": 1, "name": "_SCT", "namespace": "numpy.lib._arraysetops_impl.UniqueAllResult", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl.UniqueAllResult._NT", "id": -1, "name": "_NT", "namespace": "numpy.lib._arraysetops_impl.UniqueAllResult._replace", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._SCT", "id": 1, "name": "_SCT", "namespace": "numpy.lib._arraysetops_impl.UniqueAllResult", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_source": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "numpy.lib._arraysetops_impl.UniqueAllResult._source", "name": "_source", "type": "builtins.str"}}, "counts": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "numpy.lib._arraysetops_impl.UniqueAllResult.counts", "name": "counts", "type": {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}}}, "counts-redefinition": {".class": "SymbolTableNode", "cross_ref": "numpy.lib._arraysetops_impl.UniqueAllResult.counts", "kind": "<PERSON><PERSON><PERSON>"}, "indices": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "numpy.lib._arraysetops_impl.UniqueAllResult.indices", "name": "indices", "type": {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}}}, "indices-redefinition": {".class": "SymbolTableNode", "cross_ref": "numpy.lib._arraysetops_impl.UniqueAllResult.indices", "kind": "<PERSON><PERSON><PERSON>"}, "inverse_indices": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "numpy.lib._arraysetops_impl.UniqueAllResult.inverse_indices", "name": "inverse_indices", "type": {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}}}, "inverse_indices-redefinition": {".class": "SymbolTableNode", "cross_ref": "numpy.lib._arraysetops_impl.UniqueAllResult.inverse_indices", "kind": "<PERSON><PERSON><PERSON>"}, "values": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "numpy.lib._arraysetops_impl.UniqueAllResult.values", "name": "values", "type": {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._SCT", "id": 1, "name": "_SCT", "namespace": "numpy.lib._arraysetops_impl.UniqueAllResult", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}}}, "values-redefinition": {".class": "SymbolTableNode", "cross_ref": "numpy.lib._arraysetops_impl.UniqueAllResult.values", "kind": "<PERSON><PERSON><PERSON>"}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl.UniqueAllResult.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._SCT", "id": 1, "name": "_SCT", "namespace": "numpy.lib._arraysetops_impl.UniqueAllResult", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._SCT", "id": 1, "name": "_SCT", "namespace": "numpy.lib._arraysetops_impl.UniqueAllResult", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy.lib._arraysetops_impl.UniqueAllResult"}}, "values": [], "variance": 0}, "slots": null, "tuple_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._SCT", "id": 1, "name": "_SCT", "namespace": "numpy.lib._arraysetops_impl.UniqueAllResult", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_vars": ["_SCT"], "typeddict_type": null}}, "UniqueCountsResult": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy.lib._arraysetops_impl.UniqueCountsResult", "name": "UniqueCountsResult", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._SCT", "id": 1, "name": "_SCT", "namespace": "numpy.lib._arraysetops_impl.UniqueCountsResult", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}]}, "deletable_attributes": [], "deprecated": null, "flags": ["is_named_tuple"], "fullname": "numpy.lib._arraysetops_impl.UniqueCountsResult", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"namedtuple": {"fields": ["values", "counts"]}}, "module_name": "numpy.lib._arraysetops_impl", "mro": ["numpy.lib._arraysetops_impl.UniqueCountsResult", "builtins.tuple", "typing.Sequence", "typing.Reversible", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "_NT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl.UniqueCountsResult._NT", "name": "_NT", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._SCT", "id": 1, "name": "_SCT", "namespace": "numpy.lib._arraysetops_impl.UniqueCountsResult", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, "__annotations__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "numpy.lib._arraysetops_impl.UniqueCountsResult.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "numpy.lib._arraysetops_impl.UniqueCountsResult.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "numpy.lib._arraysetops_impl.UniqueCountsResult.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "values"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "counts"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "__new__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["_cls", "values", "counts"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static"], "fullname": "numpy.lib._arraysetops_impl.UniqueCountsResult.__new__", "name": "__new__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["_cls", "values", "counts"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl.UniqueCountsResult._NT", "id": -1, "name": "_NT", "namespace": "numpy.lib._arraysetops_impl.UniqueCountsResult.__new__", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._SCT", "id": 1, "name": "_SCT", "namespace": "numpy.lib._arraysetops_impl.UniqueCountsResult", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._SCT", "id": 1, "name": "_SCT", "namespace": "numpy.lib._arraysetops_impl.UniqueCountsResult", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__new__ of UniqueCountsResult", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl.UniqueCountsResult._NT", "id": -1, "name": "_NT", "namespace": "numpy.lib._arraysetops_impl.UniqueCountsResult.__new__", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._SCT", "id": 1, "name": "_SCT", "namespace": "numpy.lib._arraysetops_impl.UniqueCountsResult", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl.UniqueCountsResult._NT", "id": -1, "name": "_NT", "namespace": "numpy.lib._arraysetops_impl.UniqueCountsResult.__new__", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._SCT", "id": 1, "name": "_SCT", "namespace": "numpy.lib._arraysetops_impl.UniqueCountsResult", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_asdict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["_self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.lib._arraysetops_impl.UniqueCountsResult._asdict", "name": "_asdict", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["_self"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl.UniqueCountsResult._NT", "id": -1, "name": "_NT", "namespace": "numpy.lib._arraysetops_impl.UniqueCountsResult._asdict", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._SCT", "id": 1, "name": "_SCT", "namespace": "numpy.lib._arraysetops_impl.UniqueCountsResult", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_asdict of UniqueCountsResult", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl.UniqueCountsResult._NT", "id": -1, "name": "_NT", "namespace": "numpy.lib._arraysetops_impl.UniqueCountsResult._asdict", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._SCT", "id": 1, "name": "_SCT", "namespace": "numpy.lib._arraysetops_impl.UniqueCountsResult", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_field_defaults": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "numpy.lib._arraysetops_impl.UniqueCountsResult._field_defaults", "name": "_field_defaults", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_field_types": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "numpy.lib._arraysetops_impl.UniqueCountsResult._field_types", "name": "_field_types", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "numpy.lib._arraysetops_impl.UniqueCountsResult._fields", "name": "_fields", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_make": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "numpy.lib._arraysetops_impl.UniqueCountsResult._make", "name": "_make", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl.UniqueCountsResult._NT", "id": -1, "name": "_NT", "namespace": "numpy.lib._arraysetops_impl.UniqueCountsResult._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._SCT", "id": 1, "name": "_SCT", "namespace": "numpy.lib._arraysetops_impl.UniqueCountsResult", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_make of UniqueCountsResult", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl.UniqueCountsResult._NT", "id": -1, "name": "_NT", "namespace": "numpy.lib._arraysetops_impl.UniqueCountsResult._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._SCT", "id": 1, "name": "_SCT", "namespace": "numpy.lib._arraysetops_impl.UniqueCountsResult", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl.UniqueCountsResult._NT", "id": -1, "name": "_NT", "namespace": "numpy.lib._arraysetops_impl.UniqueCountsResult._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._SCT", "id": 1, "name": "_SCT", "namespace": "numpy.lib._arraysetops_impl.UniqueCountsResult", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_classmethod", "is_ready"], "fullname": "numpy.lib._arraysetops_impl.UniqueCountsResult._make", "name": "_make", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl.UniqueCountsResult._NT", "id": -1, "name": "_NT", "namespace": "numpy.lib._arraysetops_impl.UniqueCountsResult._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._SCT", "id": 1, "name": "_SCT", "namespace": "numpy.lib._arraysetops_impl.UniqueCountsResult", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_make of UniqueCountsResult", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl.UniqueCountsResult._NT", "id": -1, "name": "_NT", "namespace": "numpy.lib._arraysetops_impl.UniqueCountsResult._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._SCT", "id": 1, "name": "_SCT", "namespace": "numpy.lib._arraysetops_impl.UniqueCountsResult", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl.UniqueCountsResult._NT", "id": -1, "name": "_NT", "namespace": "numpy.lib._arraysetops_impl.UniqueCountsResult._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._SCT", "id": 1, "name": "_SCT", "namespace": "numpy.lib._arraysetops_impl.UniqueCountsResult", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}}, "plugin_generated": true}, "_replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5], "arg_names": ["_self", "values", "counts"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.lib._arraysetops_impl.UniqueCountsResult._replace", "name": "_replace", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5], "arg_names": ["_self", "values", "counts"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl.UniqueCountsResult._NT", "id": -1, "name": "_NT", "namespace": "numpy.lib._arraysetops_impl.UniqueCountsResult._replace", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._SCT", "id": 1, "name": "_SCT", "namespace": "numpy.lib._arraysetops_impl.UniqueCountsResult", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._SCT", "id": 1, "name": "_SCT", "namespace": "numpy.lib._arraysetops_impl.UniqueCountsResult", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_replace of UniqueCountsResult", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl.UniqueCountsResult._NT", "id": -1, "name": "_NT", "namespace": "numpy.lib._arraysetops_impl.UniqueCountsResult._replace", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._SCT", "id": 1, "name": "_SCT", "namespace": "numpy.lib._arraysetops_impl.UniqueCountsResult", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl.UniqueCountsResult._NT", "id": -1, "name": "_NT", "namespace": "numpy.lib._arraysetops_impl.UniqueCountsResult._replace", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._SCT", "id": 1, "name": "_SCT", "namespace": "numpy.lib._arraysetops_impl.UniqueCountsResult", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_source": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "numpy.lib._arraysetops_impl.UniqueCountsResult._source", "name": "_source", "type": "builtins.str"}}, "counts": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "numpy.lib._arraysetops_impl.UniqueCountsResult.counts", "name": "counts", "type": {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}}}, "counts-redefinition": {".class": "SymbolTableNode", "cross_ref": "numpy.lib._arraysetops_impl.UniqueCountsResult.counts", "kind": "<PERSON><PERSON><PERSON>"}, "values": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "numpy.lib._arraysetops_impl.UniqueCountsResult.values", "name": "values", "type": {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._SCT", "id": 1, "name": "_SCT", "namespace": "numpy.lib._arraysetops_impl.UniqueCountsResult", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}}}, "values-redefinition": {".class": "SymbolTableNode", "cross_ref": "numpy.lib._arraysetops_impl.UniqueCountsResult.values", "kind": "<PERSON><PERSON><PERSON>"}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl.UniqueCountsResult.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._SCT", "id": 1, "name": "_SCT", "namespace": "numpy.lib._arraysetops_impl.UniqueCountsResult", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._SCT", "id": 1, "name": "_SCT", "namespace": "numpy.lib._arraysetops_impl.UniqueCountsResult", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy.lib._arraysetops_impl.UniqueCountsResult"}}, "values": [], "variance": 0}, "slots": null, "tuple_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._SCT", "id": 1, "name": "_SCT", "namespace": "numpy.lib._arraysetops_impl.UniqueCountsResult", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_vars": ["_SCT"], "typeddict_type": null}}, "UniqueInverseResult": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy.lib._arraysetops_impl.UniqueInverseResult", "name": "UniqueInverseResult", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._SCT", "id": 1, "name": "_SCT", "namespace": "numpy.lib._arraysetops_impl.UniqueInverseResult", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}]}, "deletable_attributes": [], "deprecated": null, "flags": ["is_named_tuple"], "fullname": "numpy.lib._arraysetops_impl.UniqueInverseResult", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"namedtuple": {"fields": ["values", "inverse_indices"]}}, "module_name": "numpy.lib._arraysetops_impl", "mro": ["numpy.lib._arraysetops_impl.UniqueInverseResult", "builtins.tuple", "typing.Sequence", "typing.Reversible", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "_NT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl.UniqueInverseResult._NT", "name": "_NT", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._SCT", "id": 1, "name": "_SCT", "namespace": "numpy.lib._arraysetops_impl.UniqueInverseResult", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, "__annotations__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "numpy.lib._arraysetops_impl.UniqueInverseResult.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "numpy.lib._arraysetops_impl.UniqueInverseResult.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "numpy.lib._arraysetops_impl.UniqueInverseResult.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "values"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "inverse_indices"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "__new__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["_cls", "values", "inverse_indices"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static"], "fullname": "numpy.lib._arraysetops_impl.UniqueInverseResult.__new__", "name": "__new__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["_cls", "values", "inverse_indices"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl.UniqueInverseResult._NT", "id": -1, "name": "_NT", "namespace": "numpy.lib._arraysetops_impl.UniqueInverseResult.__new__", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._SCT", "id": 1, "name": "_SCT", "namespace": "numpy.lib._arraysetops_impl.UniqueInverseResult", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._SCT", "id": 1, "name": "_SCT", "namespace": "numpy.lib._arraysetops_impl.UniqueInverseResult", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__new__ of UniqueInverseResult", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl.UniqueInverseResult._NT", "id": -1, "name": "_NT", "namespace": "numpy.lib._arraysetops_impl.UniqueInverseResult.__new__", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._SCT", "id": 1, "name": "_SCT", "namespace": "numpy.lib._arraysetops_impl.UniqueInverseResult", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl.UniqueInverseResult._NT", "id": -1, "name": "_NT", "namespace": "numpy.lib._arraysetops_impl.UniqueInverseResult.__new__", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._SCT", "id": 1, "name": "_SCT", "namespace": "numpy.lib._arraysetops_impl.UniqueInverseResult", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_asdict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["_self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.lib._arraysetops_impl.UniqueInverseResult._asdict", "name": "_asdict", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["_self"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl.UniqueInverseResult._NT", "id": -1, "name": "_NT", "namespace": "numpy.lib._arraysetops_impl.UniqueInverseResult._asdict", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._SCT", "id": 1, "name": "_SCT", "namespace": "numpy.lib._arraysetops_impl.UniqueInverseResult", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_asdict of UniqueInverseResult", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl.UniqueInverseResult._NT", "id": -1, "name": "_NT", "namespace": "numpy.lib._arraysetops_impl.UniqueInverseResult._asdict", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._SCT", "id": 1, "name": "_SCT", "namespace": "numpy.lib._arraysetops_impl.UniqueInverseResult", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_field_defaults": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "numpy.lib._arraysetops_impl.UniqueInverseResult._field_defaults", "name": "_field_defaults", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_field_types": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "numpy.lib._arraysetops_impl.UniqueInverseResult._field_types", "name": "_field_types", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "numpy.lib._arraysetops_impl.UniqueInverseResult._fields", "name": "_fields", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_make": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "numpy.lib._arraysetops_impl.UniqueInverseResult._make", "name": "_make", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl.UniqueInverseResult._NT", "id": -1, "name": "_NT", "namespace": "numpy.lib._arraysetops_impl.UniqueInverseResult._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._SCT", "id": 1, "name": "_SCT", "namespace": "numpy.lib._arraysetops_impl.UniqueInverseResult", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_make of UniqueInverseResult", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl.UniqueInverseResult._NT", "id": -1, "name": "_NT", "namespace": "numpy.lib._arraysetops_impl.UniqueInverseResult._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._SCT", "id": 1, "name": "_SCT", "namespace": "numpy.lib._arraysetops_impl.UniqueInverseResult", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl.UniqueInverseResult._NT", "id": -1, "name": "_NT", "namespace": "numpy.lib._arraysetops_impl.UniqueInverseResult._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._SCT", "id": 1, "name": "_SCT", "namespace": "numpy.lib._arraysetops_impl.UniqueInverseResult", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_classmethod", "is_ready"], "fullname": "numpy.lib._arraysetops_impl.UniqueInverseResult._make", "name": "_make", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl.UniqueInverseResult._NT", "id": -1, "name": "_NT", "namespace": "numpy.lib._arraysetops_impl.UniqueInverseResult._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._SCT", "id": 1, "name": "_SCT", "namespace": "numpy.lib._arraysetops_impl.UniqueInverseResult", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_make of UniqueInverseResult", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl.UniqueInverseResult._NT", "id": -1, "name": "_NT", "namespace": "numpy.lib._arraysetops_impl.UniqueInverseResult._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._SCT", "id": 1, "name": "_SCT", "namespace": "numpy.lib._arraysetops_impl.UniqueInverseResult", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl.UniqueInverseResult._NT", "id": -1, "name": "_NT", "namespace": "numpy.lib._arraysetops_impl.UniqueInverseResult._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._SCT", "id": 1, "name": "_SCT", "namespace": "numpy.lib._arraysetops_impl.UniqueInverseResult", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}}, "plugin_generated": true}, "_replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5], "arg_names": ["_self", "values", "inverse_indices"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.lib._arraysetops_impl.UniqueInverseResult._replace", "name": "_replace", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5], "arg_names": ["_self", "values", "inverse_indices"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl.UniqueInverseResult._NT", "id": -1, "name": "_NT", "namespace": "numpy.lib._arraysetops_impl.UniqueInverseResult._replace", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._SCT", "id": 1, "name": "_SCT", "namespace": "numpy.lib._arraysetops_impl.UniqueInverseResult", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._SCT", "id": 1, "name": "_SCT", "namespace": "numpy.lib._arraysetops_impl.UniqueInverseResult", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_replace of UniqueInverseResult", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl.UniqueInverseResult._NT", "id": -1, "name": "_NT", "namespace": "numpy.lib._arraysetops_impl.UniqueInverseResult._replace", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._SCT", "id": 1, "name": "_SCT", "namespace": "numpy.lib._arraysetops_impl.UniqueInverseResult", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl.UniqueInverseResult._NT", "id": -1, "name": "_NT", "namespace": "numpy.lib._arraysetops_impl.UniqueInverseResult._replace", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._SCT", "id": 1, "name": "_SCT", "namespace": "numpy.lib._arraysetops_impl.UniqueInverseResult", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_source": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "numpy.lib._arraysetops_impl.UniqueInverseResult._source", "name": "_source", "type": "builtins.str"}}, "inverse_indices": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "numpy.lib._arraysetops_impl.UniqueInverseResult.inverse_indices", "name": "inverse_indices", "type": {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}}}, "inverse_indices-redefinition": {".class": "SymbolTableNode", "cross_ref": "numpy.lib._arraysetops_impl.UniqueInverseResult.inverse_indices", "kind": "<PERSON><PERSON><PERSON>"}, "values": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "numpy.lib._arraysetops_impl.UniqueInverseResult.values", "name": "values", "type": {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._SCT", "id": 1, "name": "_SCT", "namespace": "numpy.lib._arraysetops_impl.UniqueInverseResult", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}}}, "values-redefinition": {".class": "SymbolTableNode", "cross_ref": "numpy.lib._arraysetops_impl.UniqueInverseResult.values", "kind": "<PERSON><PERSON><PERSON>"}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl.UniqueInverseResult.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._SCT", "id": 1, "name": "_SCT", "namespace": "numpy.lib._arraysetops_impl.UniqueInverseResult", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._SCT", "id": 1, "name": "_SCT", "namespace": "numpy.lib._arraysetops_impl.UniqueInverseResult", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy.lib._arraysetops_impl.UniqueInverseResult"}}, "values": [], "variance": 0}, "slots": null, "tuple_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._SCT", "id": 1, "name": "_SCT", "namespace": "numpy.lib._arraysetops_impl.UniqueInverseResult", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_vars": ["_SCT"], "typeddict_type": null}}, "_ArrayLike": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._array_like._ArrayLike", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_ArrayLikeBool_co": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._array_like._ArrayLikeBool_co", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_ArrayLikeDT64_co": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._array_like._ArrayLikeDT64_co", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_ArrayLikeNumber_co": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._array_like._ArrayLikeNumber_co", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_ArrayLikeObject_co": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._array_like._ArrayLikeObject_co", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_ArrayLikeTD64_co": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._array_like._ArrayLikeTD64_co", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_EitherSCT": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._EitherSCT", "name": "_EitherSCT", "upper_bound": "builtins.object", "values": [{".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "numpy.bool"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int8"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int32"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int64"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint8"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint32"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint64"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uintp"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.float16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.float32"}, "numpy.float64", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.complex64"}, "numpy.complex128", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.clongdouble"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["datetime.<PERSON><PERSON><PERSON>", "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.timedelta64"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["datetime.date", "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.datetime64"}, "numpy.bytes_", "numpy.str_", "numpy.void", "numpy.object_", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.integer"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.floating"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.complexfloating"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.character"}], "variance": 0}}, "_NumberType": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._NumberType", "name": "_NumberType", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": ["builtins.int", "builtins.float", "builtins.complex"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.number"}, "values": [], "variance": 0}}, "_SCT": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._SCT", "name": "_SCT", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "numpy.lib._arraysetops_impl.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.lib._arraysetops_impl.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.lib._arraysetops_impl.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.lib._arraysetops_impl.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.lib._arraysetops_impl.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.lib._arraysetops_impl.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.lib._arraysetops_impl.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "deprecated": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.deprecated", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ediff1d": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "numpy.lib._arraysetops_impl.ediff1d", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["ary", "to_end", "to_begin"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.lib._arraysetops_impl.ediff1d", "name": "ediff1d", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["ary", "to_end", "to_begin"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBool_co"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ediff1d", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.int8"}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.lib._arraysetops_impl.ediff1d", "name": "ediff1d", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["ary", "to_end", "to_begin"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBool_co"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ediff1d", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.int8"}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["ary", "to_end", "to_begin"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.lib._arraysetops_impl.ediff1d", "name": "ediff1d", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["ary", "to_end", "to_begin"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._NumberType", "id": -1, "name": "_NumberType", "namespace": "numpy.lib._arraysetops_impl.ediff1d#1", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": ["builtins.int", "builtins.float", "builtins.complex"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.number"}, "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like._ArrayLike"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ediff1d", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._NumberType", "id": -1, "name": "_NumberType", "namespace": "numpy.lib._arraysetops_impl.ediff1d#1", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": ["builtins.int", "builtins.float", "builtins.complex"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.number"}, "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._NumberType", "id": -1, "name": "_NumberType", "namespace": "numpy.lib._arraysetops_impl.ediff1d#1", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": ["builtins.int", "builtins.float", "builtins.complex"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.number"}, "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.lib._arraysetops_impl.ediff1d", "name": "ediff1d", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["ary", "to_end", "to_begin"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._NumberType", "id": -1, "name": "_NumberType", "namespace": "numpy.lib._arraysetops_impl.ediff1d#1", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": ["builtins.int", "builtins.float", "builtins.complex"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.number"}, "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like._ArrayLike"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ediff1d", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._NumberType", "id": -1, "name": "_NumberType", "namespace": "numpy.lib._arraysetops_impl.ediff1d#1", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": ["builtins.int", "builtins.float", "builtins.complex"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.number"}, "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._NumberType", "id": -1, "name": "_NumberType", "namespace": "numpy.lib._arraysetops_impl.ediff1d#1", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": ["builtins.int", "builtins.float", "builtins.complex"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.number"}, "values": [], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["ary", "to_end", "to_begin"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.lib._arraysetops_impl.ediff1d", "name": "ediff1d", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["ary", "to_end", "to_begin"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeNumber_co"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ediff1d", "ret_type": {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.lib._arraysetops_impl.ediff1d", "name": "ediff1d", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["ary", "to_end", "to_begin"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeNumber_co"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ediff1d", "ret_type": {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["ary", "to_end", "to_begin"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.lib._arraysetops_impl.ediff1d", "name": "ediff1d", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["ary", "to_end", "to_begin"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeDT64_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeTD64_co"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ediff1d", "ret_type": {".class": "TypeAliasType", "args": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["datetime.<PERSON><PERSON><PERSON>", "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.timedelta64"}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.lib._arraysetops_impl.ediff1d", "name": "ediff1d", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["ary", "to_end", "to_begin"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeDT64_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeTD64_co"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ediff1d", "ret_type": {".class": "TypeAliasType", "args": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["datetime.<PERSON><PERSON><PERSON>", "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.timedelta64"}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["ary", "to_end", "to_begin"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.lib._arraysetops_impl.ediff1d", "name": "ediff1d", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["ary", "to_end", "to_begin"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeObject_co"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ediff1d", "ret_type": {".class": "TypeAliasType", "args": ["numpy.object_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.lib._arraysetops_impl.ediff1d", "name": "ediff1d", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["ary", "to_end", "to_begin"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeObject_co"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ediff1d", "ret_type": {".class": "TypeAliasType", "args": ["numpy.object_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["ary", "to_end", "to_begin"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBool_co"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ediff1d", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.int8"}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["ary", "to_end", "to_begin"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._NumberType", "id": -1, "name": "_NumberType", "namespace": "numpy.lib._arraysetops_impl.ediff1d#1", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": ["builtins.int", "builtins.float", "builtins.complex"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.number"}, "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like._ArrayLike"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ediff1d", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._NumberType", "id": -1, "name": "_NumberType", "namespace": "numpy.lib._arraysetops_impl.ediff1d#1", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": ["builtins.int", "builtins.float", "builtins.complex"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.number"}, "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._NumberType", "id": -1, "name": "_NumberType", "namespace": "numpy.lib._arraysetops_impl.ediff1d#1", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": ["builtins.int", "builtins.float", "builtins.complex"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.number"}, "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["ary", "to_end", "to_begin"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeNumber_co"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ediff1d", "ret_type": {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["ary", "to_end", "to_begin"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeDT64_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeTD64_co"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ediff1d", "ret_type": {".class": "TypeAliasType", "args": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["datetime.<PERSON><PERSON><PERSON>", "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.timedelta64"}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["ary", "to_end", "to_begin"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeObject_co"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ediff1d", "ret_type": {".class": "TypeAliasType", "args": ["numpy.object_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "generic": {".class": "SymbolTableNode", "cross_ref": "numpy.generic", "kind": "Gdef", "module_hidden": true, "module_public": false}, "in1d": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 5], "arg_names": ["element", "test_elements", "assume_unique", "invert", "kind"], "dataclass_transform_spec": null, "deprecated": "function numpy.lib._arraysetops_impl.in1d is deprecated: Use 'isin' instead", "flags": ["is_decorated"], "fullname": "numpy.lib._arraysetops_impl.in1d", "name": "in1d", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 5], "arg_names": ["element", "test_elements", "assume_unique", "invert", "kind"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, "builtins.bool", "builtins.bool", {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.str"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "in1d", "ret_type": {".class": "TypeAliasType", "args": [{".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "numpy.bool"}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.lib._arraysetops_impl.in1d", "name": "in1d", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 5], "arg_names": ["element", "test_elements", "assume_unique", "invert", "kind"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, "builtins.bool", "builtins.bool", {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.str"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "in1d", "ret_type": {".class": "TypeAliasType", "args": [{".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "numpy.bool"}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "int8": {".class": "SymbolTableNode", "cross_ref": "numpy.int8", "kind": "Gdef", "module_hidden": true, "module_public": false}, "intersect1d": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "numpy.lib._arraysetops_impl.intersect1d", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["ar1", "ar2", "assume_unique", "return_indices"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.lib._arraysetops_impl.intersect1d", "name": "intersect1d", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["ar1", "ar2", "assume_unique", "return_indices"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._EitherSCT", "id": -1, "name": "_EitherSCT", "namespace": "numpy.lib._arraysetops_impl.intersect1d#0", "upper_bound": "builtins.object", "values": [{".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "numpy.bool"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int8"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int32"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int64"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint8"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint32"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint64"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uintp"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.float16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.float32"}, "numpy.float64", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.complex64"}, "numpy.complex128", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.clongdouble"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["datetime.<PERSON><PERSON><PERSON>", "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.timedelta64"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["datetime.date", "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.datetime64"}, "numpy.bytes_", "numpy.str_", "numpy.void", "numpy.object_", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.integer"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.floating"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.complexfloating"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.character"}], "variance": 0}], "type_ref": "numpy._typing._array_like._ArrayLike"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._EitherSCT", "id": -1, "name": "_EitherSCT", "namespace": "numpy.lib._arraysetops_impl.intersect1d#0", "upper_bound": "builtins.object", "values": [{".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "numpy.bool"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int8"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int32"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int64"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint8"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint32"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint64"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uintp"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.float16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.float32"}, "numpy.float64", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.complex64"}, "numpy.complex128", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.clongdouble"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["datetime.<PERSON><PERSON><PERSON>", "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.timedelta64"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["datetime.date", "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.datetime64"}, "numpy.bytes_", "numpy.str_", "numpy.void", "numpy.object_", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.integer"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.floating"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.complexfloating"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.character"}], "variance": 0}], "type_ref": "numpy._typing._array_like._ArrayLike"}, "builtins.bool", {".class": "LiteralType", "fallback": "builtins.bool", "value": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "intersect1d", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._EitherSCT", "id": -1, "name": "_EitherSCT", "namespace": "numpy.lib._arraysetops_impl.intersect1d#0", "upper_bound": "builtins.object", "values": [{".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "numpy.bool"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int8"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int32"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int64"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint8"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint32"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint64"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uintp"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.float16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.float32"}, "numpy.float64", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.complex64"}, "numpy.complex128", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.clongdouble"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["datetime.<PERSON><PERSON><PERSON>", "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.timedelta64"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["datetime.date", "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.datetime64"}, "numpy.bytes_", "numpy.str_", "numpy.void", "numpy.object_", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.integer"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.floating"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.complexfloating"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.character"}], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._EitherSCT", "id": -1, "name": "_EitherSCT", "namespace": "numpy.lib._arraysetops_impl.intersect1d#0", "upper_bound": "builtins.object", "values": [{".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "numpy.bool"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int8"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int32"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int64"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint8"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint32"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint64"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uintp"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.float16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.float32"}, "numpy.float64", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.complex64"}, "numpy.complex128", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.clongdouble"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["datetime.<PERSON><PERSON><PERSON>", "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.timedelta64"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["datetime.date", "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.datetime64"}, "numpy.bytes_", "numpy.str_", "numpy.void", "numpy.object_", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.integer"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.floating"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.complexfloating"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.character"}], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.lib._arraysetops_impl.intersect1d", "name": "intersect1d", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["ar1", "ar2", "assume_unique", "return_indices"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._EitherSCT", "id": -1, "name": "_EitherSCT", "namespace": "numpy.lib._arraysetops_impl.intersect1d#0", "upper_bound": "builtins.object", "values": [{".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "numpy.bool"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int8"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int32"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int64"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint8"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint32"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint64"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uintp"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.float16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.float32"}, "numpy.float64", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.complex64"}, "numpy.complex128", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.clongdouble"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["datetime.<PERSON><PERSON><PERSON>", "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.timedelta64"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["datetime.date", "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.datetime64"}, "numpy.bytes_", "numpy.str_", "numpy.void", "numpy.object_", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.integer"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.floating"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.complexfloating"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.character"}], "variance": 0}], "type_ref": "numpy._typing._array_like._ArrayLike"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._EitherSCT", "id": -1, "name": "_EitherSCT", "namespace": "numpy.lib._arraysetops_impl.intersect1d#0", "upper_bound": "builtins.object", "values": [{".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "numpy.bool"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int8"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int32"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int64"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint8"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint32"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint64"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uintp"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.float16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.float32"}, "numpy.float64", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.complex64"}, "numpy.complex128", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.clongdouble"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["datetime.<PERSON><PERSON><PERSON>", "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.timedelta64"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["datetime.date", "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.datetime64"}, "numpy.bytes_", "numpy.str_", "numpy.void", "numpy.object_", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.integer"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.floating"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.complexfloating"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.character"}], "variance": 0}], "type_ref": "numpy._typing._array_like._ArrayLike"}, "builtins.bool", {".class": "LiteralType", "fallback": "builtins.bool", "value": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "intersect1d", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._EitherSCT", "id": -1, "name": "_EitherSCT", "namespace": "numpy.lib._arraysetops_impl.intersect1d#0", "upper_bound": "builtins.object", "values": [{".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "numpy.bool"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int8"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int32"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int64"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint8"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint32"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint64"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uintp"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.float16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.float32"}, "numpy.float64", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.complex64"}, "numpy.complex128", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.clongdouble"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["datetime.<PERSON><PERSON><PERSON>", "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.timedelta64"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["datetime.date", "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.datetime64"}, "numpy.bytes_", "numpy.str_", "numpy.void", "numpy.object_", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.integer"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.floating"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.complexfloating"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.character"}], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._EitherSCT", "id": -1, "name": "_EitherSCT", "namespace": "numpy.lib._arraysetops_impl.intersect1d#0", "upper_bound": "builtins.object", "values": [{".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "numpy.bool"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int8"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int32"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int64"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint8"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint32"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint64"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uintp"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.float16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.float32"}, "numpy.float64", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.complex64"}, "numpy.complex128", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.clongdouble"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["datetime.<PERSON><PERSON><PERSON>", "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.timedelta64"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["datetime.date", "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.datetime64"}, "numpy.bytes_", "numpy.str_", "numpy.void", "numpy.object_", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.integer"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.floating"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.complexfloating"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.character"}], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["ar1", "ar2", "assume_unique", "return_indices"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.lib._arraysetops_impl.intersect1d", "name": "intersect1d", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["ar1", "ar2", "assume_unique", "return_indices"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, "builtins.bool", {".class": "LiteralType", "fallback": "builtins.bool", "value": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "intersect1d", "ret_type": {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.lib._arraysetops_impl.intersect1d", "name": "intersect1d", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["ar1", "ar2", "assume_unique", "return_indices"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, "builtins.bool", {".class": "LiteralType", "fallback": "builtins.bool", "value": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "intersect1d", "ret_type": {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["ar1", "ar2", "assume_unique", "return_indices"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.lib._arraysetops_impl.intersect1d", "name": "intersect1d", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["ar1", "ar2", "assume_unique", "return_indices"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._EitherSCT", "id": -1, "name": "_EitherSCT", "namespace": "numpy.lib._arraysetops_impl.intersect1d#2", "upper_bound": "builtins.object", "values": [{".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "numpy.bool"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int8"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int32"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int64"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint8"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint32"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint64"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uintp"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.float16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.float32"}, "numpy.float64", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.complex64"}, "numpy.complex128", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.clongdouble"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["datetime.<PERSON><PERSON><PERSON>", "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.timedelta64"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["datetime.date", "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.datetime64"}, "numpy.bytes_", "numpy.str_", "numpy.void", "numpy.object_", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.integer"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.floating"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.complexfloating"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.character"}], "variance": 0}], "type_ref": "numpy._typing._array_like._ArrayLike"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._EitherSCT", "id": -1, "name": "_EitherSCT", "namespace": "numpy.lib._arraysetops_impl.intersect1d#2", "upper_bound": "builtins.object", "values": [{".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "numpy.bool"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int8"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int32"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int64"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint8"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint32"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint64"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uintp"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.float16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.float32"}, "numpy.float64", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.complex64"}, "numpy.complex128", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.clongdouble"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["datetime.<PERSON><PERSON><PERSON>", "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.timedelta64"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["datetime.date", "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.datetime64"}, "numpy.bytes_", "numpy.str_", "numpy.void", "numpy.object_", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.integer"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.floating"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.complexfloating"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.character"}], "variance": 0}], "type_ref": "numpy._typing._array_like._ArrayLike"}, "builtins.bool", {".class": "LiteralType", "fallback": "builtins.bool", "value": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "intersect1d", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._EitherSCT", "id": -1, "name": "_EitherSCT", "namespace": "numpy.lib._arraysetops_impl.intersect1d#2", "upper_bound": "builtins.object", "values": [{".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "numpy.bool"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int8"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int32"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int64"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint8"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint32"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint64"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uintp"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.float16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.float32"}, "numpy.float64", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.complex64"}, "numpy.complex128", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.clongdouble"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["datetime.<PERSON><PERSON><PERSON>", "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.timedelta64"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["datetime.date", "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.datetime64"}, "numpy.bytes_", "numpy.str_", "numpy.void", "numpy.object_", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.integer"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.floating"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.complexfloating"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.character"}], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._EitherSCT", "id": -1, "name": "_EitherSCT", "namespace": "numpy.lib._arraysetops_impl.intersect1d#2", "upper_bound": "builtins.object", "values": [{".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "numpy.bool"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int8"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int32"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int64"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint8"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint32"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint64"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uintp"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.float16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.float32"}, "numpy.float64", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.complex64"}, "numpy.complex128", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.clongdouble"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["datetime.<PERSON><PERSON><PERSON>", "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.timedelta64"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["datetime.date", "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.datetime64"}, "numpy.bytes_", "numpy.str_", "numpy.void", "numpy.object_", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.integer"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.floating"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.complexfloating"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.character"}], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.lib._arraysetops_impl.intersect1d", "name": "intersect1d", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["ar1", "ar2", "assume_unique", "return_indices"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._EitherSCT", "id": -1, "name": "_EitherSCT", "namespace": "numpy.lib._arraysetops_impl.intersect1d#2", "upper_bound": "builtins.object", "values": [{".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "numpy.bool"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int8"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int32"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int64"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint8"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint32"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint64"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uintp"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.float16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.float32"}, "numpy.float64", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.complex64"}, "numpy.complex128", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.clongdouble"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["datetime.<PERSON><PERSON><PERSON>", "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.timedelta64"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["datetime.date", "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.datetime64"}, "numpy.bytes_", "numpy.str_", "numpy.void", "numpy.object_", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.integer"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.floating"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.complexfloating"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.character"}], "variance": 0}], "type_ref": "numpy._typing._array_like._ArrayLike"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._EitherSCT", "id": -1, "name": "_EitherSCT", "namespace": "numpy.lib._arraysetops_impl.intersect1d#2", "upper_bound": "builtins.object", "values": [{".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "numpy.bool"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int8"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int32"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int64"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint8"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint32"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint64"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uintp"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.float16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.float32"}, "numpy.float64", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.complex64"}, "numpy.complex128", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.clongdouble"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["datetime.<PERSON><PERSON><PERSON>", "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.timedelta64"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["datetime.date", "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.datetime64"}, "numpy.bytes_", "numpy.str_", "numpy.void", "numpy.object_", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.integer"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.floating"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.complexfloating"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.character"}], "variance": 0}], "type_ref": "numpy._typing._array_like._ArrayLike"}, "builtins.bool", {".class": "LiteralType", "fallback": "builtins.bool", "value": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "intersect1d", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._EitherSCT", "id": -1, "name": "_EitherSCT", "namespace": "numpy.lib._arraysetops_impl.intersect1d#2", "upper_bound": "builtins.object", "values": [{".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "numpy.bool"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int8"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int32"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int64"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint8"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint32"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint64"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uintp"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.float16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.float32"}, "numpy.float64", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.complex64"}, "numpy.complex128", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.clongdouble"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["datetime.<PERSON><PERSON><PERSON>", "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.timedelta64"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["datetime.date", "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.datetime64"}, "numpy.bytes_", "numpy.str_", "numpy.void", "numpy.object_", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.integer"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.floating"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.complexfloating"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.character"}], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._EitherSCT", "id": -1, "name": "_EitherSCT", "namespace": "numpy.lib._arraysetops_impl.intersect1d#2", "upper_bound": "builtins.object", "values": [{".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "numpy.bool"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int8"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int32"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int64"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint8"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint32"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint64"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uintp"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.float16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.float32"}, "numpy.float64", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.complex64"}, "numpy.complex128", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.clongdouble"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["datetime.<PERSON><PERSON><PERSON>", "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.timedelta64"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["datetime.date", "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.datetime64"}, "numpy.bytes_", "numpy.str_", "numpy.void", "numpy.object_", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.integer"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.floating"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.complexfloating"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.character"}], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["ar1", "ar2", "assume_unique", "return_indices"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.lib._arraysetops_impl.intersect1d", "name": "intersect1d", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["ar1", "ar2", "assume_unique", "return_indices"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, "builtins.bool", {".class": "LiteralType", "fallback": "builtins.bool", "value": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "intersect1d", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.lib._arraysetops_impl.intersect1d", "name": "intersect1d", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["ar1", "ar2", "assume_unique", "return_indices"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, "builtins.bool", {".class": "LiteralType", "fallback": "builtins.bool", "value": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "intersect1d", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["ar1", "ar2", "assume_unique", "return_indices"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._EitherSCT", "id": -1, "name": "_EitherSCT", "namespace": "numpy.lib._arraysetops_impl.intersect1d#0", "upper_bound": "builtins.object", "values": [{".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "numpy.bool"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int8"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int32"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int64"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint8"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint32"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint64"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uintp"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.float16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.float32"}, "numpy.float64", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.complex64"}, "numpy.complex128", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.clongdouble"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["datetime.<PERSON><PERSON><PERSON>", "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.timedelta64"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["datetime.date", "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.datetime64"}, "numpy.bytes_", "numpy.str_", "numpy.void", "numpy.object_", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.integer"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.floating"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.complexfloating"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.character"}], "variance": 0}], "type_ref": "numpy._typing._array_like._ArrayLike"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._EitherSCT", "id": -1, "name": "_EitherSCT", "namespace": "numpy.lib._arraysetops_impl.intersect1d#0", "upper_bound": "builtins.object", "values": [{".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "numpy.bool"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int8"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int32"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int64"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint8"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint32"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint64"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uintp"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.float16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.float32"}, "numpy.float64", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.complex64"}, "numpy.complex128", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.clongdouble"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["datetime.<PERSON><PERSON><PERSON>", "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.timedelta64"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["datetime.date", "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.datetime64"}, "numpy.bytes_", "numpy.str_", "numpy.void", "numpy.object_", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.integer"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.floating"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.complexfloating"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.character"}], "variance": 0}], "type_ref": "numpy._typing._array_like._ArrayLike"}, "builtins.bool", {".class": "LiteralType", "fallback": "builtins.bool", "value": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "intersect1d", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._EitherSCT", "id": -1, "name": "_EitherSCT", "namespace": "numpy.lib._arraysetops_impl.intersect1d#0", "upper_bound": "builtins.object", "values": [{".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "numpy.bool"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int8"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int32"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int64"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint8"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint32"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint64"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uintp"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.float16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.float32"}, "numpy.float64", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.complex64"}, "numpy.complex128", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.clongdouble"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["datetime.<PERSON><PERSON><PERSON>", "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.timedelta64"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["datetime.date", "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.datetime64"}, "numpy.bytes_", "numpy.str_", "numpy.void", "numpy.object_", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.integer"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.floating"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.complexfloating"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.character"}], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._EitherSCT", "id": -1, "name": "_EitherSCT", "namespace": "numpy.lib._arraysetops_impl.intersect1d#0", "upper_bound": "builtins.object", "values": [{".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "numpy.bool"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int8"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int32"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int64"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint8"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint32"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint64"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uintp"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.float16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.float32"}, "numpy.float64", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.complex64"}, "numpy.complex128", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.clongdouble"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["datetime.<PERSON><PERSON><PERSON>", "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.timedelta64"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["datetime.date", "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.datetime64"}, "numpy.bytes_", "numpy.str_", "numpy.void", "numpy.object_", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.integer"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.floating"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.complexfloating"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.character"}], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["ar1", "ar2", "assume_unique", "return_indices"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, "builtins.bool", {".class": "LiteralType", "fallback": "builtins.bool", "value": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "intersect1d", "ret_type": {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["ar1", "ar2", "assume_unique", "return_indices"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._EitherSCT", "id": -1, "name": "_EitherSCT", "namespace": "numpy.lib._arraysetops_impl.intersect1d#2", "upper_bound": "builtins.object", "values": [{".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "numpy.bool"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int8"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int32"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int64"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint8"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint32"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint64"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uintp"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.float16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.float32"}, "numpy.float64", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.complex64"}, "numpy.complex128", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.clongdouble"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["datetime.<PERSON><PERSON><PERSON>", "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.timedelta64"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["datetime.date", "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.datetime64"}, "numpy.bytes_", "numpy.str_", "numpy.void", "numpy.object_", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.integer"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.floating"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.complexfloating"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.character"}], "variance": 0}], "type_ref": "numpy._typing._array_like._ArrayLike"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._EitherSCT", "id": -1, "name": "_EitherSCT", "namespace": "numpy.lib._arraysetops_impl.intersect1d#2", "upper_bound": "builtins.object", "values": [{".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "numpy.bool"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int8"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int32"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int64"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint8"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint32"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint64"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uintp"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.float16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.float32"}, "numpy.float64", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.complex64"}, "numpy.complex128", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.clongdouble"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["datetime.<PERSON><PERSON><PERSON>", "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.timedelta64"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["datetime.date", "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.datetime64"}, "numpy.bytes_", "numpy.str_", "numpy.void", "numpy.object_", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.integer"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.floating"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.complexfloating"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.character"}], "variance": 0}], "type_ref": "numpy._typing._array_like._ArrayLike"}, "builtins.bool", {".class": "LiteralType", "fallback": "builtins.bool", "value": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "intersect1d", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._EitherSCT", "id": -1, "name": "_EitherSCT", "namespace": "numpy.lib._arraysetops_impl.intersect1d#2", "upper_bound": "builtins.object", "values": [{".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "numpy.bool"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int8"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int32"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int64"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint8"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint32"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint64"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uintp"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.float16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.float32"}, "numpy.float64", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.complex64"}, "numpy.complex128", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.clongdouble"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["datetime.<PERSON><PERSON><PERSON>", "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.timedelta64"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["datetime.date", "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.datetime64"}, "numpy.bytes_", "numpy.str_", "numpy.void", "numpy.object_", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.integer"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.floating"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.complexfloating"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.character"}], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._EitherSCT", "id": -1, "name": "_EitherSCT", "namespace": "numpy.lib._arraysetops_impl.intersect1d#2", "upper_bound": "builtins.object", "values": [{".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "numpy.bool"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int8"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int32"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int64"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint8"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint32"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint64"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uintp"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.float16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.float32"}, "numpy.float64", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.complex64"}, "numpy.complex128", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.clongdouble"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["datetime.<PERSON><PERSON><PERSON>", "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.timedelta64"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["datetime.date", "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.datetime64"}, "numpy.bytes_", "numpy.str_", "numpy.void", "numpy.object_", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.integer"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.floating"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.complexfloating"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.character"}], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["ar1", "ar2", "assume_unique", "return_indices"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, "builtins.bool", {".class": "LiteralType", "fallback": "builtins.bool", "value": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "intersect1d", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "intp": {".class": "SymbolTableNode", "cross_ref": "numpy.intp", "kind": "Gdef", "module_hidden": true, "module_public": false}, "isin": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 5], "arg_names": ["element", "test_elements", "assume_unique", "invert", "kind"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.lib._arraysetops_impl.isin", "name": "isin", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 5], "arg_names": ["element", "test_elements", "assume_unique", "invert", "kind"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, "builtins.bool", "builtins.bool", {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.str"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "isin", "ret_type": {".class": "TypeAliasType", "args": [{".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "numpy.bool"}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "np": {".class": "SymbolTableNode", "cross_ref": "numpy", "kind": "Gdef", "module_hidden": true, "module_public": false}, "number": {".class": "SymbolTableNode", "cross_ref": "numpy.number", "kind": "Gdef", "module_hidden": true, "module_public": false}, "object_": {".class": "SymbolTableNode", "cross_ref": "numpy.object_", "kind": "Gdef", "module_hidden": true, "module_public": false}, "overload": {".class": "SymbolTableNode", "cross_ref": "typing.overload", "kind": "Gdef", "module_hidden": true, "module_public": false}, "setdiff1d": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "numpy.lib._arraysetops_impl.setdiff1d", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["ar1", "ar2", "assume_unique"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.lib._arraysetops_impl.setdiff1d", "name": "setdiff1d", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["ar1", "ar2", "assume_unique"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._EitherSCT", "id": -1, "name": "_EitherSCT", "namespace": "numpy.lib._arraysetops_impl.setdiff1d#0", "upper_bound": "builtins.object", "values": [{".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "numpy.bool"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int8"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int32"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int64"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint8"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint32"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint64"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uintp"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.float16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.float32"}, "numpy.float64", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.complex64"}, "numpy.complex128", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.clongdouble"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["datetime.<PERSON><PERSON><PERSON>", "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.timedelta64"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["datetime.date", "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.datetime64"}, "numpy.bytes_", "numpy.str_", "numpy.void", "numpy.object_", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.integer"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.floating"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.complexfloating"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.character"}], "variance": 0}], "type_ref": "numpy._typing._array_like._ArrayLike"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._EitherSCT", "id": -1, "name": "_EitherSCT", "namespace": "numpy.lib._arraysetops_impl.setdiff1d#0", "upper_bound": "builtins.object", "values": [{".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "numpy.bool"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int8"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int32"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int64"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint8"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint32"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint64"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uintp"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.float16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.float32"}, "numpy.float64", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.complex64"}, "numpy.complex128", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.clongdouble"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["datetime.<PERSON><PERSON><PERSON>", "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.timedelta64"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["datetime.date", "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.datetime64"}, "numpy.bytes_", "numpy.str_", "numpy.void", "numpy.object_", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.integer"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.floating"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.complexfloating"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.character"}], "variance": 0}], "type_ref": "numpy._typing._array_like._ArrayLike"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "setdiff1d", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._EitherSCT", "id": -1, "name": "_EitherSCT", "namespace": "numpy.lib._arraysetops_impl.setdiff1d#0", "upper_bound": "builtins.object", "values": [{".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "numpy.bool"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int8"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int32"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int64"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint8"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint32"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint64"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uintp"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.float16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.float32"}, "numpy.float64", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.complex64"}, "numpy.complex128", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.clongdouble"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["datetime.<PERSON><PERSON><PERSON>", "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.timedelta64"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["datetime.date", "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.datetime64"}, "numpy.bytes_", "numpy.str_", "numpy.void", "numpy.object_", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.integer"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.floating"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.complexfloating"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.character"}], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._EitherSCT", "id": -1, "name": "_EitherSCT", "namespace": "numpy.lib._arraysetops_impl.setdiff1d#0", "upper_bound": "builtins.object", "values": [{".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "numpy.bool"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int8"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int32"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int64"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint8"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint32"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint64"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uintp"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.float16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.float32"}, "numpy.float64", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.complex64"}, "numpy.complex128", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.clongdouble"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["datetime.<PERSON><PERSON><PERSON>", "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.timedelta64"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["datetime.date", "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.datetime64"}, "numpy.bytes_", "numpy.str_", "numpy.void", "numpy.object_", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.integer"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.floating"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.complexfloating"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.character"}], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.lib._arraysetops_impl.setdiff1d", "name": "setdiff1d", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["ar1", "ar2", "assume_unique"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._EitherSCT", "id": -1, "name": "_EitherSCT", "namespace": "numpy.lib._arraysetops_impl.setdiff1d#0", "upper_bound": "builtins.object", "values": [{".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "numpy.bool"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int8"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int32"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int64"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint8"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint32"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint64"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uintp"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.float16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.float32"}, "numpy.float64", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.complex64"}, "numpy.complex128", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.clongdouble"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["datetime.<PERSON><PERSON><PERSON>", "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.timedelta64"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["datetime.date", "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.datetime64"}, "numpy.bytes_", "numpy.str_", "numpy.void", "numpy.object_", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.integer"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.floating"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.complexfloating"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.character"}], "variance": 0}], "type_ref": "numpy._typing._array_like._ArrayLike"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._EitherSCT", "id": -1, "name": "_EitherSCT", "namespace": "numpy.lib._arraysetops_impl.setdiff1d#0", "upper_bound": "builtins.object", "values": [{".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "numpy.bool"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int8"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int32"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int64"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint8"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint32"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint64"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uintp"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.float16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.float32"}, "numpy.float64", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.complex64"}, "numpy.complex128", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.clongdouble"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["datetime.<PERSON><PERSON><PERSON>", "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.timedelta64"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["datetime.date", "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.datetime64"}, "numpy.bytes_", "numpy.str_", "numpy.void", "numpy.object_", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.integer"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.floating"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.complexfloating"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.character"}], "variance": 0}], "type_ref": "numpy._typing._array_like._ArrayLike"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "setdiff1d", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._EitherSCT", "id": -1, "name": "_EitherSCT", "namespace": "numpy.lib._arraysetops_impl.setdiff1d#0", "upper_bound": "builtins.object", "values": [{".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "numpy.bool"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int8"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int32"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int64"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint8"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint32"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint64"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uintp"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.float16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.float32"}, "numpy.float64", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.complex64"}, "numpy.complex128", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.clongdouble"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["datetime.<PERSON><PERSON><PERSON>", "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.timedelta64"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["datetime.date", "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.datetime64"}, "numpy.bytes_", "numpy.str_", "numpy.void", "numpy.object_", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.integer"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.floating"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.complexfloating"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.character"}], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._EitherSCT", "id": -1, "name": "_EitherSCT", "namespace": "numpy.lib._arraysetops_impl.setdiff1d#0", "upper_bound": "builtins.object", "values": [{".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "numpy.bool"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int8"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int32"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int64"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint8"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint32"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint64"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uintp"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.float16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.float32"}, "numpy.float64", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.complex64"}, "numpy.complex128", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.clongdouble"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["datetime.<PERSON><PERSON><PERSON>", "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.timedelta64"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["datetime.date", "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.datetime64"}, "numpy.bytes_", "numpy.str_", "numpy.void", "numpy.object_", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.integer"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.floating"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.complexfloating"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.character"}], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["ar1", "ar2", "assume_unique"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.lib._arraysetops_impl.setdiff1d", "name": "setdiff1d", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["ar1", "ar2", "assume_unique"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "setdiff1d", "ret_type": {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.lib._arraysetops_impl.setdiff1d", "name": "setdiff1d", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["ar1", "ar2", "assume_unique"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "setdiff1d", "ret_type": {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["ar1", "ar2", "assume_unique"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._EitherSCT", "id": -1, "name": "_EitherSCT", "namespace": "numpy.lib._arraysetops_impl.setdiff1d#0", "upper_bound": "builtins.object", "values": [{".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "numpy.bool"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int8"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int32"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int64"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint8"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint32"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint64"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uintp"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.float16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.float32"}, "numpy.float64", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.complex64"}, "numpy.complex128", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.clongdouble"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["datetime.<PERSON><PERSON><PERSON>", "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.timedelta64"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["datetime.date", "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.datetime64"}, "numpy.bytes_", "numpy.str_", "numpy.void", "numpy.object_", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.integer"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.floating"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.complexfloating"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.character"}], "variance": 0}], "type_ref": "numpy._typing._array_like._ArrayLike"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._EitherSCT", "id": -1, "name": "_EitherSCT", "namespace": "numpy.lib._arraysetops_impl.setdiff1d#0", "upper_bound": "builtins.object", "values": [{".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "numpy.bool"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int8"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int32"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int64"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint8"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint32"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint64"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uintp"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.float16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.float32"}, "numpy.float64", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.complex64"}, "numpy.complex128", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.clongdouble"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["datetime.<PERSON><PERSON><PERSON>", "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.timedelta64"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["datetime.date", "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.datetime64"}, "numpy.bytes_", "numpy.str_", "numpy.void", "numpy.object_", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.integer"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.floating"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.complexfloating"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.character"}], "variance": 0}], "type_ref": "numpy._typing._array_like._ArrayLike"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "setdiff1d", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._EitherSCT", "id": -1, "name": "_EitherSCT", "namespace": "numpy.lib._arraysetops_impl.setdiff1d#0", "upper_bound": "builtins.object", "values": [{".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "numpy.bool"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int8"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int32"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int64"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint8"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint32"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint64"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uintp"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.float16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.float32"}, "numpy.float64", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.complex64"}, "numpy.complex128", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.clongdouble"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["datetime.<PERSON><PERSON><PERSON>", "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.timedelta64"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["datetime.date", "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.datetime64"}, "numpy.bytes_", "numpy.str_", "numpy.void", "numpy.object_", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.integer"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.floating"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.complexfloating"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.character"}], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._EitherSCT", "id": -1, "name": "_EitherSCT", "namespace": "numpy.lib._arraysetops_impl.setdiff1d#0", "upper_bound": "builtins.object", "values": [{".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "numpy.bool"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int8"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int32"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int64"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint8"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint32"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint64"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uintp"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.float16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.float32"}, "numpy.float64", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.complex64"}, "numpy.complex128", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.clongdouble"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["datetime.<PERSON><PERSON><PERSON>", "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.timedelta64"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["datetime.date", "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.datetime64"}, "numpy.bytes_", "numpy.str_", "numpy.void", "numpy.object_", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.integer"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.floating"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.complexfloating"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.character"}], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["ar1", "ar2", "assume_unique"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "setdiff1d", "ret_type": {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "setxor1d": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "numpy.lib._arraysetops_impl.setxor1d", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["ar1", "ar2", "assume_unique"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.lib._arraysetops_impl.setxor1d", "name": "setxor1d", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["ar1", "ar2", "assume_unique"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._EitherSCT", "id": -1, "name": "_EitherSCT", "namespace": "numpy.lib._arraysetops_impl.setxor1d#0", "upper_bound": "builtins.object", "values": [{".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "numpy.bool"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int8"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int32"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int64"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint8"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint32"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint64"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uintp"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.float16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.float32"}, "numpy.float64", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.complex64"}, "numpy.complex128", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.clongdouble"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["datetime.<PERSON><PERSON><PERSON>", "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.timedelta64"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["datetime.date", "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.datetime64"}, "numpy.bytes_", "numpy.str_", "numpy.void", "numpy.object_", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.integer"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.floating"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.complexfloating"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.character"}], "variance": 0}], "type_ref": "numpy._typing._array_like._ArrayLike"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._EitherSCT", "id": -1, "name": "_EitherSCT", "namespace": "numpy.lib._arraysetops_impl.setxor1d#0", "upper_bound": "builtins.object", "values": [{".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "numpy.bool"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int8"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int32"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int64"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint8"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint32"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint64"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uintp"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.float16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.float32"}, "numpy.float64", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.complex64"}, "numpy.complex128", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.clongdouble"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["datetime.<PERSON><PERSON><PERSON>", "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.timedelta64"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["datetime.date", "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.datetime64"}, "numpy.bytes_", "numpy.str_", "numpy.void", "numpy.object_", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.integer"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.floating"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.complexfloating"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.character"}], "variance": 0}], "type_ref": "numpy._typing._array_like._ArrayLike"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "setxor1d", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._EitherSCT", "id": -1, "name": "_EitherSCT", "namespace": "numpy.lib._arraysetops_impl.setxor1d#0", "upper_bound": "builtins.object", "values": [{".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "numpy.bool"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int8"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int32"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int64"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint8"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint32"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint64"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uintp"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.float16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.float32"}, "numpy.float64", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.complex64"}, "numpy.complex128", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.clongdouble"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["datetime.<PERSON><PERSON><PERSON>", "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.timedelta64"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["datetime.date", "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.datetime64"}, "numpy.bytes_", "numpy.str_", "numpy.void", "numpy.object_", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.integer"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.floating"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.complexfloating"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.character"}], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._EitherSCT", "id": -1, "name": "_EitherSCT", "namespace": "numpy.lib._arraysetops_impl.setxor1d#0", "upper_bound": "builtins.object", "values": [{".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "numpy.bool"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int8"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int32"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int64"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint8"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint32"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint64"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uintp"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.float16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.float32"}, "numpy.float64", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.complex64"}, "numpy.complex128", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.clongdouble"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["datetime.<PERSON><PERSON><PERSON>", "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.timedelta64"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["datetime.date", "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.datetime64"}, "numpy.bytes_", "numpy.str_", "numpy.void", "numpy.object_", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.integer"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.floating"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.complexfloating"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.character"}], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.lib._arraysetops_impl.setxor1d", "name": "setxor1d", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["ar1", "ar2", "assume_unique"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._EitherSCT", "id": -1, "name": "_EitherSCT", "namespace": "numpy.lib._arraysetops_impl.setxor1d#0", "upper_bound": "builtins.object", "values": [{".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "numpy.bool"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int8"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int32"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int64"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint8"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint32"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint64"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uintp"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.float16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.float32"}, "numpy.float64", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.complex64"}, "numpy.complex128", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.clongdouble"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["datetime.<PERSON><PERSON><PERSON>", "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.timedelta64"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["datetime.date", "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.datetime64"}, "numpy.bytes_", "numpy.str_", "numpy.void", "numpy.object_", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.integer"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.floating"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.complexfloating"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.character"}], "variance": 0}], "type_ref": "numpy._typing._array_like._ArrayLike"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._EitherSCT", "id": -1, "name": "_EitherSCT", "namespace": "numpy.lib._arraysetops_impl.setxor1d#0", "upper_bound": "builtins.object", "values": [{".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "numpy.bool"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int8"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int32"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int64"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint8"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint32"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint64"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uintp"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.float16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.float32"}, "numpy.float64", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.complex64"}, "numpy.complex128", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.clongdouble"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["datetime.<PERSON><PERSON><PERSON>", "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.timedelta64"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["datetime.date", "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.datetime64"}, "numpy.bytes_", "numpy.str_", "numpy.void", "numpy.object_", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.integer"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.floating"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.complexfloating"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.character"}], "variance": 0}], "type_ref": "numpy._typing._array_like._ArrayLike"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "setxor1d", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._EitherSCT", "id": -1, "name": "_EitherSCT", "namespace": "numpy.lib._arraysetops_impl.setxor1d#0", "upper_bound": "builtins.object", "values": [{".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "numpy.bool"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int8"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int32"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int64"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint8"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint32"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint64"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uintp"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.float16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.float32"}, "numpy.float64", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.complex64"}, "numpy.complex128", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.clongdouble"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["datetime.<PERSON><PERSON><PERSON>", "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.timedelta64"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["datetime.date", "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.datetime64"}, "numpy.bytes_", "numpy.str_", "numpy.void", "numpy.object_", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.integer"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.floating"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.complexfloating"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.character"}], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._EitherSCT", "id": -1, "name": "_EitherSCT", "namespace": "numpy.lib._arraysetops_impl.setxor1d#0", "upper_bound": "builtins.object", "values": [{".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "numpy.bool"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int8"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int32"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int64"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint8"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint32"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint64"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uintp"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.float16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.float32"}, "numpy.float64", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.complex64"}, "numpy.complex128", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.clongdouble"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["datetime.<PERSON><PERSON><PERSON>", "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.timedelta64"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["datetime.date", "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.datetime64"}, "numpy.bytes_", "numpy.str_", "numpy.void", "numpy.object_", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.integer"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.floating"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.complexfloating"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.character"}], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["ar1", "ar2", "assume_unique"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.lib._arraysetops_impl.setxor1d", "name": "setxor1d", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["ar1", "ar2", "assume_unique"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "setxor1d", "ret_type": {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.lib._arraysetops_impl.setxor1d", "name": "setxor1d", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["ar1", "ar2", "assume_unique"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "setxor1d", "ret_type": {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["ar1", "ar2", "assume_unique"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._EitherSCT", "id": -1, "name": "_EitherSCT", "namespace": "numpy.lib._arraysetops_impl.setxor1d#0", "upper_bound": "builtins.object", "values": [{".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "numpy.bool"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int8"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int32"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int64"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint8"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint32"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint64"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uintp"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.float16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.float32"}, "numpy.float64", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.complex64"}, "numpy.complex128", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.clongdouble"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["datetime.<PERSON><PERSON><PERSON>", "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.timedelta64"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["datetime.date", "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.datetime64"}, "numpy.bytes_", "numpy.str_", "numpy.void", "numpy.object_", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.integer"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.floating"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.complexfloating"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.character"}], "variance": 0}], "type_ref": "numpy._typing._array_like._ArrayLike"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._EitherSCT", "id": -1, "name": "_EitherSCT", "namespace": "numpy.lib._arraysetops_impl.setxor1d#0", "upper_bound": "builtins.object", "values": [{".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "numpy.bool"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int8"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int32"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int64"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint8"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint32"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint64"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uintp"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.float16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.float32"}, "numpy.float64", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.complex64"}, "numpy.complex128", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.clongdouble"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["datetime.<PERSON><PERSON><PERSON>", "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.timedelta64"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["datetime.date", "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.datetime64"}, "numpy.bytes_", "numpy.str_", "numpy.void", "numpy.object_", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.integer"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.floating"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.complexfloating"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.character"}], "variance": 0}], "type_ref": "numpy._typing._array_like._ArrayLike"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "setxor1d", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._EitherSCT", "id": -1, "name": "_EitherSCT", "namespace": "numpy.lib._arraysetops_impl.setxor1d#0", "upper_bound": "builtins.object", "values": [{".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "numpy.bool"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int8"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int32"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int64"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint8"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint32"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint64"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uintp"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.float16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.float32"}, "numpy.float64", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.complex64"}, "numpy.complex128", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.clongdouble"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["datetime.<PERSON><PERSON><PERSON>", "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.timedelta64"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["datetime.date", "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.datetime64"}, "numpy.bytes_", "numpy.str_", "numpy.void", "numpy.object_", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.integer"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.floating"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.complexfloating"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.character"}], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._EitherSCT", "id": -1, "name": "_EitherSCT", "namespace": "numpy.lib._arraysetops_impl.setxor1d#0", "upper_bound": "builtins.object", "values": [{".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "numpy.bool"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int8"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int32"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int64"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint8"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint32"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint64"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uintp"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.float16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.float32"}, "numpy.float64", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.complex64"}, "numpy.complex128", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.clongdouble"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["datetime.<PERSON><PERSON><PERSON>", "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.timedelta64"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["datetime.date", "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.datetime64"}, "numpy.bytes_", "numpy.str_", "numpy.void", "numpy.object_", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.integer"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.floating"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.complexfloating"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.character"}], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["ar1", "ar2", "assume_unique"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "setxor1d", "ret_type": {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "timedelta64": {".class": "SymbolTableNode", "cross_ref": "numpy.timedelta64", "kind": "Gdef", "module_hidden": true, "module_public": false}, "union1d": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "numpy.lib._arraysetops_impl.union1d", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["ar1", "ar2"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.lib._arraysetops_impl.union1d", "name": "union1d", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["ar1", "ar2"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._EitherSCT", "id": -1, "name": "_EitherSCT", "namespace": "numpy.lib._arraysetops_impl.union1d#0", "upper_bound": "builtins.object", "values": [{".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "numpy.bool"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int8"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int32"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int64"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint8"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint32"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint64"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uintp"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.float16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.float32"}, "numpy.float64", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.complex64"}, "numpy.complex128", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.clongdouble"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["datetime.<PERSON><PERSON><PERSON>", "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.timedelta64"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["datetime.date", "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.datetime64"}, "numpy.bytes_", "numpy.str_", "numpy.void", "numpy.object_", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.integer"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.floating"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.complexfloating"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.character"}], "variance": 0}], "type_ref": "numpy._typing._array_like._ArrayLike"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._EitherSCT", "id": -1, "name": "_EitherSCT", "namespace": "numpy.lib._arraysetops_impl.union1d#0", "upper_bound": "builtins.object", "values": [{".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "numpy.bool"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int8"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int32"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int64"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint8"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint32"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint64"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uintp"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.float16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.float32"}, "numpy.float64", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.complex64"}, "numpy.complex128", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.clongdouble"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["datetime.<PERSON><PERSON><PERSON>", "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.timedelta64"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["datetime.date", "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.datetime64"}, "numpy.bytes_", "numpy.str_", "numpy.void", "numpy.object_", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.integer"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.floating"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.complexfloating"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.character"}], "variance": 0}], "type_ref": "numpy._typing._array_like._ArrayLike"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "union1d", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._EitherSCT", "id": -1, "name": "_EitherSCT", "namespace": "numpy.lib._arraysetops_impl.union1d#0", "upper_bound": "builtins.object", "values": [{".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "numpy.bool"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int8"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int32"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int64"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint8"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint32"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint64"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uintp"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.float16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.float32"}, "numpy.float64", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.complex64"}, "numpy.complex128", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.clongdouble"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["datetime.<PERSON><PERSON><PERSON>", "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.timedelta64"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["datetime.date", "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.datetime64"}, "numpy.bytes_", "numpy.str_", "numpy.void", "numpy.object_", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.integer"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.floating"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.complexfloating"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.character"}], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._EitherSCT", "id": -1, "name": "_EitherSCT", "namespace": "numpy.lib._arraysetops_impl.union1d#0", "upper_bound": "builtins.object", "values": [{".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "numpy.bool"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int8"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int32"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int64"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint8"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint32"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint64"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uintp"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.float16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.float32"}, "numpy.float64", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.complex64"}, "numpy.complex128", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.clongdouble"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["datetime.<PERSON><PERSON><PERSON>", "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.timedelta64"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["datetime.date", "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.datetime64"}, "numpy.bytes_", "numpy.str_", "numpy.void", "numpy.object_", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.integer"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.floating"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.complexfloating"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.character"}], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.lib._arraysetops_impl.union1d", "name": "union1d", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["ar1", "ar2"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._EitherSCT", "id": -1, "name": "_EitherSCT", "namespace": "numpy.lib._arraysetops_impl.union1d#0", "upper_bound": "builtins.object", "values": [{".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "numpy.bool"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int8"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int32"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int64"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint8"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint32"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint64"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uintp"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.float16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.float32"}, "numpy.float64", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.complex64"}, "numpy.complex128", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.clongdouble"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["datetime.<PERSON><PERSON><PERSON>", "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.timedelta64"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["datetime.date", "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.datetime64"}, "numpy.bytes_", "numpy.str_", "numpy.void", "numpy.object_", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.integer"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.floating"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.complexfloating"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.character"}], "variance": 0}], "type_ref": "numpy._typing._array_like._ArrayLike"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._EitherSCT", "id": -1, "name": "_EitherSCT", "namespace": "numpy.lib._arraysetops_impl.union1d#0", "upper_bound": "builtins.object", "values": [{".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "numpy.bool"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int8"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int32"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int64"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint8"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint32"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint64"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uintp"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.float16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.float32"}, "numpy.float64", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.complex64"}, "numpy.complex128", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.clongdouble"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["datetime.<PERSON><PERSON><PERSON>", "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.timedelta64"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["datetime.date", "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.datetime64"}, "numpy.bytes_", "numpy.str_", "numpy.void", "numpy.object_", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.integer"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.floating"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.complexfloating"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.character"}], "variance": 0}], "type_ref": "numpy._typing._array_like._ArrayLike"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "union1d", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._EitherSCT", "id": -1, "name": "_EitherSCT", "namespace": "numpy.lib._arraysetops_impl.union1d#0", "upper_bound": "builtins.object", "values": [{".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "numpy.bool"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int8"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int32"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int64"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint8"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint32"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint64"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uintp"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.float16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.float32"}, "numpy.float64", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.complex64"}, "numpy.complex128", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.clongdouble"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["datetime.<PERSON><PERSON><PERSON>", "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.timedelta64"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["datetime.date", "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.datetime64"}, "numpy.bytes_", "numpy.str_", "numpy.void", "numpy.object_", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.integer"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.floating"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.complexfloating"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.character"}], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._EitherSCT", "id": -1, "name": "_EitherSCT", "namespace": "numpy.lib._arraysetops_impl.union1d#0", "upper_bound": "builtins.object", "values": [{".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "numpy.bool"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int8"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int32"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int64"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint8"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint32"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint64"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uintp"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.float16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.float32"}, "numpy.float64", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.complex64"}, "numpy.complex128", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.clongdouble"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["datetime.<PERSON><PERSON><PERSON>", "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.timedelta64"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["datetime.date", "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.datetime64"}, "numpy.bytes_", "numpy.str_", "numpy.void", "numpy.object_", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.integer"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.floating"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.complexfloating"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.character"}], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["ar1", "ar2"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.lib._arraysetops_impl.union1d", "name": "union1d", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["ar1", "ar2"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "union1d", "ret_type": {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.lib._arraysetops_impl.union1d", "name": "union1d", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["ar1", "ar2"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "union1d", "ret_type": {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["ar1", "ar2"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._EitherSCT", "id": -1, "name": "_EitherSCT", "namespace": "numpy.lib._arraysetops_impl.union1d#0", "upper_bound": "builtins.object", "values": [{".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "numpy.bool"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int8"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int32"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int64"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint8"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint32"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint64"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uintp"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.float16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.float32"}, "numpy.float64", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.complex64"}, "numpy.complex128", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.clongdouble"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["datetime.<PERSON><PERSON><PERSON>", "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.timedelta64"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["datetime.date", "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.datetime64"}, "numpy.bytes_", "numpy.str_", "numpy.void", "numpy.object_", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.integer"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.floating"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.complexfloating"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.character"}], "variance": 0}], "type_ref": "numpy._typing._array_like._ArrayLike"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._EitherSCT", "id": -1, "name": "_EitherSCT", "namespace": "numpy.lib._arraysetops_impl.union1d#0", "upper_bound": "builtins.object", "values": [{".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "numpy.bool"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int8"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int32"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int64"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint8"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint32"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint64"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uintp"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.float16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.float32"}, "numpy.float64", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.complex64"}, "numpy.complex128", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.clongdouble"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["datetime.<PERSON><PERSON><PERSON>", "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.timedelta64"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["datetime.date", "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.datetime64"}, "numpy.bytes_", "numpy.str_", "numpy.void", "numpy.object_", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.integer"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.floating"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.complexfloating"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.character"}], "variance": 0}], "type_ref": "numpy._typing._array_like._ArrayLike"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "union1d", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._EitherSCT", "id": -1, "name": "_EitherSCT", "namespace": "numpy.lib._arraysetops_impl.union1d#0", "upper_bound": "builtins.object", "values": [{".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "numpy.bool"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int8"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int32"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int64"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint8"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint32"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint64"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uintp"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.float16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.float32"}, "numpy.float64", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.complex64"}, "numpy.complex128", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.clongdouble"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["datetime.<PERSON><PERSON><PERSON>", "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.timedelta64"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["datetime.date", "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.datetime64"}, "numpy.bytes_", "numpy.str_", "numpy.void", "numpy.object_", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.integer"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.floating"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.complexfloating"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.character"}], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._EitherSCT", "id": -1, "name": "_EitherSCT", "namespace": "numpy.lib._arraysetops_impl.union1d#0", "upper_bound": "builtins.object", "values": [{".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "numpy.bool"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int8"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int32"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int64"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint8"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint32"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint64"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uintp"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.float16"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.float32"}, "numpy.float64", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.complex64"}, "numpy.complex128", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.clongdouble"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["datetime.<PERSON><PERSON><PERSON>", "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.timedelta64"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["datetime.date", "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.datetime64"}, "numpy.bytes_", "numpy.str_", "numpy.void", "numpy.object_", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.integer"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.floating"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.complexfloating"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "numpy.character"}], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["ar1", "ar2"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "union1d", "ret_type": {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "unique": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "numpy.lib._arraysetops_impl.unique", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 5], "arg_names": ["ar", "return_index", "return_inverse", "return_counts", "axis", "equal_nan"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.lib._arraysetops_impl.unique", "name": "unique", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 5], "arg_names": ["ar", "return_index", "return_inverse", "return_counts", "axis", "equal_nan"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib._arraysetops_impl.unique#0", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like._ArrayLike"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "UnionType", "items": [{".class": "NoneType"}, "typing.SupportsIndex"], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unique", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib._arraysetops_impl.unique#0", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib._arraysetops_impl.unique#0", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.lib._arraysetops_impl.unique", "name": "unique", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 5], "arg_names": ["ar", "return_index", "return_inverse", "return_counts", "axis", "equal_nan"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib._arraysetops_impl.unique#0", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like._ArrayLike"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "UnionType", "items": [{".class": "NoneType"}, "typing.SupportsIndex"], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unique", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib._arraysetops_impl.unique#0", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib._arraysetops_impl.unique#0", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 5], "arg_names": ["ar", "return_index", "return_inverse", "return_counts", "axis", "equal_nan"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.lib._arraysetops_impl.unique", "name": "unique", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 5], "arg_names": ["ar", "return_index", "return_inverse", "return_counts", "axis", "equal_nan"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "UnionType", "items": [{".class": "NoneType"}, "typing.SupportsIndex"], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unique", "ret_type": {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.lib._arraysetops_impl.unique", "name": "unique", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 5], "arg_names": ["ar", "return_index", "return_inverse", "return_counts", "axis", "equal_nan"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "UnionType", "items": [{".class": "NoneType"}, "typing.SupportsIndex"], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unique", "ret_type": {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 5], "arg_names": ["ar", "return_index", "return_inverse", "return_counts", "axis", "equal_nan"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.lib._arraysetops_impl.unique", "name": "unique", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 5], "arg_names": ["ar", "return_index", "return_inverse", "return_counts", "axis", "equal_nan"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib._arraysetops_impl.unique#2", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like._ArrayLike"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "UnionType", "items": [{".class": "NoneType"}, "typing.SupportsIndex"], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unique", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib._arraysetops_impl.unique#2", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib._arraysetops_impl.unique#2", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.lib._arraysetops_impl.unique", "name": "unique", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 5], "arg_names": ["ar", "return_index", "return_inverse", "return_counts", "axis", "equal_nan"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib._arraysetops_impl.unique#2", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like._ArrayLike"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "UnionType", "items": [{".class": "NoneType"}, "typing.SupportsIndex"], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unique", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib._arraysetops_impl.unique#2", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib._arraysetops_impl.unique#2", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 5], "arg_names": ["ar", "return_index", "return_inverse", "return_counts", "axis", "equal_nan"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.lib._arraysetops_impl.unique", "name": "unique", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 5], "arg_names": ["ar", "return_index", "return_inverse", "return_counts", "axis", "equal_nan"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "UnionType", "items": [{".class": "NoneType"}, "typing.SupportsIndex"], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unique", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.lib._arraysetops_impl.unique", "name": "unique", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 5], "arg_names": ["ar", "return_index", "return_inverse", "return_counts", "axis", "equal_nan"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "UnionType", "items": [{".class": "NoneType"}, "typing.SupportsIndex"], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unique", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 5], "arg_names": ["ar", "return_index", "return_inverse", "return_counts", "axis", "equal_nan"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.lib._arraysetops_impl.unique", "name": "unique", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 5], "arg_names": ["ar", "return_index", "return_inverse", "return_counts", "axis", "equal_nan"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib._arraysetops_impl.unique#4", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like._ArrayLike"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "UnionType", "items": [{".class": "NoneType"}, "typing.SupportsIndex"], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unique", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib._arraysetops_impl.unique#4", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib._arraysetops_impl.unique#4", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.lib._arraysetops_impl.unique", "name": "unique", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 5], "arg_names": ["ar", "return_index", "return_inverse", "return_counts", "axis", "equal_nan"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib._arraysetops_impl.unique#4", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like._ArrayLike"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "UnionType", "items": [{".class": "NoneType"}, "typing.SupportsIndex"], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unique", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib._arraysetops_impl.unique#4", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib._arraysetops_impl.unique#4", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 5], "arg_names": ["ar", "return_index", "return_inverse", "return_counts", "axis", "equal_nan"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.lib._arraysetops_impl.unique", "name": "unique", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 5], "arg_names": ["ar", "return_index", "return_inverse", "return_counts", "axis", "equal_nan"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "UnionType", "items": [{".class": "NoneType"}, "typing.SupportsIndex"], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unique", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.lib._arraysetops_impl.unique", "name": "unique", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 5], "arg_names": ["ar", "return_index", "return_inverse", "return_counts", "axis", "equal_nan"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "UnionType", "items": [{".class": "NoneType"}, "typing.SupportsIndex"], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unique", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 5], "arg_names": ["ar", "return_index", "return_inverse", "return_counts", "axis", "equal_nan"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.lib._arraysetops_impl.unique", "name": "unique", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 5], "arg_names": ["ar", "return_index", "return_inverse", "return_counts", "axis", "equal_nan"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib._arraysetops_impl.unique#6", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like._ArrayLike"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, "typing.SupportsIndex"], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unique", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib._arraysetops_impl.unique#6", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib._arraysetops_impl.unique#6", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.lib._arraysetops_impl.unique", "name": "unique", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 5], "arg_names": ["ar", "return_index", "return_inverse", "return_counts", "axis", "equal_nan"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib._arraysetops_impl.unique#6", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like._ArrayLike"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, "typing.SupportsIndex"], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unique", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib._arraysetops_impl.unique#6", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib._arraysetops_impl.unique#6", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 5], "arg_names": ["ar", "return_index", "return_inverse", "return_counts", "axis", "equal_nan"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.lib._arraysetops_impl.unique", "name": "unique", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 5], "arg_names": ["ar", "return_index", "return_inverse", "return_counts", "axis", "equal_nan"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, "typing.SupportsIndex"], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unique", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.lib._arraysetops_impl.unique", "name": "unique", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 5], "arg_names": ["ar", "return_index", "return_inverse", "return_counts", "axis", "equal_nan"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, "typing.SupportsIndex"], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unique", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 5], "arg_names": ["ar", "return_index", "return_inverse", "return_counts", "axis", "equal_nan"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.lib._arraysetops_impl.unique", "name": "unique", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 5], "arg_names": ["ar", "return_index", "return_inverse", "return_counts", "axis", "equal_nan"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib._arraysetops_impl.unique#8", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like._ArrayLike"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "UnionType", "items": [{".class": "NoneType"}, "typing.SupportsIndex"], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unique", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib._arraysetops_impl.unique#8", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib._arraysetops_impl.unique#8", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.lib._arraysetops_impl.unique", "name": "unique", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 5], "arg_names": ["ar", "return_index", "return_inverse", "return_counts", "axis", "equal_nan"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib._arraysetops_impl.unique#8", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like._ArrayLike"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "UnionType", "items": [{".class": "NoneType"}, "typing.SupportsIndex"], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unique", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib._arraysetops_impl.unique#8", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib._arraysetops_impl.unique#8", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 5], "arg_names": ["ar", "return_index", "return_inverse", "return_counts", "axis", "equal_nan"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.lib._arraysetops_impl.unique", "name": "unique", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 5], "arg_names": ["ar", "return_index", "return_inverse", "return_counts", "axis", "equal_nan"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "UnionType", "items": [{".class": "NoneType"}, "typing.SupportsIndex"], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unique", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.lib._arraysetops_impl.unique", "name": "unique", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 5], "arg_names": ["ar", "return_index", "return_inverse", "return_counts", "axis", "equal_nan"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "UnionType", "items": [{".class": "NoneType"}, "typing.SupportsIndex"], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unique", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 5], "arg_names": ["ar", "return_index", "return_inverse", "return_counts", "axis", "equal_nan"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.lib._arraysetops_impl.unique", "name": "unique", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 5], "arg_names": ["ar", "return_index", "return_inverse", "return_counts", "axis", "equal_nan"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib._arraysetops_impl.unique#10", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like._ArrayLike"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, "typing.SupportsIndex"], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unique", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib._arraysetops_impl.unique#10", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib._arraysetops_impl.unique#10", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.lib._arraysetops_impl.unique", "name": "unique", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 5], "arg_names": ["ar", "return_index", "return_inverse", "return_counts", "axis", "equal_nan"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib._arraysetops_impl.unique#10", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like._ArrayLike"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, "typing.SupportsIndex"], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unique", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib._arraysetops_impl.unique#10", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib._arraysetops_impl.unique#10", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 5], "arg_names": ["ar", "return_index", "return_inverse", "return_counts", "axis", "equal_nan"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.lib._arraysetops_impl.unique", "name": "unique", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 5], "arg_names": ["ar", "return_index", "return_inverse", "return_counts", "axis", "equal_nan"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, "typing.SupportsIndex"], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unique", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.lib._arraysetops_impl.unique", "name": "unique", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 5], "arg_names": ["ar", "return_index", "return_inverse", "return_counts", "axis", "equal_nan"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, "typing.SupportsIndex"], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unique", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 5], "arg_names": ["ar", "return_index", "return_inverse", "return_counts", "axis", "equal_nan"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.lib._arraysetops_impl.unique", "name": "unique", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 5], "arg_names": ["ar", "return_index", "return_inverse", "return_counts", "axis", "equal_nan"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib._arraysetops_impl.unique#12", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like._ArrayLike"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, "typing.SupportsIndex"], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unique", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib._arraysetops_impl.unique#12", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib._arraysetops_impl.unique#12", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.lib._arraysetops_impl.unique", "name": "unique", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 5], "arg_names": ["ar", "return_index", "return_inverse", "return_counts", "axis", "equal_nan"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib._arraysetops_impl.unique#12", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like._ArrayLike"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, "typing.SupportsIndex"], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unique", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib._arraysetops_impl.unique#12", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib._arraysetops_impl.unique#12", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 5], "arg_names": ["ar", "return_index", "return_inverse", "return_counts", "axis", "equal_nan"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.lib._arraysetops_impl.unique", "name": "unique", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 5], "arg_names": ["ar", "return_index", "return_inverse", "return_counts", "axis", "equal_nan"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, "typing.SupportsIndex"], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unique", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.lib._arraysetops_impl.unique", "name": "unique", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 5], "arg_names": ["ar", "return_index", "return_inverse", "return_counts", "axis", "equal_nan"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, "typing.SupportsIndex"], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unique", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 5], "arg_names": ["ar", "return_index", "return_inverse", "return_counts", "axis", "equal_nan"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.lib._arraysetops_impl.unique", "name": "unique", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 5], "arg_names": ["ar", "return_index", "return_inverse", "return_counts", "axis", "equal_nan"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib._arraysetops_impl.unique#14", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like._ArrayLike"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, "typing.SupportsIndex"], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unique", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib._arraysetops_impl.unique#14", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib._arraysetops_impl.unique#14", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.lib._arraysetops_impl.unique", "name": "unique", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 5], "arg_names": ["ar", "return_index", "return_inverse", "return_counts", "axis", "equal_nan"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib._arraysetops_impl.unique#14", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like._ArrayLike"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, "typing.SupportsIndex"], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unique", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib._arraysetops_impl.unique#14", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib._arraysetops_impl.unique#14", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 5], "arg_names": ["ar", "return_index", "return_inverse", "return_counts", "axis", "equal_nan"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.lib._arraysetops_impl.unique", "name": "unique", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 5], "arg_names": ["ar", "return_index", "return_inverse", "return_counts", "axis", "equal_nan"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, "typing.SupportsIndex"], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unique", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.lib._arraysetops_impl.unique", "name": "unique", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 5], "arg_names": ["ar", "return_index", "return_inverse", "return_counts", "axis", "equal_nan"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, "typing.SupportsIndex"], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unique", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 5], "arg_names": ["ar", "return_index", "return_inverse", "return_counts", "axis", "equal_nan"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib._arraysetops_impl.unique#0", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like._ArrayLike"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "UnionType", "items": [{".class": "NoneType"}, "typing.SupportsIndex"], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unique", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib._arraysetops_impl.unique#0", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib._arraysetops_impl.unique#0", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 5], "arg_names": ["ar", "return_index", "return_inverse", "return_counts", "axis", "equal_nan"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "UnionType", "items": [{".class": "NoneType"}, "typing.SupportsIndex"], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unique", "ret_type": {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 5], "arg_names": ["ar", "return_index", "return_inverse", "return_counts", "axis", "equal_nan"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib._arraysetops_impl.unique#2", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like._ArrayLike"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "UnionType", "items": [{".class": "NoneType"}, "typing.SupportsIndex"], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unique", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib._arraysetops_impl.unique#2", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib._arraysetops_impl.unique#2", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 5], "arg_names": ["ar", "return_index", "return_inverse", "return_counts", "axis", "equal_nan"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "UnionType", "items": [{".class": "NoneType"}, "typing.SupportsIndex"], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unique", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 5], "arg_names": ["ar", "return_index", "return_inverse", "return_counts", "axis", "equal_nan"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib._arraysetops_impl.unique#4", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like._ArrayLike"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "UnionType", "items": [{".class": "NoneType"}, "typing.SupportsIndex"], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unique", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib._arraysetops_impl.unique#4", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib._arraysetops_impl.unique#4", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 5], "arg_names": ["ar", "return_index", "return_inverse", "return_counts", "axis", "equal_nan"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "UnionType", "items": [{".class": "NoneType"}, "typing.SupportsIndex"], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unique", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 5], "arg_names": ["ar", "return_index", "return_inverse", "return_counts", "axis", "equal_nan"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib._arraysetops_impl.unique#6", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like._ArrayLike"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, "typing.SupportsIndex"], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unique", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib._arraysetops_impl.unique#6", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib._arraysetops_impl.unique#6", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 5], "arg_names": ["ar", "return_index", "return_inverse", "return_counts", "axis", "equal_nan"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, "typing.SupportsIndex"], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unique", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 5], "arg_names": ["ar", "return_index", "return_inverse", "return_counts", "axis", "equal_nan"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib._arraysetops_impl.unique#8", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like._ArrayLike"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "UnionType", "items": [{".class": "NoneType"}, "typing.SupportsIndex"], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unique", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib._arraysetops_impl.unique#8", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib._arraysetops_impl.unique#8", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 5], "arg_names": ["ar", "return_index", "return_inverse", "return_counts", "axis", "equal_nan"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "UnionType", "items": [{".class": "NoneType"}, "typing.SupportsIndex"], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unique", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 5], "arg_names": ["ar", "return_index", "return_inverse", "return_counts", "axis", "equal_nan"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib._arraysetops_impl.unique#10", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like._ArrayLike"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, "typing.SupportsIndex"], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unique", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib._arraysetops_impl.unique#10", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib._arraysetops_impl.unique#10", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 5], "arg_names": ["ar", "return_index", "return_inverse", "return_counts", "axis", "equal_nan"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, "typing.SupportsIndex"], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unique", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 5], "arg_names": ["ar", "return_index", "return_inverse", "return_counts", "axis", "equal_nan"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib._arraysetops_impl.unique#12", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like._ArrayLike"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, "typing.SupportsIndex"], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unique", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib._arraysetops_impl.unique#12", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib._arraysetops_impl.unique#12", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 5], "arg_names": ["ar", "return_index", "return_inverse", "return_counts", "axis", "equal_nan"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, "typing.SupportsIndex"], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unique", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 5], "arg_names": ["ar", "return_index", "return_inverse", "return_counts", "axis", "equal_nan"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib._arraysetops_impl.unique#14", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like._ArrayLike"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, "typing.SupportsIndex"], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unique", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib._arraysetops_impl.unique#14", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib._arraysetops_impl.unique#14", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 5], "arg_names": ["ar", "return_index", "return_inverse", "return_counts", "axis", "equal_nan"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, "typing.SupportsIndex"], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unique", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "unique_all": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "numpy.lib._arraysetops_impl.unique_all", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.lib._arraysetops_impl.unique_all", "name": "unique_all", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib._arraysetops_impl.unique_all#0", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like._ArrayLike"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unique_all", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib._arraysetops_impl.unique_all#0", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy.lib._arraysetops_impl.UniqueAllResult"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib._arraysetops_impl.unique_all#0", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.lib._arraysetops_impl.unique_all", "name": "unique_all", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib._arraysetops_impl.unique_all#0", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like._ArrayLike"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unique_all", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib._arraysetops_impl.unique_all#0", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy.lib._arraysetops_impl.UniqueAllResult"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib._arraysetops_impl.unique_all#0", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.lib._arraysetops_impl.unique_all", "name": "unique_all", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unique_all", "ret_type": {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy.lib._arraysetops_impl.UniqueAllResult"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.lib._arraysetops_impl.unique_all", "name": "unique_all", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unique_all", "ret_type": {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy.lib._arraysetops_impl.UniqueAllResult"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib._arraysetops_impl.unique_all#0", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like._ArrayLike"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unique_all", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib._arraysetops_impl.unique_all#0", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy.lib._arraysetops_impl.UniqueAllResult"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib._arraysetops_impl.unique_all#0", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unique_all", "ret_type": {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy.lib._arraysetops_impl.UniqueAllResult"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "unique_counts": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "numpy.lib._arraysetops_impl.unique_counts", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.lib._arraysetops_impl.unique_counts", "name": "unique_counts", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib._arraysetops_impl.unique_counts#0", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like._ArrayLike"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unique_counts", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib._arraysetops_impl.unique_counts#0", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy.lib._arraysetops_impl.UniqueCountsResult"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib._arraysetops_impl.unique_counts#0", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.lib._arraysetops_impl.unique_counts", "name": "unique_counts", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib._arraysetops_impl.unique_counts#0", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like._ArrayLike"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unique_counts", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib._arraysetops_impl.unique_counts#0", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy.lib._arraysetops_impl.UniqueCountsResult"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib._arraysetops_impl.unique_counts#0", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.lib._arraysetops_impl.unique_counts", "name": "unique_counts", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unique_counts", "ret_type": {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy.lib._arraysetops_impl.UniqueCountsResult"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.lib._arraysetops_impl.unique_counts", "name": "unique_counts", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unique_counts", "ret_type": {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy.lib._arraysetops_impl.UniqueCountsResult"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib._arraysetops_impl.unique_counts#0", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like._ArrayLike"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unique_counts", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib._arraysetops_impl.unique_counts#0", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy.lib._arraysetops_impl.UniqueCountsResult"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib._arraysetops_impl.unique_counts#0", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unique_counts", "ret_type": {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy.lib._arraysetops_impl.UniqueCountsResult"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "unique_inverse": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "numpy.lib._arraysetops_impl.unique_inverse", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.lib._arraysetops_impl.unique_inverse", "name": "unique_inverse", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib._arraysetops_impl.unique_inverse#0", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like._ArrayLike"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unique_inverse", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib._arraysetops_impl.unique_inverse#0", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy.lib._arraysetops_impl.UniqueInverseResult"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib._arraysetops_impl.unique_inverse#0", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.lib._arraysetops_impl.unique_inverse", "name": "unique_inverse", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib._arraysetops_impl.unique_inverse#0", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like._ArrayLike"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unique_inverse", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib._arraysetops_impl.unique_inverse#0", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy.lib._arraysetops_impl.UniqueInverseResult"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib._arraysetops_impl.unique_inverse#0", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.lib._arraysetops_impl.unique_inverse", "name": "unique_inverse", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unique_inverse", "ret_type": {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy.lib._arraysetops_impl.UniqueInverseResult"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.lib._arraysetops_impl.unique_inverse", "name": "unique_inverse", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unique_inverse", "ret_type": {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy.lib._arraysetops_impl.UniqueInverseResult"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib._arraysetops_impl.unique_inverse#0", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like._ArrayLike"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unique_inverse", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib._arraysetops_impl.unique_inverse#0", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy.lib._arraysetops_impl.UniqueInverseResult"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib._arraysetops_impl.unique_inverse#0", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unique_inverse", "ret_type": {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy.lib._arraysetops_impl.UniqueInverseResult"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "unique_values": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "numpy.lib._arraysetops_impl.unique_values", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.lib._arraysetops_impl.unique_values", "name": "unique_values", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib._arraysetops_impl.unique_values#0", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like._ArrayLike"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unique_values", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib._arraysetops_impl.unique_values#0", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib._arraysetops_impl.unique_values#0", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.lib._arraysetops_impl.unique_values", "name": "unique_values", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib._arraysetops_impl.unique_values#0", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like._ArrayLike"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unique_values", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib._arraysetops_impl.unique_values#0", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib._arraysetops_impl.unique_values#0", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.lib._arraysetops_impl.unique_values", "name": "unique_values", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unique_values", "ret_type": {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.lib._arraysetops_impl.unique_values", "name": "unique_values", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unique_values", "ret_type": {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib._arraysetops_impl.unique_values#0", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like._ArrayLike"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unique_values", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib._arraysetops_impl.unique_values#0", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib._arraysetops_impl._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib._arraysetops_impl.unique_values#0", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.generic"}, "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unique_values", "ret_type": {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}}, "path": "c:\\Python313\\Lib\\site-packages\\numpy\\lib\\_arraysetops_impl.pyi"}