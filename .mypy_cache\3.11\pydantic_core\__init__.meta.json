{"data_mtime": 1750100860, "dep_lines": [6, 30, 1, 3, 4, 33, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 10, 5, 5, 5, 30, 30, 30], "dependencies": ["pydantic_core._pydantic_core", "pydantic_core.core_schema", "__future__", "sys", "typing", "typing_extensions", "builtins", "_frozen_importlib", "_typeshed", "abc"], "hash": "b0acc252874e7f932f42df4e1cea267f1eb4e783", "id": "pydantic_core", "ignore_all": true, "interface_hash": "61a2e685ef616560679693f7577a80a5f6fa3e2f", "mtime": 1748795470, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Python313\\Lib\\site-packages\\pydantic_core\\__init__.py", "plugin_data": null, "size": 4547, "suppressed": [], "version_id": "1.15.0"}