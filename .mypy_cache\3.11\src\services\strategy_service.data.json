{".class": "MypyFile", "_fullname": "src.services.strategy_service", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "Card": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "src.services.strategy_service.Card", "name": "Card", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "src.services.strategy_service.Card", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 56, "name": "suit", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 57, "name": "rank", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 58, "name": "value", "type": "builtins.int"}], "frozen": false}, "dataclass_tag": {}}, "module_name": "src.services.strategy_service", "mro": ["src.services.strategy_service.Card", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "src.services.strategy_service.Card.__dataclass_fields__", "name": "__dataclass_fields__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__eq__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "src.services.strategy_service.Card.__eq__", "name": "__eq__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["src.services.strategy_service.Card", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__eq__ of Card", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__hash__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "src.services.strategy_service.Card.__hash__", "name": "__hash__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["src.services.strategy_service.Card"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__hash__ of Card", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "suit", "rank", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "src.services.strategy_service.Card.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "suit", "rank", "value"], "arg_types": ["src.services.strategy_service.Card", "builtins.str", "builtins.str", "builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Card", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__lt__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "src.services.strategy_service.Card.__lt__", "name": "__lt__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["src.services.strategy_service.Card", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__lt__ of Card", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "src.services.strategy_service.Card.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "suit"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "rank"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "value"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5], "arg_names": ["suit", "rank", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "src.services.strategy_service.Card.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5], "arg_names": ["suit", "rank", "value"], "arg_types": ["builtins.str", "builtins.str", "builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of Card", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "src.services.strategy_service.Card.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5], "arg_names": ["suit", "rank", "value"], "arg_types": ["builtins.str", "builtins.str", "builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of Card", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "__str__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "src.services.strategy_service.Card.__str__", "name": "__str__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["src.services.strategy_service.Card"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__str__ of Card", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "rank": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "src.services.strategy_service.Card.rank", "name": "rank", "type": "builtins.str"}}, "suit": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "src.services.strategy_service.Card.suit", "name": "suit", "type": "builtins.str"}}, "value": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "src.services.strategy_service.Card.value", "name": "value", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "src.services.strategy_service.Card.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "src.services.strategy_service.Card", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CardAnalyzer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "src.services.strategy_service.CardAnalyzer", "name": "CardAnalyzer", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "src.services.strategy_service.CardAnalyzer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "src.services.strategy_service", "mro": ["src.services.strategy_service.CardAnalyzer", "builtins.object"], "names": {".class": "SymbolTable", "_is_consecutive": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["values"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "src.services.strategy_service.CardAnalyzer._is_consecutive", "name": "_is_consecutive", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["values"], "arg_types": [{".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_is_consecutive of CardAnalyzer", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "src.services.strategy_service.CardAnalyzer._is_consecutive", "name": "_is_consecutive", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["values"], "arg_types": [{".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_is_consecutive of CardAnalyzer", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "analyze_combination": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["cards"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "src.services.strategy_service.CardAnalyzer.analyze_combination", "name": "analyze_combination", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cards"], "arg_types": [{".class": "Instance", "args": ["src.services.strategy_service.Card"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "analyze_combination of CardAnalyzer", "ret_type": "src.services.strategy_service.CardCombination", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "src.services.strategy_service.CardAnalyzer.analyze_combination", "name": "analyze_combination", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cards"], "arg_types": [{".class": "Instance", "args": ["src.services.strategy_service.Card"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "analyze_combination of CardAnalyzer", "ret_type": "src.services.strategy_service.CardCombination", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "create_card": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["suit", "rank"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "src.services.strategy_service.CardAnalyzer.create_card", "name": "create_card", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["suit", "rank"], "arg_types": ["builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_card of CardAnalyzer", "ret_type": "src.services.strategy_service.Card", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "src.services.strategy_service.CardAnalyzer.create_card", "name": "create_card", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["suit", "rank"], "arg_types": ["builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_card of CardAnalyzer", "ret_type": "src.services.strategy_service.Card", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "find_all_combinations": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["cards"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "src.services.strategy_service.CardAnalyzer.find_all_combinations", "name": "find_all_combinations", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cards"], "arg_types": [{".class": "Instance", "args": ["src.services.strategy_service.Card"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "find_all_combinations of CardAnalyzer", "ret_type": {".class": "Instance", "args": ["src.services.strategy_service.CardCombination"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "src.services.strategy_service.CardAnalyzer.find_all_combinations", "name": "find_all_combinations", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cards"], "arg_types": [{".class": "Instance", "args": ["src.services.strategy_service.Card"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "find_all_combinations of CardAnalyzer", "ret_type": {".class": "Instance", "args": ["src.services.strategy_service.CardCombination"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "src.services.strategy_service.CardAnalyzer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "src.services.strategy_service.CardAnalyzer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CardCombination": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "src.services.strategy_service.CardCombination", "name": "CardCombination", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "src.services.strategy_service.CardCombination", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 78, "name": "cards", "type": {".class": "Instance", "args": ["src.services.strategy_service.Card"], "extra_attrs": null, "type_ref": "builtins.list"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 79, "name": "card_type", "type": "src.services.strategy_service.CardType"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 80, "name": "main_value", "type": "builtins.int"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 81, "name": "length", "type": "builtins.int"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 82, "name": "power", "type": "builtins.int"}], "frozen": false}, "dataclass_tag": {}}, "module_name": "src.services.strategy_service", "mro": ["src.services.strategy_service.CardCombination", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "src.services.strategy_service.CardCombination.__dataclass_fields__", "name": "__dataclass_fields__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1, 1], "arg_names": ["self", "cards", "card_type", "main_value", "length", "power"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "src.services.strategy_service.CardCombination.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1, 1], "arg_names": ["self", "cards", "card_type", "main_value", "length", "power"], "arg_types": ["src.services.strategy_service.CardCombination", {".class": "Instance", "args": ["src.services.strategy_service.Card"], "extra_attrs": null, "type_ref": "builtins.list"}, "src.services.strategy_service.CardType", "builtins.int", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of CardCombination", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "src.services.strategy_service.CardCombination.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "cards"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "card_type"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "main_value"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "length"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "power"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-post_init": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "src.services.strategy_service.CardCombination.__mypy-post_init", "name": "__mypy-post_init", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["src.services.strategy_service.CardCombination"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-post_init of CardCombination", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5, 5, 5], "arg_names": ["cards", "card_type", "main_value", "length", "power"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "src.services.strategy_service.CardCombination.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5], "arg_names": ["cards", "card_type", "main_value", "length", "power"], "arg_types": [{".class": "Instance", "args": ["src.services.strategy_service.Card"], "extra_attrs": null, "type_ref": "builtins.list"}, "src.services.strategy_service.CardType", "builtins.int", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of Card<PERSON><PERSON><PERSON>", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "src.services.strategy_service.CardCombination.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5], "arg_names": ["cards", "card_type", "main_value", "length", "power"], "arg_types": [{".class": "Instance", "args": ["src.services.strategy_service.Card"], "extra_attrs": null, "type_ref": "builtins.list"}, "src.services.strategy_service.CardType", "builtins.int", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of Card<PERSON><PERSON><PERSON>", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "__post_init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "src.services.strategy_service.CardCombination.__post_init__", "name": "__post_init__", "type": null}}, "__str__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "src.services.strategy_service.CardCombination.__str__", "name": "__str__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["src.services.strategy_service.CardCombination"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__str__ of CardCombination", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_calculate_power": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "src.services.strategy_service.CardCombination._calculate_power", "name": "_calculate_power", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["src.services.strategy_service.CardCombination"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_calculate_power of CardCombination", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "can_beat": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "src.services.strategy_service.CardCombination.can_beat", "name": "can_beat", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "other"], "arg_types": ["src.services.strategy_service.CardCombination", "src.services.strategy_service.CardCombination"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "can_beat of CardCombination", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "card_type": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "src.services.strategy_service.CardCombination.card_type", "name": "card_type", "type": "src.services.strategy_service.CardType"}}, "cards": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "src.services.strategy_service.CardCombination.cards", "name": "cards", "type": {".class": "Instance", "args": ["src.services.strategy_service.Card"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "length": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "src.services.strategy_service.CardCombination.length", "name": "length", "type": "builtins.int"}}, "main_value": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "src.services.strategy_service.CardCombination.main_value", "name": "main_value", "type": "builtins.int"}}, "power": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "src.services.strategy_service.CardCombination.power", "name": "power", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "src.services.strategy_service.CardCombination.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "src.services.strategy_service.CardCombination", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CardType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["enum.Enum"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "src.services.strategy_service.CardType", "name": "CardType", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_enum"], "fullname": "src.services.strategy_service.CardType", "has_param_spec_type": false, "metaclass_type": "enum.Enum<PERSON>", "metadata": {}, "module_name": "src.services.strategy_service", "mro": ["src.services.strategy_service.CardType", "enum.Enum", "builtins.object"], "names": {".class": "SymbolTable", "BOMB": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "src.services.strategy_service.CardType.BOMB", "name": "BOMB", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "bomb"}, "type_ref": "builtins.str"}}}, "INVALID": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "src.services.strategy_service.CardType.INVALID", "name": "INVALID", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "invalid"}, "type_ref": "builtins.str"}}}, "PAIR": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "src.services.strategy_service.CardType.PAIR", "name": "PAIR", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "pair"}, "type_ref": "builtins.str"}}}, "PAIR_STRAIGHT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "src.services.strategy_service.CardType.PAIR_STRAIGHT", "name": "PAIR_STRAIGHT", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "pair_straight"}, "type_ref": "builtins.str"}}}, "ROCKET": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "src.services.strategy_service.CardType.ROCKET", "name": "ROCKET", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "rocket"}, "type_ref": "builtins.str"}}}, "SINGLE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "src.services.strategy_service.CardType.SINGLE", "name": "SINGLE", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "single"}, "type_ref": "builtins.str"}}}, "STRAIGHT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "src.services.strategy_service.CardType.STRAIGHT", "name": "STRAIGHT", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "straight"}, "type_ref": "builtins.str"}}}, "TRIPLE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "src.services.strategy_service.CardType.TRIPLE", "name": "TRIPLE", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "triple"}, "type_ref": "builtins.str"}}}, "TRIPLE_STRAIGHT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "src.services.strategy_service.CardType.TRIPLE_STRAIGHT", "name": "TRIPLE_STRAIGHT", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "triple_straight"}, "type_ref": "builtins.str"}}}, "TRIPLE_WITH_PAIR": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "src.services.strategy_service.CardType.TRIPLE_WITH_PAIR", "name": "TRIPLE_WITH_PAIR", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "triple_with_pair"}, "type_ref": "builtins.str"}}}, "TRIPLE_WITH_SINGLE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "src.services.strategy_service.CardType.TRIPLE_WITH_SINGLE", "name": "TRIPLE_WITH_SINGLE", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "triple_with_single"}, "type_ref": "builtins.str"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "src.services.strategy_service.CardType.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "src.services.strategy_service.CardType", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Counter": {".class": "SymbolTableNode", "cross_ref": "collections.Counter", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "Enum": {".class": "SymbolTableNode", "cross_ref": "enum.Enum", "kind": "Gdef"}, "F": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "src.services.strategy_service.F", "name": "F", "type": {".class": "AnyType", "missing_import_name": "src.services.strategy_service.F", "source_any": null, "type_of_any": 3}}}, "GameState": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "src.services.strategy_service.GameState", "name": "GameState", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "src.services.strategy_service.GameState", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 139, "name": "my_cards", "type": {".class": "Instance", "args": ["src.services.strategy_service.Card"], "extra_attrs": null, "type_ref": "builtins.list"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 140, "name": "played_cards", "type": {".class": "Instance", "args": ["src.services.strategy_service.Card"], "extra_attrs": null, "type_ref": "builtins.list"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 141, "name": "last_combination", "type": {".class": "UnionType", "items": ["src.services.strategy_service.CardCombination", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 142, "name": "my_position", "type": "src.services.strategy_service.PlayerPosition"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 143, "name": "scores", "type": {".class": "Instance", "args": ["src.services.strategy_service.PlayerPosition", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 144, "name": "remaining_cards", "type": {".class": "Instance", "args": ["src.services.strategy_service.PlayerPosition", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}}], "frozen": false}, "dataclass_tag": {}}, "module_name": "src.services.strategy_service", "mro": ["src.services.strategy_service.GameState", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "src.services.strategy_service.GameState.__dataclass_fields__", "name": "__dataclass_fields__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 1, 1], "arg_names": ["self", "my_cards", "played_cards", "last_combination", "my_position", "scores", "remaining_cards"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "src.services.strategy_service.GameState.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 1, 1], "arg_names": ["self", "my_cards", "played_cards", "last_combination", "my_position", "scores", "remaining_cards"], "arg_types": ["src.services.strategy_service.GameState", {".class": "Instance", "args": ["src.services.strategy_service.Card"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["src.services.strategy_service.Card"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": ["src.services.strategy_service.CardCombination", {".class": "NoneType"}], "uses_pep604_syntax": false}, "src.services.strategy_service.PlayerPosition", {".class": "Instance", "args": ["src.services.strategy_service.PlayerPosition", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["src.services.strategy_service.PlayerPosition", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of GameState", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "src.services.strategy_service.GameState.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "my_cards"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "played_cards"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "last_combination"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "my_position"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "scores"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "remaining_cards"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-post_init": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "src.services.strategy_service.GameState.__mypy-post_init", "name": "__mypy-post_init", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["src.services.strategy_service.GameState"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-post_init of GameState", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5, 5, 5, 5], "arg_names": ["my_cards", "played_cards", "last_combination", "my_position", "scores", "remaining_cards"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "src.services.strategy_service.GameState.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5], "arg_names": ["my_cards", "played_cards", "last_combination", "my_position", "scores", "remaining_cards"], "arg_types": [{".class": "Instance", "args": ["src.services.strategy_service.Card"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["src.services.strategy_service.Card"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": ["src.services.strategy_service.CardCombination", {".class": "NoneType"}], "uses_pep604_syntax": false}, "src.services.strategy_service.PlayerPosition", {".class": "Instance", "args": ["src.services.strategy_service.PlayerPosition", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["src.services.strategy_service.PlayerPosition", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of GameState", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "src.services.strategy_service.GameState.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5], "arg_names": ["my_cards", "played_cards", "last_combination", "my_position", "scores", "remaining_cards"], "arg_types": [{".class": "Instance", "args": ["src.services.strategy_service.Card"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["src.services.strategy_service.Card"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": ["src.services.strategy_service.CardCombination", {".class": "NoneType"}], "uses_pep604_syntax": false}, "src.services.strategy_service.PlayerPosition", {".class": "Instance", "args": ["src.services.strategy_service.PlayerPosition", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["src.services.strategy_service.PlayerPosition", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of GameState", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "__post_init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "src.services.strategy_service.GameState.__post_init__", "name": "__post_init__", "type": null}}, "last_combination": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "src.services.strategy_service.GameState.last_combination", "name": "last_combination", "type": {".class": "UnionType", "items": ["src.services.strategy_service.CardCombination", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "my_cards": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "src.services.strategy_service.GameState.my_cards", "name": "my_cards", "type": {".class": "Instance", "args": ["src.services.strategy_service.Card"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "my_position": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "src.services.strategy_service.GameState.my_position", "name": "my_position", "type": "src.services.strategy_service.PlayerPosition"}}, "played_cards": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "src.services.strategy_service.GameState.played_cards", "name": "played_cards", "type": {".class": "Instance", "args": ["src.services.strategy_service.Card"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "remaining_cards": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "src.services.strategy_service.GameState.remaining_cards", "name": "remaining_cards", "type": {".class": "Instance", "args": ["src.services.strategy_service.PlayerPosition", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "scores": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "src.services.strategy_service.GameState.scores", "name": "scores", "type": {".class": "Instance", "args": ["src.services.strategy_service.PlayerPosition", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "src.services.strategy_service.GameState.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "src.services.strategy_service.GameState", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "LoggerMixin": {".class": "SymbolTableNode", "cross_ref": "src.utils.logger.LoggerMixin", "kind": "Gdef"}, "MonteCarloSimulator": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "src.services.strategy_service.MonteCarloSimulator", "name": "MonteCarloSimulator", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "src.services.strategy_service.MonteCarloSimulator", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "src.services.strategy_service", "mro": ["src.services.strategy_service.MonteCarloSimulator", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "num_simulations"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "src.services.strategy_service.MonteCarloSimulator.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "num_simulations"], "arg_types": ["src.services.strategy_service.MonteCarloSimulator", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of MonteCarloSimulator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_simulate_single_game": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "game_state", "combination"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "src.services.strategy_service.MonteCarloSimulator._simulate_single_game", "name": "_simulate_single_game", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "game_state", "combination"], "arg_types": ["src.services.strategy_service.MonteCarloSimulator", "src.services.strategy_service.GameState", "src.services.strategy_service.CardCombination"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_simulate_single_game of MonteCarloSimulator", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "logger": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "src.services.strategy_service.MonteCarloSimulator.logger", "name": "logger", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "num_simulations": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "src.services.strategy_service.MonteCarloSimulator.num_simulations", "name": "num_simulations", "type": "builtins.int"}}, "simulate_game_outcome": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "game_state", "combination"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "src.services.strategy_service.MonteCarloSimulator.simulate_game_outcome", "name": "simulate_game_outcome", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "game_state", "combination"], "arg_types": ["src.services.strategy_service.MonteCarloSimulator", "src.services.strategy_service.GameState", "src.services.strategy_service.CardCombination"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "simulate_game_outcome of MonteCarloSimulator", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "src.services.strategy_service.MonteCarloSimulator.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "src.services.strategy_service.MonteCarloSimulator", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "PlayerPosition": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["enum.Enum"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "src.services.strategy_service.PlayerPosition", "name": "PlayerPosition", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_enum"], "fullname": "src.services.strategy_service.PlayerPosition", "has_param_spec_type": false, "metaclass_type": "enum.Enum<PERSON>", "metadata": {}, "module_name": "src.services.strategy_service", "mro": ["src.services.strategy_service.PlayerPosition", "enum.Enum", "builtins.object"], "names": {".class": "SymbolTable", "FARMER1": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "src.services.strategy_service.PlayerPosition.FARMER1", "name": "FARMER1", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "farmer1"}, "type_ref": "builtins.str"}}}, "FARMER2": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "src.services.strategy_service.PlayerPosition.FARMER2", "name": "FARMER2", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "farmer2"}, "type_ref": "builtins.str"}}}, "LANDLORD": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "src.services.strategy_service.PlayerPosition.LANDLORD", "name": "LANDLORD", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "landlord"}, "type_ref": "builtins.str"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "src.services.strategy_service.PlayerPosition.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "src.services.strategy_service.PlayerPosition", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "RuleEngine": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "src.services.strategy_service.RuleEngine", "name": "RuleEngine", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "src.services.strategy_service.RuleEngine", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "src.services.strategy_service", "mro": ["src.services.strategy_service.RuleEngine", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "src.services.strategy_service.RuleEngine.__init__", "name": "__init__", "type": null}}, "_count_consecutive_potential": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "values"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "src.services.strategy_service.RuleEngine._count_consecutive_potential", "name": "_count_consecutive_potential", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "values"], "arg_types": ["src.services.strategy_service.RuleEngine", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_count_consecutive_potential of RuleEngine", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "calculate_win_probability": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "game_state"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "src.services.strategy_service.RuleEngine.calculate_win_probability", "name": "calculate_win_probability", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "game_state"], "arg_types": ["src.services.strategy_service.RuleEngine", "src.services.strategy_service.GameState"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "calculate_win_probability of RuleEngine", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "evaluate_hand_strength": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "cards"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "src.services.strategy_service.RuleEngine.evaluate_hand_strength", "name": "evaluate_hand_strength", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "cards"], "arg_types": ["src.services.strategy_service.RuleEngine", {".class": "Instance", "args": ["src.services.strategy_service.Card"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "evaluate_hand_strength of RuleEngine", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "logger": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "src.services.strategy_service.RuleEngine.logger", "name": "logger", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "should_play_aggressive": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "game_state"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "src.services.strategy_service.RuleEngine.should_play_aggressive", "name": "should_play_aggressive", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "game_state"], "arg_types": ["src.services.strategy_service.RuleEngine", "src.services.strategy_service.GameState"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "should_play_aggressive of RuleEngine", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "src.services.strategy_service.RuleEngine.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "src.services.strategy_service.RuleEngine", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Set": {".class": "SymbolTableNode", "cross_ref": "typing.Set", "kind": "Gdef"}, "StrategyResult": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "src.services.strategy_service.StrategyResult", "name": "StrategyResult", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "src.services.strategy_service.StrategyResult", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 156, "name": "recommended_combination", "type": {".class": "UnionType", "items": ["src.services.strategy_service.CardCombination", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 157, "name": "confidence", "type": "builtins.float"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 158, "name": "reasoning", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 159, "name": "alternative_combinations", "type": {".class": "Instance", "args": ["src.services.strategy_service.CardCombination"], "extra_attrs": null, "type_ref": "builtins.list"}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 160, "name": "win_probability", "type": "builtins.float"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 161, "name": "risk_level", "type": "builtins.str"}], "frozen": false}, "dataclass_tag": {}}, "module_name": "src.services.strategy_service", "mro": ["src.services.strategy_service.StrategyResult", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "src.services.strategy_service.StrategyResult.__dataclass_fields__", "name": "__dataclass_fields__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1, 1, 1], "arg_names": ["self", "recommended_combination", "confidence", "reasoning", "alternative_combinations", "win_probability", "risk_level"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "src.services.strategy_service.StrategyResult.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1, 1, 1], "arg_names": ["self", "recommended_combination", "confidence", "reasoning", "alternative_combinations", "win_probability", "risk_level"], "arg_types": ["src.services.strategy_service.StrategyResult", {".class": "UnionType", "items": ["src.services.strategy_service.CardCombination", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.float", "builtins.str", {".class": "Instance", "args": ["src.services.strategy_service.CardCombination"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.float", "builtins.str"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of StrategyResult", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "src.services.strategy_service.StrategyResult.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "recommended_combination"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "confidence"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "reasoning"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "alternative_combinations"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "win_probability"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "risk_level"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5, 5, 5, 5], "arg_names": ["recommended_combination", "confidence", "reasoning", "alternative_combinations", "win_probability", "risk_level"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "src.services.strategy_service.StrategyResult.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5], "arg_names": ["recommended_combination", "confidence", "reasoning", "alternative_combinations", "win_probability", "risk_level"], "arg_types": [{".class": "UnionType", "items": ["src.services.strategy_service.CardCombination", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.float", "builtins.str", {".class": "Instance", "args": ["src.services.strategy_service.CardCombination"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.float", "builtins.str"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of StrategyResult", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "src.services.strategy_service.StrategyResult.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5], "arg_names": ["recommended_combination", "confidence", "reasoning", "alternative_combinations", "win_probability", "risk_level"], "arg_types": [{".class": "UnionType", "items": ["src.services.strategy_service.CardCombination", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.float", "builtins.str", {".class": "Instance", "args": ["src.services.strategy_service.CardCombination"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.float", "builtins.str"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of StrategyResult", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "alternative_combinations": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "src.services.strategy_service.StrategyResult.alternative_combinations", "name": "alternative_combinations", "type": {".class": "Instance", "args": ["src.services.strategy_service.CardCombination"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "confidence": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "src.services.strategy_service.StrategyResult.confidence", "name": "confidence", "type": "builtins.float"}}, "reasoning": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "src.services.strategy_service.StrategyResult.reasoning", "name": "reasoning", "type": "builtins.str"}}, "recommended_combination": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "src.services.strategy_service.StrategyResult.recommended_combination", "name": "recommended_combination", "type": {".class": "UnionType", "items": ["src.services.strategy_service.CardCombination", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "risk_level": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "src.services.strategy_service.StrategyResult.risk_level", "name": "risk_level", "type": "builtins.str"}}, "win_probability": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "src.services.strategy_service.StrategyResult.win_probability", "name": "win_probability", "type": "builtins.float"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "src.services.strategy_service.StrategyResult.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "src.services.strategy_service.StrategyResult", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "StrategyService": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["src.utils.logger.LoggerMixin"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "src.services.strategy_service.StrategyService", "name": "StrategyService", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "src.services.strategy_service.StrategyService", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "src.services.strategy_service", "mro": ["src.services.strategy_service.StrategyService", "src.utils.logger.LoggerMixin", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "src.services.strategy_service.StrategyService.__init__", "name": "__init__", "type": null}}, "_assess_risk_level": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "combination", "game_state"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "src.services.strategy_service.StrategyService._assess_risk_level", "name": "_assess_risk_level", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "combination", "game_state"], "arg_types": ["src.services.strategy_service.StrategyService", "src.services.strategy_service.CardCombination", "src.services.strategy_service.GameState"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_assess_risk_level of StrategyService", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_calculate_confidence": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "scored_combinations"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "src.services.strategy_service.StrategyService._calculate_confidence", "name": "_calculate_confidence", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "scored_combinations"], "arg_types": ["src.services.strategy_service.StrategyService", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["src.services.strategy_service.CardCombination", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_calculate_confidence of StrategyService", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_evaluate_combination": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "combination", "game_state"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "src.services.strategy_service.StrategyService._evaluate_combination", "name": "_evaluate_combination", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "combination", "game_state"], "arg_types": ["src.services.strategy_service.StrategyService", "src.services.strategy_service.CardCombination", "src.services.strategy_service.GameState"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_evaluate_combination of StrategyService", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_evaluate_hand_balance": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "cards"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "src.services.strategy_service.StrategyService._evaluate_hand_balance", "name": "_evaluate_hand_balance", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "cards"], "arg_types": ["src.services.strategy_service.StrategyService", {".class": "Instance", "args": ["src.services.strategy_service.Card"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_evaluate_hand_balance of StrategyService", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_filter_valid_combinations": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "combinations", "last_combination"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "src.services.strategy_service.StrategyService._filter_valid_combinations", "name": "_filter_valid_combinations", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "combinations", "last_combination"], "arg_types": ["src.services.strategy_service.StrategyService", {".class": "Instance", "args": ["src.services.strategy_service.CardCombination"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": ["src.services.strategy_service.CardCombination", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_filter_valid_combinations of StrategyService", "ret_type": {".class": "Instance", "args": ["src.services.strategy_service.CardCombination"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_generate_reasoning": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "combination", "game_state"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "src.services.strategy_service.StrategyService._generate_reasoning", "name": "_generate_reasoning", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "combination", "game_state"], "arg_types": ["src.services.strategy_service.StrategyService", "src.services.strategy_service.CardCombination", "src.services.strategy_service.GameState"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_generate_reasoning of StrategyService", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_init_deep_learning_model": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "src.services.strategy_service.StrategyService._init_deep_learning_model", "name": "_init_deep_learning_model", "type": null}}, "_parse_cards": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "card_strings"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "src.services.strategy_service.StrategyService._parse_cards", "name": "_parse_cards", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "card_strings"], "arg_types": ["src.services.strategy_service.StrategyService", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_parse_cards of StrategyService", "ret_type": {".class": "Instance", "args": ["src.services.strategy_service.Card"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "analyze_strategy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "game_state"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "src.services.strategy_service.StrategyService.analyze_strategy", "name": "analyze_strategy", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "game_state"], "arg_types": ["src.services.strategy_service.StrategyService", "src.services.strategy_service.GameState"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "analyze_strategy of StrategyService", "ret_type": "src.services.strategy_service.StrategyResult", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "card_analyzer": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "src.services.strategy_service.StrategyService.card_analyzer", "name": "card_analyzer", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "cleanup": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "src.services.strategy_service.StrategyService.cleanup", "name": "cleanup", "type": null}}, "dl_model": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "src.services.strategy_service.StrategyService.dl_model", "name": "dl_model", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "get_card_play_suggestion": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "my_cards", "last_played", "position"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "src.services.strategy_service.StrategyService.get_card_play_suggestion", "name": "get_card_play_suggestion", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "my_cards", "last_played", "position"], "arg_types": ["src.services.strategy_service.StrategyService", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_card_play_suggestion of StrategyService", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "monte_carlo": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "src.services.strategy_service.StrategyService.monte_carlo", "name": "monte_carlo", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "rule_engine": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "src.services.strategy_service.StrategyService.rule_engine", "name": "rule_engine", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "src.services.strategy_service.StrategyService.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "src.services.strategy_service.StrategyService", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TORCH_AVAILABLE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "src.services.strategy_service.TORCH_AVAILABLE", "name": "TORCH_AVAILABLE", "type": "builtins.bool"}}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.services.strategy_service.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.services.strategy_service.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.services.strategy_service.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.services.strategy_service.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.services.strategy_service.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.services.strategy_service.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "dataclass": {".class": "SymbolTableNode", "cross_ref": "dataclasses.dataclass", "kind": "Gdef"}, "deepcopy": {".class": "SymbolTableNode", "cross_ref": "copy.deepcopy", "kind": "Gdef"}, "defaultdict": {".class": "SymbolTableNode", "cross_ref": "collections.defaultdict", "kind": "Gdef"}, "field": {".class": "SymbolTableNode", "cross_ref": "dataclasses.field", "kind": "Gdef"}, "math": {".class": "SymbolTableNode", "cross_ref": "math", "kind": "Gdef"}, "nn": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "src.services.strategy_service.nn", "name": "nn", "type": {".class": "AnyType", "missing_import_name": "src.services.strategy_service.nn", "source_any": null, "type_of_any": 3}}}, "np": {".class": "SymbolTableNode", "cross_ref": "numpy", "kind": "Gdef"}, "random": {".class": "SymbolTableNode", "cross_ref": "random", "kind": "Gdef"}, "torch": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "src.services.strategy_service.torch", "name": "torch", "type": {".class": "AnyType", "missing_import_name": "src.services.strategy_service.torch", "source_any": null, "type_of_any": 3}}}}, "path": "E:\\db\\0617\\src\\services\\strategy_service.py"}