#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
斗地主AI助手 - 主启动脚本

这是应用程序的主入口点，负责初始化所有服务和启动用户界面。

作者: AI Assistant
版本: 1.0.0
创建时间: 2025年
"""

import sys
import os
import asyncio
from pathlib import Path
from typing import Optional

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

import flet as ft
from loguru import logger

# 导入核心模块
from src.utils.config import ConfigManager
from src.utils.logger import setup_logger, LoggerMixin
from src.services.window_service import WindowService
from src.services.vision_service import VisionService  # 编码问题已修复
from src.services.ocr_service import OCRService  # 编码问题已修复
from src.services.strategy_service import StrategyService
from src.ui.main_view import MainView  # 使用完整的主视图
from src.ui.simple_main_view import SimpleMainView  # 保留简单版本作为备用
from src.ui.simple_settings_view import SimpleSettingsView  # 简单设置视图
from src.ui.simple_window_selector import SimpleWindowSelector  # 简单窗口选择器


class DoudizhuAIApp(LoggerMixin):
    """斗地主AI助手主应用程序"""
    
    def __init__(self):
        """初始化应用程序"""
        self.config = None
        self.services = {}
        self.views = {}
        self.current_view = "main"
        
        # Flet页面对象
        self.page: Optional[ft.Page] = None
        
        # 应用状态
        self.is_running = False
        self.selected_window = None
    
    async def initialize(self):
        """异步初始化应用程序"""
        try:
            self.logger.info("开始初始化斗地主AI助手...")
            
            # 1. 初始化配置管理器
            self.logger.info("初始化配置管理器...")
            self.config = ConfigManager()
            
            # 2. 初始化核心服务
            self.logger.info("初始化核心服务...")
            await self._initialize_services()
            
            # 3. 初始化UI视图
            self.logger.info("初始化用户界面...")
            self._initialize_views()
            
            self.logger.success("应用程序初始化完成")
            
        except Exception as e:
            self.logger.error(f"应用程序初始化失败: {e}")
            raise
    
    async def _initialize_services(self):
        """初始化核心服务"""
        try:
            # 窗口服务
            self.logger.info("初始化窗口服务...")
            self.services['window'] = WindowService()

            # 视觉服务
            self.logger.info("初始化视觉服务...")
            self.services['vision'] = VisionService(self.config)

            # OCR服务
            self.logger.info("初始化OCR服务...")
            self.services['ocr'] = OCRService(self.config)

            # 策略服务
            self.logger.info("初始化策略服务...")
            self.services['strategy'] = StrategyService()

            # 测试服务连接
            await self._test_services()

        except Exception as e:
            self.logger.error(f"服务初始化失败: {e}")
            raise
    
    async def _test_services(self):
        """测试服务连接和功能"""
        try:
            self.logger.info("测试服务功能...")

            # 测试窗口服务
            windows = self.services['window'].get_all_windows()
            self.logger.info(f"发现 {len(windows)} 个窗口")

            # 测试OCR服务
            ocr_engines = self.services['ocr'].get_available_engines()
            self.logger.info(f"可用OCR引擎: {ocr_engines}")

            # 测试策略服务
            strategy_status = self.services['strategy'].get_status()
            self.logger.info(f"策略服务状态: {strategy_status}")

            self.logger.success("所有服务测试通过")

        except Exception as e:
            self.logger.warning(f"服务测试出现问题: {e}")
    
    def _initialize_views(self):
        """初始化UI视图"""
        try:
            self.logger.info("初始化完整UI视图...")

            # 主视图 - 使用完整版本
            self.views['main'] = MainView(
                page=self.page,
                config_manager=self.config,
                window_service=self.services['window'],
                vision_service=self.services['vision'],
                ocr_service=self.services['ocr'],
                strategy_service=self.services['strategy'],
                on_settings_click=self._show_settings,
                on_window_select_click=self._show_window_selector
            )

            # 设置视图
            self.views['settings'] = SimpleSettingsView(
                page=self.page,
                config_manager=self.config,
                on_back_click=self._show_main
            )

            # 窗口选择器
            self.views['window_selector'] = SimpleWindowSelector(
                page=self.page,
                window_service=self.services['window'],
                config_manager=self.config,
                on_window_selected=self._on_window_selected,
                on_close=self._show_main
            )

            self.logger.info("完整UI视图初始化完成")

        except Exception as e:
            self.logger.error(f"视图初始化失败: {e}")
            raise
    
    def _show_main(self):
        """显示主视图"""
        self.current_view = "main"
        self._update_page_content()
    
    def _show_settings(self):
        """显示设置视图"""
        self.current_view = "settings"
        self._update_page_content()
    
    def _show_window_selector(self):
        """显示窗口选择器"""
        self.logger.info("切换到窗口选择器视图")
        self.current_view = "window_selector"
        self.logger.info(f"当前视图设置为: {self.current_view}")
        self._update_page_content()
    
    def _on_window_selected(self, window_info):
        """窗口选择回调"""
        self.selected_window = window_info
        window_title = window_info.get('title', '未知窗口') if isinstance(window_info, dict) else str(window_info)
        self.logger.info(f"选择了窗口: {window_title}")

        # 通知主视图窗口已选择
        if 'main' in self.views:
            self.views['main'].set_target_window(window_info)

        # 返回主视图
        self._show_main()

        # 在视图切换完成后更新状态
        self._update_connection_status("已连接")
    
    def _update_page_content(self):
        """更新页面内容"""
        self.logger.info(f"更新页面内容，当前视图: {self.current_view}")
        self.logger.info(f"可用视图: {list(self.views.keys())}")

        if self.page and self.current_view in self.views:
            self.logger.info("开始切换视图...")
            # 清除当前内容
            self.page.controls.clear()

            # 添加新视图
            current_view = self.views[self.current_view]
            self.logger.info(f"添加视图: {type(current_view).__name__}")
            self.page.add(current_view)

            # 更新页面
            self.page.update()
            self.logger.info("页面更新完成")
        else:
            self.logger.error(f"无法切换视图: page={self.page is not None}, view_exists={self.current_view in self.views}")

    def _update_connection_status(self, status: str):
        """更新连接状态"""
        try:
            self.logger.info(f"更新连接状态: {status}")

            # 确保在主视图中更新状态
            if self.current_view == "main" and 'main' in self.views:
                main_view = self.views['main']

                # 直接更新状态栏
                if hasattr(main_view, 'status_bar') and main_view.status_bar:
                    status_bar = main_view.status_bar
                    if len(status_bar.controls) >= 6:
                        status_container = status_bar.controls[5]
                        if hasattr(status_container, 'content'):
                            status_row = status_container.content
                            if hasattr(status_row, 'controls') and len(status_row.controls) >= 2:
                                # 更新状态文本和颜色
                                if status == "已连接":
                                    color = ft.Colors.GREEN
                                    bg_color = ft.Colors.GREEN_50
                                else:
                                    color = ft.Colors.RED
                                    bg_color = ft.Colors.RED_50

                                status_row.controls[0].color = color
                                status_row.controls[1].value = status
                                status_row.controls[1].color = color
                                status_container.bgcolor = bg_color

                                # 更新页面
                                if self.page:
                                    self.page.update()
                                    self.logger.info(f"连接状态更新完成: {status}")
                                else:
                                    self.logger.warning("页面对象不存在")
                            else:
                                self.logger.warning("状态行控件不足")
                        else:
                            self.logger.warning("状态容器没有content")
                    else:
                        self.logger.warning("状态栏控件数量不足")
                else:
                    self.logger.warning("状态栏不存在")
            else:
                self.logger.warning(f"当前不在主视图: {self.current_view}")

        except Exception as e:
            self.logger.error(f"更新连接状态失败: {e}")

    def setup_page(self, page: ft.Page):
        """设置Flet页面"""
        self.page = page
        
        # 页面基本设置
        page.title = "斗地主AI助手"
        page.window_width = 1200
        page.window_height = 800
        page.window_min_width = 800
        page.window_min_height = 600
        page.theme_mode = ft.ThemeMode.LIGHT
        page.padding = 0
        
        # 设置页面图标（如果有的话）
        # page.window_icon = "assets/icon.ico"
        
        # 页面关闭事件
        page.on_window_event = self._on_window_event
        
        # 显示初始视图
        self._update_page_content()
        
        self.logger.info("Flet页面设置完成")
    
    def _on_window_event(self, e):
        """窗口事件处理"""
        if e.data == "close":
            self.logger.info("用户关闭应用程序")
            self.shutdown()
    
    def start_monitoring(self):
        """开始监控"""
        if 'main' in self.views:
            self.views['main'].start_monitoring()
            self.is_running = True
            self.logger.info("开始游戏监控")
    
    def stop_monitoring(self):
        """停止监控"""
        if 'main' in self.views:
            self.views['main'].stop_monitoring()
            self.is_running = False
            self.logger.info("停止游戏监控")
    
    def shutdown(self):
        """关闭应用程序"""
        try:
            self.logger.info("开始关闭应用程序...")
            
            # 停止监控
            self.stop_monitoring()
            
            # 保存配置
            if self.config:
                self.config.save()
                self.logger.info("配置已保存")
            
            # 清理服务资源
            for service_name, service in self.services.items():
                if hasattr(service, 'cleanup'):
                    service.cleanup()
                    self.logger.info(f"{service_name}服务已清理")
            
            self.logger.success("应用程序已安全关闭")
            
        except Exception as e:
            self.logger.error(f"关闭应用程序时出错: {e}")
        
        finally:
            # 强制退出
            if self.page:
                self.page.window_destroy()


async def main_async(page: ft.Page):
    """异步主函数"""
    app = None
    try:
        # 创建应用程序实例
        app = DoudizhuAIApp()
        
        # 初始化应用程序
        await app.initialize()
        
        # 设置页面
        app.setup_page(page)
        
        # 保持应用程序运行
        logger.info("斗地主AI助手已启动")
        
    except Exception as e:
        logger.error(f"应用程序启动失败: {e}")
        if page:
            # 显示错误信息
            page.add(
                ft.Container(
                    content=ft.Column([
                        ft.Icon(ft.Icons.ERROR, size=64, color=ft.Colors.RED),
                        ft.Text(
                            "应用程序启动失败",
                            size=24,
                            weight=ft.FontWeight.BOLD,
                            color=ft.Colors.RED
                        ),
                        ft.Text(
                            str(e),
                            size=14,
                            color=ft.Colors.GREY_700
                        ),
                        ft.ElevatedButton(
                            "退出",
                            on_click=lambda _: page.window_destroy(),
                            bgcolor=ft.Colors.RED,
                            color=ft.Colors.WHITE
                        )
                    ], horizontal_alignment=ft.CrossAxisAlignment.CENTER),
                    alignment=ft.alignment.center,
                    expand=True
                )
            )
            page.update()


def main(page: ft.Page):
    """Flet主函数"""
    # 运行异步主函数
    asyncio.run(main_async(page))


if __name__ == "__main__":
    try:
        # 设置日志
        setup_logger()
        
        logger.info("="*50)
        logger.info("斗地主AI助手启动中...")
        logger.info(f"Python版本: {sys.version}")
        logger.info(f"工作目录: {os.getcwd()}")
        logger.info(f"项目根目录: {project_root}")
        logger.info("="*50)
        
        # 检查依赖
        try:
            import cv2
            import numpy as np
            import PIL
            logger.info("核心依赖检查通过")
        except ImportError as e:
            logger.error(f"缺少必要依赖: {e}")
            logger.error("请运行: pip install -r requirements.txt")
            sys.exit(1)
        
        # 启动Flet应用
        ft.app(
            target=main,
            assets_dir="assets",  # 如果有资源文件夹
            port=8550  # 端口
        )
        
    except KeyboardInterrupt:
        logger.info("用户中断程序")
    except Exception as e:
        logger.error(f"程序运行出错: {e}")
        sys.exit(1)
    finally:
        logger.info("程序已退出")