{".class": "MypyFile", "_fullname": "cv2.ogl", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "BUFFER_ARRAY_BUFFER": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.ogl.BUFFER_ARRAY_BUFFER", "name": "BUFFER_ARRAY_BUFFER", "type": "builtins.int"}}, "BUFFER_ELEMENT_ARRAY_BUFFER": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.ogl.BUFFER_ELEMENT_ARRAY_BUFFER", "name": "BUFFER_ELEMENT_ARRAY_BUFFER", "type": "builtins.int"}}, "BUFFER_PIXEL_PACK_BUFFER": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.ogl.BUFFER_PIXEL_PACK_BUFFER", "name": "BUFFER_PIXEL_PACK_BUFFER", "type": "builtins.int"}}, "BUFFER_PIXEL_UNPACK_BUFFER": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.ogl.BUFFER_PIXEL_UNPACK_BUFFER", "name": "BUFFER_PIXEL_UNPACK_BUFFER", "type": "builtins.int"}}, "BUFFER_READ_ONLY": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.ogl.BUFFER_READ_ONLY", "name": "BUFFER_READ_ONLY", "type": "builtins.int"}}, "BUFFER_READ_WRITE": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.ogl.BUFFER_READ_WRITE", "name": "BUFFER_READ_WRITE", "type": "builtins.int"}}, "BUFFER_WRITE_ONLY": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.ogl.BUFFER_WRITE_ONLY", "name": "BUFFER_WRITE_ONLY", "type": "builtins.int"}}, "Buffer_ARRAY_BUFFER": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.ogl.<PERSON><PERSON><PERSON>_ARRAY_BUFFER", "name": "Buffer_ARRAY_BUFFER", "type": "builtins.int"}}, "Buffer_Access": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "cv2.ogl.<PERSON><PERSON>er_Access", "line": 35, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "builtins.int"}}, "Buffer_ELEMENT_ARRAY_BUFFER": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.ogl.<PERSON><PERSON><PERSON>_ELEMENT_ARRAY_BUFFER", "name": "Buffer_ELEMENT_ARRAY_BUFFER", "type": "builtins.int"}}, "Buffer_PIXEL_PACK_BUFFER": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.ogl.<PERSON><PERSON><PERSON>_PIXEL_PACK_BUFFER", "name": "Buffer_PIXEL_PACK_BUFFER", "type": "builtins.int"}}, "Buffer_PIXEL_UNPACK_BUFFER": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.ogl.<PERSON><PERSON><PERSON>_PIXEL_UNPACK_BUFFER", "name": "Buffer_PIXEL_UNPACK_BUFFER", "type": "builtins.int"}}, "Buffer_READ_ONLY": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.ogl.<PERSON><PERSON><PERSON>_READ_ONLY", "name": "Buffer_READ_ONLY", "type": "builtins.int"}}, "Buffer_READ_WRITE": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.ogl.<PERSON><PERSON><PERSON>_READ_WRITE", "name": "Buffer_READ_WRITE", "type": "builtins.int"}}, "Buffer_Target": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "cv2.ogl.<PERSON><PERSON><PERSON>_Target", "line": 26, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "builtins.int"}}, "Buffer_WRITE_ONLY": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.ogl.<PERSON><PERSON><PERSON>_WRITE_ONLY", "name": "Buffer_WRITE_ONLY", "type": "builtins.int"}}, "LINES": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.ogl.LINES", "name": "LINES", "type": "builtins.int"}}, "LINE_LOOP": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.ogl.LINE_LOOP", "name": "LINE_LOOP", "type": "builtins.int"}}, "LINE_STRIP": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.ogl.LINE_STRIP", "name": "LINE_STRIP", "type": "builtins.int"}}, "POINTS": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.ogl.POINTS", "name": "POINTS", "type": "builtins.int"}}, "POLYGON": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.ogl.POLYGON", "name": "POLYGON", "type": "builtins.int"}}, "QUADS": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.ogl.QUADS", "name": "QUADS", "type": "builtins.int"}}, "QUAD_STRIP": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.ogl.QUAD_STRIP", "name": "QUAD_STRIP", "type": "builtins.int"}}, "RenderModes": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "cv2.ogl.RenderModes", "line": 14, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "builtins.int"}}, "TEXTURE2D_DEPTH_COMPONENT": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.ogl.TEXTURE2D_DEPTH_COMPONENT", "name": "TEXTURE2D_DEPTH_COMPONENT", "type": "builtins.int"}}, "TEXTURE2D_NONE": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.ogl.TEXTURE2D_NONE", "name": "TEXTURE2D_NONE", "type": "builtins.int"}}, "TEXTURE2D_RGB": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.ogl.TEXTURE2D_RGB", "name": "TEXTURE2D_RGB", "type": "builtins.int"}}, "TEXTURE2D_RGBA": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.ogl.TEXTURE2D_RGBA", "name": "TEXTURE2D_RGBA", "type": "builtins.int"}}, "TRIANGLES": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.ogl.TRIANGLES", "name": "TRIANGLES", "type": "builtins.int"}}, "TRIANGLE_FAN": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.ogl.TRIANGLE_FAN", "name": "TRIANGLE_FAN", "type": "builtins.int"}}, "TRIANGLE_STRIP": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.ogl.TRIANGLE_STRIP", "name": "TRIANGLE_STRIP", "type": "builtins.int"}}, "Texture2D_DEPTH_COMPONENT": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.ogl.Texture2D_DEPTH_COMPONENT", "name": "Texture2D_DEPTH_COMPONENT", "type": "builtins.int"}}, "Texture2D_Format": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "cv2.ogl.Texture2D_Format", "line": 46, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "builtins.int"}}, "Texture2D_NONE": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.ogl.Texture2D_NONE", "name": "Texture2D_NONE", "type": "builtins.int"}}, "Texture2D_RGB": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.ogl.Texture2D_RGB", "name": "Texture2D_RGB", "type": "builtins.int"}}, "Texture2D_RGBA": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.ogl.Texture2D_RGBA", "name": "Texture2D_RGBA", "type": "builtins.int"}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "cv2.ogl.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.ogl.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.ogl.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.ogl.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.ogl.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.ogl.__package__", "name": "__package__", "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.ogl.__path__", "name": "__path__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.ogl.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}}, "path": "c:\\Python313\\Lib\\site-packages\\cv2\\ogl\\__init__.pyi"}