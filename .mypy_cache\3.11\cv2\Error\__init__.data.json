{".class": "MypyFile", "_fullname": "cv2.<PERSON><PERSON><PERSON>", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "BAD_ALIGN": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.Error.BAD_ALIGN", "name": "BAD_ALIGN", "type": "builtins.int"}}, "BAD_ALPHA_CHANNEL": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.Error.BAD_ALPHA_CHANNEL", "name": "BAD_ALPHA_CHANNEL", "type": "builtins.int"}}, "BAD_CALL_BACK": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.Error.BAD_CALL_BACK", "name": "BAD_CALL_BACK", "type": "builtins.int"}}, "BAD_COI": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.Error.BAD_COI", "name": "BAD_COI", "type": "builtins.int"}}, "BAD_DATA_PTR": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.Error.BAD_DATA_PTR", "name": "BAD_DATA_PTR", "type": "builtins.int"}}, "BAD_DEPTH": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.Error.BAD_DEPTH", "name": "BAD_DEPTH", "type": "builtins.int"}}, "BAD_IMAGE_SIZE": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.Error.BAD_IMAGE_SIZE", "name": "BAD_IMAGE_SIZE", "type": "builtins.int"}}, "BAD_MODEL_OR_CH_SEQ": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.Error.BAD_MODEL_OR_CH_SEQ", "name": "BAD_MODEL_OR_CH_SEQ", "type": "builtins.int"}}, "BAD_NUM_CHANNEL1U": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.Error.BAD_NUM_CHANNEL1U", "name": "BAD_NUM_CHANNEL1U", "type": "builtins.int"}}, "BAD_NUM_CHANNELS": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.Error.BAD_NUM_CHANNELS", "name": "BAD_NUM_CHANNELS", "type": "builtins.int"}}, "BAD_OFFSET": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.Error.BAD_OFFSET", "name": "BAD_OFFSET", "type": "builtins.int"}}, "BAD_ORDER": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.Error.BAD_ORDER", "name": "BAD_ORDER", "type": "builtins.int"}}, "BAD_ORIGIN": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.Error.BAD_ORIGIN", "name": "BAD_ORIGIN", "type": "builtins.int"}}, "BAD_ROISIZE": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.Error.BAD_ROISIZE", "name": "BAD_ROISIZE", "type": "builtins.int"}}, "BAD_STEP": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.Error.BAD_STEP", "name": "BAD_STEP", "type": "builtins.int"}}, "BAD_TILE_SIZE": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.Error.BAD_TILE_SIZE", "name": "BAD_TILE_SIZE", "type": "builtins.int"}}, "BadAlign": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.<PERSON><PERSON><PERSON><PERSON>", "name": "BadAlign", "type": "builtins.int"}}, "BadAlphaChannel": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.<PERSON><PERSON><PERSON><PERSON>", "name": "BadAlphaChannel", "type": "builtins.int"}}, "BadCOI": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.<PERSON><PERSON><PERSON>.BadCOI", "name": "BadCOI", "type": "builtins.int"}}, "BadCallBack": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.<PERSON><PERSON><PERSON><PERSON>CallBack", "name": "BadCallBack", "type": "builtins.int"}}, "BadDataPtr": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.<PERSON><PERSON><PERSON>.BadDataPtr", "name": "BadDataPtr", "type": "builtins.int"}}, "BadDepth": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.<PERSON><PERSON><PERSON><PERSON>", "name": "Bad<PERSON>ept<PERSON>", "type": "builtins.int"}}, "BadImageSize": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.E<PERSON><PERSON>.BadImageSize", "name": "BadImageSize", "type": "builtins.int"}}, "BadModelOrChSeq": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.<PERSON><PERSON><PERSON>.BadModelOrChSeq", "name": "BadModelOrChSeq", "type": "builtins.int"}}, "BadNumChannel1U": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.<PERSON><PERSON><PERSON>.BadNumChannel1U", "name": "BadNumChannel1U", "type": "builtins.int"}}, "BadNumChannels": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.<PERSON><PERSON><PERSON><PERSON>", "name": "BadNumChannels", "type": "builtins.int"}}, "BadOffset": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.<PERSON><PERSON><PERSON><PERSON>", "name": "BadOffset", "type": "builtins.int"}}, "BadOrder": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.<PERSON><PERSON><PERSON><PERSON>", "name": "BadOrder", "type": "builtins.int"}}, "BadOrigin": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.<PERSON><PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "builtins.int"}}, "BadROISize": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.<PERSON><PERSON><PERSON>.BadROISize", "name": "BadROISize", "type": "builtins.int"}}, "BadStep": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.<PERSON><PERSON><PERSON>.BadStep", "name": "BadStep", "type": "builtins.int"}}, "BadTileSize": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.<PERSON><PERSON><PERSON>.BadTileSize", "name": "BadTileSize", "type": "builtins.int"}}, "Code": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "cv2.Error.Code", "line": 114, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "builtins.int"}}, "GPU_API_CALL_ERROR": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.Error.GPU_API_CALL_ERROR", "name": "GPU_API_CALL_ERROR", "type": "builtins.int"}}, "GPU_NOT_SUPPORTED": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.Error.GPU_NOT_SUPPORTED", "name": "GPU_NOT_SUPPORTED", "type": "builtins.int"}}, "GpuApiCallError": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.E<PERSON>r.GpuApiCallError", "name": "GpuApiCallError", "type": "builtins.int"}}, "GpuNotSupported": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.E<PERSON><PERSON>.GpuNotSupported", "name": "GpuNotSupported", "type": "builtins.int"}}, "HEADER_IS_NULL": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.Error.HEADER_IS_NULL", "name": "HEADER_IS_NULL", "type": "builtins.int"}}, "HeaderIsNull": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.<PERSON><PERSON><PERSON>.HeaderIsNull", "name": "Header<PERSON>s<PERSON><PERSON>", "type": "builtins.int"}}, "MASK_IS_TILED": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.Error.MASK_IS_TILED", "name": "MASK_IS_TILED", "type": "builtins.int"}}, "MaskIsTiled": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.E<PERSON><PERSON>.MaskIsTiled", "name": "MaskIsTiled", "type": "builtins.int"}}, "OPEN_CLAPI_CALL_ERROR": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.Error.OPEN_CLAPI_CALL_ERROR", "name": "OPEN_CLAPI_CALL_ERROR", "type": "builtins.int"}}, "OPEN_CLDOUBLE_NOT_SUPPORTED": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.Error.OPEN_CLDOUBLE_NOT_SUPPORTED", "name": "OPEN_CLDOUBLE_NOT_SUPPORTED", "type": "builtins.int"}}, "OPEN_CLINIT_ERROR": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.E<PERSON>r.OPEN_CLINIT_ERROR", "name": "OPEN_CLINIT_ERROR", "type": "builtins.int"}}, "OPEN_CLNO_AMDBLAS_FFT": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.Error.OPEN_CLNO_AMDBLAS_FFT", "name": "OPEN_CLNO_AMDBLAS_FFT", "type": "builtins.int"}}, "OPEN_GL_API_CALL_ERROR": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.Error.OPEN_GL_API_CALL_ERROR", "name": "OPEN_GL_API_CALL_ERROR", "type": "builtins.int"}}, "OPEN_GL_NOT_SUPPORTED": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.E<PERSON>r.OPEN_GL_NOT_SUPPORTED", "name": "OPEN_GL_NOT_SUPPORTED", "type": "builtins.int"}}, "OpenCLApiCallError": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.Error.OpenCLApiCallError", "name": "OpenCLApiCallError", "type": "builtins.int"}}, "OpenCLDoubleNotSupported": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.Error.OpenCLDoubleNotSupported", "name": "OpenCLDoubleNotSupported", "type": "builtins.int"}}, "OpenCLInitError": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.Error.OpenCLInitError", "name": "OpenCLInitError", "type": "builtins.int"}}, "OpenCLNoAMDBlasFft": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.Error.OpenCLNoAMDBlasFft", "name": "OpenCLNoAMDBlasFft", "type": "builtins.int"}}, "OpenGlApiCallError": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.Error.OpenGlApiCallError", "name": "OpenGlApiCallError", "type": "builtins.int"}}, "OpenGlNotSupported": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.Error.OpenGlNotSupported", "name": "OpenGlNotSupported", "type": "builtins.int"}}, "STS_ASSERT": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.Error.STS_ASSERT", "name": "STS_ASSERT", "type": "builtins.int"}}, "STS_AUTO_TRACE": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.E<PERSON>r.STS_AUTO_TRACE", "name": "STS_AUTO_TRACE", "type": "builtins.int"}}, "STS_BACK_TRACE": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.Error.STS_BACK_TRACE", "name": "STS_BACK_TRACE", "type": "builtins.int"}}, "STS_BAD_ARG": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.Error.STS_BAD_ARG", "name": "STS_BAD_ARG", "type": "builtins.int"}}, "STS_BAD_FLAG": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.Error.STS_BAD_FLAG", "name": "STS_BAD_FLAG", "type": "builtins.int"}}, "STS_BAD_FUNC": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.Error.STS_BAD_FUNC", "name": "STS_BAD_FUNC", "type": "builtins.int"}}, "STS_BAD_MASK": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.Error.STS_BAD_MASK", "name": "STS_BAD_MASK", "type": "builtins.int"}}, "STS_BAD_MEM_BLOCK": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.Error.STS_BAD_MEM_BLOCK", "name": "STS_BAD_MEM_BLOCK", "type": "builtins.int"}}, "STS_BAD_POINT": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.Error.STS_BAD_POINT", "name": "STS_BAD_POINT", "type": "builtins.int"}}, "STS_BAD_SIZE": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.Error.STS_BAD_SIZE", "name": "STS_BAD_SIZE", "type": "builtins.int"}}, "STS_DIV_BY_ZERO": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.Error.STS_DIV_BY_ZERO", "name": "STS_DIV_BY_ZERO", "type": "builtins.int"}}, "STS_ERROR": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.E<PERSON>r.STS_ERROR", "name": "STS_ERROR", "type": "builtins.int"}}, "STS_FILTER_OFFSET_ERR": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.Error.STS_FILTER_OFFSET_ERR", "name": "STS_FILTER_OFFSET_ERR", "type": "builtins.int"}}, "STS_FILTER_STRUCT_CONTENT_ERR": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.Error.STS_FILTER_STRUCT_CONTENT_ERR", "name": "STS_FILTER_STRUCT_CONTENT_ERR", "type": "builtins.int"}}, "STS_INPLACE_NOT_SUPPORTED": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.Error.STS_INPLACE_NOT_SUPPORTED", "name": "STS_INPLACE_NOT_SUPPORTED", "type": "builtins.int"}}, "STS_INTERNAL": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.E<PERSON>r.STS_INTERNAL", "name": "STS_INTERNAL", "type": "builtins.int"}}, "STS_KERNEL_STRUCT_CONTENT_ERR": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.Error.STS_KERNEL_STRUCT_CONTENT_ERR", "name": "STS_KERNEL_STRUCT_CONTENT_ERR", "type": "builtins.int"}}, "STS_NOT_IMPLEMENTED": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.Error.STS_NOT_IMPLEMENTED", "name": "STS_NOT_IMPLEMENTED", "type": "builtins.int"}}, "STS_NO_CONV": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.E<PERSON>r.STS_NO_CONV", "name": "STS_NO_CONV", "type": "builtins.int"}}, "STS_NO_MEM": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.<PERSON><PERSON>r.STS_NO_MEM", "name": "STS_NO_MEM", "type": "builtins.int"}}, "STS_NULL_PTR": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.Error.STS_NULL_PTR", "name": "STS_NULL_PTR", "type": "builtins.int"}}, "STS_OBJECT_NOT_FOUND": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.Error.STS_OBJECT_NOT_FOUND", "name": "STS_OBJECT_NOT_FOUND", "type": "builtins.int"}}, "STS_OK": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.<PERSON><PERSON><PERSON>.STS_OK", "name": "STS_OK", "type": "builtins.int"}}, "STS_OUT_OF_RANGE": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.Error.STS_OUT_OF_RANGE", "name": "STS_OUT_OF_RANGE", "type": "builtins.int"}}, "STS_PARSE_ERROR": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.Error.STS_PARSE_ERROR", "name": "STS_PARSE_ERROR", "type": "builtins.int"}}, "STS_UNMATCHED_FORMATS": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.Error.STS_UNMATCHED_FORMATS", "name": "STS_UNMATCHED_FORMATS", "type": "builtins.int"}}, "STS_UNMATCHED_SIZES": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.Error.STS_UNMATCHED_SIZES", "name": "STS_UNMATCHED_SIZES", "type": "builtins.int"}}, "STS_UNSUPPORTED_FORMAT": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.Error.STS_UNSUPPORTED_FORMAT", "name": "STS_UNSUPPORTED_FORMAT", "type": "builtins.int"}}, "STS_VEC_LENGTH_ERR": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.Error.STS_VEC_LENGTH_ERR", "name": "STS_VEC_LENGTH_ERR", "type": "builtins.int"}}, "StsAssert": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.<PERSON><PERSON><PERSON><PERSON>", "name": "StsAssert", "type": "builtins.int"}}, "StsAutoTrace": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.<PERSON><PERSON><PERSON>.StsAutoTrace", "name": "StsAutoTrace", "type": "builtins.int"}}, "StsBackTrace": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.<PERSON><PERSON><PERSON>.StsBackTrace", "name": "StsBackTrace", "type": "builtins.int"}}, "StsBadArg": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.<PERSON><PERSON><PERSON>.StsBadArg", "name": "StsBadArg", "type": "builtins.int"}}, "StsBadFlag": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.<PERSON><PERSON>r.StsBadFlag", "name": "StsBadFlag", "type": "builtins.int"}}, "StsBadFunc": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.<PERSON><PERSON><PERSON>.StsBadFunc", "name": "StsBadFunc", "type": "builtins.int"}}, "StsBadMask": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.<PERSON><PERSON><PERSON>.StsBadMask", "name": "StsBadMask", "type": "builtins.int"}}, "StsBadMemBlock": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.<PERSON><PERSON><PERSON>.StsBadMemBlock", "name": "StsBadMemBlock", "type": "builtins.int"}}, "StsBadPoint": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.<PERSON><PERSON><PERSON>.StsBadPoint", "name": "StsBadPoint", "type": "builtins.int"}}, "StsBadSize": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.<PERSON><PERSON><PERSON>.StsBadSize", "name": "StsBadSize", "type": "builtins.int"}}, "StsDivByZero": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.<PERSON><PERSON><PERSON>.StsDivByZero", "name": "StsDivByZero", "type": "builtins.int"}}, "StsError": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.<PERSON><PERSON><PERSON>.StsError", "name": "StsError", "type": "builtins.int"}}, "StsFilterOffsetErr": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.<PERSON><PERSON><PERSON>.StsFilterOffsetErr", "name": "StsFilterOffsetErr", "type": "builtins.int"}}, "StsFilterStructContentErr": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.<PERSON><PERSON><PERSON>.StsFilterStructContentErr", "name": "StsFilterStructContentErr", "type": "builtins.int"}}, "StsInplaceNotSupported": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.E<PERSON><PERSON>.StsInplaceNotSupported", "name": "StsInplaceNotSupported", "type": "builtins.int"}}, "StsInternal": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.<PERSON><PERSON><PERSON>.StsInternal", "name": "StsInternal", "type": "builtins.int"}}, "StsKernelStructContentErr": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.<PERSON><PERSON><PERSON>.StsKernelStructContentErr", "name": "StsKernelStructContentErr", "type": "builtins.int"}}, "StsNoConv": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.<PERSON><PERSON><PERSON>.StsNoConv", "name": "StsNoConv", "type": "builtins.int"}}, "StsNoMem": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.<PERSON><PERSON><PERSON>.StsNoMem", "name": "StsNoMem", "type": "builtins.int"}}, "StsNotImplemented": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.<PERSON><PERSON><PERSON>.StsNotImplemented", "name": "StsNotImplemented", "type": "builtins.int"}}, "StsNullPtr": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.<PERSON><PERSON><PERSON><PERSON>", "name": "StsNullPtr", "type": "builtins.int"}}, "StsObjectNotFound": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.E<PERSON><PERSON>.StsObjectNotFound", "name": "StsObjectNotFound", "type": "builtins.int"}}, "StsOk": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.<PERSON><PERSON><PERSON>.StsOk", "name": "StsOk", "type": "builtins.int"}}, "StsOutOfRange": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.<PERSON><PERSON><PERSON>.StsOutOfRange", "name": "StsOutOfRange", "type": "builtins.int"}}, "StsParseError": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.<PERSON><PERSON><PERSON>.StsParseError", "name": "StsParseError", "type": "builtins.int"}}, "StsUnmatchedFormats": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.<PERSON><PERSON><PERSON>.StsUnmatchedFormats", "name": "StsUnmatchedFormats", "type": "builtins.int"}}, "StsUnmatchedSizes": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.<PERSON><PERSON><PERSON>.StsUnmatchedSizes", "name": "StsUnmatchedSizes", "type": "builtins.int"}}, "StsUnsupportedFormat": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.<PERSON><PERSON><PERSON>.StsUnsupportedFormat", "name": "StsUnsupportedFormat", "type": "builtins.int"}}, "StsVecLengthErr": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.<PERSON><PERSON><PERSON>.StsVecLengthErr", "name": "StsVecLengthErr", "type": "builtins.int"}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "cv2.Error.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.Error.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.Error.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.Error.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.Error.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.Error.__package__", "name": "__package__", "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.Error.__path__", "name": "__path__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cv2.Error.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}}, "path": "c:\\Python313\\Lib\\site-packages\\cv2\\Error\\__init__.pyi"}