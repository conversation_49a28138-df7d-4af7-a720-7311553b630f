{"data_mtime": 1750100848, "dep_lines": [5, 6, 7, 4, 8, 3, 9, 10, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 10, 10, 10, 10, 10, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["cv2.gapi.ie", "cv2.gapi.onnx", "cv2.gapi.ov", "cv2.gapi", "cv2.typing", "cv2", "numpy", "typing", "builtins", "_frozen_importlib", "_typeshed", "abc", "cv2.mat_wrapper", "numpy._typing", "numpy._typing._nbit_base", "types", "typing_extensions"], "hash": "d1b7440aee517437e41d262071cd0d5b34b8485d", "id": "cv2.detail", "ignore_all": true, "interface_hash": "c3516e9347fe6c01933d2bf67e00df6cf17fd637", "mtime": 1750096905, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Python313\\Lib\\site-packages\\cv2\\detail\\__init__.pyi", "plugin_data": null, "size": 22974, "suppressed": [], "version_id": "1.15.0"}