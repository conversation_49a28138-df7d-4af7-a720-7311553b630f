{".class": "MypyFile", "_fullname": "struct", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "Struct": {".class": "SymbolTableNode", "cross_ref": "_struct.Struct", "kind": "Gdef"}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "struct.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "struct.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "struct.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "struct.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "struct.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "struct.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "struct.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "calcsize": {".class": "SymbolTableNode", "cross_ref": "_struct.calcsize", "kind": "Gdef"}, "error": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.Exception"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "struct.error", "name": "error", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "struct.error", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "struct", "mro": ["struct.error", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "struct.error.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "struct.error", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "iter_unpack": {".class": "SymbolTableNode", "cross_ref": "_struct.iter_unpack", "kind": "Gdef"}, "pack": {".class": "SymbolTableNode", "cross_ref": "_struct.pack", "kind": "Gdef"}, "pack_into": {".class": "SymbolTableNode", "cross_ref": "_struct.pack_into", "kind": "Gdef"}, "unpack": {".class": "SymbolTableNode", "cross_ref": "_struct.unpack", "kind": "Gdef"}, "unpack_from": {".class": "SymbolTableNode", "cross_ref": "_struct.unpack_from", "kind": "Gdef"}}, "path": "c:\\Users\\<USER>\\.vscode\\extensions\\ms-python.mypy-type-checker-2025.2.0\\bundled\\libs\\mypy\\typeshed\\stdlib\\struct.pyi"}